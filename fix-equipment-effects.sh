#!/bin/bash

# Lineage 381 持續特效修復腳本
# 診斷和修復遊戲中持續特效無效果的問題

echo "========================================"
echo "  Lineage 381 持續特效修復腳本"
echo "  診斷和修復特效系統問題"
echo "========================================"
echo ""

# 設定顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查前置條件
check_prerequisites() {
    log_info "檢查前置條件..."
    
    # 檢查是否在專案根目錄
    if [ ! -f "config/裝備特效優化設定.properties" ]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi
    
    # 檢查 MySQL/MariaDB
    if command -v mysql &> /dev/null; then
        log_info "MySQL/MariaDB 客戶端已安裝"
    else
        log_error "MySQL/MariaDB 客戶端未安裝"
        exit 1
    fi
    
    log_success "前置條件檢查完成"
}

# 診斷資料庫問題
diagnose_database() {
    log_info "診斷資料庫問題..."
    
    # 檢查資料庫連接
    if mysql -u root -p381 -e "SELECT 1" &>/dev/null; then
        log_success "資料庫連接正常"
    else
        log_error "無法連接到資料庫，請檢查連接設定"
        return 1
    fi
    
    # 檢查表格是否存在
    TABLE_EXISTS=$(mysql -u root -p381 -e "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '381' AND table_name = 'w_裝備持續特效';" 2>/dev/null | tail -1)
    
    if [ "$TABLE_EXISTS" = "1" ]; then
        log_success "w_裝備持續特效 表格存在"
        
        # 檢查表格資料
        DATA_COUNT=$(mysql -u root -p381 -e "SELECT COUNT(*) FROM w_裝備持續特效;" 2>/dev/null | tail -1)
        if [ "$DATA_COUNT" -gt "0" ]; then
            log_success "表格包含 $DATA_COUNT 筆特效資料"
        else
            log_warning "表格存在但無資料"
            return 2
        fi
    else
        log_error "w_裝備持續特效 表格不存在"
        return 3
    fi
    
    return 0
}

# 創建資料庫表格
create_database_table() {
    log_info "創建資料庫表格..."
    
    # 創建表格的 SQL
    cat > temp_create_table.sql << 'EOF'
-- 裝備持續特效資料庫表格
CREATE TABLE IF NOT EXISTS `w_裝備持續特效` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `armor_id` varchar(255) NOT NULL COMMENT '裝備ID，多個用逗號分隔',
  `gfxId` int(11) NOT NULL COMMENT '特效ID',
  `description` varchar(255) DEFAULT NULL COMMENT '特效描述',
  `enabled` tinyint(1) DEFAULT 1 COMMENT '是否啟用',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_armor_id` (`armor_id`),
  KEY `idx_gfxId` (`gfxId`),
  KEY `idx_enabled` (`enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='裝備持續特效設定表';

-- 插入測試資料
INSERT IGNORE INTO `w_裝備持續特效` (`armor_id`, `gfxId`, `description`, `enabled`) VALUES
('20011,20012,20013', 1001, '精靈套裝特效', 1),
('20021,20022,20023', 1002, '騎士套裝特效', 1),
('20031,20032,20033', 1003, '法師套裝特效', 1),
('20041,20042,20043', 1004, '王族套裝特效', 1),
('20051,20052,20053', 1005, '黑暗妖精套裝特效', 1),
('20061,20062,20063', 1006, '龍騎士套裝特效', 1),
('20071,20072,20073', 1007, '幻術師套裝特效', 1),
('20081,20082,20083', 1008, '戰士套裝特效', 1),
('100001', 2001, '單件裝備特效1', 1),
('100002', 2002, '單件裝備特效2', 1),
('100003', 2003, '單件裝備特效3', 1);
EOF

    # 執行 SQL
    if mysql -u root -p381 < temp_create_table.sql; then
        log_success "資料庫表格創建完成"
        rm temp_create_table.sql
        return 0
    else
        log_error "資料庫表格創建失敗"
        rm temp_create_table.sql
        return 1
    fi
}

# 檢查配置文件
check_configuration() {
    log_info "檢查配置文件..."
    
    CONFIG_FILE="config/裝備特效優化設定.properties"
    
    # 檢查關鍵配置
    if grep -q "ENABLE_EQUIPMENT_EFFECT_OPTIMIZATION=true" "$CONFIG_FILE"; then
        log_success "特效優化已啟用"
    else
        log_warning "特效優化未啟用"
        # 修復配置
        sed -i 's/ENABLE_EQUIPMENT_EFFECT_OPTIMIZATION=false/ENABLE_EQUIPMENT_EFFECT_OPTIMIZATION=true/' "$CONFIG_FILE"
        log_info "已自動啟用特效優化"
    fi
    
    # 檢查除錯模式
    if grep -q "ENABLE_DEBUG_MODE=false" "$CONFIG_FILE"; then
        log_info "啟用除錯模式以便診斷..."
        sed -i 's/ENABLE_DEBUG_MODE=false/ENABLE_DEBUG_MODE=true/' "$CONFIG_FILE"
        sed -i 's/DEBUG_EFFECT_DETAILS=false/DEBUG_EFFECT_DETAILS=true/' "$CONFIG_FILE"
        log_success "除錯模式已啟用"
    fi
    
    # 調整特效檢查間隔
    if grep -q "EFFECT_CHECK_INTERVAL=15000" "$CONFIG_FILE"; then
        log_info "調整特效檢查間隔..."
        sed -i 's/EFFECT_CHECK_INTERVAL=15000/EFFECT_CHECK_INTERVAL=5000/' "$CONFIG_FILE"
        log_success "特效檢查間隔已調整為5秒"
    fi
}

# 創建 GM 測試命令
create_gm_commands() {
    log_info "創建 GM 測試命令..."
    
    # 創建測試命令文件
    cat > gm-effect-commands.txt << 'EOF'
# GM 特效測試命令
# 在遊戲中使用以下命令測試特效系統

.testeffect     # 測試特效系統，顯示除錯資訊
.reloadeffect   # 重新載入特效系統
.effectstats    # 顯示特效系統統計資訊
.startgfx       # 手動啟動特效計時器
.stopgfx        # 停止特效計時器

# 使用方法：
# 1. 登入遊戲
# 2. 穿戴測試裝備
# 3. 使用 .testeffect 命令
# 4. 查看伺服器日誌輸出
# 5. 使用 .effectstats 查看統計
EOF

    log_success "GM 測試命令已創建: gm-effect-commands.txt"
}

# 創建除錯增強代碼
create_debug_enhancement() {
    log_info "創建除錯增強代碼..."
    
    # 創建除錯增強文件
    cat > debug-enhancement.java << 'EOF'
// 在 ArmorSkillSound.java 中添加的除錯方法

/**
 * 除錯特效系統
 */
public static void debugEffectSystem(L1PcInstance pc) {
    System.out.println("=== 特效系統除錯 ===");
    System.out.println("玩家: " + pc.getName() + " (ID: " + pc.getId() + ")");
    System.out.println("特效快取大小: " + effectCache.size());
    System.out.println("是否已初始化: " + isInitialized);
    
    // 檢查玩家裝備
    System.out.println("玩家裝備:");
    boolean hasAnyEquipment = false;
    for (int i = 0; i < 15; i++) {
        L1ItemInstance item = pc.getInventory().getItemEquipped(i);
        if (item != null) {
            System.out.println("  位置 " + i + ": " + item.getItem().getName() + 
                             " (ID: " + item.getItem().getItemId() + ")");
            hasAnyEquipment = true;
        }
    }
    
    if (!hasAnyEquipment) {
        System.out.println("  無裝備");
    }
    
    // 檢查特效資料
    System.out.println("特效資料:");
    if (effectCache.isEmpty()) {
        System.out.println("  特效快取為空！");
    } else {
        for (EffectData effectData : effectCache.values()) {
            System.out.println("  特效ID: " + effectData.gfxId + 
                             ", 裝備ID: " + Arrays.toString(effectData.armorIds));
            if (hasRequiredArmor(pc, effectData.armorIds)) {
                System.out.println("    -> 符合條件！應該觸發特效");
            }
        }
    }
    
    // 檢查冷卻狀態
    Long lastEffectTime = playerEffectTimestamps.get(pc.getId());
    if (lastEffectTime != null) {
        long cooldownRemaining = EFFECT_COOLDOWN - (System.currentTimeMillis() - lastEffectTime);
        if (cooldownRemaining > 0) {
            System.out.println("特效冷卻中，剩餘: " + cooldownRemaining + "ms");
        } else {
            System.out.println("特效冷卻已結束");
        }
    } else {
        System.out.println("無特效冷卻記錄");
    }
    
    System.out.println("==================");
}

/**
 * 強制觸發特效（忽略冷卻）
 */
public static void forceEffectTrigger(L1PcInstance pc) {
    System.out.println("強制觸發特效: " + pc.getName());
    
    // 清除冷卻
    playerEffectTimestamps.remove(pc.getId());
    
    // 觸發特效
    forArmorSkillSound(pc);
}
EOF

    log_success "除錯增強代碼已創建: debug-enhancement.java"
}

# 生成診斷報告
generate_diagnosis_report() {
    log_info "生成診斷報告..."
    
    REPORT_FILE="effect-diagnosis-report.txt"
    
    {
        echo "========================================"
        echo "持續特效診斷報告"
        echo "生成時間: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 資料庫檢查結果:"
        if mysql -u root -p381 -e "SELECT COUNT(*) as table_count FROM information_schema.tables WHERE table_schema = '381' AND table_name = 'w_裝備持續特效';" 2>/dev/null | tail -1 | grep -q "1"; then
            echo "   ✅ w_裝備持續特效 表格存在"
            DATA_COUNT=$(mysql -u root -p381 -e "SELECT COUNT(*) FROM w_裝備持續特效;" 2>/dev/null | tail -1)
            echo "   ✅ 表格包含 $DATA_COUNT 筆資料"
        else
            echo "   ❌ w_裝備持續特效 表格不存在"
        fi
        echo ""
        
        echo "2. 配置檢查結果:"
        if grep -q "ENABLE_EQUIPMENT_EFFECT_OPTIMIZATION=true" "config/裝備特效優化設定.properties"; then
            echo "   ✅ 特效優化已啟用"
        else
            echo "   ❌ 特效優化未啟用"
        fi
        
        if grep -q "ENABLE_DEBUG_MODE=true" "config/裝備特效優化設定.properties"; then
            echo "   ✅ 除錯模式已啟用"
        else
            echo "   ❌ 除錯模式未啟用"
        fi
        echo ""
        
        echo "3. 修復建議:"
        echo "   1. 確保資料庫表格存在且有資料"
        echo "   2. 啟用除錯模式查看詳細日誌"
        echo "   3. 使用 GM 命令測試特效"
        echo "   4. 檢查伺服器日誌輸出"
        echo ""
        
        echo "4. 測試步驟:"
        echo "   1. 重新啟動遊戲伺服器"
        echo "   2. 登入遊戲並穿戴裝備"
        echo "   3. 使用 .testeffect 命令"
        echo "   4. 查看伺服器日誌: tail -f logs/lineage-server.log | grep -i effect"
        echo ""
        
        echo "5. 常見問題:"
        echo "   - 表格不存在: 執行此腳本創建表格"
        echo "   - 無特效資料: 檢查資料庫插入是否成功"
        echo "   - 裝備ID不匹配: 檢查實際裝備ID"
        echo "   - 特效冷卻: 調整 EFFECT_CHECK_INTERVAL"
        
    } > "$REPORT_FILE"
    
    log_success "診斷報告已生成: $REPORT_FILE"
}

# 主函數
main() {
    log_info "開始持續特效診斷和修復"
    
    # 檢查前置條件
    check_prerequisites
    
    # 診斷資料庫
    diagnose_database
    DB_STATUS=$?
    
    if [ $DB_STATUS -eq 3 ] || [ $DB_STATUS -eq 2 ]; then
        log_info "需要創建或修復資料庫表格"
        create_database_table
    fi
    
    # 檢查配置
    check_configuration
    
    # 創建測試工具
    create_gm_commands
    create_debug_enhancement
    
    # 生成診斷報告
    generate_diagnosis_report
    
    echo ""
    echo "========================================"
    log_success "持續特效診斷和修復完成！"
    echo "========================================"
    echo ""
    echo "修復摘要："
    echo "✅ 資料庫表格檢查/創建"
    echo "✅ 配置文件優化"
    echo "✅ 除錯工具創建"
    echo "✅ GM 測試命令準備"
    echo "✅ 診斷報告生成"
    echo ""
    echo "下一步："
    echo "1. 重新啟動遊戲伺服器"
    echo "2. 查看診斷報告: cat effect-diagnosis-report.txt"
    echo "3. 使用 GM 命令測試: cat gm-effect-commands.txt"
    echo "4. 監控日誌: tail -f logs/lineage-server.log | grep -i effect"
    echo ""
    echo "如果問題仍然存在，請檢查："
    echo "- 裝備ID是否正確"
    echo "- 特效ID是否有效"
    echo "- 玩家是否穿戴了對應裝備"
}

# 處理中斷信號
trap 'log_error "診斷被中斷"; exit 1' INT TERM

# 執行主流程
main "$@"
