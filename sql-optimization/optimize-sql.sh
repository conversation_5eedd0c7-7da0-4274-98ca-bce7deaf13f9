#!/bin/bash

# Lineage 381 SQL 優化腳本
# 優化資料庫配置、連接池和 SQL 查詢

echo "========================================"
echo "  Lineage 381 SQL 優化腳本"
echo "  提升資料庫效能和安全性"
echo "========================================"
echo ""

# 設定顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查前置條件
check_prerequisites() {
    log_info "檢查前置條件..."
    
    # 檢查是否在專案根目錄
    if [ ! -f "config/sql.properties" ]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi
    
    # 檢查 MariaDB/MySQL 是否運行
    if command -v mysql &> /dev/null; then
        log_info "MySQL/MariaDB 客戶端已安裝"
    else
        log_warning "MySQL/MariaDB 客戶端未安裝，部分功能可能無法使用"
    fi
    
    log_success "前置條件檢查完成"
}

# 備份當前 SQL 配置
backup_sql_config() {
    log_info "備份當前 SQL 配置..."
    
    BACKUP_DIR="sql-backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 備份配置文件
    if [ -f "config/sql.properties" ]; then
        cp config/sql.properties "$BACKUP_DIR/"
        log_success "sql.properties 已備份"
    fi
    
    if [ -f "config/c3p0-config.xml" ]; then
        cp config/c3p0-config.xml "$BACKUP_DIR/"
        log_success "c3p0-config.xml 已備份"
    fi
    
    if [ -f "config/application.yml" ]; then
        cp config/application.yml "$BACKUP_DIR/"
        log_success "application.yml 已備份"
    fi
    
    echo "BACKUP_DIR=$BACKUP_DIR" > .sql-backup-info
    log_success "SQL 配置備份完成: $BACKUP_DIR"
}

# 分析當前 SQL 配置
analyze_sql_config() {
    log_info "分析當前 SQL 配置..."
    
    # 檢查密碼安全性
    if grep -q "Password.*=" config/sql.properties; then
        PASSWORD=$(grep "Password.*=" config/sql.properties | head -1 | cut -d'=' -f2 | tr -d ' ')
        if [ ${#PASSWORD} -lt 8 ]; then
            log_warning "資料庫密碼長度不足 8 位，建議使用更強密碼"
        fi
        
        if [[ "$PASSWORD" =~ ^[0-9]+$ ]]; then
            log_warning "資料庫密碼僅包含數字，建議使用複雜密碼"
        fi
    fi
    
    # 檢查連接池配置
    if [ -f "config/c3p0-config.xml" ]; then
        MAX_POOL=$(grep "maxPoolSize" config/c3p0-config.xml | sed 's/.*>\([0-9]*\)<.*/\1/')
        if [ "$MAX_POOL" -gt 100 ]; then
            log_warning "連接池最大連接數過高: $MAX_POOL，可能影響效能"
        fi
    fi
    
    # 檢查 SSL 配置
    if grep -q "useSSL=false" config/sql.properties; then
        log_warning "資料庫連接未啟用 SSL，存在安全風險"
    fi
    
    log_success "SQL 配置分析完成"
}

# 優化 SQL 配置
optimize_sql_config() {
    log_info "優化 SQL 配置..."
    
    # 創建優化後的 sql.properties
    cat > config/sql.properties.optimized << 'EOF'
#-------------------------------------------------------------
# SQL config (MariaDB 10.11 優化版)
#-------------------------------------------------------------

# 資料庫驅動程式 (MariaDB 10.11)
Driver_LOGIN = org.mariadb.jdbc.Driver

# 資料庫位置(登入伺服器) (MariaDB 10.11)
URL1_LOGIN = *****************************/

# 資料庫名稱(登入伺服器)
URL2_LOGIN = 381

# 連接額外條件(登入伺服器) (MariaDB 10.11 優化)
URL3_LOGIN = ?useUnicode=true&characterEncoding=utf8&useSSL=true&allowPublicKeyRetrieval=true&serverTimezone=Asia/Taipei&rewriteBatchedStatements=true&cachePrepStmts=true&useServerPrepStmts=true&autoReconnect=true&maxReconnects=3&socketTimeout=30000&connectTimeout=30000&useCompression=true&useBulkStmts=false

# 資料庫 帳號名稱(登入伺服器) - 建議使用環境變數
Login_LOGIN = ${DB_USERNAME:lineage_user}

# 資料庫 帳號密碼(登入伺服器) - 建議使用環境變數
Password_LOGIN = ${DB_PASSWORD:secure_password}

# 資料庫位置(遊戲伺服器) (MariaDB 10.11)
URL1 = *****************************/

# 資料庫名稱(遊戲伺服器)
URL2 = 381

# 連接額外條件(遊戲伺服器) (MariaDB 10.11 優化)
URL3 = ?useUnicode=true&characterEncoding=utf8&useSSL=true&allowPublicKeyRetrieval=true&serverTimezone=Asia/Taipei&rewriteBatchedStatements=true&cachePrepStmts=true&useServerPrepStmts=true&autoReconnect=true&maxReconnects=3&socketTimeout=30000&connectTimeout=30000&useCompression=true&useBulkStmts=false

# 資料庫 帳號名稱(遊戲伺服器) - 建議使用環境變數
Login = ${DB_USERNAME:lineage_user}

# 資料庫 帳號密碼(遊戲伺服器) - 建議使用環境變數
Password = ${DB_PASSWORD:secure_password}
EOF

    log_success "優化的 sql.properties 已創建"
    
    # 創建 HikariCP 配置
    cat > config/hikari-config.yml << 'EOF'
# HikariCP 連接池配置 (替代 C3P0)
hikari:
  # 連接池基本配置
  maximum-pool-size: 50
  minimum-idle: 10
  connection-timeout: 30000
  idle-timeout: 600000
  max-lifetime: 1800000
  leak-detection-threshold: 60000
  
  # 連接池名稱
  pool-name: LineageHikariPool
  
  # 連接測試
  connection-test-query: SELECT 1
  validation-timeout: 5000
  
  # 效能優化
  cache-prep-stmts: true
  prep-stmt-cache-size: 250
  prep-stmt-cache-sql-limit: 2048
  use-server-prep-stmts: true
  
  # 資料來源屬性
  data-source-properties:
    cachePrepStmts: true
    prepStmtCacheSize: 250
    prepStmtCacheSqlLimit: 2048
    useServerPrepStmts: true
    useLocalSessionState: true
    rewriteBatchedStatements: true
    cacheResultSetMetadata: true
    cacheServerConfiguration: true
    elideSetAutoCommits: true
    maintainTimeStats: false
EOF

    log_success "HikariCP 配置已創建"
}

# 創建資料庫安全腳本
create_security_script() {
    log_info "創建資料庫安全腳本..."
    
    cat > sql-optimization/create-db-user.sql << 'EOF'
-- Lineage 381 資料庫安全配置
-- 創建專用資料庫用戶，替代 root 用戶

-- 創建專用用戶 (請修改密碼)
CREATE USER IF NOT EXISTS 'lineage_user'@'localhost' IDENTIFIED BY 'your_secure_password_here';

-- 授予登入資料庫權限
GRANT SELECT, INSERT, UPDATE, DELETE ON `381`.* TO 'lineage_user'@'localhost';

-- 授予必要的管理權限 (謹慎使用)
GRANT CREATE, DROP, ALTER, INDEX ON `381`.* TO 'lineage_user'@'localhost';

-- 如果需要存儲過程權限
GRANT EXECUTE ON `381`.* TO 'lineage_user'@'localhost';

-- 刷新權限
FLUSH PRIVILEGES;

-- 檢查用戶權限
SHOW GRANTS FOR 'lineage_user'@'localhost';

-- 測試連接 (可選)
-- mysql -u lineage_user -p -h localhost 381
EOF

    log_success "資料庫安全腳本已創建: sql-optimization/create-db-user.sql"
}

# 創建 SQL 效能優化腳本
create_performance_script() {
    log_info "創建 SQL 效能優化腳本..."
    
    cat > sql-optimization/optimize-database.sql << 'EOF'
-- Lineage 381 資料庫效能優化
-- 優化資料庫配置和索引

-- 設定資料庫參數
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL innodb_log_file_size = *********; -- 256MB
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL sync_binlog = 0;
SET GLOBAL innodb_doublewrite = 0;

-- 查詢快取設定 (MySQL 5.7 及以下)
-- SET GLOBAL query_cache_size = ********; -- 64MB
-- SET GLOBAL query_cache_type = ON;

-- 連接相關設定
SET GLOBAL max_connections = 500;
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;

-- 檢查常用表的索引
-- 角色表索引優化
ALTER TABLE `characters` ADD INDEX IF NOT EXISTS `idx_account_name` (`account_name`);
ALTER TABLE `characters` ADD INDEX IF NOT EXISTS `idx_char_name` (`char_name`);
ALTER TABLE `characters` ADD INDEX IF NOT EXISTS `idx_level` (`level`);

-- 物品表索引優化
ALTER TABLE `character_items` ADD INDEX IF NOT EXISTS `idx_char_id` (`char_id`);
ALTER TABLE `character_items` ADD INDEX IF NOT EXISTS `idx_item_id` (`item_id`);

-- 血盟表索引優化
ALTER TABLE `clan_data` ADD INDEX IF NOT EXISTS `idx_clan_name` (`clan_name`);
ALTER TABLE `clan_members` ADD INDEX IF NOT EXISTS `idx_char_id` (`char_id`);
ALTER TABLE `clan_members` ADD INDEX IF NOT EXISTS `idx_clan_id` (`clan_id`);

-- 帳號表索引優化
ALTER TABLE `accounts` ADD INDEX IF NOT EXISTS `idx_login` (`login`);
ALTER TABLE `accounts` ADD INDEX IF NOT EXISTS `idx_lastactive` (`lastactive`);

-- 分析表統計資訊
ANALYZE TABLE `characters`;
ANALYZE TABLE `character_items`;
ANALYZE TABLE `clan_data`;
ANALYZE TABLE `clan_members`;
ANALYZE TABLE `accounts`;

-- 優化表
OPTIMIZE TABLE `characters`;
OPTIMIZE TABLE `character_items`;
OPTIMIZE TABLE `clan_data`;
OPTIMIZE TABLE `clan_members`;
OPTIMIZE TABLE `accounts`;

-- 顯示優化結果
SHOW TABLE STATUS LIKE 'characters';
SHOW TABLE STATUS LIKE 'character_items';
EOF

    log_success "資料庫效能優化腳本已創建: sql-optimization/optimize-database.sql"
}

# 創建監控腳本
create_monitoring_script() {
    log_info "創建資料庫監控腳本..."
    
    cat > sql-optimization/monitor-database.sql << 'EOF'
-- Lineage 381 資料庫監控查詢
-- 用於監控資料庫效能和狀態

-- 1. 檢查資料庫連接狀態
SELECT 
    VARIABLE_NAME,
    VARIABLE_VALUE
FROM INFORMATION_SCHEMA.GLOBAL_STATUS 
WHERE VARIABLE_NAME IN (
    'Connections',
    'Max_used_connections',
    'Threads_connected',
    'Threads_running',
    'Aborted_connects',
    'Aborted_clients'
);

-- 2. 檢查 InnoDB 狀態
SELECT 
    VARIABLE_NAME,
    VARIABLE_VALUE
FROM INFORMATION_SCHEMA.GLOBAL_STATUS 
WHERE VARIABLE_NAME LIKE 'Innodb%'
AND VARIABLE_NAME IN (
    'Innodb_buffer_pool_reads',
    'Innodb_buffer_pool_read_requests',
    'Innodb_buffer_pool_pages_free',
    'Innodb_buffer_pool_pages_total'
);

-- 3. 檢查慢查詢
SELECT 
    VARIABLE_NAME,
    VARIABLE_VALUE
FROM INFORMATION_SCHEMA.GLOBAL_STATUS 
WHERE VARIABLE_NAME IN (
    'Slow_queries',
    'Questions',
    'Uptime'
);

-- 4. 檢查表大小
SELECT 
    table_name AS '表名',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS '大小(MB)',
    table_rows AS '記錄數'
FROM information_schema.tables 
WHERE table_schema = '381'
ORDER BY (data_length + index_length) DESC
LIMIT 20;

-- 5. 檢查正在執行的查詢
SELECT 
    ID,
    USER,
    HOST,
    DB,
    COMMAND,
    TIME,
    STATE,
    LEFT(INFO, 100) AS QUERY
FROM INFORMATION_SCHEMA.PROCESSLIST 
WHERE COMMAND != 'Sleep'
ORDER BY TIME DESC;

-- 6. 檢查鎖定狀態
SELECT 
    r.trx_id waiting_trx_id,
    r.trx_mysql_thread_id waiting_thread,
    r.trx_query waiting_query,
    b.trx_id blocking_trx_id,
    b.trx_mysql_thread_id blocking_thread,
    b.trx_query blocking_query
FROM information_schema.innodb_lock_waits w
INNER JOIN information_schema.innodb_trx b ON b.trx_id = w.blocking_trx_id
INNER JOIN information_schema.innodb_trx r ON r.trx_id = w.requesting_trx_id;
EOF

    log_success "資料庫監控腳本已創建: sql-optimization/monitor-database.sql"
}

# 生成優化報告
generate_optimization_report() {
    log_info "生成 SQL 優化報告..."
    
    REPORT_FILE="sql-optimization-report.txt"
    
    {
        echo "========================================"
        echo "SQL 優化報告"
        echo "生成時間: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 配置文件備份:"
        if [ -f ".sql-backup-info" ]; then
            cat .sql-backup-info
        fi
        echo ""
        
        echo "2. 優化項目:"
        echo "   ✅ SQL 配置優化"
        echo "   ✅ 連接池升級建議 (C3P0 → HikariCP)"
        echo "   ✅ 安全性強化"
        echo "   ✅ 效能調優腳本"
        echo "   ✅ 監控腳本"
        echo ""
        
        echo "3. 創建的文件:"
        echo "   - config/sql.properties.optimized"
        echo "   - config/hikari-config.yml"
        echo "   - sql-optimization/create-db-user.sql"
        echo "   - sql-optimization/optimize-database.sql"
        echo "   - sql-optimization/monitor-database.sql"
        echo ""
        
        echo "4. 下一步建議:"
        echo "   1. 執行資料庫安全腳本"
        echo "   2. 應用優化配置"
        echo "   3. 升級到 HikariCP"
        echo "   4. 執行效能優化腳本"
        echo "   5. 設定監控"
        echo ""
        
        echo "5. 注意事項:"
        echo "   - 在生產環境執行前請先測試"
        echo "   - 建議在低峰時段執行優化"
        echo "   - 執行前請備份資料庫"
        
    } > "$REPORT_FILE"
    
    log_success "SQL 優化報告已生成: $REPORT_FILE"
}

# 主函數
main() {
    log_info "開始 SQL 優化流程"
    
    # 創建優化目錄
    mkdir -p sql-optimization
    
    # 檢查前置條件
    check_prerequisites
    
    # 備份當前配置
    backup_sql_config
    
    # 分析當前配置
    analyze_sql_config
    
    # 優化 SQL 配置
    optimize_sql_config
    
    # 創建安全腳本
    create_security_script
    
    # 創建效能優化腳本
    create_performance_script
    
    # 創建監控腳本
    create_monitoring_script
    
    # 生成優化報告
    generate_optimization_report
    
    echo ""
    echo "========================================"
    log_success "SQL 優化完成！"
    echo "========================================"
    echo ""
    echo "優化摘要："
    echo "✅ 配置已分析和優化"
    echo "✅ 安全腳本已創建"
    echo "✅ 效能優化腳本已創建"
    echo "✅ 監控腳本已創建"
    echo "✅ 優化報告已生成"
    echo ""
    echo "下一步："
    echo "1. 查看優化報告: cat sql-optimization-report.txt"
    echo "2. 執行安全腳本: mysql -u root -p < sql-optimization/create-db-user.sql"
    echo "3. 應用優化配置: cp config/sql.properties.optimized config/sql.properties"
    echo "4. 執行效能優化: mysql -u root -p 381 < sql-optimization/optimize-database.sql"
}

# 處理中斷信號
trap 'log_error "SQL 優化被中斷"; exit 1' INT TERM

# 執行主流程
main "$@"
