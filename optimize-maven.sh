#!/bin/bash

# Lineage 381 Maven 優化腳本
# 自動優化 Maven 配置和構建環境

echo "========================================"
echo "  Lineage 381 Maven 優化腳本"
echo "  提升構建效能和開發體驗"
echo "========================================"
echo ""

# 設定顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 檢查前置條件
check_prerequisites() {
    log_info "檢查前置條件..."
    
    # 檢查是否在專案根目錄
    if [ ! -f "pom.xml" ]; then
        log_error "請在專案根目錄執行此腳本"
        exit 1
    fi
    
    # 檢查 Java 版本
    if command -v java &> /dev/null; then
        JAVA_VERSION=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
        log_info "當前 Java 版本: $JAVA_VERSION"
        
        if [ "$JAVA_VERSION" -lt 11 ]; then
            log_warning "建議升級到 Java 11 或更高版本以獲得最佳效能"
        fi
    else
        log_error "Java 未安裝"
        exit 1
    fi
    
    log_success "前置條件檢查完成"
}

# 備份當前配置
backup_current_config() {
    log_info "備份當前配置..."
    
    BACKUP_DIR="maven-backup-$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # 備份 pom.xml
    if [ -f "pom.xml" ]; then
        cp pom.xml "$BACKUP_DIR/"
        log_success "pom.xml 已備份"
    fi
    
    # 備份 Maven 設定檔 (如果存在)
    if [ -f "$HOME/.m2/settings.xml" ]; then
        cp "$HOME/.m2/settings.xml" "$BACKUP_DIR/"
        log_success "settings.xml 已備份"
    fi
    
    # 備份 .mvn 目錄 (如果存在)
    if [ -d ".mvn" ]; then
        cp -r .mvn "$BACKUP_DIR/"
        log_success ".mvn 目錄已備份"
    fi
    
    echo "BACKUP_DIR=$BACKUP_DIR" > .maven-backup-info
    log_success "配置備份完成: $BACKUP_DIR"
}

# 應用優化配置
apply_optimized_config() {
    log_info "應用優化配置..."
    
    # 1. 更新 pom.xml
    if [ -f "pom-optimized.xml" ]; then
        cp pom-optimized.xml pom.xml
        log_success "pom.xml 已更新為優化版本"
    else
        log_warning "pom-optimized.xml 不存在，跳過 pom.xml 更新"
    fi
    
    # 2. 創建 .mvn 目錄
    mkdir -p .mvn
    
    # 3. 複製 Maven 配置檔案
    if [ -f ".mvn/maven.config" ]; then
        log_success "maven.config 已存在"
    else
        log_warning ".mvn/maven.config 不存在，請手動創建"
    fi
    
    if [ -f ".mvn/jvm.config" ]; then
        log_success "jvm.config 已存在"
    else
        log_warning ".mvn/jvm.config 不存在，請手動創建"
    fi
    
    # 4. 設定 Maven settings.xml
    MAVEN_HOME="$HOME/.m2"
    mkdir -p "$MAVEN_HOME"
    
    if [ -f "maven-config/settings.xml" ]; then
        cp maven-config/settings.xml "$MAVEN_HOME/"
        log_success "Maven settings.xml 已更新"
    else
        log_warning "maven-config/settings.xml 不存在，請手動配置"
    fi
    
    log_success "優化配置應用完成"
}

# 清理和重新構建
clean_and_rebuild() {
    log_info "清理和重新構建..."
    
    # 清理舊的構建產物
    log_info "清理舊的構建產物..."
    if command -v mvn &> /dev/null; then
        mvn clean -q
        log_success "清理完成"
    else
        log_error "Maven 未安裝或未配置到 PATH"
        return 1
    fi
    
    # 重新下載依賴
    log_info "重新下載依賴..."
    mvn dependency:resolve -q
    if [ $? -eq 0 ]; then
        log_success "依賴下載完成"
    else
        log_warning "部分依賴下載失敗，請檢查網路連接"
    fi
    
    # 編譯測試
    log_info "編譯測試..."
    mvn compile -q
    if [ $? -eq 0 ]; then
        log_success "編譯成功"
    else
        log_error "編譯失敗，請檢查錯誤訊息"
        return 1
    fi
    
    log_success "清理和重新構建完成"
}

# 效能測試
performance_test() {
    log_info "執行效能測試..."
    
    # 測試構建時間
    log_info "測試構建時間..."
    
    echo "開始計時..." > build-time.log
    START_TIME=$(date +%s)
    
    mvn clean package -q -DskipTests=true
    BUILD_RESULT=$?
    
    END_TIME=$(date +%s)
    BUILD_TIME=$((END_TIME - START_TIME))
    
    echo "構建時間: ${BUILD_TIME}秒" >> build-time.log
    
    if [ $BUILD_RESULT -eq 0 ]; then
        log_success "構建成功，耗時: ${BUILD_TIME}秒"
    else
        log_error "構建失敗"
        return 1
    fi
    
    # 生成依賴報告
    log_info "生成依賴分析報告..."
    mvn dependency:tree > dependency-tree.txt 2>/dev/null
    mvn dependency:analyze > dependency-analysis.txt 2>/dev/null
    
    log_success "效能測試完成"
}

# 生成優化報告
generate_optimization_report() {
    log_info "生成優化報告..."
    
    REPORT_FILE="maven-optimization-report.txt"
    
    {
        echo "========================================"
        echo "Maven 優化報告"
        echo "生成時間: $(date)"
        echo "========================================"
        echo ""
        
        echo "1. 系統資訊:"
        echo "   Java 版本: $(java -version 2>&1 | head -n 1)"
        echo "   作業系統: $(uname -s)"
        echo ""
        
        echo "2. Maven 配置:"
        if command -v mvn &> /dev/null; then
            echo "   Maven 版本: $(mvn -version 2>&1 | head -n 1)"
        else
            echo "   Maven: 未安裝或未配置"
        fi
        echo ""
        
        echo "3. 構建效能:"
        if [ -f "build-time.log" ]; then
            cat build-time.log
        else
            echo "   構建時間: 未測試"
        fi
        echo ""
        
        echo "4. 依賴分析:"
        if [ -f "dependency-analysis.txt" ]; then
            echo "   依賴分析報告已生成: dependency-analysis.txt"
        fi
        if [ -f "dependency-tree.txt" ]; then
            echo "   依賴樹報告已生成: dependency-tree.txt"
        fi
        echo ""
        
        echo "5. 優化建議:"
        echo "   - 定期更新依賴版本"
        echo "   - 使用並行構建 (-T 參數)"
        echo "   - 配置適當的 JVM 記憶體"
        echo "   - 使用本地 Maven 倉庫鏡像"
        echo ""
        
        echo "6. 備份資訊:"
        if [ -f ".maven-backup-info" ]; then
            cat .maven-backup-info
        fi
        
    } > "$REPORT_FILE"
    
    log_success "優化報告已生成: $REPORT_FILE"
}

# 主函數
main() {
    log_info "開始 Maven 優化流程"
    
    # 檢查前置條件
    check_prerequisites
    
    # 備份當前配置
    backup_current_config
    
    # 應用優化配置
    apply_optimized_config
    
    # 清理和重新構建
    if clean_and_rebuild; then
        log_success "構建優化成功"
    else
        log_error "構建失敗，正在恢復備份..."
        # 恢復備份的邏輯可以在這裡添加
        exit 1
    fi
    
    # 效能測試
    performance_test
    
    # 生成優化報告
    generate_optimization_report
    
    echo ""
    echo "========================================"
    log_success "Maven 優化完成！"
    echo "========================================"
    echo ""
    echo "優化摘要："
    echo "✅ 配置已優化"
    echo "✅ 依賴已更新"
    echo "✅ 構建已測試"
    echo "✅ 報告已生成"
    echo ""
    echo "下一步建議："
    echo "1. 查看優化報告: cat maven-optimization-report.txt"
    echo "2. 執行完整測試: mvn test"
    echo "3. 檢查依賴分析: cat dependency-analysis.txt"
    echo ""
    echo "如有問題，可從備份恢復配置"
}

# 處理中斷信號
trap 'log_error "優化被中斷"; exit 1' INT TERM

# 執行主流程
main "$@"
