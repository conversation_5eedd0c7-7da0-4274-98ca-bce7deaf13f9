<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
          http://maven.apache.org/xsd/settings-1.0.0.xsd">

    <!-- 本地倉庫路徑 -->
    <localRepository>${user.home}/.m2/repository</localRepository>

    <!-- 是否與遠程倉庫互動 -->
    <interactiveMode>true</interactiveMode>

    <!-- 是否離線模式 -->
    <offline>false</offline>

    <!-- 插件組 -->
    <pluginGroups>
        <pluginGroup>org.springframework.boot</pluginGroup>
        <pluginGroup>org.apache.maven.plugins</pluginGroup>
    </pluginGroups>

    <!-- 代理設定 (如果需要) -->
    <proxies>
        <!-- 
        <proxy>
            <id>optional</id>
            <active>true</active>
            <protocol>http</protocol>
            <username>proxyuser</username>
            <password>proxypass</password>
            <host>proxy.host.net</host>
            <port>80</port>
            <nonProxyHosts>local.net|some.host.com</nonProxyHosts>
        </proxy>
        -->
    </proxies>

    <!-- 伺服器認證設定 -->
    <servers>
        <!-- 
        <server>
            <id>deploymentRepo</id>
            <username>repouser</username>
            <password>repopwd</password>
        </server>
        -->
    </servers>

    <!-- 鏡像設定 -->
    <mirrors>
        <!-- 阿里雲 Maven 鏡像 (中國大陸用戶推薦) -->
        <mirror>
            <id>aliyun-central</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Central Mirror</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        
        <mirror>
            <id>aliyun-spring</id>
            <mirrorOf>spring-releases</mirrorOf>
            <name>Aliyun Spring Mirror</name>
            <url>https://maven.aliyun.com/repository/spring</url>
        </mirror>
        
        <!-- 華為雲鏡像 (備選) -->
        <!--
        <mirror>
            <id>huawei-central</id>
            <mirrorOf>central</mirrorOf>
            <name>Huawei Central Mirror</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </mirror>
        -->
    </mirrors>

    <!-- 配置檔案 -->
    <profiles>
        <!-- JDK 11 配置 -->
        <profile>
            <id>jdk-11</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>11</jdk>
            </activation>
            <properties>
                <maven.compiler.source>11</maven.compiler.source>
                <maven.compiler.target>11</maven.compiler.target>
                <maven.compiler.compilerVersion>11</maven.compiler.compilerVersion>
                <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
                <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
            </properties>
            <repositories>
                <repository>
                    <id>central</id>
                    <name>Maven Central Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>spring-releases</id>
                    <name>Spring Releases</name>
                    <url>https://repo.spring.io/release</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <name>Maven Plugin Repository</name>
                    <url>https://repo1.maven.org/maven2</url>
                    <layout>default</layout>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <releases>
                        <updatePolicy>never</updatePolicy>
                    </releases>
                </pluginRepository>
            </pluginRepositories>
        </profile>

        <!-- 開發環境配置 -->
        <profile>
            <id>development</id>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
                <maven.test.skip>false</maven.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
            </properties>
        </profile>

        <!-- 生產環境配置 -->
        <profile>
            <id>production</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
                <maven.test.skip>false</maven.test.skip>
                <maven.javadoc.skip>false</maven.javadoc.skip>
            </properties>
        </profile>

        <!-- 快速構建配置 -->
        <profile>
            <id>quick-build</id>
            <properties>
                <maven.test.skip>true</maven.test.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
                <skipTests>true</skipTests>
                <skipITs>true</skipITs>
            </properties>
        </profile>

        <!-- 效能優化配置 -->
        <profile>
            <id>performance</id>
            <properties>
                <!-- 並行構建 -->
                <maven.compile.fork>true</maven.compile.fork>
                <maven.compiler.maxmem>1024m</maven.compiler.maxmem>
                
                <!-- 測試優化 -->
                <surefire.forkCount>4</surefire.forkCount>
                <surefire.reuseForks>true</surefire.reuseForks>
                <surefire.parallel>methods</surefire.parallel>
                <surefire.threadCount>4</surefire.threadCount>
            </properties>
        </profile>

        <!-- 安全掃描配置 -->
        <profile>
            <id>security-scan</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.owasp</groupId>
                        <artifactId>dependency-check-maven</artifactId>
                        <version>9.0.10</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>check</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <!-- 預設啟用的配置檔案 -->
    <activeProfiles>
        <activeProfile>jdk-11</activeProfile>
        <activeProfile>development</activeProfile>
        <activeProfile>performance</activeProfile>
    </activeProfiles>
</settings>
