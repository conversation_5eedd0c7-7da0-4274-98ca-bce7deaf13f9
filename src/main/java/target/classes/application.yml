# Spring Boot Application Configuration
# Lineage 381 Server Configuration

spring:
  application:
    name: lineage-381-server
  
  profiles:
    active: dev
  
  # 資料庫配置
  datasource:
    login:
      driver-class-name: org.mariadb.jdbc.Driver
      url: ***********************************************************************************************************************************************************************************************************************
      username: root
      password: 0913z1007Y
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    
    game:
      driver-class-name: org.mariadb.jdbc.Driver
      url: ***********************************************************************************************************************************************************************************************************************
      username: root
      password: 0913z1007Y
      hikari:
        maximum-pool-size: 30
        minimum-idle: 10
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000

# 伺服器配置
server:
  # 遊戲伺服器配置
  game:
    enabled: true
    hostname: "*"
    port: 2000-2001
    server-no: 1
    max-online-users: 30
    auto-restart: false
    test-server: false
    test-server-password: "wftest2"
  
  # 登入伺服器配置
  login:
    enabled: true
    port: 2000
    auto-create-accounts: true
    restart-login-time: 30
  
  # 客戶端配置
  client:
    language: 3
    timezone: "CST"
  
  # 遊戲設定
  game-settings:
    gui: true
    cache-map-files: false
    news: false
    ban-ip-from-idc: true
    pc-recognize-range: 13
  
  # 自動儲存設定
  autosave:
    interval: 1200
    inventory-interval: 300

# 日誌配置
logging:
  level:
    com.lineage: INFO
    com.lineage.server: DEBUG
    com.lineage.william: INFO
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/lineage-server.log
    max-size: 100MB
    max-history: 30

# 裝備系統配置
equipment:
  # 裝備特效配置
  effect:
    enabled: true
    cache-enabled: true
    cache-cleanup-interval: 60000
    effect-cooldown: 5000
    performance-monitoring: true
    debug-mode: false
    detailed-error-logging: true
    verbose-logging: false
  
  # 裝備保護配置
  protection:
    max-weapon-level: 15
    max-armor-level: 11
    max-special-armor-level: 9

# 技能配置
skills:
  # 各職業技能配置檔案路徑
  config-path: "config/"
  files:
    - "王族_技能設定表.properties"
    - "騎士_技能設定表.properties"
    - "法師_技能設定表.properties"
    - "妖精_技能設定表.properties"
    - "黑暗妖精_技能設定表.properties"
    - "龍騎士_技能設定表.properties"
    - "幻術師_技能設定表.properties"

# 其他配置
other:
  # IP 檢查配置
  ip-check:
    enabled: true
    config-file: "config/ipcheck.properties"
  
  # 記錄配置
  record:
    enabled: true
    config-file: "config/record.properties"
  
  # 封包配置
  pack:
    config-file: "config/pack.properties"
  
  # 其他設定
  settings:
    config-file: "config/other.properties"
  
  # 替代設定
  alt-settings:
    config-file: "config/altsettings.properties"
  
  # 倍率設定
  rates:
    config-file: "config/rates.properties" 