package com.lineage.config;

import com.lineage.DatabaseFactory;
import com.lineage.server.utils.SQLUtil;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class ConfigDescs {
	private static final Log _log = LogFactory.getLog(ConfigDescs.class);
	private static final Map<Integer, String> _show_desc = new HashMap();
	private static ConfigDescs _instance;

	public static ConfigDescs get() {
		if (_instance == null) {
			_instance = new ConfigDescs();
		}
		return _instance;
	}

	private ConfigDescs() {
		load();
	}

	private void load() {
		Connection co = null;
		PreparedStatement pm = null;
		ResultSet rs = null;
		try {
			co = DatabaseFactory.get().getConnection();
			pm = co.prepareStatement("SELECT * FROM `廣播_定時_顯示`");
			rs = pm.executeQuery();
			while (rs.next()) {
				int id = rs.getInt("代碼");
				String message = rs.getString("訊息內容");
				_show_desc.put(id, message);
			}
		} catch (SQLException e) {
			_log.error("廣播_定時_顯示", e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pm);
			SQLUtil.close(co);
		}
		_log.info("廣播_定時_顯示定時公告->" + _show_desc.size());
	}

	public static String getShow(int nameid) {
		try {
			return _show_desc.get(nameid);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static int get_show_size() {
		return _show_desc.size();
	}
}