package com.lineage.config;

import com.lineage.DatabaseFactory;
import com.lineage.server.serverpackets.S_BoxMessage;
import com.lineage.server.serverpackets.S_PacketBoxGree;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.utils.SQLUtil;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

public class ConfigDrop {
    private static final Log _log = LogFactory.getLog(ConfigDrop.class);
    private static final Map<Integer, Drop> _drop_msg_list = new HashMap();
    private static final Random _random = new Random();
    private static ConfigDrop _instance;

    private ConfigDrop() {
        load();
    }

    public static ConfigDrop get() {
        if (_instance == null) {
            _instance = new ConfigDrop();
        }
        return _instance;
    }

    public static void msg(String string1, String string2, String string3, String string4) {
        if (_drop_msg_list.isEmpty()) {
            return;
        }
        try {
            int r = _random.nextInt(_drop_msg_list.size());
            String msg = _drop_msg_list.get(Integer.valueOf(r))._message;
            if (msg != null) {
                int type = _drop_msg_list.get(Integer.valueOf(r))._type;
                String out = null;
                switch (type) {
                    case 0:
                        out = String.format(msg, new Object[]{string1, string2, string3});
                        break;
                    case 1:
                        out = String.format(msg, new Object[]{string2, string1, string3});
                        break;
                    case 2:
                        out = String.format(msg, new Object[]{string1, string4, string2, string3});
                        break;
                    case 3:
                        out = String.format(msg, new Object[]{string2, string1, string4, string3});
                }

                if (ConfigOther.dropmsg == 0) {
                    World.get().broadcastPacketToAll(new S_BoxMessage(out));
                } else {
                    World.get().broadcastPacketToAll(new S_PacketBoxGree(2, out));
                    World.get().broadcastPacketToAll(new S_ServerMessage(out));
                }
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    private void load() {
        Connection co = null;
        PreparedStatement pm = null;
        ResultSet rs = null;
        int i = 0;
        try {
            co = DatabaseFactory.get().getConnection();
            pm = co.prepareStatement("SELECT * FROM `廣播_掉寶_顯示`");
            rs = pm.executeQuery();
            while (rs.next()) {
                int id = rs.getInt("代號");
                int type = rs.getInt("類型");
                if (id > 5) {
                    String message = rs.getString("訊息內容");
                    Drop msg = new Drop();
                    msg._type = type;
                    msg._message = message;
                    _drop_msg_list.put(i, msg);
                    i++;
                }
            }
        } catch (SQLException e) {
            _log.error("廣播_掉寶_顯示", e);
        } finally {
            SQLUtil.close(rs);
            SQLUtil.close(pm);
            SQLUtil.close(co);
        }
        _log.info("廣播_掉寶_顯示掉寶公告->" + _drop_msg_list.size());
    }

    private static class Drop {
        private int _type;
        private String _message;

        private Drop() {
        }
    }
}