package com.lineage.config;

import com.lineage.server.utils.PerformanceTimer;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public final class ConfigDarkElfSkill {
	private static final Log _log = LogFactory.getLog(ConfigDarkElfSkill.class);
	public static int ARMOR_BREAK_CHANCE_1;
	public static int ARMOR_BREAK_CHANCE_2;
	public static int ARMOR_BREAK_CHANCE_3;
	public static double ARMOR_BREAK_DMG;
	public static int BURNING_CHANCE;
	public static double BURNING_DMG;
	public static int DOUBLE_BREAK_CHANCE;
	public static double DOUBLE_BREAK_DMG;
	public static int DARK_BLIND_1;
	public static int DARK_BLIND_2;
	public static int DARK_BLIND_3;
	public static double DARK_BLIND_INT;
	public static double DARK_BLIND_MR;
	private static final String CONFIG_FILE = "./config/黑暗妖精_技能設定表.properties";

	public static void load() throws ConfigErrorException {
		PerformanceTimer timer = new PerformanceTimer();
		Properties set = new Properties();
		try {
			InputStream is = new FileInputStream(new File("./config/黑暗妖精_技能設定表.properties"));
			set.load(is);
			is.close();
			BURNING_CHANCE = Integer.parseInt(set.getProperty("BURNING_CHANCE", "15"));
			ARMOR_BREAK_DMG = Double.parseDouble(set.getProperty("ARMOR_BREAK_DMG", "1.3"));
			BURNING_DMG = Double.parseDouble(set.getProperty("BURNING_DMG", "1.3"));
			DOUBLE_BREAK_CHANCE = Integer.parseInt(set.getProperty("DOUBLE_BREAK_CHANCE", "15"));
			DOUBLE_BREAK_DMG = Double.parseDouble(set.getProperty("DOUBLE_BREAK_DMG", "1.5"));
			DARK_BLIND_1 = Integer.parseInt(set.getProperty("DARK_BLIND_1", "5"));
			DARK_BLIND_2 = Integer.parseInt(set.getProperty("DARK_BLIND_2", "10"));
			DARK_BLIND_3 = Integer.parseInt(set.getProperty("DARK_BLIND_3", "15"));
			DARK_BLIND_INT = Double.parseDouble(set.getProperty("DARK_BLIND_INT", "0"));
			DARK_BLIND_MR = Double.parseDouble(set.getProperty("DARK_BLIND_MR", "0"));
			ARMOR_BREAK_CHANCE_1 = Integer.parseInt(set.getProperty("ARMOR_BREAK_CHANCE_1", "5"));
			ARMOR_BREAK_CHANCE_2 = Integer.parseInt(set.getProperty("ARMOR_BREAK_CHANCE_2", "10"));
			ARMOR_BREAK_CHANCE_3 = Integer.parseInt(set.getProperty("ARMOR_BREAK_CHANCE_3", "15"));
		} catch (Exception e) {
			throw new ConfigErrorException("設置檔案遺失: ./config/黑暗妖精_技能設定表.properties");
		} finally {
			set.clear();
			_log.info("Config/黑暗妖精_技能設定表讀取完成 (" + timer.get() + "ms)");
		}
	}
}