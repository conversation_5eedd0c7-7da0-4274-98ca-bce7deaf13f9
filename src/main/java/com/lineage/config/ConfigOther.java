package com.lineage.config;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;
import java.util.StringTokenizer;

public final class ConfigOther {
    private static final String LIANG = "./config/other.properties";
    /**
     * 衝暈疊加開關
     */
    public static int SHOCK_STUN_OVERLAY;
    //小天Kevin 新增 裝備噴
    public static int Lost_Item_high;
    public static int Lost_Item_Low;
    public static int Lost_Item_Count;
    public static int Lost_Item_Rnd;
    public static int Lost_Item_high_2;
    public static int Lost_Item_Low_2;
    public static int Lost_Item_Count_2;
    public static int Lost_Item_Rnd_2;
    public static int Lost_Item_high_3;
    public static int Lost_Item_Low_3;
    public static int Lost_Item_Count_3;
    public static int Lost_Item_Rnd_3;
    public static int Lost_Item_high_4;
    public static int Lost_Item_Low_4;
    public static int Lost_Item_Count_4;
    public static int Lost_Item_Rnd_4;
    public static int Lost_Item_high_5;
    public static int Lost_Item_Low_5;
    public static int Lost_Item_Count_5;
    public static int Lost_Item_Rnd_5;
    public static int Lost_Item_high_6;
    public static int Lost_Item_Low_6;
    public static int Lost_Item_Count_6;
    public static int Lost_Item_Rnd_6;
    public static int Lost_Item_high_7;
    public static int Lost_Item_Low_7;
    public static int Lost_Item_Count_7;
    public static int Lost_Item_Rnd_7;
    public static int Lost_Item_high_8;
    public static int Lost_Item_Low_8;
    public static int Lost_Item_Count_8;
    public static int Lost_Item_Rnd_8;
    //小天Kevin 新增 技能噴
    public static int Lost_Skill_high;
    public static int Lost_Skill_Low;
    public static int Lost_Skill_Count;
    public static int Lost_Skill_Rnd;
    public static int Lost_Skill_high_2;
    public static int Lost_Skill_Low_2;
    public static int Lost_Skill_Count_2;
    public static int Lost_Skill_Rnd_2;
    public static int Lost_Skill_high_3;
    public static int Lost_Skill_Low_3;
    public static int Lost_Skill_Count_3;
    public static int Lost_Skill_Rnd_3;
    public static int Lost_Skill_high_4;
    public static int Lost_Skill_Low_4;
    public static int Lost_Skill_Count_4;
    public static int Lost_Skill_Rnd_4;
    public static int Lost_Skill_high_5;
    public static int Lost_Skill_Low_5;
    public static int Lost_Skill_Count_5;
    public static int Lost_Skill_Rnd_5;
    public static int Lost_Skill_high_6;
    public static int Lost_Skill_Low_6;
    public static int Lost_Skill_Count_6;
    public static int Lost_Skill_Rnd_6;
    public static int Lost_Skill_high_7;
    public static int Lost_Skill_Low_7;
    public static int Lost_Skill_Count_7;
    public static int Lost_Skill_Rnd_7;
    public static int Lost_Skill_high_8;
    public static int Lost_Skill_Low_8;
    public static int Lost_Skill_Count_8;
    public static int Lost_Skill_Rnd_8;

    /**
     * 道具發動強奪機制
     */
    public static int Lost_Item;
    public static int Lost_Count_1;
    public static int Lost_Count_2;
    public static int Lost_Item2;
    public static int Lost_Count2_1;
    public static int Lost_Count2_2;
    public static int ExpRateLost;
    public static int LostItem_ON;
    public static int LostItem2_ON;
    /**
     * GM上線自動隱身
     */
    public static boolean ALT_GM_HIDE;
    //以上小天Kevin新增
    public static int Call_party_itemid;
    public static int Call_party_count;
    public static int target_party_itemid;
    public static int target_party_count;
    public static String clanmsg5;
    public static String clanmsg6;
    public static int pcdmgpet;
    public static int pcdmgsumm;
    public static int petcountchatype;
    public static int petcountchatype1;
    public static int tamingmonstercount;
    public static int summmonstercount;
    public static boolean summoncountcha;
    public static boolean petcountcha;
    public static double petdmgother = 0.0D;
    public static double summondmgother = 0.0D;
    public static boolean war_pet_summ;
    public static double petdmgotherpc_war;
    public static double summondmgotherpc_war;
    public static double pcdmgpet_war;
    public static double pcdmgsumm_war;
    public static double petdmgotherpc = 0.0D;
    public static double summondmgotherpc = 0.0D;
    public static boolean Quitgameshop = false;
    public static int recaseff;
    public static int recaseffcount;
    public static int PcLevelUp;
    public static boolean Pickupitemtoall = false;
    public static boolean DropitemMsgall = false;
    public static int PetLevelUp;
    public static boolean AllCall_clan_Crown;
    public static int Call_alliance_itemid;
    public static int Call_alliance_count;
    public static int Call_clan_itemid;
    public static int Call_clan_count;
    public static int target_clan_itemid;
    public static int target_clan_count;
    public static String alliancemsg;
    public static String alliancemsg1;
    public static String alliancemsg2;
    public static String clanmsg;
    public static String partymsg;
    public static String clanmsg1;
    public static String clanmsg2;
    public static boolean Reset_Map;
    public static int Reset_Map_Time;
    public static boolean SPEED = false;
    public static double SPEED_TIME = 1.0D;
    public static double IMMUNE_TO_HARM = 1.0D;
    public static double IMMUNE_TO_HARM_NPC = 1.0D;
    public static int CharNamebyte;
    public static int CharNameEnbyte;
    public static boolean KILLRED = true;
    public static boolean IS_CHECK = false;
    public static boolean dropcolor = true;
    public static boolean CLANDEL;
    public static boolean CLANTITLE;
    public static int SCLANCOUNT;
    public static int dead_score;
    public static boolean LIGHT;
    public static boolean HPBAR;
    public static boolean SHOPINFO;
    public static int CASTLEHPR;
    public static int CASTLEMPR;
    public static int FORESTHPR;
    public static int FORESTMPR;
    public static boolean WAR_DOLL;
    public static int SET_GLOBAL;
    public static int SET_GLOBAL_COUNT;
    public static int SET_GLOBAL_TIME;
    public static int SET_SHOUT_TIME;
    public static int ENCOUNTER_LV;
    public static int ILLEGAL_SPEEDUP_PUNISHMENT;
    public static String CreateCharInfo;
    public static boolean WHO_ONLINE_MSG_ON;
    public static int[] NEW_CHAR_LOC;
    public static int[] AtkNo;
    public static int[] AtkNo_pc;
    public static int[] WAR_DISABLE_SKILLS;
    public static int[] WAR_DISABLE_ITEM;
    public static int[] MAP_IDSKILL;
    public static int[] MAP_SKILL;
    public static int[] MAP_IDITEM;
    public static int[] MAP_ITEM;
    public static int drainedMana;
    public static boolean newcharpra;
    public static int newcharpralv;
    public static int summtime;
    public static boolean logpcpower;
    public static int armor_type1;
    public static int armor_type2;
    public static int armor_type3;
    public static int armor_type4;
    public static int armor_type5;
    public static int armor_type6;
    public static int armor_type7;
    public static int armor_type8;
    public static int armor_type9;
    public static int armor_type10;
    public static int armor_type11;
    public static int armor_type12;
    public static int armor_type13;
    public static int armor_type14;
    public static int armor_type15;
    public static int armor_type16;
    public static int armor_type17;
    public static int armor_type18;
    public static int armor_type19;
    public static int armor_type20;
    public static int armor_type21;
    public static int armor_type22;
    public static int armor_type23;
    public static int armor_type24;
    public static int armor_type25;
    public static int Attack_1;
    public static int Attack_2;
    public static int Attack_3;
    public static int Attack_4;
    public static int Attack_5;
    public static int Attack_Miss;
    public static int NeedItem;
    public static int NeedItemCount;
    public static String Msg;
    public static String ItemMsg;
    public static int NeedItem1;
    public static int NeedItemCount1;
    public static String Msg1;
    public static String ItemMsg1;
    public static int[] Give_skill;
    public static int[] Give_skill1;
    public static int NeedItem2;
    public static int NeedItemCount2;
    public static String Msg2;
    public static String ItemMsg2;
    public static int[] Give_skill2;
    public static int TextMinPlayer;
    public static int TextMaxPlayer;
    public static int TextLevel;
    public static int tcheckitem;
    public static int TextMoney;
    public static int Textnpa1;
    public static int Textnpa2;
    public static int Textnpa3;
    public static int Textnpa4;
    public static int Textnpa5;
    public static int Textnpa6;
    public static int Textnpa7;
    public static int Textnpa8;
    public static int Textnpa9;
    public static int Textnpa10;
    public static boolean RankLevel = false;
    public static int day_level = 40;
    public static int restday = 18;
    public static boolean Illusionistpc = false;
    public static boolean DragonKnightpc = false;
    public static int clancount = 3;
    public static int clancountexp = 10;
    public static int clanLeaderlv = 45;
    public static boolean warProtector = true;
    public static boolean restsavepclog = true;
    public static int checkitem76;
    public static int checkitemcount76;
    public static int checkitem81;
    public static int checkitemcount81;
    public static int checkitem59;
    public static int checkitemcount59;
    public static int monbossitem = 40308;
    public static int monbossitemcount = 10000;
    public static int montime = 40;
    public static int monsec = 18;
    public static int killmsg = 1;
    public static int dropmsg = 1;
    public static int boxsmsg = 1;
    public static int killlevel = 40;
    public static boolean logpcgiveitem;
    public static int logpclevel;
    public static int logpcresthp;
    public static int logpcrestmp;
    public static boolean logpcallmsg;
    public static int logpctfcount;
    public static boolean onlydaypre;
    public static int onlydaytime;
    public static int dateStartTime;
    public static int dollcount;
    public static int dollcount1;
    public static int petlevel;
    public static int petexp;
    public static int giftset;
    public static boolean partyexp;
    public static double partyexp1;
    public static int partycount;
    public static int warehouselevel;
    public static int shopitemrest = 0;
    public static int tradelevel;
    public static boolean npclevlecolor;
    public static int Scarecrowlevel;

    /**
     * Kevin Willie
     * 穿雲箭超時限制
     */
    //Willie 穿雲箭回覆時間
    public static Long CALL_ALL_REPLY_SECOND = 5L;

    public static void load() throws ConfigErrorException {
        Properties set = new Properties();
        try {
            InputStream is = new FileInputStream(new File("./config/other.properties"));
            InputStreamReader isr = new InputStreamReader(is, "utf-8");
            set.load(isr);
            is.close();
            ALT_GM_HIDE = Boolean.parseBoolean(set.getProperty("ALT_GM_HIDE", "true"));
            Call_party_itemid = Integer.parseInt(set.getProperty("Call_party_itemid", "10"));
            Call_party_count = Integer.parseInt(set.getProperty("Call_party_count", "10"));
            target_party_itemid = Integer.parseInt(set.getProperty("target_party_itemid", "10"));
            target_party_count = Integer.parseInt(set.getProperty("target_party_count", "10"));
            clanmsg5 = set.getProperty("clanmsg5", "");
            clanmsg6 = set.getProperty("clanmsg6", "");
            pcdmgpet = Integer.parseInt(set.getProperty("pcdmgpet", "100"));
            pcdmgsumm = Integer.parseInt(set.getProperty("pcdmgsumm", "100"));
            petcountchatype = Integer.parseInt(set.getProperty("petcountchatype", "100"));
            petcountchatype1 = Integer.parseInt(set.getProperty("petcountchatype1", "100"));
            tamingmonstercount = Integer.parseInt(set.getProperty("tamingmonstercount", "10"));
            summmonstercount = Integer.parseInt(set.getProperty("summmonstercount", "10"));
            summoncountcha = Boolean.parseBoolean(set.getProperty("summoncountcha", "false"));
            petcountcha = Boolean.parseBoolean(set.getProperty("petcountcha", "false"));
            petdmgother = Double.parseDouble(set.getProperty("petdmgother", "0.0"));
            summondmgother = Double.parseDouble(set.getProperty("summondmgother", "0.0"));
            petdmgotherpc = Double.parseDouble(set.getProperty("petdmgotherpc", "0.0"));
            summondmgotherpc = Double.parseDouble(set.getProperty("summondmgotherpc", "0.0"));
            petdmgotherpc_war = Double.parseDouble(set.getProperty("petdmgotherpc_war", "0.0"));
            summondmgotherpc_war = Double.parseDouble(set.getProperty("summondmgotherpc_war", "0.0"));
            pcdmgpet_war = Double.parseDouble(set.getProperty("pcdmgpet_war", "0.0"));
            pcdmgsumm_war = Double.parseDouble(set.getProperty("pcdmgsumm_war", "0.0"));
            war_pet_summ = Boolean.parseBoolean(set.getProperty("war_pet_summ", "false"));
            Quitgameshop = Boolean.parseBoolean(set.getProperty("Quitgameshop", "false"));
            recaseff = Integer.parseInt(set.getProperty("recaseff", "20"));
            recaseffcount = Integer.parseInt(set.getProperty("recaseffcount", "20"));
            Pickupitemtoall = Boolean.parseBoolean(set.getProperty("Pickupitemtoall", "false"));
            DropitemMsgall = Boolean.parseBoolean(set.getProperty("DropitemMsgall", "false"));
            PcLevelUp = Integer.parseInt(set.getProperty("PcLevelUp", "10"));
            PetLevelUp = Integer.parseInt(set.getProperty("PetLevelUp", "50"));
            AllCall_clan_Crown = Boolean.parseBoolean(set.getProperty("AllCall_clan_Crown", "false"));
            Call_alliance_itemid = Integer.parseInt(set.getProperty("Call_alliance_itemid", "10"));
            Call_alliance_count = Integer.parseInt(set.getProperty("Call_alliance_count", "10"));
            alliancemsg = set.getProperty("alliancemsg", "");
            alliancemsg1 = set.getProperty("alliancemsg1", "");
            alliancemsg2 = set.getProperty("alliancemsg2", "");
            Call_clan_itemid = Integer.parseInt(set.getProperty("Call_clan_itemid", "10"));
            Call_clan_count = Integer.parseInt(set.getProperty("Call_clan_count", "10"));
            target_clan_itemid = Integer.parseInt(set.getProperty("target_clan_itemid", "10"));
            target_clan_count = Integer.parseInt(set.getProperty("target_clan_count", "10"));
            clanmsg = set.getProperty("clanmsg", "");
            partymsg = set.getProperty("partymsg", "");
            clanmsg1 = set.getProperty("clanmsg1", "");
            clanmsg2 = set.getProperty("clanmsg2", "");
            Reset_Map = Boolean.parseBoolean(set.getProperty("Reset_Map", "false"));
            Reset_Map_Time = Integer.parseInt(set.getProperty("Reset_Map_Time", "20"));
            MAP_IDITEM = toIntArray(set.getProperty("MAP_IDITEM", ""), ",");
            MAP_ITEM = toIntArray(set.getProperty("MAP_ITEM", ""), ",");
            MAP_IDSKILL = toIntArray(set.getProperty("MAP_IDSKILL", ""), ",");
            MAP_SKILL = toIntArray(set.getProperty("MAP_SKILL", ""), ",");
            WAR_DISABLE_SKILLS = toIntArray(set.getProperty("WAR_DISABLE_SKILLS", ""), ",");
            WAR_DISABLE_ITEM = toIntArray(set.getProperty("WAR_DISABLE_ITEM", ""), ",");
            SPEED = Boolean.parseBoolean(set.getProperty("speed", "false"));
            dropcolor = Boolean.parseBoolean(set.getProperty("dropcolor", "false"));
            SPEED_TIME = Double.parseDouble(set.getProperty("speed_time", "1.0"));
            IMMUNE_TO_HARM = Double.parseDouble(set.getProperty("IMMUNE_TO_HARM", "1.0"));
            IMMUNE_TO_HARM_NPC = Double.parseDouble(set.getProperty("IMMUNE_TO_HARM_NPC", "1.0"));
            AtkNo = toIntArray(set.getProperty("AtkNo", ""), ",");
            AtkNo_pc = toIntArray(set.getProperty("AtkNo_pc", ""), ",");
            ILLEGAL_SPEEDUP_PUNISHMENT = Integer.parseInt(set.getProperty("Punishment", "0"));
            ENCOUNTER_LV = Integer.parseInt(set.getProperty("encounter_lv", "20"));
            KILLRED = Boolean.parseBoolean(set.getProperty("kill_red", "false"));
            CLANDEL = Boolean.parseBoolean(set.getProperty("clanadel", "false"));
            CLANTITLE = Boolean.parseBoolean(set.getProperty("clanatitle", "false"));
            CharNamebyte = Integer.parseInt(set.getProperty("CharNamebyte", "5"));
            CharNameEnbyte = Integer.parseInt(set.getProperty("CharNameEnbyte", "12"));
            armor_type1 = Integer.parseInt(set.getProperty("armor_type1", "100"));
            armor_type2 = Integer.parseInt(set.getProperty("armor_type2", "100"));
            armor_type3 = Integer.parseInt(set.getProperty("armor_type3", "100"));
            armor_type4 = Integer.parseInt(set.getProperty("armor_type4", "100"));
            armor_type5 = Integer.parseInt(set.getProperty("armor_type5", "100"));
            armor_type6 = Integer.parseInt(set.getProperty("armor_type6", "100"));
            armor_type7 = Integer.parseInt(set.getProperty("armor_type7", "100"));
            armor_type8 = Integer.parseInt(set.getProperty("armor_type8", "100"));
            armor_type9 = Integer.parseInt(set.getProperty("armor_type9", "100"));
            armor_type10 = Integer.parseInt(set.getProperty("armor_type10", "100"));
            armor_type11 = Integer.parseInt(set.getProperty("armor_type11", "100"));
            armor_type12 = Integer.parseInt(set.getProperty("armor_type12", "100"));
            armor_type13 = Integer.parseInt(set.getProperty("armor_type13", "100"));
            armor_type14 = Integer.parseInt(set.getProperty("armor_type14", "100"));
            armor_type15 = Integer.parseInt(set.getProperty("armor_type15", "100"));
            armor_type16 = Integer.parseInt(set.getProperty("armor_type16", "100"));
            armor_type17 = Integer.parseInt(set.getProperty("armor_type17", "100"));
            armor_type18 = Integer.parseInt(set.getProperty("armor_type18", "100"));
            armor_type19 = Integer.parseInt(set.getProperty("armor_type19", "100"));
            armor_type20 = Integer.parseInt(set.getProperty("armor_type20", "100"));
            armor_type21 = Integer.parseInt(set.getProperty("armor_type21", "100"));
            armor_type22 = Integer.parseInt(set.getProperty("armor_type22", "100"));
            armor_type23 = Integer.parseInt(set.getProperty("armor_type23", "100"));
            armor_type24 = Integer.parseInt(set.getProperty("armor_type24", "100"));
            armor_type25 = Integer.parseInt(set.getProperty("armor_type25", "100"));
            SCLANCOUNT = Integer.parseInt(set.getProperty("sclancount", "100"));
            dead_score = Integer.parseInt(set.getProperty("dead_score", "100"));
            LIGHT = Boolean.parseBoolean(set.getProperty("light", "false"));
            HPBAR = Boolean.parseBoolean(set.getProperty("hpbar", "false"));
            SHOPINFO = Boolean.parseBoolean(set.getProperty("shopinfo", "false"));
            SET_GLOBAL = Integer.parseInt(set.getProperty("set_global", "100"));
            SET_GLOBAL_COUNT = Integer.parseInt(set.getProperty("set_global_count", "100"));
            SET_GLOBAL_TIME = Integer.parseInt(set.getProperty("set_global_time", "5"));
            SET_SHOUT_TIME = Integer.parseInt(set.getProperty("set_shout_time", "5"));
            WAR_DOLL = Boolean.parseBoolean(set.getProperty("war_doll", "true"));
            logpcpower = Boolean.parseBoolean(set.getProperty("logpcpower", "true"));
            newcharpra = Boolean.parseBoolean(set.getProperty("newcharpra", "true"));
            newcharpralv = Integer.parseInt(set.getProperty("newcharpralv", "5"));
            String tmp13 = set.getProperty("CreateCharInfo", "");
            if (!tmp13.equalsIgnoreCase("null")) {
                CreateCharInfo = tmp13;
            }
            WHO_ONLINE_MSG_ON = Boolean.parseBoolean(set.getProperty("WHO_ONLINE_MSG_ON", "true"));
            summtime = Integer.parseInt(set.getProperty("summtime", "3600"));
            drainedMana = Integer.parseInt(set.getProperty("drainedMana", "5"));
            NeedItem = Integer.parseInt(set.getProperty("NeedItem", "3600"));
            NeedItemCount = Integer.parseInt(set.getProperty("NeedItemCount", "3600"));
            Msg = set.getProperty("Msg", "");
            NeedItem1 = Integer.parseInt(set.getProperty("NeedItem1", "3600"));
            NeedItemCount1 = Integer.parseInt(set.getProperty("NeedItemCount1", "3600"));
            Msg1 = set.getProperty("Msg1", "");
            ItemMsg1 = set.getProperty("ItemMsg1", "");
            ItemMsg = set.getProperty("ItemMsg", "");
            NeedItem2 = Integer.parseInt(set.getProperty("NeedItem2", "3600"));
            NeedItemCount2 = Integer.parseInt(set.getProperty("NeedItemCount2", "3600"));
            Msg2 = set.getProperty("Msg2", "");
            ItemMsg2 = set.getProperty("ItemMsg2", "");
            Give_skill = toIntArray(set.getProperty("Give_skill", ""), ",");
            Give_skill1 = toIntArray(set.getProperty("Give_skill1", ""), ",");
            Give_skill2 = toIntArray(set.getProperty("Give_skill2", ""), ",");
            Attack_1 = Integer.parseInt(set.getProperty("Attack_1", "100"));
            Attack_2 = Integer.parseInt(set.getProperty("Attack_2", "100"));
            Attack_3 = Integer.parseInt(set.getProperty("Attack_3", "100"));
            Attack_4 = Integer.parseInt(set.getProperty("Attack_4", "100"));
            Attack_5 = Integer.parseInt(set.getProperty("Attack_5", "100"));
            Attack_Miss = Integer.parseInt(set.getProperty("Attack_Miss", "100"));
            TextMinPlayer = Integer.parseInt(set.getProperty("TextMinPlayer", "1"));
            TextMaxPlayer = Integer.parseInt(set.getProperty("TextMaxPlayer", "20"));
            TextLevel = Integer.parseInt(set.getProperty("TextLevel", "52"));
            tcheckitem = Integer.parseInt(set.getProperty("tcheckitem", "5"));
            TextMoney = Integer.parseInt(set.getProperty("TextMoney", "5"));
            Textnpa1 = Integer.parseInt(set.getProperty("Textnpa1", "5"));
            Textnpa2 = Integer.parseInt(set.getProperty("Textnpa2", "5"));
            Textnpa3 = Integer.parseInt(set.getProperty("Textnpa3", "5"));
            Textnpa4 = Integer.parseInt(set.getProperty("Textnpa4", "5"));
            Textnpa5 = Integer.parseInt(set.getProperty("Textnpa5", "5"));
            Textnpa6 = Integer.parseInt(set.getProperty("Textnpa6", "5"));
            Textnpa7 = Integer.parseInt(set.getProperty("Textnpa7", "5"));
            Textnpa8 = Integer.parseInt(set.getProperty("Textnpa8", "5"));
            Textnpa9 = Integer.parseInt(set.getProperty("Textnpa9", "5"));
            Textnpa10 = Integer.parseInt(set.getProperty("Textnpa10", "5"));
            String rb1 = set.getProperty("NEW_CHAR_LOC", "33080,33392,4");
            if (!rb1.equalsIgnoreCase("null")) {
                String[] rb2 = rb1.split(",");
                int[] arrayOfInt = ConfigOther.NEW_CHAR_LOC = new int[]{Integer.valueOf(rb2[0]).intValue(),
                        Integer.valueOf(rb2[1]).intValue(), Integer.valueOf(rb2[2]).intValue()};
            }
            RankLevel = Boolean.parseBoolean(set.getProperty("RankLevel", "false"));
            day_level = Integer.parseInt(set.getProperty("day_level", "5"));
            restday = Integer.parseInt(set.getProperty("restday", "5"));
            Illusionistpc = Boolean.parseBoolean(set.getProperty("Illusionistpc", "false"));
            DragonKnightpc = Boolean.parseBoolean(set.getProperty("DragonKnightpc", "false"));
            clancount = Integer.parseInt(set.getProperty("clancount", "5"));
            clancountexp = Integer.parseInt(set.getProperty("clancountexp", "5"));
            clanLeaderlv = Integer.parseInt(set.getProperty("clanLeaderlv", "5"));
            warProtector = Boolean.parseBoolean(set.getProperty("warProtector", "false"));
            restsavepclog = Boolean.parseBoolean(set.getProperty("restsavepclog", "false"));
            checkitem76 = Integer.parseInt(set.getProperty("checkitem76", "5"));
            checkitemcount76 = Integer.parseInt(set.getProperty("checkitemcount76", "5"));
            checkitem81 = Integer.parseInt(set.getProperty("checkitem81", "5"));
            checkitemcount81 = Integer.parseInt(set.getProperty("checkitemcount81", "5"));
            checkitem59 = Integer.parseInt(set.getProperty("checkitem59", "5"));
            checkitemcount59 = Integer.parseInt(set.getProperty("checkitemcount59", "5"));
            monbossitem = Integer.parseInt(set.getProperty("monbossitem", "5"));
            monbossitemcount = Integer.parseInt(set.getProperty("monbossitemcount", "5"));
            montime = Integer.parseInt(set.getProperty("montime", "5"));
            monsec = Integer.parseInt(set.getProperty("monsec", "5"));
            killmsg = Integer.parseInt(set.getProperty("killmsg", "5"));
            dropmsg = Integer.parseInt(set.getProperty("dropmsg", "5"));
            boxsmsg = Integer.parseInt(set.getProperty("boxsmsg", "5"));
            killlevel = Integer.parseInt(set.getProperty("killlevel", "5"));
            logpcgiveitem = Boolean.parseBoolean(set.getProperty("logpcgiveitem", "false"));
            logpclevel = Integer.parseInt(set.getProperty("logpclevel", "5"));
            logpcresthp = Integer.parseInt(set.getProperty("logpcresthp", "5"));
            logpcrestmp = Integer.parseInt(set.getProperty("logpcrestmp", "5"));
            logpcallmsg = Boolean.parseBoolean(set.getProperty("logpcallmsg", "false"));
            logpctfcount = Integer.parseInt(set.getProperty("logpctfcount", "5"));
            onlydaypre = Boolean.parseBoolean(set.getProperty("onlydaypre", "false"));
            onlydaytime = Integer.parseInt(set.getProperty("onlydaytime", "5"));
            dateStartTime = Integer.parseInt(set.getProperty("dateStartTime", "5"));
            dollcount = Integer.parseInt(set.getProperty("dollcount", "5"));
            dollcount1 = Integer.parseInt(set.getProperty("dollcount1", "5"));
            petlevel = Integer.parseInt(set.getProperty("petlevel", "5"));
            petexp = Integer.parseInt(set.getProperty("petexp", "5"));
            giftset = Integer.parseInt(set.getProperty("giftset", "5"));
            partyexp = Boolean.parseBoolean(set.getProperty("partyexp", "false"));
            partyexp1 = Double.parseDouble(set.getProperty("partyexp1", "0.1"));
            partycount = Integer.parseInt(set.getProperty("partycount", "5"));
            warehouselevel = Integer.parseInt(set.getProperty("warehouselevel", "5"));
            shopitemrest = Integer.parseInt(set.getProperty("shopitemrest", "0"));
            tradelevel = Integer.parseInt(set.getProperty("tradelevel", "5"));
            npclevlecolor = Boolean.parseBoolean(set.getProperty("npclevlecolor", "false"));
            Scarecrowlevel = Integer.parseInt(set.getProperty("Scarecrowlevel", "5"));
            //Kevin小天新增 裝備噴
            Lost_Item_high = Integer.parseInt(set.getProperty("Lost_Item_high", "10"));
            Lost_Item_Low = Integer.parseInt(set.getProperty("Lost_Item_Low", "10"));
            Lost_Item_Count = Integer.parseInt(set.getProperty("Lost_Item_Count", "10"));
            Lost_Item_Rnd = Integer.parseInt(set.getProperty("Lost_Item_Rnd", "10"));


            Lost_Item_high_2 = Integer.parseInt(set.getProperty("Lost_Item_high_2", "10"));
            Lost_Item_Low_2 = Integer.parseInt(set.getProperty("Lost_Item_Low_2", "10"));
            Lost_Item_Count_2 = Integer.parseInt(set.getProperty("Lost_Item_Count_2", "10"));
            Lost_Item_Rnd_2 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_2", "10"));

            Lost_Item_high_3 = Integer.parseInt(set.getProperty("Lost_Item_high_3", "10"));
            Lost_Item_Low_3 = Integer.parseInt(set.getProperty("Lost_Item_Low_3", "10"));
            Lost_Item_Count_3 = Integer.parseInt(set.getProperty("Lost_Item_Count_3", "10"));
            Lost_Item_Rnd_3 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_3", "10"));


            Lost_Item_high_4 = Integer.parseInt(set.getProperty("Lost_Item_high_4", "10"));
            Lost_Item_Low_4 = Integer.parseInt(set.getProperty("Lost_Item_Low_4", "10"));
            Lost_Item_Count_4 = Integer.parseInt(set.getProperty("Lost_Item_Count_4", "10"));
            Lost_Item_Rnd_4 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_4", "10"));


            Lost_Item_high_5 = Integer.parseInt(set.getProperty("Lost_Item_high_5", "10"));
            Lost_Item_Low_5 = Integer.parseInt(set.getProperty("Lost_Item_Low_5", "10"));
            Lost_Item_Count_5 = Integer.parseInt(set.getProperty("Lost_Item_Count_5", "10"));
            Lost_Item_Rnd_5 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_5", "10"));

            Lost_Item_high_6 = Integer.parseInt(set.getProperty("Lost_Item_high_6", "10"));
            Lost_Item_Low_6 = Integer.parseInt(set.getProperty("Lost_Item_Low_6", "10"));
            Lost_Item_Count_6 = Integer.parseInt(set.getProperty("Lost_Item_Count_6", "10"));
            Lost_Item_Rnd_6 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_6", "10"));

            Lost_Item_high_7 = Integer.parseInt(set.getProperty("Lost_Item_high_7", "10"));
            Lost_Item_Low_7 = Integer.parseInt(set.getProperty("Lost_Item_Low_7", "10"));
            Lost_Item_Count_7 = Integer.parseInt(set.getProperty("Lost_Item_Count_7", "10"));
            Lost_Item_Rnd_7 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_7", "10"));

            Lost_Item_high_8 = Integer.parseInt(set.getProperty("Lost_Item_high_8", "10"));
            Lost_Item_Low_8 = Integer.parseInt(set.getProperty("Lost_Item_Low_8", "10"));
            Lost_Item_Count_8 = Integer.parseInt(set.getProperty("Lost_Item_Count_8", "10"));
            Lost_Item_Rnd_8 = Integer.parseInt(set.getProperty("Lost_Item_Rnd_8", "10"));
            //Kevin小天新增 技能噴
            Lost_Skill_high = Integer.parseInt(set.getProperty("Lost_Skill_high", "10"));
            Lost_Skill_Low = Integer.parseInt(set.getProperty("Lost_Skill_Low", "10"));
            Lost_Skill_Count = Integer.parseInt(set.getProperty("Lost_Skill_Count", "10"));
            Lost_Skill_Rnd = Integer.parseInt(set.getProperty("Lost_Skill_Rnd", "10"));


            Lost_Skill_high_2 = Integer.parseInt(set.getProperty("Lost_Skill_high_2", "10"));
            Lost_Skill_Low_2 = Integer.parseInt(set.getProperty("Lost_Skill_Low_2", "10"));
            Lost_Skill_Count_2 = Integer.parseInt(set.getProperty("Lost_Skill_Count_2", "10"));
            Lost_Skill_Rnd_2 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_2", "10"));

            Lost_Skill_high_3 = Integer.parseInt(set.getProperty("Lost_Skill_high_3", "10"));
            Lost_Skill_Low_3 = Integer.parseInt(set.getProperty("Lost_Skill_Low_3", "10"));
            Lost_Skill_Count_3 = Integer.parseInt(set.getProperty("Lost_Skill_Count_3", "10"));
            Lost_Skill_Rnd_3 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_3", "10"));


            Lost_Skill_high_4 = Integer.parseInt(set.getProperty("Lost_Skill_high_4", "10"));
            Lost_Skill_Low_4 = Integer.parseInt(set.getProperty("Lost_Skill_Low_4", "10"));
            Lost_Skill_Count_4 = Integer.parseInt(set.getProperty("Lost_Skill_Count_4", "10"));
            Lost_Skill_Rnd_4 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_4", "10"));


            Lost_Skill_high_5 = Integer.parseInt(set.getProperty("Lost_Skill_high_5", "10"));
            Lost_Skill_Low_5 = Integer.parseInt(set.getProperty("Lost_Skill_Low_5", "10"));
            Lost_Skill_Count_5 = Integer.parseInt(set.getProperty("Lost_Skill_Count_5", "10"));
            Lost_Skill_Rnd_5 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_5", "10"));

            Lost_Skill_high_6 = Integer.parseInt(set.getProperty("Lost_Skill_high_6", "10"));
            Lost_Skill_Low_6 = Integer.parseInt(set.getProperty("Lost_Skill_Low_6", "10"));
            Lost_Skill_Count_6 = Integer.parseInt(set.getProperty("Lost_Skill_Count_6", "10"));
            Lost_Skill_Rnd_6 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_6", "10"));

            Lost_Skill_high_7 = Integer.parseInt(set.getProperty("Lost_Skill_high_7", "10"));
            Lost_Skill_Low_7 = Integer.parseInt(set.getProperty("Lost_Skill_Low_7", "10"));
            Lost_Skill_Count_7 = Integer.parseInt(set.getProperty("Lost_Skill_Count_7", "10"));
            Lost_Skill_Rnd_7 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_7", "10"));

            Lost_Skill_high_8 = Integer.parseInt(set.getProperty("Lost_Skill_high_8", "10"));
            Lost_Skill_Low_8 = Integer.parseInt(set.getProperty("Lost_Skill_Low_8", "10"));
            Lost_Skill_Count_8 = Integer.parseInt(set.getProperty("Lost_Skill_Count_8", "10"));
            Lost_Skill_Rnd_8 = Integer.parseInt(set.getProperty("Lost_Skill_Rnd_8", "10"));

            /**
             * Kevin 小天 新增噴道具給對方
             */
            Lost_Item = Integer.parseInt(set.getProperty("Lost_Item", "10"));
            Lost_Count_1 = Integer.parseInt(set.getProperty("Lost_Count_1", "10"));
            Lost_Count_2 = Integer.parseInt(set.getProperty("Lost_Count_2", "10"));
            //Kevin 以下直接不檢查背包也不扣背包物品，直接獲得設定
            Lost_Item2 = Integer.parseInt(set.getProperty("Lost_Item2", "10"));
            Lost_Count2_1 = Integer.parseInt(set.getProperty("Lost_Count2_1", "10"));
            Lost_Count2_2 = Integer.parseInt(set.getProperty("Lost_Count2_2", "10"));
            ExpRateLost = Integer.parseInt(set.getProperty("ExpRateLost", "0"));
            LostItem_ON = Integer.parseInt(set.getProperty("LostItem_ON", "0"));
            LostItem2_ON = Integer.parseInt(set.getProperty("LostItem2_ON", "0"));

            /**
             * Kevin Willie
             * 穿雲箭超時限制
             */
            CALL_ALL_REPLY_SECOND = Long.valueOf(set.getProperty("call_all_reply_second", "5"));
            /**
             * 衝暈疊加限制
             */
            SHOCK_STUN_OVERLAY = Integer.parseInt(set.getProperty("SHOCK_STUN_OVERLAY", "0"));

        } catch (Exception e) {
            throw new ConfigErrorException("設置檔案遺失: ./config/other.properties");
        } finally {
            set.clear();
        }
    }

    public static int[] toIntArray(String text, String type) {
        StringTokenizer st = new StringTokenizer(text, type);
        int[] iReturn = new int[st.countTokens()];
        int i = 0;
        while (i < iReturn.length) {
            iReturn[i] = Integer.parseInt(st.nextToken());
            i++;
        }
        return iReturn;
    }
}