package com.lineage.config;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

/**
 * 配置管理器
 * 統一管理所有配置檔案和設定
 */
public class ConfigurationManager {
    
    private static ConfigurationManager instance;
    private Properties properties;
    private LineageServerConfig serverConfig;
    private EquipmentConfig equipmentConfig;
    private DatabaseConfig databaseConfig;
    
    private ConfigurationManager() {
        properties = new Properties();
        serverConfig = new LineageServerConfig();
        equipmentConfig = new EquipmentConfig();
        databaseConfig = new DatabaseConfig();
        loadConfigurations();
    }
    
    public static ConfigurationManager getInstance() {
        if (instance == null) {
            instance = new ConfigurationManager();
        }
        return instance;
    }
    
    /**
     * 載入所有配置檔案
     */
    private void loadConfigurations() {
        try {
            // 載入主要配置檔案
            loadPropertiesFile("config/application.properties");
            loadPropertiesFile("config/server.properties");
            loadPropertiesFile("config/sql.properties");
            loadPropertiesFile("config/other.properties");
            loadPropertiesFile("config/rates.properties");
            
            // 載入技能配置檔案
            loadSkillConfigurations();
            
            // 載入其他配置檔案
            loadOtherConfigurations();
            
        } catch (Exception e) {
            System.err.println("載入配置檔案時發生錯誤: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 載入屬性檔案
     */
    private void loadPropertiesFile(String filename) {
        try (InputStream input = new FileInputStream(filename)) {
            Properties fileProps = new Properties();
            fileProps.load(input);
            properties.putAll(fileProps);
        } catch (IOException e) {
            System.err.println("無法載入配置檔案: " + filename + " - " + e.getMessage());
        }
    }
    
    /**
     * 載入技能配置檔案
     */
    private void loadSkillConfigurations() {
        String[] skillFiles = {
            "config/王族_技能設定表.properties",
            "config/騎士_技能設定表.properties",
            "config/法師_技能設定表.properties",
            "config/妖精_技能設定表.properties",
            "config/黑暗妖精_技能設定表.properties",
            "config/龍騎士_技能設定表.properties",
            "config/幻術師_技能設定表.properties"
        };
        
        for (String skillFile : skillFiles) {
            loadPropertiesFile(skillFile);
        }
    }
    
    /**
     * 載入其他配置檔案
     */
    private void loadOtherConfigurations() {
        String[] otherFiles = {
            "config/ipcheck.properties",
            "config/record.properties",
            "config/pack.properties",
            "config/altsettings.properties",
            "config/log4j.properties",
            "config/logging.properties"
        };
        
        for (String otherFile : otherFiles) {
            loadPropertiesFile(otherFile);
        }
    }
    
    /**
     * 獲取屬性值
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 獲取屬性值，如果不存在則返回預設值
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
    
    /**
     * 獲取整數屬性值
     */
    public int getIntProperty(String key, int defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                System.err.println("無法解析整數屬性: " + key + " = " + value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 獲取布林屬性值
     */
    public boolean getBooleanProperty(String key, boolean defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            return Boolean.parseBoolean(value);
        }
        return defaultValue;
    }
    
    /**
     * 獲取長整數屬性值
     */
    public long getLongProperty(String key, long defaultValue) {
        String value = properties.getProperty(key);
        if (value != null) {
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                System.err.println("無法解析長整數屬性: " + key + " = " + value);
            }
        }
        return defaultValue;
    }
    
    /**
     * 獲取伺服器配置
     */
    public LineageServerConfig getServerConfig() {
        return serverConfig;
    }
    
    /**
     * 獲取裝備配置
     */
    public EquipmentConfig getEquipmentConfig() {
        return equipmentConfig;
    }
    
    /**
     * 獲取資料庫配置
     */
    public DatabaseConfig getDatabaseConfig() {
        return databaseConfig;
    }
    
    /**
     * 重新載入配置
     */
    public void reloadConfigurations() {
        properties.clear();
        loadConfigurations();
    }
    
    /**
     * 獲取所有屬性
     */
    public Properties getAllProperties() {
        return new Properties(properties);
    }
    
    /**
     * 檢查配置是否有效
     */
    public boolean validateConfiguration() {
        // 檢查必要的配置項目
        String[] requiredProperties = {
            "GameserverHostname",
            "GameserverPort",
            "ServerNo",
            "MaximumOnlineUsers"
        };
        
        for (String required : requiredProperties) {
            if (getProperty(required) == null) {
                System.err.println("缺少必要配置: " + required);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 輸出配置摘要
     */
    public void printConfigurationSummary() {
        System.out.println("=== Lineage 伺服器配置摘要 ===");
        System.out.println("伺服器主機: " + getProperty("GameserverHostname", "未設定"));
        System.out.println("伺服器端口: " + getProperty("GameserverPort", "未設定"));
        System.out.println("伺服器編號: " + getProperty("ServerNo", "未設定"));
        System.out.println("最大線上人數: " + getProperty("MaximumOnlineUsers", "未設定"));
        System.out.println("自動儲存間隔: " + getProperty("AutosaveInterval", "未設定"));
        System.out.println("GUI 模式: " + getProperty("GUI", "未設定"));
        System.out.println("測試伺服器: " + getProperty("TestServer", "未設定"));
        System.out.println("================================");
    }
} 