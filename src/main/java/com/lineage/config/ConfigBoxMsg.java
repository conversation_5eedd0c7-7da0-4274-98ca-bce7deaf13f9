package com.lineage.config;

import com.lineage.server.serverpackets.S_PacketBoxGree;
import com.lineage.server.world.World;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.LineNumberReader;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class ConfigBoxMsg {
	private static final Log _log = LogFactory.getLog(ConfigBoxMsg.class);
	private static final Map<Integer, String> _box_msg_list = new HashMap();
	private static final Random _random = new Random();
	public static boolean ISMSG = false;
	private static final String _box_text = "./config/box_desc.txt";

	public static void load() throws ConfigErrorException {
		try {
			InputStream is = new FileInputStream(new File("./config/box_desc.txt"));
			InputStreamReader isr = new InputStreamReader(is, "utf-8");
			LineNumberReader lnr = new LineNumberReader(isr);
			boolean isWhile = false;
			int i = 1;
			String desc = null;
			while ((desc = lnr.readLine()) != null) {
				if (!isWhile) {
					isWhile = true;
				} else if ((desc.trim().length() != 0) && (!desc.startsWith("#"))) {
					if (desc.startsWith("ISMSG")) {
						desc = desc.replaceAll(" ", "");
						ISMSG = Boolean.parseBoolean(desc.substring(6));
					} else {
						_box_msg_list.put(new Integer(i++), desc);
					}
				}
			}
			is.close();
			isr.close();
			lnr.close();
		} catch (Exception e) {
			_log.error("設置檔案遺失: ./config/box_desc.txt");
		}
	}

	public static void msg(String string1, String string2) {
		try {
			String msg = _box_msg_list.get(Integer.valueOf(_random.nextInt(_box_msg_list.size()) + 1));
			if (msg != null) {
				String out = String.format(msg, new Object[] { string1, string2 });
				World.get().broadcastPacketToAll(new S_PacketBoxGree(2, out));
			}
		} catch (Exception e) {
			_log.error(e.getLocalizedMessage(), e);
		}
	}
}