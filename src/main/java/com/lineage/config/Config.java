package com.lineage.config;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;

public final class Config {
    public static final int vergame = 3800;
    private static final String SERVER_CONFIG_FILE = "./config/server.properties";
    public static String checkip = "";
    public static boolean CACHE_MAP_FILES;
    public static boolean LOGINS_TO_AUTOENTICATION;
    public static String RSA_KEY_E;
    public static String RSA_KEY_N;
    public static boolean DEBUG = false;
    public static int SERVERNO;
    public static boolean ISUBUNTU = false;
    public static String GAME_SERVER_HOST_NAME;
    public static String GAME_SERVER_PORT;
    public static String SERVERNAME;
    public static String CHAT_SERVER_HOST_NAME;
    public static int CHAT_SERVER_PORT;
    public static String TIME_ZONE;
    public static int CLIENT_LANGUAGE;
    public static String CLIENT_LANGUAGE_CODE;
    public static String[] LANGUAGE_CODE_ARRAY = {"UTF8", "EUCKR", "UTF8", "BIG5", "SJIS", "GBK"};
    public static String[] AUTORESTART = null;
    public static boolean AUTO_CREATE_ACCOUNTS;
    public static short MAX_ONLINE_USERS = 10;
    public static int AUTOSAVE_INTERVAL;
    public static int AUTOSAVE_INTERVAL_INVENTORY;
    public static int PC_RECOGNIZE_RANGE;
    public static int RESTART_LOGIN;
    public static boolean NEWS;
    public static boolean GUI;
    public static boolean TestServer;
    public static boolean BanIpFromIDC;
    public static String TestServerPassWords;

    public static void load() throws ConfigErrorException {
        Properties pack = new Properties();
        try {
            InputStream is = new FileInputStream(new File("./config/pack.properties"));
            pack.load(is);
            is.close();
            LOGINS_TO_AUTOENTICATION = Boolean.parseBoolean(pack.getProperty("Autoentication", "false"));
            RSA_KEY_E = pack.getProperty("RSA_KEY_E", "0");
            RSA_KEY_N = pack.getProperty("RSA_KEY_N", "0");
        } catch (Exception e) {
            System.err.println("沒有找到登入器加密設置檔案: ./config/pack.properties");
        } finally {
            pack.clear();
        }
        Properties set = new Properties();
        try {
            Object is2 = new FileInputStream(new File("./config/server.properties"));
            set.load((InputStream) is2);
            ((InputStream) is2).close();
            GUI = Boolean.parseBoolean(set.getProperty("GUI", "true"));
            SERVERNO = Integer.parseInt(set.getProperty("ServerNo", "1"));
            GAME_SERVER_HOST_NAME = set.getProperty("GameserverHostname", "*");
            GAME_SERVER_PORT = set.getProperty("GameserverPort", "2000-2001");
            CLIENT_LANGUAGE = Integer.parseInt(set.getProperty("ClientLanguage", "3"));
            CLIENT_LANGUAGE_CODE = LANGUAGE_CODE_ARRAY[CLIENT_LANGUAGE];
            String tmp = set.getProperty("AutoRestart", "");
            if (!tmp.equalsIgnoreCase("null")) {
                AUTORESTART = tmp.split(",");
            }
            CACHE_MAP_FILES = Boolean.parseBoolean(set.getProperty("CacheMapFiles", "false"));
            TIME_ZONE = set.getProperty("TimeZone", "CST");
            AUTO_CREATE_ACCOUNTS = Boolean.parseBoolean(set.getProperty("AutoCreateAccounts", "true"));
            MAX_ONLINE_USERS = Short.parseShort(set.getProperty("MaximumOnlineUsers", "30"));
            //限制人數
            //c

            AUTOSAVE_INTERVAL = Integer.parseInt(set.getProperty("AutosaveInterval", "1200"), 10);
            AUTOSAVE_INTERVAL /= 60;
            if (AUTOSAVE_INTERVAL <= 0) {
                AUTOSAVE_INTERVAL = 20;
            }
            AUTOSAVE_INTERVAL_INVENTORY = Integer.parseInt(set.getProperty("AutosaveIntervalOfInventory", "300"), 10);
            AUTOSAVE_INTERVAL_INVENTORY /= 60;
            if (AUTOSAVE_INTERVAL_INVENTORY <= 0) {
                AUTOSAVE_INTERVAL_INVENTORY = 5;
            }
            PC_RECOGNIZE_RANGE = Integer.parseInt(set.getProperty("PcRecognizeRange", "13"));
            RESTART_LOGIN = Integer.parseInt(set.getProperty("restartlogin", "30"));
            NEWS = Boolean.parseBoolean(set.getProperty("News", "false"));
            BanIpFromIDC = Boolean.parseBoolean(set.getProperty("BanIpFromIDC","true"));
            TestServer = Boolean.parseBoolean(set.getProperty("TestServer","false"));
            TestServerPassWords = set.getProperty("TestServerPassWords","wftest2");
        } catch (Exception e2) {
            throw new ConfigErrorException("設置檔案遺失: ./config/server.properties");
        } finally {
            set.clear();
        }
    }
}
