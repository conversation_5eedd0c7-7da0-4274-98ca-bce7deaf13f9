package com.lineage.config;

import com.lineage.server.beans.clan.ClanLevelUpCondition;
import com.lineage.server.beans.errors.LogicError;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;

public final class ConfigClan {
    public static List<ClanLevelUpCondition> clansLevelUpCondition = new ArrayList<>();
    public static Integer CLAN_MAX_LEVEL = 10;
    public static int clanlevel;
    public static String clanmsg1;
    public static boolean clandelt;
    public static boolean clanskill;
    public static int clanresttime;
    public static int PcContribution;
    public static int PcClanAdena;
    public static int ClanItem44070;
    public static int ClanContribution;
    public static int ClanItem44070_1;
    public static int ClanContribution_1;
    private static final String OTHER_SETTINGS_FILE = "./config/其他控制端/血盟等級設定表.properties";

    public static void load() throws ConfigErrorException {
        Properties set = new Properties();
        try {
            InputStream is = new FileInputStream(new File("./config/其他控制端/血盟等級設定表.properties"));
            InputStreamReader isr = new InputStreamReader(is, "utf-8");
            set.load(isr);
            is.close();

            setClansLevelCondition(set);

            clanlevel = Integer.parseInt(set.getProperty("clanlevel", "0"));
            clanmsg1 = set.getProperty("clanmsg1", "");
            PcClanAdena = Integer.parseInt(set.getProperty("PcClanAdena", "0"));
            ClanItem44070 = Integer.parseInt(set.getProperty("ClanItem44070", "0"));
            ClanContribution = Integer.parseInt(set.getProperty("ClanContribution", "0"));
            ClanItem44070_1 = Integer.parseInt(set.getProperty("ClanItem44070_1", "0"));
            ClanContribution_1 = Integer.parseInt(set.getProperty("ClanContribution_1", "0"));
            clandelt = Boolean.parseBoolean(set.getProperty("clandelt", "true"));
            clanskill = Boolean.parseBoolean(set.getProperty("clanskill", "true"));
            clanresttime = Integer.parseInt(set.getProperty("clanresttime", "0"));
            PcContribution = Integer.parseInt(set.getProperty("PcContribution", "0"));
        } catch (LogicError e) {
            throw new ConfigErrorException(e.getMsg());
        } catch (Exception e) {
            throw new ConfigErrorException("設置檔案遺失: ./config/其他控制端/血盟等級設定表.properties");
        } finally {
            set.clear();
        }
    }

    private static void setClansLevelCondition(Properties properties) {
        for (int i = 0; i < CLAN_MAX_LEVEL; i++) {
            clansLevelUpCondition.add(getClanLevelCondition(properties, i + 1));
        }
    }

    private static ClanLevelUpCondition getClanLevelCondition(Properties properties, Integer level) {
        int[] items = toIntArray(properties.getProperty("clanlv" + level, ""), ",");
        int[] itemsCount = toIntArray(properties.getProperty("clanlvcount" + level, ""), ",");
        int energy = Integer.parseInt(properties.getProperty("clanenergy" + level, "0"));
        int adena = Integer.parseInt(properties.getProperty("clanadena" + level, "0"));


        if (items.length > 0 && items.length != itemsCount.length) {
            throw new LogicError("設置檔案錯誤: 血盟升級道具與數量不匹配");
        }

        ClanLevelUpCondition condition = new ClanLevelUpCondition();
        condition.setClanEnergy(energy);
        condition.setClanAdena(adena);

        for (int i = 0; i < items.length; i++) {
            condition.putMaterial(items[i], itemsCount[i]);
        }

        return condition;
    }

    public static int[] toIntArray(String text, String type) {
        StringTokenizer st = new StringTokenizer(text, type);
        int[] iReturn = new int[st.countTokens()];
        int i = 0;
        while (i < iReturn.length) {
            iReturn[i] = Integer.parseInt(st.nextToken());
            i++;
        }
        return iReturn;
    }
}
