package com.lineage.config;

import com.lineage.server.utils.PerformanceTimer;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Properties;
import java.util.StringTokenizer;

public final class ConfigKnightSkill {
    private static final Log _log = LogFactory.getLog(ConfigKnightSkill.class);
    private static final String CONFIG_FILE = "./config/騎士_技能設定表.properties";
    public static int[] SHOCK_STUN_TIMER;
    public static int IMPACT_HALO_1;
    public static int IMPACT_HALO_2;
    public static int IMPACT_HALO_3;
    public static int IMPACT_HALO_4;
    public static double IMPACT_HALO_INT;
    public static double IMPACT_HALO_MR;
    public static int COUNTER_BARRIER_ROM;
    public static double COUNTER_BARRIER_DMG;
    public static int SOLID_CARRIAGE_MODE;

    public static void load() throws ConfigErrorException {
        PerformanceTimer timer = new PerformanceTimer();
        Properties set = new Properties();
        try {
            InputStream is = new FileInputStream(new File("./config/騎士_技能設定表.properties"));
            set.load(is);
            is.close();
            IMPACT_HALO_1 = Integer.parseInt(set.getProperty("IMPACT_HALO_1", "50"));
            IMPACT_HALO_2 = Integer.parseInt(set.getProperty("IMPACT_HALO_2", "70"));
            IMPACT_HALO_3 = Integer.parseInt(set.getProperty("IMPACT_HALO_3", "90"));
            IMPACT_HALO_4 = Integer.parseInt(set.getProperty("IMPACT_HALO_4", "90"));
            IMPACT_HALO_INT = Double.parseDouble(set.getProperty("IMPACT_HALO_INT", "0"));
            IMPACT_HALO_MR = Double.parseDouble(set.getProperty("IMPACT_HALO_MR", "0"));
            COUNTER_BARRIER_ROM = Integer.parseInt(set.getProperty("COUNTER_BARRIER_ROM", "33"));
            COUNTER_BARRIER_DMG = Double.parseDouble(set.getProperty("COUNTER_BARRIER_DMG", "2.0"));
            SOLID_CARRIAGE_MODE = Integer.parseInt(set.getProperty("SOLID_CARRIAGE_MODE", "1"));
            SHOCK_STUN_TIMER = toIntArray(set.getProperty("SHOCK_STUN_TIMER", ""), "~");
        } catch (Exception e) {
            throw new ConfigErrorException("設置檔案遺失: ./config/騎士_技能設定表.properties");
        } finally {
            set.clear();
            _log.info("Config/騎士_技能設定表讀取完成 (" + timer.get() + "ms)");
        }
    }

    public static int[] toIntArray(String text, String type) {
        StringTokenizer st = new StringTokenizer(text, type);
        int[] iReturn = new int[st.countTokens()];
        int i = 0;
        while (i < iReturn.length) {
            iReturn[i] = Integer.parseInt(st.nextToken());
            i++;
        }
        return iReturn;
    }
}