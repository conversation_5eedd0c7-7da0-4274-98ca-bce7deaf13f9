package com.lineage.echo;

import com.lineage.commons.system.LanSecurityManager;
import com.lineage.config.Config;
import com.lineage.config.ConfigIpCheck;
import com.lineage.echo.encryptions.Encryption;
import com.lineage.list.OnlineUser;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_Disconnect;
import com.lineage.server.templates.L1Account;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.utils.StreamUtil;
import com.lineage.server.utils.SystemUtil;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.math.BigInteger;
import java.net.Socket;
import java.net.SocketException;

public class ClientExecutor extends OpcodesClient implements Runnable {
    private static final Log _log;
    private static final int M = 3;
    private static final int O = 2;

    static {
        _log = LogFactory.getLog(ClientExecutor.class);
    }

    public int _xorByte;
    public long _authdata;
    private Socket _csocket;
    private L1Account _account;
    private L1PcInstance _activeChar;
    private StringBuilder _ip;
    private StringBuilder _mac;
    private int _kick;
    private boolean _isStrat;
    private EncryptExecutor _encrypt;
    private DecryptExecutor _decrypt;
    private PacketHandlerExecutor _handler;
    private Encryption _keys;
    private int _error;
    private int _saveInventory;
    private int _savePc;
    private PacketHc o;
    private PacketHc m;
    private byte _loginStatus;

    public ClientExecutor(Socket socket) throws IOException {
        _csocket = null;
        _account = null;
        _activeChar = null;
        _ip = null;
        _mac = null;
        _kick = 0;
        _isStrat = true;
        _error = -1;
        _saveInventory = 0;
        _savePc = 0;
        _xorByte = -16;
        _loginStatus = 2;
        _csocket = socket;
        //TODO 伺服器綑綁
        if (Config.LOGINS_TO_AUTOENTICATION) {
            int randomNumber = (int) (Math.random() * *********) + 255;
            _xorByte = randomNumber % 255 + 1;
            _authdata = new BigInteger(Integer.toString(randomNumber)).modPow(new BigInteger(Config.RSA_KEY_E), new BigInteger(Config.RSA_KEY_N)).longValue();
        }
        _ip = new StringBuilder().append(socket.getInetAddress().getHostAddress());
        _handler = new PacketHandler(this);
        _keys = new Encryption();
        _decrypt = new DecryptExecutor(this, socket.getInputStream());
        _encrypt = new EncryptExecutor(this, socket.getOutputStream());
    }

    public void start() {
    }

    @Override
    public void run() {
        o = new PacketHc(this, 2);
        GeneralThreadPool.get().schedule(o, 0L);
        try {
            _encrypt.satrt();
            _encrypt.outStart();
            boolean isEcho = false;
            while (_isStrat) {
                byte[] decrypt = null;
                try {
                    decrypt = readPacket();
                } catch (Exception e) {
                    break;
                }
                if (decrypt.length > 1440) {
                    ClientExecutor._log.warn("客戶端送出長度異常封包:" + _ip.toString() + " 帳號:"
                            + ((_account != null) ? _account.get_login() : "未登入"));
                    LanSecurityManager.BANIPMAP.put(_ip.toString(), Integer.valueOf(100));
                    break;
                }
                if (_account != null) {
                    if (!OnlineUser.get().isLan(_account.get_login())) {
                        break;
                    }
                    if (!_account.is_isLoad()) {
                        break;
                    }
                }
                int opcode = decrypt[0] & 0xFF;
                if (_activeChar == null) {
                    if (opcode == 14) {
                        if (ConfigIpCheck.IPCHECKPACK) {
                            _csocket.setSoTimeout(0);
                            LanSecurityManager.BANIPPACK.remove(_ip.toString());
                        }
                        isEcho = true;
                    } else if (opcode == 119) {
                        m = new PacketHc(this, 3);
                        GeneralThreadPool.get().schedule(m, 0L);
                        set_savePc(Config.AUTOSAVE_INTERVAL);
                        set_saveInventory(Config.AUTOSAVE_INTERVAL_INVENTORY);
                    }
                    if (!isEcho) {
                        continue;
                    }
                    _handler.handlePacket(decrypt);
                } else {
                    if (!isEcho) {
                        continue;
                    }
                    if (m == null) {
                        continue;
                    }
                    switch (opcode) {
                        case 122: {
                            _isStrat = false;
                            continue;
                        }
                        case 7:
                        case 25:
                        case 138: {
                            _handler.handlePacket(decrypt);
                            continue;
                        }
                        case 29: {
                            m.requestWork(decrypt);
                            continue;
                        }
                        default: {
                            o.requestWork(decrypt);
                            continue;
                        }
                    }
                }
            }
        } catch (Exception ex) {
        } finally {
            set_savePc(-1);
            set_saveInventory(-1);
            close();
        }
    }

    public void close() {
        try {
            String mac = null;
            if (_mac != null) {
                mac = _mac.toString();
            }
            if (_csocket == null) {
                return;
            }
            _kick = 0;
            if (_account != null) {
                OnlineUser.get().remove(_account.get_login());
            }
            if (_activeChar != null) {
                quitGame();
            }
            String ipAddr = _ip.toString();
            String account = null;
            if (_kick < 1 && _account != null) {
                account = _account.get_login();
            }
            _decrypt.stop();
            _encrypt.stop();
            StreamUtil.close(_csocket);
            _handler = null;
            _mac = null;
            _ip = null;
            _activeChar = null;
            _account = null;
            _decrypt = null;
            _encrypt = null;
            _csocket = null;
            _keys = null;
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("\n--------------------------------------------------");
            stringBuilder.append("\n       客戶端 離線: (");
            if (account != null) {
                stringBuilder.append(String.valueOf(account) + " ");
            }
            if (mac != null) {
                stringBuilder.append(" " + mac + " / ");
            }
            stringBuilder.append(String.valueOf(ipAddr) + ") 完成連線中斷!!");
            stringBuilder.append("\n--------------------------------------------------");
            ClientExecutor._log.info(stringBuilder.toString());
            SystemUtil.printMemoryUsage(ClientExecutor._log);
        } catch (Exception ex) {
        }
    }

    public L1Account getAccount() {
        return _account;
    }

    public void setAccount(L1Account account) {
        _account = account;
    }

    public String getAccountName() {
        if (_account == null) {
            return null;
        }
        return _account.get_login();
    }

    public L1PcInstance getActiveChar() {
        return _activeChar;
    }

    public void setActiveChar(L1PcInstance pc) {
        _activeChar = pc;
    }

    public StringBuilder getIp() {
        return _ip;
    }

    public StringBuilder getMac() {
        return _mac;
    }

    public boolean setMac(StringBuilder mac) {
        _mac = mac;
        return true;
    }

    public Socket get_socket() {
        return _csocket;
    }

    public boolean kick() {
        try {
            _encrypt.encrypt(new S_Disconnect());
        } catch (Exception ex) {
        }
        quitGame();
        _kick = 1;
        _isStrat = false;
        close();
        return true;
    }

    public void quitGame() {
        try {
            if (_activeChar == null) {
                return;
            }
            synchronized (_activeChar) {
                QuitGame.quitGame(_activeChar);
                _activeChar = null;
            }
        } catch (Exception ex) {
        }
    }

    private byte[] readPacket() {
        try {
            byte[] data = null;
            data = _decrypt.decrypt();
            return data;
        } catch (Exception ex) {
            return null;
        }
    }

    public EncryptExecutor out() {
        return _encrypt;
    }

    public Encryption get_keys() {
        return _keys;
    }

    public void set_keys(Encryption keys) {
        _keys = keys;
    }

    public int get_error() {
        return _error;
    }

    public void set_error(int error) {
        _error = error;
        if (_error >= 2) {
            kick();
        }
    }

    public int get_saveInventory() {
        return _saveInventory;
    }

    public void set_saveInventory(int saveInventory) {
        _saveInventory = saveInventory;
    }

    public int get_savePc() {
        return _savePc;
    }

    public void set_savePc(int savePc) {
        _savePc = savePc;
    }

    public byte getLoginStatus() {
        return _loginStatus;
    }

    public void setLoginStatus(byte i) {
        _loginStatus = i;
    }

    public void RemoveSocket() {
        try {
            _csocket.setSoTimeout(0);
        } catch (SocketException e) {
            e.printStackTrace();
        }
    }

    public void SetSocket(int time) {
        try {
            _csocket.setSoTimeout(time * 1000);
        } catch (SocketException e) {
            e.printStackTrace();
        }
    }
}
