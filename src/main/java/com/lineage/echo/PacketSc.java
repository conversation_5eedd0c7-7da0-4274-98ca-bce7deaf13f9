package com.lineage.echo;

import java.io.IOException;
import java.util.Iterator;
import com.lineage.server.types.UByte8;
import com.lineage.server.types.UChar8;
import com.lineage.echo.encryptions.PacketPrint;
import com.lineage.server.serverpackets.ServerBasePacket;
import java.util.concurrent.ConcurrentLinkedQueue;
import com.lineage.config.Config;
import org.apache.commons.logging.LogFactory;
import com.lineage.echo.encryptions.Encryption;
import java.util.Queue;
import org.apache.commons.logging.Log;

public class PacketSc implements Runnable {
	private static final Log _log;
	private static boolean _debug;
	private final Queue<byte[]> _queue;
	private final ClientExecutor _client;
	private final EncryptExecutor _executor;
	private Encryption _keys;

	static {
		_log = LogFactory.getLog(PacketSc.class);
		_debug = Config.DEBUG;
	}

	public PacketSc(final ClientExecutor client, final EncryptExecutor executor) {
		this._client = client;
		this._keys = client.get_keys();
		this._executor = executor;
		this._queue = new ConcurrentLinkedQueue();
	}

	private void requestWork(final byte[] data) {
		this._queue.offer(data);
	}

	public void encrypt(final ServerBasePacket packet) throws Exception {
		byte[] encrypt = packet.getContent();
		if (encrypt.length > 0 && this._executor.out() != null) {
			if (PacketSc._debug) {
				PacketSc._log.info("服務端: " + packet.getType() + "\nOP ID: " + (encrypt[0] & 0xFF) + "\nInfo:\n"
						+ PacketPrint.get().printData(encrypt, encrypt.length));
			}
			char[] ac = new char[encrypt.length];
			ac = UChar8.fromArray(encrypt);
			ac = this._keys.encrypt(ac);
			if (ac == null) {
				return;
			}
			encrypt = UByte8.fromArray(ac);
			this.requestWork(encrypt);
		}
	}

	@Override
	public void run() {
		try {
			while (this._client.get_socket() != null) {
				final Iterator<byte[]> iter = this._queue.iterator();
				while (iter.hasNext()) {
					final byte[] decrypt = iter.next();
					iter.remove();
					this.outPacket(decrypt);
					Thread.sleep(1L);
				}
				Thread.sleep(10L);
			}
		} catch (Exception e) {
			PacketSc._log.error(e.getLocalizedMessage(), e);
		} finally {
			this._queue.clear();
		}
	}

	private void outPacket(final byte[] decrypt) {
		try {
			final int outLength = decrypt.length + 2;
			this._executor.out().write(outLength & 0xFF);
			this._executor.out().write(outLength >> 8 & 0xFF);
			this._executor.out().write(decrypt);
			this._executor.out().flush();
		} catch (IOException e) {
			this._executor.stop();
		}
	}

	public void stop() {
		this._queue.clear();
	}
}
