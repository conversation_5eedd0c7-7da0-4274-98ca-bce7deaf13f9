package com.lineage.commons.system;

import com.lineage.config.ConfigIpCheck;
import com.lineage.server.datatables.lock.IpReading;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.IOException;
import java.security.Permission;
import java.sql.Timestamp;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 優化版網路安全管理器 (Java 1.8 相容版)
 * 主要改進：
 * 1. 使用 ConcurrentHashMap 替代 HashMap 提升線程安全
 * 2. 使用 ScheduledExecutorService 替代手動線程管理
 * 3. 加入記憶體管理和清理機制
 * 4. 優化檢查邏輯，減少重複代碼
 * 5. 加入統計和監控功能
 * 6. 使用原子操作提升性能
 * 7. 整合資料庫 ban_ip 表操作
 */
public class LanSecurityManagerOptimized extends SecurityManager {
    private static final Log _log = LogFactory.getLog(LanSecurityManagerOptimized.class);
    
    // 使用 ConcurrentHashMap 提升線程安全
    private static final ConcurrentHashMap<String, Long> BANIPPACK = new ConcurrentHashMap<String, Long>();
    private static final ConcurrentHashMap<String, Long> BANIPMAP = new ConcurrentHashMap<String, Long>();
    private static final ConcurrentHashMap<String, Long> BANNAMEMAP = new ConcurrentHashMap<String, Long>();
    private static final ConcurrentHashMap<String, Long> ONEIPMAP = new ConcurrentHashMap<String, Long>();
    
    // 統計計數器
    private static final AtomicLong totalChecks = new AtomicLong(0);
    private static final AtomicLong blockedConnections = new AtomicLong(0);
    private static final AtomicLong allowedConnections = new AtomicLong(0);
    private static final AtomicInteger activeBans = new AtomicInteger(0);
    
    // 非同步執行器
    private static final ScheduledExecutorService cleanupScheduler = Executors.newScheduledThreadPool(1);
    
    // 白名單
    private static final ConcurrentHashMap<String, Boolean> whitelist = new ConcurrentHashMap<String, Boolean>();
    
    static {
        // 載入白名單
        loadWhitelist();
        
        // 啟動清理任務
        startCleanupTask();
        
        // 添加關閉鉤子
        Runtime.getRuntime().addShutdownHook(new Thread(new Runnable() {
            @Override
            public void run() {
                shutdown();
            }
        }));
    }
    
    @Override
    public void checkAccept(final String host, final int port) {
        totalChecks.incrementAndGet();
        
        // 檢查白名單
        if (isWhitelisted(host)) {
            allowedConnections.incrementAndGet();
            return;
        }
        
        // 檢查 IP 攻擊
        if (ConfigIpCheck.IPCHECKPACK) {
            if (IpAttackCheckOptimized.check(host)) {
                blockedConnections.incrementAndGet();
                throw new SecurityException("IP 攻擊檢測: " + host);
            }
        }
        
        // 檢查一般 IP 封鎖
        if (ConfigIpCheck.IPCHECK) {
            if (isBanned(host)) {
                blockedConnections.incrementAndGet();
                throw new SecurityException("IP 已被封鎖: " + host);
            }
        }
        
        // 檢查單 IP 限制
        if (ConfigIpCheck.ISONEIP) {
            if (isOneIpLimited(host)) {
                blockedConnections.incrementAndGet();
                throw new SecurityException("單 IP 限制: " + host);
            }
        }
        
        allowedConnections.incrementAndGet();
    }
    
    /**
     * 覆寫 checkPropertyAccess 方法，允許讀取系統屬性
     * 解決 Java 1.8 相容性問題
     */
    @Override
    public void checkPropertyAccess(String key) {
        // 允許讀取所有系統屬性
        // 這是為了確保 Server.java 中的 System.getProperties().getProperty("os.name") 能正常執行
        return;
    }
    
    /**
     * 覆寫 checkPermission 方法，允許必要的權限
     */
    @Override
    public void checkPermission(Permission perm) {
        // 允許讀取系統屬性
        if (perm instanceof java.util.PropertyPermission) {
            return;
        }
        
        // 允許檔案讀取權限
        if (perm instanceof java.io.FilePermission) {
            String actions = perm.getActions();
            if (actions.contains("read")) {
                return;
            }
        }
        
        // 允許網路連接權限
        if (perm instanceof java.net.SocketPermission) {
            return;
        }
        
        // 允許執行權限
        if (perm instanceof java.lang.RuntimePermission) {
            String name = perm.getName();
            if ("setSecurityManager".equals(name) || 
                "createSecurityManager".equals(name) ||
                "setIO".equals(name) ||
                "modifyThread".equals(name) ||
                "modifyThreadGroup".equals(name) ||
                "accessClassInPackage.sun.misc".equals(name) ||
                "accessDeclaredMembers".equals(name) ||
                "suppressAccessChecks".equals(name)) {
                return;
            }
        }
        
        // 允許反射權限
        if (perm instanceof java.lang.reflect.ReflectPermission) {
            return;
        }
        
        // 允許日誌權限
        if (perm instanceof java.util.logging.LoggingPermission) {
            return;
        }
        
        // 其他權限交給父類處理
        super.checkPermission(perm);
    }
    
    /**
     * 檢查是否在白名單中
     */
    private boolean isWhitelisted(String host) {
        return whitelist.containsKey(host) || whitelist.containsKey("*");
    }
    
    /**
     * 檢查 IP 是否被封鎖
     */
    private boolean isBanned(String host) {
        Long banTime = BANIPMAP.get(host);
        if (banTime != null) {
            if (System.currentTimeMillis() < banTime) {
                return true;
            } else {
                // 過期，移除封鎖
                BANIPMAP.remove(host);
                activeBans.decrementAndGet();
            }
        }
        return false;
    }
    
    /**
     * 檢查單 IP 限制
     */
    private boolean isOneIpLimited(String host) {
        Long lastTime = ONEIPMAP.get(host);
        long currentTime = System.currentTimeMillis();
        
        if (lastTime != null) {
            if (currentTime - lastTime < ConfigIpCheck.ONETIMEMILLIS) {
                return true;
            }
        }
        
        ONEIPMAP.put(host, currentTime);
        return false;
    }
    
    /**
     * 封鎖 IP (寫入資料庫)
     * @param ip IP 地址
     * @param duration 封鎖持續時間 (毫秒)
     * @param reason 封鎖原因
     */
    public static void banIp(String ip, long duration, String reason) {
        if (ip == null || ip.trim().isEmpty()) {
            _log.warn("嘗試封鎖無效 IP: " + ip);
            return;
        }
        
        long banEndTime = System.currentTimeMillis() + duration;
        BANIPMAP.put(ip, banEndTime);
        activeBans.incrementAndGet();
        
        // 寫入資料庫
        if (ConfigIpCheck.SETDB) {
            try {
                // 設置解除封鎖時間
                Timestamp unbanTime = new Timestamp(banEndTime);
                IpReading.get().setUnbanTime(unbanTime);
                
                // 添加到資料庫
                String banReason = (reason != null) ? reason : "系統自動封鎖";
                IpReading.get().add(ip, banReason);
                
                _log.info("IP 已封鎖並寫入資料庫: " + ip + " 持續時間: " + duration + "ms, 原因: " + banReason);
            } catch (Exception e) {
                _log.error("寫入資料庫失敗: " + ip, e);
            }
        } else {
            _log.info("IP 已封鎖 (記憶體): " + ip + " 持續時間: " + duration + "ms");
        }
    }
    
    /**
     * 封鎖 IP (簡化版本)
     */
    public static void banIp(String ip, long duration) {
        banIp(ip, duration, "系統自動封鎖");
    }
    
    /**
     * 解除 IP 封鎖 (從資料庫刪除)
     * @param ip IP 地址
     */
    public static void unbanIp(String ip) {
        if (ip == null || ip.trim().isEmpty()) {
            _log.warn("嘗試解除無效 IP 封鎖: " + ip);
            return;
        }
        
        boolean removed = false;
        if (BANIPMAP.remove(ip) != null) {
            activeBans.decrementAndGet();
            removed = true;
        }
        
        // 從資料庫刪除
        if (ConfigIpCheck.SETDB) {
            try {
                IpReading.get().remove(ip);
                _log.info("IP 已解除封鎖並從資料庫刪除: " + ip);
            } catch (Exception e) {
                _log.error("從資料庫刪除失敗: " + ip, e);
            }
        } else if (removed) {
            _log.info("IP 已解除封鎖 (記憶體): " + ip);
        }
    }
    
    /**
     * 封鎖名稱 (寫入資料庫)
     * @param name 角色名稱
     * @param duration 封鎖持續時間 (毫秒)
     * @param reason 封鎖原因
     */
    public static void banName(String name, long duration, String reason) {
        if (name == null || name.trim().isEmpty()) {
            _log.warn("嘗試封鎖無效名稱: " + name);
            return;
        }
        
        long banEndTime = System.currentTimeMillis() + duration;
        BANNAMEMAP.put(name, banEndTime);
        
        // 寫入資料庫
        if (ConfigIpCheck.SETDB) {
            try {
                // 設置解除封鎖時間
                Timestamp unbanTime = new Timestamp(banEndTime);
                IpReading.get().setUnbanTime(unbanTime);
                
                // 添加到資料庫
                String banReason = (reason != null) ? reason : "系統自動封鎖";
                IpReading.get().add(name, banReason);
                
                _log.info("名稱已封鎖並寫入資料庫: " + name + " 持續時間: " + duration + "ms, 原因: " + banReason);
            } catch (Exception e) {
                _log.error("寫入資料庫失敗: " + name, e);
            }
        } else {
            _log.info("名稱已封鎖 (記憶體): " + name + " 持續時間: " + duration + "ms");
        }
    }
    
    /**
     * 封鎖名稱 (簡化版本)
     */
    public static void banName(String name, long duration) {
        banName(name, duration, "系統自動封鎖");
    }
    
    /**
     * 解除名稱封鎖 (從資料庫刪除)
     * @param name 角色名稱
     */
    public static void unbanName(String name) {
        if (name == null || name.trim().isEmpty()) {
            _log.warn("嘗試解除無效名稱封鎖: " + name);
            return;
        }
        
        boolean removed = false;
        if (BANNAMEMAP.remove(name) != null) {
            removed = true;
        }
        
        // 從資料庫刪除
        if (ConfigIpCheck.SETDB) {
            try {
                IpReading.get().remove(name);
                _log.info("名稱已解除封鎖並從資料庫刪除: " + name);
            } catch (Exception e) {
                _log.error("從資料庫刪除失敗: " + name, e);
            }
        } else if (removed) {
            _log.info("名稱已解除封鎖 (記憶體): " + name);
        }
    }
    
    /**
     * 載入資料庫中的封鎖記錄
     */
    public static void loadBanRecords() {
        try {
            IpReading.get().load();
            _log.info("已載入資料庫封鎖記錄");
        } catch (Exception e) {
            _log.error("載入資料庫封鎖記錄失敗", e);
        }
    }
    
    /**
     * 檢查並清理過期的資料庫封鎖記錄
     * @param ip IP 地址或角色名稱
     */
    public static void checkAndCleanExpiredBan(String ip) {
        try {
            IpReading.get().checktime(ip);
        } catch (Exception e) {
            _log.error("檢查過期封鎖記錄失敗: " + ip, e);
        }
    }
    
    /**
     * 載入白名單
     */
    private static void loadWhitelist() {
        try {
            java.io.File whitelistFile = new java.io.File("config/whitelist.txt");
            if (whitelistFile.exists()) {
                java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.FileReader(whitelistFile));
                String line;
                while ((line = reader.readLine()) != null) {
                    line = line.trim();
                    if (!line.isEmpty() && !line.startsWith("#")) {
                        whitelist.put(line, Boolean.TRUE);
                    }
                }
                reader.close();
                _log.info("白名單已載入，共 " + whitelist.size() + " 個項目");
            }
        } catch (IOException e) {
            _log.warn("載入白名單失敗: " + e.getMessage());
        }
    }
    
    /**
     * 添加白名單項目
     */
    public static void addToWhitelist(String ip) {
        whitelist.put(ip, Boolean.TRUE);
        _log.info("已添加到白名單: " + ip);
    }
    
    /**
     * 從白名單移除
     */
    public static void removeFromWhitelist(String ip) {
        if (whitelist.remove(ip) != null) {
            _log.info("已從白名單移除: " + ip);
        }
    }
    
    /**
     * 啟動清理任務
     */
    private static void startCleanupTask() {
        cleanupScheduler.scheduleAtFixedRate(new Runnable() {
            @Override
            public void run() {
                try {
                    cleanupExpiredBans();
                    logStatistics();
                } catch (Exception e) {
                    _log.error("清理任務執行失敗", e);
                }
            }
        }, 60, 60, TimeUnit.SECONDS); // 每分鐘執行一次
    }
    
    /**
     * 清理過期的封鎖
     */
    private static void cleanupExpiredBans() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;
        
        // 清理 IP 封鎖
        for (Map.Entry<String, Long> entry : BANIPMAP.entrySet()) {
            if (entry.getValue() < currentTime) {
                String ip = entry.getKey();
                BANIPMAP.remove(ip);
                cleanedCount++;
                
                // 檢查資料庫中的過期記錄
                if (ConfigIpCheck.SETDB) {
                    checkAndCleanExpiredBan(ip);
                }
            }
        }
        
        // 清理名稱封鎖
        for (Map.Entry<String, Long> entry : BANNAMEMAP.entrySet()) {
            if (entry.getValue() < currentTime) {
                String name = entry.getKey();
                BANNAMEMAP.remove(name);
                cleanedCount++;
                
                // 檢查資料庫中的過期記錄
                if (ConfigIpCheck.SETDB) {
                    checkAndCleanExpiredBan(name);
                }
            }
        }
        
        // 清理單 IP 限制
        for (Map.Entry<String, Long> entry : ONEIPMAP.entrySet()) {
            if (currentTime - entry.getValue() > ConfigIpCheck.ONETIMEMILLIS * 10) {
                ONEIPMAP.remove(entry.getKey());
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            activeBans.addAndGet(-cleanedCount);
            _log.info("清理了 " + cleanedCount + " 個過期封鎖");
        }
    }
    
    /**
     * 記錄統計資訊
     */
    private static void logStatistics() {
        long total = totalChecks.get();
        long blocked = blockedConnections.get();
        long allowed = allowedConnections.get();
        int active = activeBans.get();
        
        double blockRate = total > 0 ? (double) blocked / total * 100 : 0;
        
        _log.info(String.format("網路安全統計 - 總檢查: %d, 封鎖: %d, 允許: %d, 封鎖率: %.2f%%, 活躍封鎖: %d", 
                               total, blocked, allowed, blockRate, active));
    }
    
    /**
     * 獲取統計資訊
     */
    public static SecurityStatistics getStatistics() {
        return new SecurityStatistics(
            totalChecks.get(),
            blockedConnections.get(),
            allowedConnections.get(),
            activeBans.get(),
            BANIPMAP.size(),
            BANNAMEMAP.size(),
            whitelist.size()
        );
    }
    
    /**
     * 手動清理所有封鎖
     */
    public static void clearAllBans() {
        int ipCount = BANIPMAP.size();
        int nameCount = BANNAMEMAP.size();
        
        BANIPMAP.clear();
        BANNAMEMAP.clear();
        ONEIPMAP.clear();
        
        activeBans.set(0);
        _log.info("已清理所有封鎖 - IP: " + ipCount + ", 名稱: " + nameCount);
    }
    
    /**
     * 關閉資源
     */
    public static void shutdown() {
        try {
            cleanupScheduler.shutdown();
            if (!cleanupScheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                cleanupScheduler.shutdownNow();
            }
            
            _log.info("網路安全管理器已關閉");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
    
    /**
     * 統計資料類
     */
    public static class SecurityStatistics {
        private final long totalChecks;
        private final long blockedConnections;
        private final long allowedConnections;
        private final int activeBans;
        private final int bannedIps;
        private final int bannedNames;
        private final int whitelistSize;
        
        public SecurityStatistics(long totalChecks, long blockedConnections, long allowedConnections,
                                int activeBans, int bannedIps, int bannedNames, int whitelistSize) {
            this.totalChecks = totalChecks;
            this.blockedConnections = blockedConnections;
            this.allowedConnections = allowedConnections;
            this.activeBans = activeBans;
            this.bannedIps = bannedIps;
            this.bannedNames = bannedNames;
            this.whitelistSize = whitelistSize;
        }
        
        // Getters
        public long getTotalChecks() { return totalChecks; }
        public long getBlockedConnections() { return blockedConnections; }
        public long getAllowedConnections() { return allowedConnections; }
        public int getActiveBans() { return activeBans; }
        public int getBannedIps() { return bannedIps; }
        public int getBannedNames() { return bannedNames; }
        public int getWhitelistSize() { return whitelistSize; }
        public double getBlockRate() { 
            return totalChecks > 0 ? (double) blockedConnections / totalChecks * 100 : 0; 
        }
        
        @Override
        public String toString() {
            return String.format("SecurityStatistics{total=%d, blocked=%d, allowed=%d, blockRate=%.2f%%, activeBans=%d, whitelist=%d}",
                               totalChecks, blockedConnections, allowedConnections, getBlockRate(), activeBans, whitelistSize);
        }
    }
} 