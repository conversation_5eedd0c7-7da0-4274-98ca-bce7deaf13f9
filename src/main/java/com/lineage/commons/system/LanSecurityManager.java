package com.lineage.commons.system;

import java.util.Iterator;
import java.io.IOException;
import com.lineage.server.thread.GeneralThreadPool;
import java.security.Permission;
import com.lineage.config.ConfigIpCheck;
import com.lineage.server.datatables.lock.IpReading;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.logging.LogFactory;
import java.util.Map;
import org.apache.commons.logging.Log;

public class LanSecurityManager extends SecurityManager {
	private static final Log _log;
	public static final Map<String, Integer> BANIPPACK;
	public static final Map<String, Integer> BANIPMAP;
	public static final Map<String, Integer> BANNAMEMAP;
	public static final Map<String, Integer> ONEIPMAP;
	public static final Map<String, Long> ONETIMEMILLISMAP;
	public static final Map<String, Integer> Loginer;

	static {
		_log = LogFactory.getLog(LanSecurityManager.class);
		BANIPPACK = new ConcurrentHashMap();
		BANIPMAP = new HashMap();
		BANNAMEMAP = new HashMap();
		ONEIPMAP = new ConcurrentHashMap();
		ONETIMEMILLISMAP = new ConcurrentHashMap();
		Loginer = new HashMap();
	}

	@Override
	public void checkAccept(final String host, final int port) {
		IpReading.get().checktime(host);
		if (ConfigIpCheck.IPCHECKPACK) {
			if (LanSecurityManager.BANIPMAP.containsKey(host)) {
				throw new SecurityException();
			}
			if (LanSecurityManager.BANIPPACK.containsKey(host)) {
				throw new SecurityException();
			}
		} else {
			if (LanSecurityManager.BANIPMAP.containsKey(host)) {
				throw new SecurityException();
			}
			if (LanSecurityManager.ONEIPMAP.containsKey(host)) {
				throw new SecurityException();
			}
			if (LanSecurityManager.ONETIMEMILLISMAP.containsKey(host)) {
				throw new SecurityException();
			}
			if (ConfigIpCheck.ONETIMEMILLIS != 0) {
				LanSecurityManager.ONETIMEMILLISMAP.put(host, Long.valueOf(System.currentTimeMillis()));
			}
			if (ConfigIpCheck.ISONEIP) {
				LanSecurityManager.ONEIPMAP.put(host, Integer.valueOf(port));
			}
			if (ConfigIpCheck.IPCHECK && !IpAttackCheck.get().check(host)) {
				throw new SecurityException();
			}
		}
	}

	@Override
	public void checkAccess(final Thread t) {
	}

	@Override
	public void checkPermission(final Permission perm) {
	}

	public void stsrt_cmd() throws IOException {
		final RemoveIp removeIp = new RemoveIp(ConfigIpCheck.ONETIMEMILLIS);
		GeneralThreadPool.get().execute(removeIp);
	}

	public void stsrt_cmd_tmp() throws IOException {
		final RemoveTmpIp removeIp = new RemoveTmpIp();
		GeneralThreadPool.get().execute(removeIp);
	}

	private class RemoveTmpIp implements Runnable {
		@Override
		public void run() {
			try {
				while (true) {
					Thread.sleep(1000L);
					if (!LanSecurityManager.BANIPPACK.isEmpty()) {
						final Iterator<String> iterator = LanSecurityManager.BANIPPACK.keySet().iterator();
						while (iterator.hasNext()) {
							final String ip = iterator.next();
							final int time = LanSecurityManager.BANIPPACK.get(ip).intValue() - 1;
							if (time <= 0) {
								LanSecurityManager.BANIPPACK.remove(ip);
							} else {
								LanSecurityManager.BANIPPACK.put(ip, Integer.valueOf(time));
							}
						}
					}
				}
			} catch (Exception e) {
				LanSecurityManager._log.error(e.getLocalizedMessage(), e);
				return;
			}
		}
	}

	private class RemoveIp implements Runnable {
		public int _time;

		public RemoveIp(final int oNETIMEMILLIS) {
			this._time = 60000;
			this._time = oNETIMEMILLIS;
		}

		@Override
		public void run() {
			try {
				while (true) {
					Thread.sleep(10000L);
					if (!LanSecurityManager.ONETIMEMILLISMAP.isEmpty()) {
						final Iterator<String> iterator = LanSecurityManager.ONETIMEMILLISMAP.keySet().iterator();
						while (iterator.hasNext()) {
							final String ip = iterator.next();
							final long time = LanSecurityManager.ONETIMEMILLISMAP.get(ip).longValue();
							if (System.currentTimeMillis() - time >= this._time) {
								LanSecurityManager.ONETIMEMILLISMAP.remove(ip);
							}
						}
					}
				}
			} catch (Exception e) {
				LanSecurityManager._log.error(e.getLocalizedMessage(), e);
				return;
			}
		}
	}
}
