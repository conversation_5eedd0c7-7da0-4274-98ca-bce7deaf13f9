package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ShowPolyList;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Sosc_Japan extends ItemExecutor {
	public static ItemExecutor get() {
		return new Sosc_Japan();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		pc.sendPackets(new S_ShowPolyList(pc.getId(), "japanpoly"));
		pc.setPolyScroll(item);
		pc.setcheckpoly(true);
	}
}
