package com.lineage.data.item_etcitem;

import com.lineage.data.cmd.CreateNewItem;
import com.lineage.server.utils.Random;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Ancient_Items_Glove extends ItemExecutor {
	public static ItemExecutor get() {
		return new Ancient_Items_Glove();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int[] Armorid = { 20175, 20191 };
		final int Armorran = Random.nextInt(Armorid.length);
		final int[] goodArmorid = { 310125 };
		final int goodArmorran = Random.nextInt(goodArmorid.length);
		final int[] Enchant = { 0, 1, 2, 3, 4 };
		final int[] HighEnchant = { 5, 6, 7 };
		int enchantlvl = 0;
		if (Random.nextInt(100) + 1 <= 1) {
			enchantlvl = HighEnchant[Random.nextInt(HighEnchant.length)];
		} else {
			enchantlvl = Enchant[Random.nextInt(Enchant.length)];
		}
		pc.getInventory().removeItem(item, 1L);
		if (Random.nextInt(100) < 99) {
			CreateNewItem.createNewItem_LV(pc, Armorid[Armorran], 1, enchantlvl);
		} else {
			CreateNewItem.createNewItem_LV(pc, goodArmorid[goodArmorran], 1, enchantlvl);
		}
	}
}
