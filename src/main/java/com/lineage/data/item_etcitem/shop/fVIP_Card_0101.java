package com.lineage.data.item_etcitem.shop;

import com.lineage.server.templates.L1Item;
import com.lineage.server.serverpackets.S_ItemName;
import com.lineage.server.datatables.lock.CharItemsTimeReading;
import java.sql.Timestamp;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.data.event.CardSet;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.ItemExecutor;

public class fVIP_Card_0101 extends ItemExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(fVIP_Card_0101.class);
	}

	private fVIP_Card_0101() {
	}

	public static ItemExecutor get() {
		return new VIP_Card_01();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		try {
			if (item == null) {
				return;
			}
			if (pc == null) {
				return;
			}
			if (!CardSet.START) {
				pc.sendPackets(new S_ServerMessage("\\fR目前未開放VIP道具使用"));
				return;
			}
			if (item.get_card_use() == 2) {
				pc.sendPackets(new S_ServerMessage("\\fT這張卡片已經到期!"));
				return;
			}
			if (item.get_card_use() == 1) {
				pc.sendPackets(new S_ServerMessage("\\fT已經有其他雕像正在使用中"));
				return;
			}
			final int[] itemids = { 70423, 70424, 70425, 70426, 70427 };
			final int[] array;
			final int length = (array = itemids).length;
			int i = 0;
			while (i < length) {
				final int itemid = array[i];
				if (pc.getInventory().checkCardEquipped(itemid)) {
					final L1Item temp = ItemTable.get().getTemplate(itemid);
					pc.sendPackets(new S_ServerMessage("\\fR你已經啟動了一個" + temp.getName()));
					return;
				}
				++i;
			}
			final long time = System.currentTimeMillis();
			final long x1 = CardSet.USE_TIME * 60 * 60;
			final long x2 = x1 * 1000L;
			final long upTime = x2 + time;
			final Timestamp ts = new Timestamp(upTime);
			item.set_time(ts);
			item.set_card_use(1);
			CharItemsTimeReading.get().addTime(item.getId(), ts);
			pc.sendPackets(new S_ItemName(item));
			pc.sendPackets(new S_ServerMessage("\\fR這個雕像已經開始使用"));
			CardSet.set_card_mode(pc, item);
		} catch (Exception e) {
			fVIP_Card_0101._log.error(e.getLocalizedMessage(), e);
		}
	}
}
