package com.lineage.data.item_etcitem.shop;

import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.datatables.ItemPowerUpdateTable;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.datatables.RecordTable;
import com.lineage.server.datatables.lock.CharItemsReading;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1Item;
import com.lineage.server.templates.L1ItemPowerUpdate;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Map;
import java.util.Random;

public class Power_Up_01 extends ItemExecutor {
    private static final Log _log;
    private static final Random _random;

    static {
        _log = LogFactory.getLog(Power_Up_01.class);
        _random = new Random();
    }

    public static ItemExecutor get() {
        return new Power_Up_01();
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {
        try {
            int targObjId = data[0];
            L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
            if (tgItem == null) {
                return;
            }
            if (tgItem.isEquipped()) {
                pc.sendPackets(new S_SystemMessage("請先卸除該裝備在升級。"));
                return;
            }
            L1ItemPowerUpdate info = ItemPowerUpdateTable.get().get(tgItem.getItemId());
            if (info == null) {
                pc.sendPackets(new S_ServerMessage(79));
                return;
            }
            if (info.get_mode() == 4) {
                pc.sendPackets(new S_ServerMessage(79));
                return;
            }
            if (info.get_nedid() != item.getItemId()) {
                pc.sendPackets(new S_ServerMessage(79));
                return;
            }
            Map<Integer, L1ItemPowerUpdate> tmplist = ItemPowerUpdateTable.get().get_type_id(tgItem.getItemId());
            if (tmplist.isEmpty()) {
                pc.sendPackets(new S_ServerMessage(79));
                return;
            }
            int order_id = info.get_order_id();
            L1ItemPowerUpdate tginfo = tmplist.get(Integer.valueOf(order_id + 1));
            if (tginfo == null) {
                pc.sendPackets(new S_ServerMessage(79));
                return;
            }
            pc.getInventory().removeItem(item, 1L);
            if (Power_Up_01._random.nextInt(1000) < info.get_random()) {
                L1Item l1item = ItemTable.get().getTemplate(tginfo.get_itemid());
                pc.sendPackets(new S_DeleteInventoryItem(tgItem.getId()));
                tgItem.setItemId(tginfo.get_itemid());
                tgItem.setItem(l1item);
                if (tgItem.getBless() == 0) {
                    l1item.setBless(0);
                } else {
                    l1item.setBless(1);
                }
                try {
                    CharItemsReading.get().updateItemId_Name(tgItem);
                    CharItemsReading.get().updateItemBless(tgItem);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                pc.sendPackets(new S_AddItem(tgItem));
                pc.sendPackets(new S_ServerMessage(1410, tgItem.getName()));
                if (info.getallmsg() != null) {
                    if (!pc.isGm()) {
                        World.get().broadcastPacketToAll(
                                new S_SystemMessage(String.format(info.getallmsg(), pc.getName(), tgItem.getName())));
                        World.get().broadcastPacketToAll(
                                new S_PacketBoxGree(String.format(info.getallmsg(), pc.getName(), tgItem.getName())));
                    }
                }
            } else {
                switch (info.get_mode()) {
                    case 0: {
                        pc.sendPackets(new S_ServerMessage(1411, tgItem.getName()));
                        break;
                    }
                    case 1: {
                        if (tgItem.getproctect7()) {
                            tgItem.setproctect7(false);
                            pc.sendPackets(new S_ItemStatus(tgItem));
                            pc.getInventory().updateItem(tgItem, 4);
                            pc.getInventory().saveItem(tgItem, 4);
                            pc.sendPackets(new S_ServerMessage("該裝備受到保護卷的祝福,沒發生什麼事情"));
                            return;
                        }
                        L1ItemPowerUpdate lastinfo = tmplist.get(Integer.valueOf(order_id - 1));
                        pc.sendPackets(new S_ServerMessage(1411, tgItem.getName()));
                        pc.getInventory().removeItem(tgItem, 1L);
                        L1ItemInstance lastitem = ItemTable.get().createItem(lastinfo.get_itemid());
                        lastitem.setIdentified(true);
                        lastitem.setCount(1L);
                        pc.getInventory().storeItem(lastitem);
                        break;
                    }
                    case 2: {
                        if (tgItem.getproctect()) {
                            tgItem.setproctect(false);
                            pc.sendPackets(new S_ItemStatus(tgItem));
                            pc.getInventory().updateItem(tgItem, 4);
                            pc.getInventory().saveItem(tgItem, 4);
                            pc.sendPackets(new S_ServerMessage("該裝備受到保護卷的祝福,沒發生什麼事情"));
                            RecordTable.get().recordFailureArmor(pc.getName(), item.getAllName(), tgItem.getAllName(),tgItem.getEnchantLevel(),
                                    item.getId(), "受到娃娃防暴的保護", pc.getIp());
                            return;
                        }
                        if (tgItem.getproctect6()) {
                            tgItem.setproctect6(false);
                            pc.sendPackets(new S_ItemStatus(tgItem));
                            pc.getInventory().updateItem(tgItem, 4);
                            pc.getInventory().saveItem(tgItem, 4);
                            pc.sendPackets(new S_ServerMessage("該裝備受到保護卷的祝福,沒發生什麼事情"));
                            RecordTable.get().recordFailureArmor(pc.getName(), item.getAllName(), tgItem.getAllName(),tgItem.getEnchantLevel(),
                                    item.getId(), "受到保護卷的保護", pc.getIp());
                            return;
                        }
                        pc.sendPackets(new S_ServerMessage(164, tgItem.getLogName(), "$252"));
                        pc.getInventory().removeItem(tgItem, 1L);
                        break;
                    }
                    case 3: {
                        if (Power_Up_01._random.nextBoolean()) {
                            L1ItemPowerUpdate lastinfo2 = tmplist.get(Integer.valueOf(order_id - 1));
                            pc.sendPackets(new S_ServerMessage(1411, tgItem.getName()));
                            pc.getInventory().removeItem(tgItem, 1L);
                            L1ItemInstance lastitem2 = ItemTable.get().createItem(lastinfo2.get_itemid());
                            lastitem2.setIdentified(true);
                            lastitem2.setCount(1L);
                            pc.getInventory().storeItem(lastitem2);
                            break;
                        }
                        pc.sendPackets(new S_ServerMessage(164, tgItem.getLogName(), "$252"));
                        pc.getInventory().removeItem(tgItem, 1L);
                        break;
                    }
                }
            }
        } catch (Exception e2) {
            Power_Up_01._log.error(e2.getLocalizedMessage(), e2);
        }
    }
}
