package com.lineage.data.item_etcitem.brave;

import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.serverpackets.S_Liquor;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SkillSound;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class StatusBraveCake extends ItemExecutor {
    private static final Log _log;
    private static final int _int7 = 64;
    private static final int _int6 = 32;
    private static final int _int5 = 16;
    private static final int _int4 = 8;
    private static final int _int3 = 4;
    private static final int _int2 = 2;
    private static final int _int1 = 1;

    static {
        _log = LogFactory.getLog(StatusBraveCake.class);
    }

    // private int _need;
    // private int _count;
    private int _time;
    private int _gfxid;
    private boolean _isCrown;
    private boolean _isKnight;
    private boolean _isElf;
    private boolean _isWizard;
    private boolean _isDarkelf;
    private boolean _isDragonKnight;
    private boolean _isIllusionist;
    private boolean _notConsume;

    public StatusBraveCake() {
        //_need = 0;
        //_count = 0;
        _time = 600;
    }

    public static ItemExecutor get() {
        return new StatusBraveCake();
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {
        // L1Item needitem = ItemTable.get().getTemplate(_need);
        if (item == null) {
            return;
        }
        if (pc == null) {
            return;
        }
        if (pc.hasSkillEffect(44)) {
            return;
        }
        //Kevin加入需要扣除的道具才能使用
       /* if (_need > 0) {
            if (!pc.getInventory().consumeItem(_need, _count)) { //Kevin checkitem改consumeItem
                pc.sendPackets(new S_SystemMessage("【" + needitem.getName() + "】不足，需要【" + _count + "】個才能使用"));
                return;
            }
        } else {*/
        // pc.getInventory().consumeItem(_need, _count);

        if (L1BuffUtil.stopPotion(pc)) {
            if (check(pc)) {
                if (_gfxid > 0) {
                    pc.sendPacketsX8(new S_SkillSound(pc.getId(), _gfxid));
                }

                if (pc.hasSkillEffect(STATUS_BRAVE3)) {
                    pc.killSkillEffectTimer(STATUS_BRAVE3);
                }
               /* if (_need > 0) {
                    if (!pc.getInventory().consumeItem(_need, _count)) { //Kevin checkitem改consumeItem
                        pc.sendPackets(new S_SystemMessage("【" + needitem.getName() + "】不足，需要【" + _count + "】個才能使用"));
                        return;
                    }
                } else {
                    pc.getInventory().consumeItem(_need, _count);
                */
                if (!_notConsume) {
                    pc.getInventory().removeItem(item, 1L);
                }
                L1BuffUtil.cancelAbsoluteBarrier(pc);
                pc.sendPacketsAll(new S_Liquor(pc.getId(), 8));
                pc.sendPackets(new S_ServerMessage(1065));
                // pc.sendPackets(new S_ServerMessage("使用巧克力蛋糕，扣除【" + needitem.getName() + "】：" + _count));

                pc.setSkillEffect(STATUS_BRAVE3, _time * 1000);
            } else {
                pc.sendPackets(new S_ServerMessage(79));
            }
        }
    }

    private boolean check(L1PcInstance pc) {
        try {
            if (pc.isCrown() && _isCrown) {
                return true;
            }
            if (pc.isKnight() && _isKnight) {
                return true;
            }
            if (pc.isElf() && _isElf) {
                return true;
            }
            if (pc.isWizard() && _isWizard) {
                return true;
            }
            if (pc.isDarkelf() && _isDarkelf) {
                return true;
            }
            if (pc.isDragonKnight() && _isDragonKnight) {
                return true;
            }
            if (pc.isIllusionist() && _isIllusionist) {
                return true;
            }
        } catch (Exception e) {
            StatusBraveCake._log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private void set_use_type(int use_type) {
        try {
            if (use_type >= 64) {
                use_type -= 64;
                _isIllusionist = true;
            }
            if (use_type >= 32) {
                use_type -= 32;
                _isDragonKnight = true;
            }
            if (use_type >= 16) {
                use_type -= 16;
                _isDarkelf = true;
            }
            if (use_type >= 8) {
                use_type -= 8;
                _isWizard = true;
            }
            if (use_type >= 4) {
                use_type -= 4;
                _isElf = true;
            }
            if (use_type >= 2) {
                use_type -= 2;
                _isKnight = true;
            }
            if (use_type >= 1) {
                --use_type;
                _isCrown = true;
            }
            if (use_type > 0) {
                StatusBraveCake._log.error("StatusBraveCake 可執行職業設定錯誤:餘數大於0");
            }
        } catch (Exception e) {
            StatusBraveCake._log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public void set_set(String[] set) {
        try {
            _time = Integer.parseInt(set[1]);
            if (_time <= 0) {
                StatusBraveCake._log.error("StatusBraveCake 設置錯誤:技能效果時間小於等於0! 使用預設600秒");
                _time = 600;
            }
        } catch (Exception ex) {
        }
        try {
            _gfxid = Integer.parseInt(set[2]);
        } catch (Exception ex2) {
        }
        try {
            int user_type = Integer.parseInt(set[3]);
            set_use_type(user_type);
        } catch (Exception ex3) {
        }
        try {
            if (set.length > 4) {
                _notConsume = Boolean.parseBoolean(set[4]);
            }
        } catch (Exception ex4) {
        }
       /* try {
            _need = Integer.parseInt(set[5]);
        } catch (Exception ex5) {
        }
        try {
            _count = Integer.parseInt(set[6]);
        } catch (Exception ex6) {
        }*/
    }
}
