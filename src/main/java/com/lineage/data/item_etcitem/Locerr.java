package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.Random;
import com.lineage.data.executor.ItemExecutor;

public class Locerr extends ItemExecutor {
	private static Random _random;

	static {
		_random = new Random();
	}

	private Locerr() {
	}

	public static ItemExecutor get() {
		return new Locerr();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc.isParalyzed() || pc.isSleeped() || pc.isParalyzedX()) {
			return;
		}
		if (pc.isDead()) {
			return;
		}
		if (pc.isInvisble()) {
			return;
		}
		if (pc.hasSkillEffect(55889)) {
			pc.sendPackets(new S_ServerMessage("無法連續使用該功能"));
			return;
		}
		pc.setSkillEffect(55889, 3000);
		pc.set_misslocTime(1);
	}
}
