package com.lineage.data.item_etcitem.quest2;

import java.util.Iterator;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1DoorInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class DragonDoorKeyC1 extends ItemExecutor {
	public static ItemExecutor get() {
		return new DragonDoorKeyC1();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (pc.getMapId() == 1017) {
			pc.getInventory().removeItem(item, 1L);
			final Iterator<L1Object> iterator = World.get().getVisibleObjects(1017).values().iterator();
			while (iterator.hasNext()) {
				final L1Object obj = iterator.next();
				if (obj instanceof L1DoorInstance) {
					final L1DoorInstance door = (L1DoorInstance) obj;
					if (door.get_showId() != pc.get_showId() || door.getDoorId() != 10037) {
						continue;
					}
					door.open();
					door.deleteMe();
				}
			}
		} else {
			pc.sendPackets(new S_ServerMessage(79));
		}
	}
}
