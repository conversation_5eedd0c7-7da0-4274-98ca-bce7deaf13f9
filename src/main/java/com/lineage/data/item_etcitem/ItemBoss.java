package com.lineage.data.item_etcitem;

import java.util.Iterator;
import java.util.Map;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import java.util.Calendar;
import com.lineage.server.model.L1Spawn;
import com.lineage.server.datatables.lock.SpawnBossReading;
import java.text.SimpleDateFormat;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class ItemBoss extends ItemExecutor {
	private ItemBoss() {
	}

	public static ItemExecutor get() {
		return new ItemBoss();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		String[] htmldata1 = null;
		htmldata1 = new String[150];
		int i = 0;
		try {
			final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			final Map<Integer, L1Spawn> BossSpawn = SpawnBossReading.get().get_bossSpawnTable();
			final Iterator<L1Spawn> iterator = BossSpawn.values().iterator();
			while (iterator.hasNext()) {
				final L1Spawn spawn = iterator.next();
				if (i > 150) {
					break;
				}
				if (Calendar.getInstance().after(spawn.get_nextSpawnTime())) {
					htmldata1[i] = String.valueOf(spawn.getName()) + "\n ->[已刷新]";
				} else {
					htmldata1[i] = String.valueOf(spawn.getName()) + "\r\n"
							+ sdf.format(spawn.get_nextSpawnTime().getTime());
				}
				++i;
			}
			pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "serverboss", htmldata1));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
