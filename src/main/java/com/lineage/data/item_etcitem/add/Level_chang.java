package com.lineage.data.item_etcitem.add;

import com.lineage.server.datatables.RecordTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Level_chang extends ItemExecutor {
	private boolean type;

	public Level_chang() {
		this.type = false;
	}

	public static ItemExecutor get() {
		return new Level_chang();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		pc.getInventory().removeItem(item, 1L);
		pc.setsavepclog((int) pc.getExp());
		pc.setExp(1L);
		pc.onChangeExp();
		try {
			Thread.sleep(1000L);
		} catch (Exception ex) {
		}
		pc.setExp(pc.gesavepclog());
		pc.onChangeExp();
		if (this.type) {
			pc.setCurrentHp(1);
			pc.setCurrentMp(1);
		}
		pc.sendPackets(new S_ServerMessage(822));
		RecordTable.get().reshp(pc.getName());
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this.type = Boolean.parseBoolean(set[1]);
		} catch (Exception ex) {
		}
	}
}
