package com.lineage.data.item_etcitem.add;

import com.lineage.data.event.PowerItemSet;
import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.datatables.lock.CharItemPowerReading;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_ItemName;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.templates.L1ItemPower_name;
import com.lineage.server.utils.log.PlayerLogUtil;

import java.sql.Timestamp;
import java.util.Random;

public class hole_armor extends ItemExecutor {
	private int _type;
	private int a;
	private static final Random _random;

	static {
		_random = new Random();
	}

	private hole_armor() {
	}

	public static ItemExecutor get() {
		return new hole_armor();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (tgItem.isEquipped()) {
			pc.sendPackets(new S_ServerMessage("\\fR你必須先解除物品裝備!"));
			return;
		}
		L1ItemPower_name power = null;
		boolean update = false;
		int count = 0;
		switch (tgItem.getItem().getUseType()) {
		case 2:
		case 13:
		case 18:
		case 19:
		case 20:
		case 21:
		case 22:
		case 25: {
			count = PowerItemSet.ARMORHOLE;
			if (tgItem.get_power_name() != null) {
				power = tgItem.get_power_name();
				update = true;
				break;
			}
			power = new L1ItemPower_name();
			break;
		}
		}
		if (tgItem.getItem().getUseType() == 2 || tgItem.getItem().getUseType() == 18
				|| tgItem.getItem().getUseType() == 19 || tgItem.getItem().getUseType() == 20
				|| tgItem.getItem().getUseType() == 21 || tgItem.getItem().getUseType() == 22
				|| tgItem.getItem().getUseType() == 13 || tgItem.getItem().getUseType() == 25) {
			if (power.get_hole_count() == PowerItemSet.ARMORHOLE) {
				pc.sendPackets(new S_ServerMessage("\\fT擴充數量已經滿了"));
				return;
			}
			switch (this._type) {
			case 1: {
				if (power == null) {
					break;
				}
				if (hole_armor._random.nextInt(100) + 1 <= this.a) {
					power.set_item_obj_id(tgItem.getId());
					power.set_hole_count(power.get_hole_count() + 1);
					打洞成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "(成功打出1洞) ,時間:"
							+ new Timestamp(System.currentTimeMillis()) + ")");
					tgItem.set_power_name(power);
					pc.sendPackets(new S_ServerMessage("\\fR成功增加了洞數-最高可達" + PowerItemSet.ARMORHOLE + "個洞(請注意)"));
					if (update) {
						CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
					} else {
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
						CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
					}
					pc.sendPackets(new S_ItemName(tgItem));
					break;
				}
				pc.sendPackets(new S_ServerMessage("\\fR強化失敗..運氣不好"));
				break;
			}
			case 2: {
				if (power == null) {
					break;
				}
				if (hole_armor._random.nextInt(100) + 1 <= this.a) {
					power.set_item_obj_id(tgItem.getId());
					power.set_hole_count(power.get_hole_count() + 1);
					tgItem.set_power_name(power);
					pc.sendPackets(new S_ServerMessage("\\fR成功增加了洞數-最高可達" + PowerItemSet.ARMORHOLE + "個洞(請注意)"));
					打洞成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "(成功打出1洞) ,時間:"
							+ new Timestamp(System.currentTimeMillis()) + ")");
					if (update) {
						CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
					} else {
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
						CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
					}
					pc.sendPackets(new S_ItemName(tgItem));
					break;
				}
				pc.sendPackets(new S_ServerMessage("\\fR強化失敗..運氣不好"));
				break;
			}
			case 3: {
				if (power == null) {
					break;
				}
				if (hole_armor._random.nextInt(100) + 1 <= this.a) {
					power.set_item_obj_id(tgItem.getId());
					power.set_hole_count(power.get_hole_count() + 1);
					tgItem.set_power_name(power);
					pc.sendPackets(new S_ServerMessage("\\fR成功增加了洞數-最高可達" + PowerItemSet.ARMORHOLE + "個洞(請注意)"));
					打洞成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "(成功打出1洞) ,時間:"
							+ new Timestamp(System.currentTimeMillis()) + ")");
					if (update) {
						CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
					} else {
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
						CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
					}
					pc.sendPackets(new S_ItemName(tgItem));
					break;
				}
				pc.sendPackets(new S_ServerMessage("\\fR強化失敗..運氣不好"));
				break;
			}
			case 4: {
				if (power == null) {
					break;
				}
				if (hole_armor._random.nextInt(100) + 1 <= this.a) {
					power.set_item_obj_id(tgItem.getId());
					power.set_hole_count(power.get_hole_count() + 1);
					tgItem.set_power_name(power);
					pc.sendPackets(new S_ServerMessage("\\fR成功增加了洞數-最高可達" + PowerItemSet.ARMORHOLE + "個洞(請注意)"));
					打洞成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "(成功打出1洞) ,時間:"
							+ new Timestamp(System.currentTimeMillis()) + ")");
					if (update) {
						CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
					} else {
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
						CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
					}
					pc.sendPackets(new S_ItemName(tgItem));
					break;
				}
				pc.sendPackets(new S_ServerMessage("\\fR強化失敗..運氣不好"));
				break;
			}
			case 5: {
				if (power == null) {
					break;
				}
				if (hole_armor._random.nextInt(100) + 1 <= this.a) {
					power.set_item_obj_id(tgItem.getId());
					power.set_hole_count(power.get_hole_count() + 1);
					tgItem.set_power_name(power);
					pc.sendPackets(new S_ServerMessage("\\fR成功增加了洞數-最高可達" + PowerItemSet.ARMORHOLE + "個洞(請注意)"));
					打洞成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "(成功打出1洞) ,時間:"
							+ new Timestamp(System.currentTimeMillis()) + ")");
					if (update) {
						CharItemPowerReading.get().updateItem(tgItem.getId(), tgItem.get_power_name());
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
					} else {
						pc.sendPackets(new S_ItemStatus(tgItem));
						pc.sendPackets(new S_ItemName(tgItem));
						CharItemPowerReading.get().storeItem(tgItem.getId(), tgItem.get_power_name());
					}
					pc.sendPackets(new S_ItemName(tgItem));
					break;
				}
				pc.sendPackets(new S_ServerMessage("\\fR強化失敗..運氣不好"));
				break;
			}
			}
			pc.getInventory().removeItem(item, 1L);
		} else {
			pc.sendPackets(new S_ServerMessage("\\fR該物品無法擴充"));
		}
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this._type = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
		try {
			this.a = Integer.parseInt(set[2]);
		} catch (Exception ex2) {
		}
	}

	public static void 打洞成功(final String info) {
		PlayerLogUtil.writeLog("打洞成功", info);
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/打洞成功.txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}
}
