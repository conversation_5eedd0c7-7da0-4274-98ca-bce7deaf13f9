package com.lineage.data.item_etcitem.add;

import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_OwnCharStatus2;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Nostrumcon extends ItemExecutor {
	private int a;
	private int b;

	private Nostrumcon() {
	}

	public static ItemExecutor get() {
		return new Nostrumcon();
	}

	@Override
	public final void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		try {
			int i = pc.getBaseCon();
			while (i < this.a) {
				pc.addBaseCon(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			i = pc.getBaseWis();
			while (i < this.a) {
				pc.addBaseWis(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			i = pc.getBaseStr();
			while (i < this.a) {
				pc.addBaseStr(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			i = pc.getBaseInt();
			while (i < this.a) {
				pc.addBaseInt(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			i = pc.getBaseCha();
			while (i < this.a) {
				pc.addBaseCha(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			i = pc.getBaseDex();
			while (i < this.a) {
				pc.addBaseDex(1);
				pc.setBonusStats(pc.getBonusStats() + 1);
				++i;
			}
			pc.setElixirStats(this.b);
			pc.sendPackets(new S_OwnCharStatus2(pc));
			pc.save();
			pc.getInventory().removeItem(item, 1L);
			pc.sendPackets(new S_ServerMessage("服用後請登出後在進來"));
		} catch (Exception ex) {
		}
	}

	@Override
	public void set_set(final String[] set) {
		this.a = Integer.parseInt(set[1]);
		this.b = Integer.parseInt(set[2]);
	}
}
