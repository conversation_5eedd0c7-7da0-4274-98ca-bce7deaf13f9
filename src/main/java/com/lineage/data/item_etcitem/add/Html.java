package com.lineage.data.item_etcitem.add;

import com.lineage.server.templates.L1ItemHtml;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.datatables.ItemHtmlTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Html extends ItemExecutor {
	public static ItemExecutor get() {
		return new Html();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int itemId = item.getItemId();
		final L1ItemHtml html = ItemHtmlTable.get().getHtml(itemId);
		if (html == null) {
			return;
		}
		final int quest_id = html.getQuestId();
		final int quest_step = html.getQuestStep();
		if (quest_id != 0 && quest_step != 0 && pc.getQuest().get_step(quest_id) != quest_step) {
			return;
		}
		final String htm = html.getHtml();
		if (htm != null) {
			pc.sendPackets(new S_NPCTalkReturn(pc.getId(), htm));
		}
	}
}
