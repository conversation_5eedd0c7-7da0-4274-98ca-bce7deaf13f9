package com.lineage.data.item_etcitem.add;

import com.lineage.data.cmd.EnchantExecutor;
import com.lineage.data.cmd.EnchantWeapon;
import java.util.Random;
import com.lineage.config.Configtype;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Ran_EnchantArmor extends ItemExecutor {
	private int _chance;

	private Ran_EnchantArmor() {
	}

	public static ItemExecutor get() {
		return new Ran_EnchantArmor();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (tgItem.getItem().getType2() != 2) {
			pc.sendPackets(new S_SystemMessage("物品必須是防具系列"));
			return;
		}
		if (tgItem.getItem().get_safeenchant() < 0 || tgItem.getBless() >= 128) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		final int use_type = tgItem.getItem().getUseType();
		if (use_type == 23 || use_type == 24 || use_type == 37 || use_type == 40) {
			pc.sendPackets(new S_SystemMessage("飾品無法使用。"));
			return;
		}
		if (tgItem.getEnchantLevel() >= Configtype.armorlv) {
			pc.sendPackets(new S_SystemMessage("已經超過最高強化上限。"));
			return;
		}
		pc.getInventory().removeItem(item, 1L);
		final Random random = new Random();
		final EnchantExecutor enchantExecutor = new EnchantWeapon();
		if (this._chance > random.nextInt(100) + 1) {
			enchantExecutor.successEnchant(pc, tgItem, 1);
		} else {
			enchantExecutor.successEnchant(pc, tgItem, 0);
		}
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this._chance = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
	}
}
