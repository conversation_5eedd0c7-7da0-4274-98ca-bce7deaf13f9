package com.lineage.data.item_etcitem.reel;

import com.lineage.data.cmd.EnchantExecutor;
import com.lineage.server.serverpackets.S_BlueMessage;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.model.L1ItemUpdata;
import com.lineage.data.cmd.EnchantArmor;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.Random;
import com.lineage.data.executor.ItemExecutor;

public class GianScroll extends ItemExecutor {
	final Random random;

	private GianScroll() {
		this.random = new Random();
	}

	public static ItemExecutor get() {
		return new GianScroll();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (tgItem.isEquipped()) {
			pc.sendPackets(new S_ServerMessage("(預防誤點機制啟動)裝備中無法強化"));
			return;
		}
		final int safe_enchant = tgItem.getItem().get_safeenchant();
		boolean isErr = false;
		final int use_type = tgItem.getItem().getUseType();
		switch (use_type) {
		case 2:
		case 18:
		case 19:
		case 20:
		case 21:
		case 22:
		case 25:
		case 47: {
			if (safe_enchant < 0) {
				isErr = true;
				break;
			}
			break;
		}
		default: {
			isErr = true;
			break;
		}
		}
		final int armorId = tgItem.getItem().getItemId();
		if (armorId >= 401004 && armorId <= 401007) {
			isErr = true;
		}
		if (armorId >= 120444 && armorId <= 120448) {
			isErr = true;
		}
		if (armorId >= 120477 && armorId <= 120479) {
			isErr = true;
		}
		if (tgItem.getBless() >= 128) {
			isErr = true;
		}
		if (isErr) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		if (tgItem.getEnchantLevel() == 15) {
			pc.sendPackets(new S_ServerMessage("您的防具已達上限值。"));
			return;
		}
		final int enchant_level = tgItem.getEnchantLevel();
		final EnchantExecutor enchantExecutor = new EnchantArmor();
		int randomELevel = enchantExecutor.randomELevel(tgItem, item.getBless());
		pc.getInventory().removeItem(item, 1L);
		boolean isEnchant = true;
		if (enchant_level < -6) {
			isEnchant = false;
		} else if (enchant_level < safe_enchant) {
			isEnchant = true;
		} else {
			final Random random = new Random();
			final int rnd = random.nextInt(100) + 1;
			int enchant_level_tmp;
			if (safe_enchant == 0) {
				enchant_level_tmp = enchant_level + 2;
			} else {
				enchant_level_tmp = enchant_level;
			}
			int enchant_chance_armor;
			if (enchant_level >= 9) {
				enchant_chance_armor = (int) L1ItemUpdata.enchant_armor_up9(enchant_level_tmp);
			} else {
				enchant_chance_armor = (int) L1ItemUpdata.enchant_armor_dn9(enchant_level_tmp);
			}
			if (rnd < enchant_chance_armor) {
				isEnchant = true;
			} else if (enchant_level >= 9 && rnd < enchant_chance_armor * 2) {
				randomELevel = 0;
			} else {
				isEnchant = false;
			}
		}
		if (randomELevel <= 0 && enchant_level > -6) {
			isEnchant = true;
		}
		if (isEnchant) {
			enchantExecutor.successEnchant(pc, tgItem, randomELevel);
			tgItem.setproctect(false);
			tgItem.setproctect1(false);
			tgItem.setproctect2(false);
			tgItem.setproctect3(false);
			tgItem.setproctect4(false);
			tgItem.setproctect5(false);
			pc.sendPackets(new S_ItemStatus(tgItem));
			pc.getInventory().saveItem(tgItem, 4);
			if (pc.getAccessLevel() == 0 && !pc.getcheck_lv()
					&& tgItem.getEnchantLevel() >= tgItem.getItem().get_safeenchant() + 3) {
				World.get().broadcastPacketToAll(new S_BlueMessage(166,
						"\\f=【" + pc.getName() + "】的+" + enchant_level + " " + tgItem.getName() + "強化成功"));
			}
			pc.setcheck_lv(false);
		} else if (tgItem.getproctect()) {
			tgItem.setproctect(false);
			pc.sendPackets(new S_ItemStatus(tgItem));
			pc.getInventory().updateItem(tgItem, 4);
			pc.getInventory().saveItem(tgItem, 4);
			pc.sendPackets(new S_ServerMessage("受到高級裝備保護卷軸的影響,失敗後物品無變化。"));
		} else if (tgItem.getproctect1()) {
			tgItem.setproctect1(false);
			tgItem.setEnchantLevel(tgItem.getEnchantLevel() - 1);
			pc.sendPackets(new S_ItemStatus(tgItem));
			pc.getInventory().updateItem(tgItem, 4);
			pc.getInventory().saveItem(tgItem, 4);
			pc.sendPackets(new S_ServerMessage("受到中級裝備保護卷軸的影響,失敗後物品倒扣1。"));
		} else if (tgItem.getproctect2()) {
			tgItem.setproctect2(false);
			tgItem.setEnchantLevel(0);
			pc.sendPackets(new S_ItemStatus(tgItem));
			pc.getInventory().updateItem(tgItem, 4);
			pc.getInventory().saveItem(tgItem, 4);
			pc.sendPackets(new S_ServerMessage("受到初級裝備保護卷軸的影響,失敗後物品歸0。"));
		} else if (tgItem.getproctect3()) {
			if (this.random.nextInt(100) + 1 <= pc.getproctctran()) {
				tgItem.setproctect3(false);
				pc.setproctctran(0);
				tgItem.setEnchantLevel(0);
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.getInventory().updateItem(tgItem, 4);
				pc.getInventory().saveItem(tgItem, 4);
				pc.sendPackets(new S_ServerMessage("受到初級裝備保護卷軸的影響,失敗後物品歸0。"));
			} else {
				pc.setproctctran(0);
				enchantExecutor.failureEnchant(pc, tgItem);
			}
		} else if (tgItem.getproctect4()) {
			if (this.random.nextInt(100) + 1 <= pc.getproctctran()) {
				tgItem.setproctect4(false);
				pc.setproctctran(0);
				tgItem.setEnchantLevel(tgItem.getEnchantLevel() - 1);
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.getInventory().updateItem(tgItem, 4);
				pc.getInventory().saveItem(tgItem, 4);
				pc.sendPackets(new S_ServerMessage("受到中級裝備保護卷軸的影響,失敗後物品倒扣1。"));
			} else {
				pc.setproctctran(0);
				enchantExecutor.failureEnchant(pc, tgItem);
			}
		} else if (tgItem.getproctect5()) {
			if (this.random.nextInt(100) + 1 <= pc.getproctctran()) {
				tgItem.setproctect5(false);
				pc.setproctctran(0);
				pc.sendPackets(new S_ItemStatus(tgItem));
				pc.getInventory().updateItem(tgItem, 4);
				pc.getInventory().saveItem(tgItem, 4);
				pc.sendPackets(new S_ServerMessage("受到高級裝備保護卷軸的影響,失敗後物品無變化。"));
			} else {
				pc.setproctctran(0);
				enchantExecutor.failureEnchant(pc, tgItem);
			}
		} else {
			enchantExecutor.failureEnchant(pc, tgItem);
		}
	}
}
