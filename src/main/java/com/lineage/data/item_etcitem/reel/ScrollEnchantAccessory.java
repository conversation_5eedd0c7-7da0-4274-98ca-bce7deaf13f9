package com.lineage.data.item_etcitem.reel;

import com.lineage.data.cmd.EnchantAccessory;
import com.lineage.data.cmd.EnchantExecutor;
import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.utils.log.PlayerLogUtil;

import java.sql.Timestamp;
import java.util.Random;

public class ScrollEnchantAccessory extends ItemExecutor {
	private int typelv;
	private int a;
	private int b;
	private int c;
	private int d;
	private int e;
	private int f;
	private int g;
	private int h;
	private int i;

	private ScrollEnchantAccessory() {
	}

	public static ItemExecutor get() {
		return new ScrollEnchantAccessory();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (tgItem.isEquipped()) {
			pc.sendPackets(new S_ServerMessage("(預防誤點機制啟動)裝備中無法強化"));
			return;
		}
		final int safe_enchant = tgItem.getItem().get_safeenchant();
		boolean isErr = false;
		final int use_type = tgItem.getItem().getUseType();
		switch (use_type) {
		case 23:
		case 24:
		case 37:
		case 40: {
			if (tgItem.getItem().get_greater() == 3) {
				isErr = true;
			}
			if (safe_enchant < 0) {
				isErr = true;
				break;
			}
			break;
		}
		default: {
			isErr = true;
			break;
		}
		}
		if (tgItem.getBless() >= 128) {
			isErr = true;
		}
		if (isErr) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		final int enchant_level = tgItem.getEnchantLevel();
		if (enchant_level == this.typelv) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		final EnchantExecutor enchantExecutor = new EnchantAccessory();
		final int randomELevel = enchantExecutor.randomELevel(tgItem, item.getBless());
		pc.getInventory().removeItem(item, 1L);
		final Random random = new Random();
		int lv = 0;
		if (tgItem.getEnchantLevel() == 0) {
			lv = this.a;
		} else if (tgItem.getEnchantLevel() == 1) {
			lv = this.b;
		} else if (tgItem.getEnchantLevel() == 2) {
			lv = this.c;
		} else if (tgItem.getEnchantLevel() == 3) {
			lv = this.d;
		} else if (tgItem.getEnchantLevel() == 4) {
			lv = this.e;
		} else if (tgItem.getEnchantLevel() == 5) {
			lv = this.f;
		} else if (tgItem.getEnchantLevel() == 6) {
			lv = this.g;
		} else if (tgItem.getEnchantLevel() == 7) {
			lv = this.h;
		} else if (tgItem.getEnchantLevel() == 8) {
			lv = this.i;
		}
		if (random.nextInt(100) <= lv) {
			enchantExecutor.successEnchant(pc, tgItem, randomELevel);
			飾品強化成功記錄表("IP(" + pc.getNetConnection().getIp() + ")" + "玩家" + ":【 " + pc.getName() + " 】 " + "的" + "【  "
					+ tgItem.getEnchantLevel() + " " + tgItem.getName() + "】- (強化成功)" + "時間:" + "("
					+ new Timestamp(System.currentTimeMillis()) + ")。");
		} else {
			enchantExecutor.failureEnchant(pc, tgItem);
			飾品強化失敗記錄表("IP(" + pc.getNetConnection().getIp() + ")" + "玩家" + ":【 " + pc.getName() + " 】 " + "的" + "【  "
					+ tgItem.getEnchantLevel() + " " + tgItem.getName() + "】- (爆了)" + "時間:" + "("
					+ new Timestamp(System.currentTimeMillis()) + ")。");
		}
	}

	public static void 飾品強化失敗記錄表(final String info) {
		PlayerLogUtil.writeLog("[飾品強化[失敗]記錄表]", info);
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[飾品強化[失敗]記錄表].txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}

	public static void 飾品強化成功記錄表(final String info) {
		PlayerLogUtil.writeLog("[飾品強化[成功]記錄表]", info);
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[飾品強化[成功]記錄表].txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this.typelv = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
		try {
			this.a = Integer.parseInt(set[2]);
		} catch (Exception ex2) {
		}
		try {
			this.b = Integer.parseInt(set[3]);
		} catch (Exception ex3) {
		}
		try {
			this.c = Integer.parseInt(set[4]);
		} catch (Exception ex4) {
		}
		try {
			this.d = Integer.parseInt(set[5]);
		} catch (Exception ex5) {
		}
		try {
			this.e = Integer.parseInt(set[6]);
		} catch (Exception ex6) {
		}
		try {
			this.f = Integer.parseInt(set[7]);
		} catch (Exception ex7) {
		}
		try {
			this.g = Integer.parseInt(set[8]);
		} catch (Exception ex8) {
		}
		try {
			this.h = Integer.parseInt(set[9]);
		} catch (Exception ex9) {
		}
		try {
			this.i = Integer.parseInt(set[10]);
		} catch (Exception ex10) {
		}
	}
}
