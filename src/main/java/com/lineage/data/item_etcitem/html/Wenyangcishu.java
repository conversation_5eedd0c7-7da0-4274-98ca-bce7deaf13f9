package com.lineage.data.item_etcitem.html;

import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_SystemMessage;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

public class <PERSON>yang<PERSON>hu extends ItemExecutor {
    private static final Log _log = LogFactory.getLog(Wenyangcishu.class);
    Random _random = new Random();
    private int _type;
    private int _reduceCount;

    public static ItemExecutor get() {
        return new Wenyangcishu();
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {

        try {
            if (pc == null) {
                return;
            }

            if (pc.isDead()) {
                return;
            }

            if (pc.isPrivateShop()) {
                return;
            }

            switch (_type) {
                case 1: //伊娃
                    pc.setyiwacishu(pc.getyiwacishu() - _reduceCount);
                    if (pc.getyiwacishu() <= 0) {
                        pc.setyiwacishu(0);
                    }
                    break;
                case 2: //沙哈
                    pc.setshahacishu(pc.getshahacishu() - _reduceCount);
                    if (pc.getshahacishu() <= 0) {
                        pc.setshahacishu(0);
                    }
                    break;
                case 3: //馬普勒
                    pc.setmapulecishu(pc.getmapulecishu() - _reduceCount);
                    if (pc.getmapulecishu() <= 0) {
                        pc.setmapulecishu(0);
                    }
                    break;
                case 4: //帕格里奧
                    pc.setpageliaocishu(pc.getpageliaocishu() - _reduceCount);
                    if (pc.getpageliaocishu() <= 0) {
                        pc.setpageliaocishu(0);
                    }
                    break;
                case 5: //殷海薩(其實是另外一個)
                    pc.setyinhaisacishu(pc.getyinhaisacishu() - _reduceCount);
                    if (pc.getyinhaisacishu() <= 0) {
                        pc.setyinhaisacishu(0);
                    }
                    break;
            }

            pc.sendPackets(new S_SystemMessage("獲得[" + _reduceCount + "]次強化機會"));
            pc.getInventory().removeItem(item, 1);
            pc.save();
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public void set_set(String[] set) {
        try {
            _type = Integer.parseInt(set[1]);
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        try {
            _reduceCount = Integer.parseInt(set[2]);
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }
}