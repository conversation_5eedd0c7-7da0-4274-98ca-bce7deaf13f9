package com.lineage.data.item_etcitem.wand;

import com.lineage.server.templates.L1Item;
import java.util.Map;
import java.util.ArrayList;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.templates.L1Drop;
import java.util.HashMap;
import com.lineage.server.datatables.DropTable;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_DoActionGFX;
import com.lineage.server.world.World;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Npc_Wand extends ItemExecutor {
	public static ItemExecutor get() {
		return new Npc_Wand();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int spellsc_objid = data[0];
		this.useNpcCarriedItemsWand(pc, spellsc_objid, item);
	}

	private void useNpcCarriedItemsWand(final L1PcInstance pc, final int targetId, final L1ItemInstance item) {
		final L1Object target = World.get().findObject(targetId);
		if (target == null) {
			return;
		}
		pc.sendPacketsAll(new S_DoActionGFX(pc.getId(), 17));
		if (target instanceof L1MonsterInstance) {
			final L1NpcInstance tgnpc = (L1MonsterInstance) target;
			final ArrayList<L1Drop> dropList = DropTable.get().getdroplist(tgnpc.getNpcId());
			if (dropList != null) {
				final String msg0 = new StringBuilder().append(tgnpc.getName()).toString();
				final String msg2 = new StringBuilder().append(tgnpc.getLevel()).toString();
				final String msg3 = new StringBuilder().append(tgnpc.getMaxHp()).toString();
				final String msg4 = new StringBuilder().append(tgnpc.getMaxMp()).toString();
				final String msg5 = new StringBuilder().append(tgnpc.getAc()).toString();
				final String[] msgs = { msg0, msg2, msg3, msg4, msg5 };
				final Map<Integer, String> msgstable = new HashMap();
				int i = 0;
				while (i < msgs.length) {
					msgstable.put(Integer.valueOf(i), msgs[i]);
					++i;
				}
				final String[] itemdatas = new String[dropList.size()];
				int j = 0;
				while (j < itemdatas.length) {
					final L1Drop drop = dropList.get(j);
					final L1Item dropitem = ItemTable.get().getTemplate(drop.getItemid());
					itemdatas[j] = dropitem.getName();
					msgstable.put(Integer.valueOf(j + 5), itemdatas[j]);
					++j;
				}
				final String[] msgs2 = new String[msgstable.size()];
				int k = 0;
				while (k < msgstable.size()) {
					msgs2[k] = msgstable.get(Integer.valueOf(k));
					++k;
				}
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "mobdroplist", msgs2));
			} else {
				pc.sendPackets(new S_SystemMessage("此怪物沒有掉寶資料。"));
			}
		} else {
			pc.sendPackets(new S_SystemMessage("只能對怪物使用。"));
		}
	}
}
