package com.lineage.data.item_etcitem;

import com.lineage.server.datatables.sql.LetterTable;
import java.util.Calendar;
import java.util.TimeZone;
import com.lineage.config.Config;
import java.text.SimpleDateFormat;
import java.util.concurrent.CopyOnWriteArrayList;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.datatables.lock.CharItemsReading;
import com.lineage.server.datatables.CharObjidTable;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.world.World;
import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.L1Clan;
import com.lineage.server.world.WorldClan;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.ItemExecutor;

public class Clan_Letter extends ItemExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(Clan_Letter.class);
	}

	private Clan_Letter() {
	}

	public static ItemExecutor get() {
		return new Clan_Letter();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int itemId = item.getItemId();
		final int letterCode = data[0];
		final String letterReceiver = pc.getText();
		final byte[] letterText = pc.getTextByte();
		if (this.writeClanLetter(itemId, pc, letterCode, letterReceiver, letterText)) {
			pc.getInventory().removeItem(item, 1L);
		}
	}

	private boolean writeClanLetter(final int itemId, final L1PcInstance pc, final int letterCode,
			final String letterReceiver, final byte[] letterText) {
		L1Clan targetClan = null;
		final Collection<L1Clan> allClans = WorldClan.get().getAllClans();
		final Iterator<L1Clan> iter = allClans.iterator();
		while (iter.hasNext()) {
			final L1Clan clan = iter.next();
			if (clan.getClanName().toLowerCase().equals(letterReceiver.toLowerCase())) {
				targetClan = clan;
				break;
			}
		}
		if (targetClan == null) {
			pc.sendPackets(new S_ServerMessage(434));
			return false;
		}
		final String[] memberName = targetClan.getAllMembers();
		int i = 0;
		while (i < memberName.length) {
			final L1ItemInstance item = ItemTable.get().createItem(49016);
			if (item == null) {
				return false;
			}
			item.setCount(1L);
			if (this.sendLetter(pc, memberName[i], item, false)) {
				this.saveLetter(item.getId(), letterCode, pc.getName(), memberName[i], letterText);
			}
			++i;
		}
		return true;
	}

	private boolean sendLetter(final L1PcInstance pc, final String name, final L1ItemInstance item,
			final boolean isFailureMessage) {
		final L1PcInstance target = World.get().getPlayer(name);
		if (target == null) {
			if (CharacterTable.doesCharNameExist(name)) {
				try {
					final int objid = CharObjidTable.get().charObjid(name);
					final CopyOnWriteArrayList<L1ItemInstance> list = CharItemsReading.get()
							.loadItems(Integer.valueOf(objid));
					if (list.size() < 180) {
						CharItemsReading.get().storeItem(objid, item);
						return true;
					}
					if (isFailureMessage) {
						pc.sendPackets(new S_ServerMessage(942));
					}
					return false;
				} catch (Exception e) {
					Clan_Letter._log.error(e.getLocalizedMessage(), e);
					return true;
				}
			}
			if (isFailureMessage) {
				pc.sendPackets(new S_ServerMessage(109, name));
			}
			return false;
		}
		if (target.getInventory().checkAddItem(item, 1L) != 0) {
			if (isFailureMessage) {
				pc.sendPackets(new S_ServerMessage(942));
			}
			return false;
		}
		target.getInventory().storeItem(item);
		target.sendPackets(new S_SkillSound(target.getId(), 1091));
		target.sendPackets(new S_ServerMessage(428));
		return true;
	}

	private void saveLetter(final int itemObjectId, final int code, final String sender, final String receiver,
			final byte[] text) {
		final SimpleDateFormat sdf = new SimpleDateFormat("yy/MM/dd");
		final TimeZone tz = TimeZone.getTimeZone(Config.TIME_ZONE);
		final String date = sdf.format(Calendar.getInstance(tz).getTime());
		int spacePosition1 = 0;
		int spacePosition2 = 0;
		int i = 0;
		while (i < text.length) {
			if (text[i] == 0 && text[i + 1] == 0) {
				if (spacePosition1 == 0) {
					spacePosition1 = i;
				} else if (spacePosition1 != 0 && spacePosition2 == 0) {
					spacePosition2 = i;
					break;
				}
			}
			i += 2;
		}
		final int subjectLength = spacePosition1 + 2;
		int contentLength = spacePosition2 - spacePosition1;
		if (contentLength <= 0) {
			contentLength = 1;
		}
		final byte[] subject = new byte[subjectLength];
		final byte[] content = new byte[contentLength];
		System.arraycopy(text, 0, subject, 0, subjectLength);
		System.arraycopy(text, subjectLength, content, 0, contentLength);
		LetterTable.getInstance().writeLetter(itemObjectId, code, sender, receiver, date, 0, subject, content);
	}
}
