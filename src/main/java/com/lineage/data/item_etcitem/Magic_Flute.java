package com.lineage.data.item_etcitem;

import java.util.Iterator;
import com.lineage.data.cmd.CreateNewItem;
import com.lineage.server.model.Instance.L1GuardianInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_Sound;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Magic_Flute extends ItemExecutor {
	public static ItemExecutor get() {
		return new Magic_Flute();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		pc.sendPacketsX8(new S_Sound(165));
		final Iterator<L1Object> iterator = pc.getKnownObjects().iterator();
		while (iterator.hasNext()) {
			final L1Object visible = iterator.next();
			if (visible instanceof L1GuardianInstance) {
				final L1GuardianInstance guardian = (L1GuardianInstance) visible;
				if (guardian.getNpcTemplate().get_npcId() != 70850 || !CreateNewItem.createNewItem(pc, 88, 1L)) {
					continue;
				}
				pc.getInventory().removeItem(item, 1L);
			}
		}
	}
}
