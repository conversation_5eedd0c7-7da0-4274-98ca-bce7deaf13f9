package com.lineage.data.item_etcitem;

import com.lineage.data.executor.ItemExecutor;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.serverpackets.S_PacketBoxGree;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.utils.log.PlayerLogUtil;
import com.lineage.server.world.World;
import com.lineage.william.New_BlessItem;

import java.sql.Timestamp;
import java.util.Random;

public class Reel_item_bless_weapon extends ItemExecutor {
    private int _xianzhi;
    private boolean type2;

    private Reel_item_bless_weapon() {
        _xianzhi = 0;
        type2 = false;
    }

    public static ItemExecutor get() {
        return new Reel_item_bless_weapon();
    }

    public static void 祝福升級成功(String info) {
        PlayerLogUtil.writeLog("[祝福升級成功]", info);
//            BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[祝福升級成功].txt", true));
//            out.write(String.valueOf(info) + "\r\n");
//            out.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//        try {
    }

    public static void 祝福升級失敗(String info) {
        PlayerLogUtil.writeLog("[祝福升級失敗]", info);
//        try {
//            BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[祝福升級失敗].txt", true));
//            out.write(String.valueOf(info) + "\r\n");
//            out.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void execute(int[] data, L1PcInstance pc, L1ItemInstance item) {
        int itemobj = data[0];
        L1ItemInstance item2 = pc.getInventory().getItem(itemobj);
        if (item2 == null) {
            return;
        }
        if (item2.getBless() == 0) {
            pc.sendPackets(new S_ServerMessage("已經是祝福的物品!"));
            return;
        }
        if (item2.getBless() == 128) {
            pc.sendPackets(new S_ServerMessage("已經是祝福的物品"));
            return;
        }
        if (item2.getItem().getType2() != 1) {
            pc.sendPackets(new S_ServerMessage("該物品不是武器無法祝福"));
            return;
        }
        if (!item2.getItem().isgivebless()) {
            pc.sendPackets(new S_SystemMessage("該物品無法進行祝福。"));
            return;
        }
        Random _random = new Random();
        int _xianzhi1 = _xianzhi;
		/*if (pc.isGm()) {
			_xianzhi1 = 100;//Kevin 如果身分是GM成功率100%
		}*/
        if (_random.nextInt(100) + 1 <= _xianzhi1) {
            item2.setBless(0);
            New_BlessItem.forIntensifyweapon(pc, item2);
            pc.getInventory().updateItem(item2, 576);
            pc.getInventory().saveItem(item2, 576);
            pc.sendPackets(new S_ServerMessage("祝福成功"));
            if (!pc.isGm()) {
                World.get().broadcastPacketToAll(new S_PacketBoxGree(2, "玩家：" + pc.getName() + " 使用(" + item.getName() + ") 對 (" + item2.getViewName() + ")升級成功"));
            }
            pc.getInventory().removeItem(item, 1L);
            pc.sendPackets(new S_ItemStatus(item2));
            祝福升級成功("玩家 :" + pc.getName() + " 使用(" + item.getName() + ") 對 (" + item2.getViewName() + ")升級成功 ,時間:"
                    + new Timestamp(System.currentTimeMillis()) + ")");
        } else {
            pc.getInventory().removeItem(item, 1L);
            pc.sendPackets(new S_SystemMessage("祝福失敗"));
            if (!pc.isGm()) {
                World.get().broadcastPacketToAll(new S_PacketBoxGree(2, "玩家：" + pc.getName() + " 使用(" + item.getName() + ") 對 (" + item2.getViewName() + ")升級失敗"));
                World.get().broadcastPacketToAll(new S_ServerMessage("玩家：" + pc.getName() + " 使用(" + item.getName() + ") 對 (" + item2.getViewName() + ")升級失敗"));

            }
            祝福升級失敗("玩家 :" + pc.getName() + " 使用(" + item.getName() + ") 對 (" + item2.getViewName() + ")升級(失敗) ,時間:"
                    + new Timestamp(System.currentTimeMillis()) + ")");
            if (type2) {
                pc.getInventory().removeItem(item2, 1L);
            }
        }
    }

    @Override
    public void set_set(String[] set) {
        try {
            _xianzhi = Integer.parseInt(set[1]);
        } catch (Exception ex) {
        }
        try {
            type2 = Boolean.parseBoolean(set[2]);
        } catch (Exception ex2) {
        }
    }
}
