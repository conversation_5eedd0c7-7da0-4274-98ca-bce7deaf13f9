package com.lineage.data.item_etcitem.quest;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class Second_p4 extends ItemExecutor {
	public static ItemExecutor get() {
		return new Second_p4();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "fourth_p"));
	}
}
