package com.lineage.data.item_etcitem.quest;

import com.lineage.data.cmd.CreateNewItem;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.Random;
import com.lineage.data.executor.ItemExecutor;

public class Bonusbox_2 extends ItemExecutor {
	private final Random _random;
	private final int[] bonus_list;

	public Bonusbox_2() {
		this._random = new Random();
		this.bonus_list = new int[] { 56216, 56217, 56218, 56219, 56220, 56221, 56222, 56223, 56224, 56225, 56226,
				56227, 56228, 56229, 56230, 56231, 56232, 56233, 49334, 49333, 49332, 49331, 49330, 49329, 49328, 49327,
				56235, 49336 };
	}

	public static ItemExecutor get() {
		return new Bonusbox_2();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		pc.getInventory().removeItem(item, 1L);
		final int value = this._random.nextInt(1) + 1;
		int i = 0;
		while (i < value) {
			CreateNewItem.createNewItem(pc, this.bonus_list[this._random.nextInt(this.bonus_list.length)], 1L);
			++i;
		}
	}
}
