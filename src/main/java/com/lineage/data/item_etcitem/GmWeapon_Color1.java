package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.S_MPUpdate;
import com.lineage.server.serverpackets.S_HPUpdate;
import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.serverpackets.S_SPMR;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.william.drop_type_weapon_item2;
import com.lineage.server.datatables.sql.CharItemsTable;
import com.lineage.william.drop_type_weapon_clean2;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class GmWeapon_Color1 extends ItemExecutor {
	private int type1;

	public static ItemExecutor get() {
		return new GmWeapon_Color1();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int targObjId = data[0];
		final L1ItemInstance tgItem = pc.getInventory().getItem(targObjId);
		if (tgItem == null) {
			return;
		}
		if (!tgItem.getItem().isdropcolor()) {
			pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			return;
		}
		if (tgItem.getItem().getUseType() != 1) {
			pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			return;
		}
		if (tgItem.getItemArmorType() == this.type1) {
			pc.sendPackets(new S_SystemMessage("似乎沒發生什麼事情。"));
			return;
		}
		if (tgItem.getItemArmorType() > 0) {
			drop_type_weapon_clean2.forIntensifyArmor(pc, tgItem);
			tgItem.setItemArmorType(this.type1);
			final CharItemsTable cit = new CharItemsTable();
			try {
				cit.updateItemArmorType(tgItem);
			} catch (Exception e) {
				e.printStackTrace();
			}
			drop_type_weapon_item2.forIntensifyArmor(pc, tgItem);
			pc.getInventory().removeItem(item, 1L);
			pc.sendPackets(new S_ItemStatus(tgItem));
			pc.sendPackets(new S_SPMR(pc));
			pc.sendPackets(new S_OwnCharStatus(pc));
			pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
			pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
			return;
		}
		tgItem.setItemArmorType(this.type1);
		final CharItemsTable cit = new CharItemsTable();
		try {
			cit.updateItemArmorType(tgItem);
		} catch (Exception e) {
			e.printStackTrace();
		}
		drop_type_weapon_item2.forIntensifyArmor(pc, tgItem);
		pc.getInventory().removeItem(item, 1L);
		pc.sendPackets(new S_ItemStatus(tgItem));
		pc.sendPackets(new S_SPMR(pc));
		pc.sendPackets(new S_OwnCharStatus(pc));
		pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
		pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this.type1 = Integer.parseInt(set[1]);
		} catch (Exception ex) {
		}
	}
}
