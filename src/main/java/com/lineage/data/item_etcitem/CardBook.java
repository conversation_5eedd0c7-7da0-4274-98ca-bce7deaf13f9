package com.lineage.data.item_etcitem;

import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.ItemExecutor;

public class <PERSON><PERSON>ook extends ItemExecutor {
	private static CardBook _instance;

	public static ItemExecutor get() {
		return new CardBook();
	}

	public static CardBook getInstance() {
		if (CardBook._instance == null) {
			CardBook._instance = new CardBook();
		}
		return CardBook._instance;
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		if (item == null) {
			return;
		}
		if (pc == null) {
			return;
		}
		if (pc.isPrivateShop()) {
			pc.sendPackets(new S_SystemMessage("擺攤狀態下 無法使用"));
			return;
		}
		if (pc.isFishing()) {
			pc.sendPackets(new S_SystemMessage("釣魚狀態下 無法使用"));
			return;
		}
		pc.setCarId(-1);
		pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "card_01"));
	}
}
