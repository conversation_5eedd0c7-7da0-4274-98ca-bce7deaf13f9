package com.lineage.data.item_etcitem;

import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.ItemExecutor;

public class FishingPoleRoller extends ItemExecutor {
	private static final Log _log;
	private int _fishpoleid;

	static {
		_log = LogFactory.getLog(FishingPoleRoller.class);
	}

	public static ItemExecutor get() {
		return new FishingPoleRoller();
	}

	@Override
	public void execute(final int[] data, final L1PcInstance pc, final L1ItemInstance item) {
		final int itemobj = data[0];
		final L1ItemInstance tgitem = pc.getInventory().getItem(itemobj);
		if (tgitem == null) {
			return;
		}
		this.useRoller(pc, tgitem, item);
	}

	private void useRoller(final L1PcInstance pc, final L1ItemInstance tgitem, final L1ItemInstance item) {
		if (tgitem == null || item == null) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		if (tgitem.getItem().getUseType() != 42) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		if (tgitem.getItemId() == 83014 && item.getItemId() != 83004) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		if (tgitem.getItemId() == 83024 && item.getItemId() != 83023) {
			pc.sendPackets(new S_ServerMessage(79));
			return;
		}
		if (tgitem.getItemId() == 83014 || tgitem.getItemId() == 83024) {
			final int maxchargecount = tgitem.getItem().getMaxChargeCount();
			if (tgitem.getChargeCount() + 100 >= maxchargecount) {
				tgitem.setChargeCount(maxchargecount);
			} else {
				tgitem.setChargeCount(tgitem.getChargeCount() + 100);
			}
			pc.getInventory().updateItem(tgitem, 128);
			pc.getInventory().removeItem(item, 1L);
		}
		if (tgitem.getItemId() == 83001) {
			final L1ItemInstance newitem = ItemTable.get().createItem(this._fishpoleid);
			newitem.setChargeCount(100);
			newitem.setIdentified(true);
			pc.getInventory().storeItem(newitem);
			pc.getInventory().removeItem(tgitem, 1L);
			pc.getInventory().removeItem(item, 1L);
		}
	}

	@Override
	public void set_set(final String[] set) {
		try {
			this._fishpoleid = Integer.parseInt(set[1]);
			if (this._fishpoleid <= 0) {
				this._fishpoleid = 83014;
				FishingPoleRoller._log.error("FishingPoleRoller 設置錯誤:釣竿編號錯誤! 使用預設83014");
			}
		} catch (Exception ex) {
		}
	}
}
