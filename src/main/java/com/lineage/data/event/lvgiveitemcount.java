package com.lineage.data.event;

import com.lineage.server.templates.L1Event;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.EventExecutor;

public class lvgiveitemcount extends EventExecutor {
	private static final Log _log;
	public static boolean START;

	static {
		_log = LogFactory.getLog(lvgiveitemcount.class);
		START = false;
	}

	public static EventExecutor get() {
		return new lvgiveitemcount();
	}

	@Override
	public void execute(final L1Event event) {
		try {
			lvgiveitemcount.START = true;
		} catch (Exception e) {
			lvgiveitemcount._log.error(e.getLocalizedMessage(), e);
		}
	}
}
