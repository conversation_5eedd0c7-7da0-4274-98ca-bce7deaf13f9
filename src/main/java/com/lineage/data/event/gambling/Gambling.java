package com.lineage.data.event.gambling;

import com.lineage.data.npc.event.GamblingNpc;
import com.lineage.server.IdFactoryNpc;
import com.lineage.server.datatables.NpcTable;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.serverpackets.S_NPCPack;
import java.util.List;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.types.Point;

public class Gambling extends GamblingConfig {
    private final Map<Integer, GamblingNpc> _npcidMap;
    private final Random _random;
    public int WIN;
    private int[] winCount = new int[5]; // Win count for five dogs
    private GamblingNpc _onenpc;
    private long _adena;
    private long _upadena;
    private static final Log _log = LogFactory.getLog(Gambling.class);

    public Gambling() {
        _npcidMap = new HashMap<>();
        _random = new Random();
    }

    public void clear() {
        if (_npcidMap != null) {
            delAllNpc();
            _npcidMap.clear();
        }
        _onenpc = null;
        _adena = 0L;
    }

    public long get_allAdena() {
        return _adena;
    }

    public void set_gmaNpc(long previous) {
        // Clear existing NPCs first to avoid duplicates
        if (!_npcidMap.isEmpty()) {
            delAllNpc();
        }
        
        if (GamblingConfig.ISGFX) {
            GamblingConfig.ISGFX = false;
        } else {
            GamblingConfig.ISGFX = true;
        }
        
        chooseWinDog(); // Use weighted random to select champion dog
        
        int i = 0;
        int attempts = 0;
        int maxAttempts = 50; // Prevent infinite loop
        
        while (_npcidMap.size() < 5 && attempts < maxAttempts) {
            attempts++;
            int npcid = GamblingConfig.NPCID[_random.nextInt(GamblingConfig.NPCID.length)];
            
            if (_npcidMap.get(npcid) == null) {
                try {
                    GamblingNpc gamnpc = new GamblingNpc(this);
                    gamnpc.showNpc(npcid, i);
                    _npcidMap.put(npcid, gamnpc);
                    i++;
                } catch (Exception e) {
                    _log.error("Failed to spawn gambling NPC", e);
                }
            }
        }
        
        if (attempts >= maxAttempts) {
            _log.warn("Reached maximum attempts when spawning gambling NPCs, may not be able to spawn enough NPCs");
        }
        
        _upadena = previous;
    }

    // Weighted random selection for champion dog
    private void chooseWinDog() {
        int maxWin = 0;
        for (int i = 0; i < 5; i++) {
            if (winCount[i] > maxWin) maxWin = winCount[i];
        }
        int[] weights = new int[5];
        int totalWeight = 0;
        for (int i = 0; i < 5; i++) {
            weights[i] = (maxWin + 1) - winCount[i];
            if (weights[i] < 1) weights[i] = 1;
            totalWeight += weights[i];
        }
        int rnd = _random.nextInt(totalWeight);
        int sum = 0;
        for (int i = 0; i < 5; i++) {
            sum += weights[i];
            if (rnd < sum) {
                WIN = i;
                break;
            }
        }
    }

    public long get_allRate() {
        long adena = 0L;
        for (GamblingNpc gamblingNpc : _npcidMap.values()) {
            adena += gamblingNpc.get_adena();
        }
        return adena + _upadena;
    }

    public void set_allRate() {
        long adena = _upadena;
        for (GamblingNpc gamblingNpc : _npcidMap.values()) {
            adena += gamblingNpc.get_adena();
        }
        _adena = adena;
        for (GamblingNpc gamblingNpc : _npcidMap.values()) {
            set_npcRate(gamblingNpc, adena);
        }
    }

    private void set_npcRate(GamblingNpc npc, long adena) {
        double rate = 10 + _random.nextInt(20); //Kevin original max 5x, changed from 40 to 20
        npc.set_rate(rate / 10.0);
    }

    public Map<Integer, GamblingNpc> get_allNpc() {
        return _npcidMap;
    }

    public GamblingNpc get_oneNpc() {
        return _onenpc;
    }

    public void set_oneNpc(GamblingNpc onenpc) {
        _onenpc = onenpc;
    }

    public void startGam() {
        for (GamblingNpc gamblingNpc : _npcidMap.values()) {
            try {
                if (gamblingNpc != null && gamblingNpc.get_npc() != null) {
                    gamblingNpc.start_action(); // Set NPC action state to movement mode
                    gamblingNpc.getStart(); // Start movement thread
                }
            } catch (Exception e) {
                _log.error("Error starting gambling NPC movement", e);
            }
        }
    }

    public void delAllNpc() {
        if (_npcidMap.isEmpty()) {
            return;
        }
        
        for (GamblingNpc gamblingNpc : _npcidMap.values()) {
            try {
                if (gamblingNpc != null) {
                    gamblingNpc.del();
                }
            } catch (Exception e) {
                _log.error("Error recycling gambling NPC", e);
            }
        }
        
        // Clear NPC map for next race
        _npcidMap.clear();
    }
}
