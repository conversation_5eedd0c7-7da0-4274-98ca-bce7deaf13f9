package com.lineage.data.event;

import com.lineage.data.event.dice.DiceGame;
import com.lineage.data.executor.EventExecutor;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.templates.L1Event;
import com.lineage.server.timecontroller.event.DiceGameTime;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Iterator;

/**
 * 骰寶遊戲事件設定
 * 管理遊戲配置、NPC生成、系統啟動等
 */
public class DiceGameSet extends EventExecutor {
    private static final Log _log = LogFactory.getLog(DiceGameSet.class);
    
    // 遊戲配置
    public static int GAME_INTERVAL = 60; // 每局遊戲間隔(秒)
    public static int BETTING_TIME = 30;  // 下注時間(秒)
    public static int MIN_BET = 100;      // 最小下注金額
    public static int MAX_BET = 1000000;  // 最大下注金額
    
    // NPC配置
    public static int DICE_NPC_ID = 91173; // 骰寶NPC編號
    public static int DICE_NPC_X = 33521;  // NPC X座標
    public static int DICE_NPC_Y = 32862;  // NPC Y座標
    public static short DICE_NPC_MAP = 4;  // NPC地圖編號
    public static int DICE_NPC_HEADING = 5; // NPC面向
    
    public static EventExecutor get() {
        return new DiceGameSet();
    }
    
    @Override
    public void execute(final L1Event event) {
        try {
            // 解析事件配置
            final String[] set = event.get_eventother().split(",");
            
            try {
                DiceGameSet.GAME_INTERVAL = Integer.parseInt(set[0]);
            } catch (Exception e) {
                DiceGameSet.GAME_INTERVAL = 60;
                DiceGameSet._log.error("未設定遊戲間隔時間(秒)(使用預設60秒)");
            }
            
            try {
                DiceGameSet.BETTING_TIME = Integer.parseInt(set[1]);
            } catch (Exception e) {
                DiceGameSet.BETTING_TIME = 30;
                DiceGameSet._log.error("未設定下注時間(秒)(使用預設30秒)");
            }
            
            try {
                DiceGameSet.MIN_BET = Integer.parseInt(set[2]);
            } catch (Exception e) {
                DiceGameSet.MIN_BET = 100;
                DiceGameSet._log.error("未設定最小下注金額(使用預設100)");
            }
            
            try {
                DiceGameSet.MAX_BET = Integer.parseInt(set[3]);
            } catch (Exception e) {
                DiceGameSet.MAX_BET = 1000000;
                DiceGameSet._log.error("未設定最大下注金額(使用預設1000000)");
            }
            
            try {
                DiceGameSet.DICE_NPC_ID = Integer.parseInt(set[4]);
            } catch (Exception e) {
                DiceGameSet.DICE_NPC_ID = 91173;
                DiceGameSet._log.error("未設定骰寶NPC編號(使用預設91173)");
            }
            
            // 啟動骰寶遊戲系統
            startDiceGameSystem();
            
            // 生成骰寶NPC
            spawnDiceNPC();
            
            // 廣播系統啟動
            broadcastSystemStart();
            
        } catch (Exception e) {
            DiceGameSet._log.error("骰寶遊戲系統啟動失敗", e);
        }
    }
    
    /**
     * 啟動骰寶遊戲系統
     */
    private void startDiceGameSystem() {
        try {
            final DiceGameTime diceGameTimeController = new DiceGameTime();
            diceGameTimeController.start();
            DiceGameSet._log.info("骰寶遊戲系統啟動成功");
        } catch (Exception e) {
            DiceGameSet._log.error("啟動骰寶遊戲系統失敗", e);
        }
    }
    
    /**
     * 生成骰寶NPC
     */
    private void spawnDiceNPC() {
        try {
            // 這裡可以添加NPC生成邏輯
            // 由於需要具體的NPC生成機制，暫時只記錄日誌
            DiceGameSet._log.info("骰寶NPC生成位置：(" + DICE_NPC_X + ", " + DICE_NPC_Y + ", " + DICE_NPC_MAP + ")");
        } catch (Exception e) {
            DiceGameSet._log.error("生成骰寶NPC失敗", e);
        }
    }
    
    /**
     * 廣播系統啟動訊息
     */
    private void broadcastSystemStart() {
        try {
            final Iterator<com.lineage.server.model.Instance.L1PcInstance> iter = World.get().getAllPlayers().iterator();
            while (iter.hasNext()) {
                final com.lineage.server.model.Instance.L1PcInstance pc = iter.next();
                if (pc.isShowWorldChat()) {
                    pc.sendPackets(new S_ServerMessage("骰寶遊戲系統已啟動！"));
                }
            }
        } catch (Exception e) {
            DiceGameSet._log.error("廣播骰寶系統啟動訊息失敗", e);
        }
    }
    
    /**
     * 獲取遊戲配置
     */
    public static int getGameInterval() {
        return GAME_INTERVAL;
    }
    
    public static int getBettingTime() {
        return BETTING_TIME;
    }
    
    public static int getMinBet() {
        return MIN_BET;
    }
    
    public static int getMaxBet() {
        return MAX_BET;
    }
    
    public static int getDiceNpcId() {
        return DICE_NPC_ID;
    }
} 