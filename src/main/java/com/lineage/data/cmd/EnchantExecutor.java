package com.lineage.data.cmd;

import com.lineage.config.ConfigRate;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;

import java.util.Random;

public abstract class EnchantExecutor {

    public abstract void failureEnchant(L1PcInstance p0, L1ItemInstance p1);

    public abstract void successEnchant(L1PcInstance p0, L1ItemInstance p1, int p2);

    public int randomELevel(L1ItemInstance item, int bless) {
        int level = 0;
        switch (bless) {
            case 0:
            case 128: {
                if (item.getBless() >= 3) {
                    break;
                }
                Random random = new Random();
                int i = random.nextInt(100) + 1;
                if (item.getItem().getType2() == 1) {
                    if (item.getEnchantLevel() == 6) { //<PERSON> 武器機率過3 2 1
                        if (i < ConfigRate.ran15) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran16) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran17) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }
                    if (item.getEnchantLevel() == 7) { //<PERSON> 武器機率過3 2 1
                        if (i < ConfigRate.ran18) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran19) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }
                    if (item.getEnchantLevel() == 5) {
                        if (i < ConfigRate.ran3) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran2) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran1) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    } else {
                        if (item.getEnchantLevel() != -1) {
                            level = 1;
                            break;
                        }
                        if (i < ConfigRate.ran14) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran5) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran4) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }


                } else {
                    if (item.getItem().getType2() != 2) {
                        break;
                    }

                    if (item.getEnchantLevel() == 4) { //Kevin 防具機率過3 2 1
                        if (i < ConfigRate.ran20) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran21) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran22) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }

                    if (item.getEnchantLevel() == 6) { //Kevin 防具機率過3 2 1
                        if (i < ConfigRate.ran23) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran24) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran25) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }

                    if (item.getEnchantLevel() == 7) { //Kevin 防具機率過3 2 1
                        if (i < ConfigRate.ran26) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran27) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran28) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }

                    if (item.getEnchantLevel() == 3) {
                        if (i < ConfigRate.ran8) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran7) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran6) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    } else if (item.getEnchantLevel() == 5) {
                        if (i < ConfigRate.ran10) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran9) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    } else {
                        if (item.getEnchantLevel() != -1) {
                            level = 1;
                            break;
                        }
                        if (i < ConfigRate.ran13) {
                            level = 3;
                            break;
                        }
                        if (i < ConfigRate.ran12) {
                            level = 2;
                            break;
                        }
                        if (i < ConfigRate.ran11) {
                            level = 1;
                            break;
                        }
                        level = 1;
                        break;
                    }
                }
            }
            case 1:
            case 129: {
                if (item.getBless() < 3) {
                    level = 1;
                    break;
                }
                break;
            }
            case 2:
            case 130: {
                if (item.getBless() < 3) {
                    level = -1;
                    break;
                }
                break;
            }
            case 3:
            case 131: {
                if (item.getBless() == 3) {
                    level = 1;
                    break;
                }
                break;
            }
        }
        return level;
    }
}
