package com.lineage.data.npc.mob;

import com.lineage.data.quest.KnightLv50_1;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Character;
import org.apache.commons.logging.LogFactory;
import java.util.Random;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class K50_EarthSpiritMaster extends NpcExecutor {
	private static final Log _log;
	private static Random _random;

	static {
		_log = LogFactory.getLog(K50_EarthSpiritMaster.class);
		_random = new Random();
	}

	public static NpcExecutor get() {
		return new K50_EarthSpiritMaster();
	}

	@Override
	public int type() {
		return 8;
	}

	@Override
	public L1PcInstance death(final L1Character lastAttacker, final L1NpcInstance npc) {
		try {
			final L1PcInstance pc = CheckUtil.checkAtkPc(lastAttacker);
			if (pc != null) {
				if (pc.getQuest().isEnd(KnightLv50_1.QUEST.get_id())) {
					return pc;
				}
				if (pc.getQuest().isStart(KnightLv50_1.QUEST.get_id())) {
					switch (pc.getQuest().get_step(KnightLv50_1.QUEST.get_id())) {
					case 3: {
						K50_EarthSpiritMaster._random.nextInt(100);
						break;
					}
					}
				}
			}
			return pc;
		} catch (Exception e) {
			K50_EarthSpiritMaster._log.error(e.getLocalizedMessage(), e);
			return null;
		}
	}
}
