package com.lineage.data.npc.mob;

import com.lineage.data.cmd.CreateNewItem;
import com.lineage.data.quest.WizardLv30_1;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Character;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.NpcExecutor;

public class W30_BetrayerUndead extends NpcExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(W30_BetrayerUndead.class);
	}

	public static NpcExecutor get() {
		return new W30_BetrayerUndead();
	}

	@Override
	public int type() {
		return 8;
	}

	@Override
	public L1PcInstance death(final L1Character lastAttacker, final L1NpcInstance npc) {
		try {
			final L1PcInstance pc = CheckUtil.checkAtkPc(lastAttacker);
			if (pc != null) {
				if (pc.getQuest().isEnd(WizardLv30_1.QUEST.get_id())) {
					return pc;
				}
				if (pc.getQuest().isStart(WizardLv30_1.QUEST.get_id())) {
					if (pc.getInventory().checkItem(40579)) {
						return pc;
					}
					CreateNewItem.getQuestItem(pc, npc, 40579, 1L);
				}
			}
			return pc;
		} catch (Exception e) {
			W30_BetrayerUndead._log.error(e.getLocalizedMessage(), e);
			return null;
		}
	}
}
