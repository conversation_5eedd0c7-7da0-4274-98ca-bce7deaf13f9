package com.lineage.data.npc;

import com.lineage.server.serverpackets.S_BlueMessage;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.RandomArrayList;
import com.lineage.server.IdFactory;
import com.lineage.server.datatables.NpcTable;
import java.util.Iterator;
import com.lineage.server.model.L1Inventory;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.serverpackets.S_Weather;
import com.lineage.server.serverpackets.S_CharVisualUpdate;
import com.lineage.server.serverpackets.S_OwnCharPack;
import com.lineage.server.serverpackets.S_OtherCharPacks;
import com.lineage.server.serverpackets.S_MapID;
import com.lineage.server.world.World;
import com.lineage.server.model.L1Object;
import com.lineage.server.serverpackets.S_RemoveObject;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.config.ConfigOther;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.ArrayList;

public class TextTask {
	private final ArrayList<L1PcInstance> _members;
	private static TextTask _instance;
	public static final int STATUS_NONE = 0;
	public static final int STATUS_READY = 1;
	public static final int STATUS_PLAYING = 2;
	public static final int STATUS_CLEANING = 4;
	private static final int _minPlayer;
	private static final int _maxPlayer;
	private static final int _level;
	private static final int _bossId1;
	private static final int _bossId2;
	private static final int _bossId3;
	private static final int _bossId4;
	private static final int _bossId5;
	private static final int _bossId6;
	private static final int _bossId7;
	private static final int _bossId8;
	private static final int _bossId9;
	private static final int _bossId10;
	private static final int _adena;
	protected L1NpcInstance _targetNpc;
	private int _TextTaskStatus;

	static {
		_minPlayer = ConfigOther.TextMinPlayer;
		_maxPlayer = ConfigOther.TextMaxPlayer;
		_level = ConfigOther.TextLevel;
		_bossId1 = ConfigOther.Textnpa1;
		_bossId2 = ConfigOther.Textnpa2;
		_bossId3 = ConfigOther.Textnpa3;
		_bossId4 = ConfigOther.Textnpa4;
		_bossId5 = ConfigOther.Textnpa5;
		_bossId6 = ConfigOther.Textnpa6;
		_bossId7 = ConfigOther.Textnpa7;
		_bossId8 = ConfigOther.Textnpa8;
		_bossId9 = ConfigOther.Textnpa9;
		_bossId10 = ConfigOther.Textnpa10;
		_adena = ConfigOther.TextMoney;
	}

	public TextTask() {
		this._members = new ArrayList();
		this._targetNpc = null;
		this._TextTaskStatus = 0;
	}

	public static TextTask getInstance() {
		if (TextTask._instance == null) {
			TextTask._instance = new TextTask();
		}
		return TextTask._instance;
	}

	public String enterTextTask(final L1PcInstance pc) {
		if (pc.getLevel() < TextTask._level && !this.isMember(pc)) {
			pc.sendPackets(new S_SystemMessage("你的等級不足，無法挑戰Boss武道館。"));
			return "";
		}
		if (pc.getQuest().get_step(90558) == 1 && pc.getQuest().get_step(90559) == 0) {
			pc.sendPackets(new S_SystemMessage("您已經執行過上限1次。"));
			return "";
		}
		if (pc.getQuest().get_step(90558) == 1 && pc.getQuest().get_step(90559) == 2) {
			pc.sendPackets(new S_SystemMessage("您今日無法在進入武道館了。"));
			return "";
		}
		if (getInstance().getTextTaskStatus() == 4) {
			pc.sendPackets(new S_SystemMessage("目前Boss武道館的場地正在清潔中。"));
			return "";
		}
		if (getInstance().getTextTaskStatus() == 2) {
			pc.sendPackets(new S_ServerMessage(1182));
			return "";
		}
		if (this.getMembersCount() >= TextTask._maxPlayer && !this.isMember(pc)) {
			pc.sendPackets(new S_SystemMessage("Boss武道館人數已滿，下次請早。"));
			return "";
		}
		if (!pc.getInventory().checkItem(ConfigOther.tcheckitem, TextTask._adena)) {
			pc.sendPackets(new S_ServerMessage(189));
			return "";
		}
		if ((pc.getInventory().checkItem(40089, 1L) || pc.getInventory().checkItem(140089, 1L)) && !this.isMember(pc)) {
			pc.sendPackets(new S_SystemMessage("請把復活卷軸給刪除，不然不給進入場地。"));
			return "";
		}
		L1Teleport.teleport(pc, 32701, 32894, (short) 97, pc.getHeading(), true);
		this.addMember(pc);
		return "";
	}

	private void addMember(final L1PcInstance pc) {
		if (!this._members.contains(pc)) {
			this._members.add(pc);
			pc.getInventory().consumeItem(ConfigOther.tcheckitem, TextTask._adena);
		}
		if (this.getMembersCount() == ConfigOther.TextMinPlayer && this.getTextTaskStatus() == 0) {
			GeneralThreadPool.get().execute(new runTextTask());
			final L1PcInstance[] membersArray;
			final int length = (membersArray = this.getMembersArray()).length;
			int i = 0;
			while (i < length) {
				final L1PcInstance pc2 = membersArray[i];
				if (pc2.getMapId() == 97) {
					pc2.sendPackets(new S_SystemMessage("怪物強度-[簡單強度]。"));
				}
				++i;
			}
		}
	}

	private boolean checkPlayerCount() {
		if (this.getMembersCount() < TextTask._minPlayer) {
			this.setTextTaskStatus(4);
			this.sendMessage("人數不足 " + TextTask._minPlayer + " 人，所以強制關閉遊戲");
			final L1PcInstance[] membersArray;
			final int length = (membersArray = this.getMembersArray()).length;
			int i = 0;
			while (i < length) {
				final L1PcInstance pc = membersArray[i];
				pc.getInventory().storeItem(ConfigOther.tcheckitem, TextTask._adena);
				pc.sendPackets(new S_SystemMessage("退還金幣(" + TextTask._adena + ")。"));
				L1Teleport.teleport(pc, 33442, 32797, (short) 4, pc.getHeading(), true);
				++i;
			}
			this.clearMembers();
			return false;
		}
		return true;
	}

	private void endTextTask1() {
		this.setTextTaskStatus(4);
		this.sendMessage("Boss武道館挑戰失敗.請下次再來!!");
		final L1PcInstance[] membersArray;
		final int length = (membersArray = this.getMembersArray()).length;
		int i = 0;
		while (i < length) {
			final L1PcInstance pc = membersArray[i];
			if (pc.getMapId() == 97) {
				L1Teleport.teleport(pc, 33442, 32797, (short) 4, pc.getHeading(), true);
			}
			++i;
		}
		this.clearMembers();
		this.clearColosseum();
	}

	private void endTextTask() {
		this.setTextTaskStatus(4);
		this.sendMessage("Boss武道館時間到了.請下次再來挑戰!!");
		final L1PcInstance[] membersArray;
		final int length = (membersArray = this.getMembersArray()).length;
		int i = 0;
		while (i < length) {
			final L1PcInstance pc = membersArray[i];
			if (pc.getMapId() == 97) {
				L1Teleport.teleport(pc, 33442, 32797, (short) 4, pc.getHeading(), true);
			}
			if (pc.isDead()) {
				this.restartPlayer(pc, 32616, 32782, (short) 4);
			}
			++i;
		}
		this.clearMembers();
		this.clearColosseum();
	}

	public void restartPlayer(final L1PcInstance pc, final int locx, final int locy, final short mapid) {
		pc.removeAllKnownObjects();
		pc.broadcastPacketAll(new S_RemoveObject(pc));
		pc.setCurrentHp(pc.getLevel());
		pc.set_food(40);
		pc.setDead(false);
		pc.setStatus(0);
		World.get().moveVisibleObject(pc, mapid);
		pc.setX(locx);
		pc.setY(locy);
		pc.setMap(mapid);
		pc.sendPackets(new S_MapID(pc, pc.getMapId(), pc.getMap().isUnderwater()));
		pc.broadcastPacketAll(new S_OtherCharPacks(pc));
		pc.sendPackets(new S_OwnCharPack(pc));
		pc.sendPackets(new S_CharVisualUpdate(pc));
		pc.startHpRegeneration();
		pc.startMpRegeneration();
		pc.sendPackets(new S_Weather(World.get().getWeather()));
		pc.stopPcDeleteTimer();
		if (pc.getHellTime() > 0) {
			pc.beginHell(false);
		}
		pc.sendPackets(new S_SystemMessage("強制退出地圖,請自行登出重新進入遊戲即可!!"));
	}

	private void clearColosseum() {
		final Iterator<L1Object> iterator = World.get().getVisibleObjects(97).values().iterator();
		while (iterator.hasNext()) {
			final Object obj = iterator.next();
			if (obj instanceof L1MonsterInstance) {
				final L1MonsterInstance mob = (L1MonsterInstance) obj;
				if (mob.isDead()) {
					continue;
				}
				mob.setDead(true);
				mob.setStatus(8);
				mob.setCurrentHpDirect(0);
				mob.deleteMe();
			} else {
				if (!(obj instanceof L1Inventory)) {
					continue;
				}
				final L1Inventory inventory = (L1Inventory) obj;
				inventory.clearItems();
			}
		}
	}

	private void spawnBoss(final int npcid, final String msg1, final String msg2) {
		if (msg1.equalsIgnoreCase("9")) {
			this.sendMessage("第 " + msg1 + " 關 [" + this.getBossName(npcid) + "] 120秒後開始第 " + msg2 + " 關");
		} else if (msg1.equalsIgnoreCase("10")) {
			this.sendMessage("最後一關 [" + this.getBossName(npcid) + "] 請努力撐下去");
		} else {
			this.sendMessage("第 " + msg1 + " 關 [" + this.getBossName(npcid) + "] 60秒後開始第 " + msg2 + " 關");
		}
		this.spawn(npcid);
	}

	private void spawn(final int npcid) {
		try {
			final L1NpcInstance npc = NpcTable.get().newNpcInstance(npcid);
			npc.setId(IdFactory.get().nextId());
			npc.setMap((short) 97);
			npc.setX(32701 + RandomArrayList.getInt(5));
			npc.setY(32894 + RandomArrayList.getInt(5));
			Thread.sleep(1L);
			npc.setHomeX(npc.getX());
			npc.setHomeY(npc.getY());
			npc.setHeading(4);
			World.get().storeObject(npc);
			World.get().addVisibleObject(npc);
			npc.turnOnOffLight();
			npc.startChat(0);
		} catch (Exception ex) {
		}
	}

	private String getBossName(final int npcId) {
		String BossName = null;
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("select name from npc where npcid = ?");
			pstm.setInt(1, npcId);
			rs = pstm.executeQuery();
			if (rs.next()) {
				BossName = rs.getString("name");
			}
		} catch (SQLException ex) {
		} finally {
			SQLUtil.close(rs, pstm, con);
		}
		return BossName;
	}

	private void sendMessage(final String msg) {
		final L1PcInstance[] membersArray;
		final int length = (membersArray = this.getMembersArray()).length;
		int i = 0;
		while (i < length) {
			final L1PcInstance pc = membersArray[i];
			if (pc.getMapId() == 97) {
				pc.sendPackets(new S_BlueMessage(166, "\\f3" + msg));
			}
			++i;
		}
	}

	private void setTextTaskStatus(final int i) {
		this._TextTaskStatus = i;
	}

	private int getTextTaskStatus() {
		return this._TextTaskStatus;
	}

	private void clearMembers() {
		this._members.clear();
	}

	private boolean isMember(final L1PcInstance pc) {
		return this._members.contains(pc);
	}

	private L1PcInstance[] getMembersArray() {
		return this._members.toArray(new L1PcInstance[this._members.size()]);
	}

	private int getMembersCount() {
		return this._members.size();
	}

	private class runTextTask implements Runnable {
		@Override
		public void run() {
			try {
				TextTask.this.sendMessage("Boss武道館即將開始，請勿離開場地或登出。");
				Thread.sleep(10000L);
				TextTask.this.setTextTaskStatus(1);
				TextTask.this.sendMessage("60秒後開始遊戲");
				Thread.sleep(55000L);
				TextTask.this.sendMessage("倒數5秒");
				Thread.sleep(1000L);
				TextTask.this.sendMessage("4秒");
				Thread.sleep(1000L);
				TextTask.this.sendMessage("3秒");
				Thread.sleep(1000L);
				TextTask.this.sendMessage("2秒");
				Thread.sleep(1000L);
				TextTask.this.sendMessage("1秒");
				Thread.sleep(1000L);
				if (TextTask.this.checkPlayerCount()) {
					TextTask.this.sendMessage("Boss武道館怪物即將登場。願上帝保佑你！");
					TextTask.this.setTextTaskStatus(2);
					final L1PcInstance[] access$3;
					final int length = (access$3 = TextTask.this.getMembersArray()).length;
					int i = 0;
					while (i < length) {
						final L1PcInstance pc1 = access$3[i];
						if (pc1.getMapId() == 97) {
							pc1.getQuest().set_step(90558, 1);
							if (pc1.getQuest().get_step(90559) == 1) {
								pc1.getQuest().set_step(90559, 2);
							}
						}
						++i;
					}
					TextTask.this.spawnBoss(TextTask._bossId1, "1", "2");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId2, "2", "3");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId3, "3", "4");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId4, "4", "5");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId5, "5", "6");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId6, "6", "7");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId7, "7", "8");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId8, "8", "9");
					Thread.sleep(60000L);
					TextTask.this.spawnBoss(TextTask._bossId9, "9", "10");
					Thread.sleep(120000L);
					TextTask.this.spawnBoss(TextTask._bossId10, "10", null);
					Thread.sleep(180000L);
					TextTask.this.endTextTask();
				}
				Thread.sleep(5000L);
				TextTask.this.setTextTaskStatus(0);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
	}
}
