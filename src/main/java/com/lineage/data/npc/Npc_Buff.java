package com.lineage.data.npc;

import com.lineage.config.ConfigOther;
import com.lineage.data.executor.NpcExecutor;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1Skills;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;

import java.util.Iterator;

public class Npc_Buff extends NpcExecutor {
    private Npc_Buff() {
    }

    public static NpcExecutor get() {
        return new Npc_Buff();
    }

    @Override
    public int type() {
        return 3;
    }

    @Override
    public void talk(L1PcInstance pc, L1NpcInstance npc) {
        try {
            pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "y_npcbuff"));
        } catch (Exception ex) {
        }
    }

    @Override
    public void action(L1PcInstance pc, L1NpcInstance npc, String cmd, long amount) {
        if (cmd.equalsIgnoreCase("npcclanbuff")) {
            if (pc.hasSkillEffect(95413)) {
                return;
            }
            if (pc.getClanid() <= 0) { //Kevin 新增防呆
                pc.sendPackets(new S_ServerMessage("沒有加入血盟，有錢你也無法使用此功能"));
                return;
            }
            if (pc.getInventory().checkItem(ConfigOther.NeedItem, ConfigOther.NeedItemCount)) {
                pc.getInventory().consumeItem(ConfigOther.NeedItem, ConfigOther.NeedItemCount);
                pc.setSkillEffect(95413, 10000);
                int[] skills = ConfigOther.Give_skill;
                L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                L1PcInstance[] clanMembers = clan.getOnlineClanMember();
                int i = 0;
                while (i < skills.length) {
                    L1PcInstance[] array;
                    int length = (array = clanMembers).length;
                    int k = 0;
                    while (k < length) {
                        L1PcInstance clanMember1 = array[k];
                        L1Skills skill = SkillsTable.get().getTemplate(skills[i]);
                        L1SkillUse skillUse = new L1SkillUse();
                        skillUse.handleCommands(clanMember1, skills[i], clanMember1.getId(), clanMember1.getX(),
                                clanMember1.getY(), skill.getBuffDuration(), 4);
                        ++k;
                    }
                    ++i;
                }
                L1PcInstance[] onlineClanMember;
                int k = (onlineClanMember = clan.getOnlineClanMember()).length;
                int l = 0;
                while (l < k) {
                    L1PcInstance clanMembers2 = onlineClanMember[l];
                    clanMembers2.sendPackets(new S_SystemMessage(String.format(ConfigOther.Msg, pc.getName())));
                    ++l;
                }
            } else {
                pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg));
            }
        }
        if (cmd.equalsIgnoreCase("npcallbuff")) {
            if (pc.hasSkillEffect(95413)) {
                return;
            }
            pc.setSkillEffect(95413, 10000);
            if (pc.getInventory().checkItem(ConfigOther.NeedItem1, ConfigOther.NeedItemCount1)) {
                World.get().broadcastPacketToAll(new S_SystemMessage(String.format(ConfigOther.Msg1, pc.getName())));
                //World.get().broadcastPacketToAll( Kevin  太擋視線 拿掉
                //new S_BlueMessage(166, "\\f2" + String.format(ConfigOther.Msg1, pc.getName())));
                pc.getInventory().consumeItem(ConfigOther.NeedItem1, ConfigOther.NeedItemCount1);
                AllBuffRunnable allBuffRunnable = new AllBuffRunnable();
                GeneralThreadPool.get().execute(allBuffRunnable);
                try {
                    Thread.sleep(1000L);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            } else {
                pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg1));
            }
        }
        if (cmd.equalsIgnoreCase("pcbuff1")) {
            if (pc.hasSkillEffect(95413)) {
                return;
            }
            pc.setSkillEffect(95413, 10000);
            if (pc.getInventory().checkItem(ConfigOther.NeedItem2, ConfigOther.NeedItemCount2)) {
                pc.getInventory().consumeItem(ConfigOther.NeedItem2, ConfigOther.NeedItemCount2);
                pc.sendPackets(new S_ServerMessage(ConfigOther.Msg2));
                int[] skills = ConfigOther.Give_skill2;
                int j = 0;
                while (j < skills.length) {
                    L1Skills skill2 = SkillsTable.get().getTemplate(skills[j]);
                    L1SkillUse skillUse2 = new L1SkillUse();
                    skillUse2.handleCommands(pc, skills[j], pc.getId(), pc.getX(), pc.getY(), skill2.getBuffDuration(),
                            4);
                    ++j;
                }
            } else {
                pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg2));
            }
        }
    }

    private class AllBuffRunnable implements Runnable {
        @Override
        public void run() {
            try {
                Iterator<L1PcInstance> iterator = World.get().getAllPlayers().iterator();
                while (iterator.hasNext()) {
                    L1PcInstance tgpc = iterator.next();
                    startPc(tgpc);
                    Thread.sleep(1L);
                }
            } catch (Exception ex) {
            }
        }

        public void startPc(L1PcInstance target) {
            int[] allBuffSkill = ConfigOther.Give_skill1;
            int i = 0;
            while (i < allBuffSkill.length) {
                L1Skills skill = SkillsTable.get().getTemplate(allBuffSkill[i]);
                L1SkillUse skillUse = new L1SkillUse();
                skillUse.handleCommands(target, allBuffSkill[i], target.getId(), target.getX(), target.getY(),
                        skill.getBuffDuration(), 4);
                ++i;
            }
        }
    }
}
