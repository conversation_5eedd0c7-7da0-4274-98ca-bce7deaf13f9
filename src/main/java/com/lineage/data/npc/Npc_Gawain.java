package com.lineage.data.npc;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.NpcExecutor;

public class Npc_<PERSON><PERSON>in extends NpcExecutor {
	public static NpcExecutor get() {
		return new Npc_Gawain();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "gawain01"));
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String cmd, final long amount) {
		final int[] oldweapon = { 272, 273 };
		final int newweapon = 410174;
		boolean success = false;
		if (cmd.equalsIgnoreCase("A")) {
			int i = 0;
			while (i < oldweapon.length) {
				if (pc.getInventory().checkEnchantItem(oldweapon[i], 8, 1L)
						&& pc.getInventory().checkItem(40308, 5000000L)) {
					pc.getInventory().consumeEnchantItem(oldweapon[i], 8, 1L);
					pc.getInventory().consumeItem(40308, 5000000L);
					final L1ItemInstance item = ItemTable.get().createItem(newweapon);
					item.setEnchantLevel(7);
					item.setIdentified(true);
					pc.getInventory().storeItem(item);
					success = true;
					pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "gawain05"));
					break;
				}
				++i;
			}
		} else if (cmd.equalsIgnoreCase("B")) {
			int i = 0;
			while (i < oldweapon.length) {
				if (pc.getInventory().checkEnchantItem(oldweapon[i], 9, 1L)
						&& pc.getInventory().checkItem(40308, 10000000L)) {
					pc.getInventory().consumeEnchantItem(oldweapon[i], 9, 1L);
					pc.getInventory().consumeItem(40308, 10000000L);
					final L1ItemInstance item = ItemTable.get().createItem(newweapon);
					item.setEnchantLevel(8);
					item.setIdentified(true);
					pc.getInventory().storeItem(item);
					success = true;
					pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "gawain05"));
					break;
				}
				++i;
			}
		}
		if (!success) {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "gawain04"));
		}
	}
}
