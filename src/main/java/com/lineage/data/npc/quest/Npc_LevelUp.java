package com.lineage.data.npc.quest;

import com.lineage.config.ConfigAlt;
import com.lineage.data.executor.NpcExecutor;
import com.lineage.server.clientpackets.C_CreateChar;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.skill.L1SkillMode;
import com.lineage.server.serverpackets.*;
import com.lineage.server.utils.CalcInitHpMp;
import com.lineage.server.utils.CalcStat;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class Npc_LevelUp extends NpcExecutor {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(Npc_LevelUp.class);
    }

    public static NpcExecutor get() {
        return new Npc_LevelUp();
    }

    public static void setHpMp(L1PcInstance pc) {
        int i = 0;
        while (i < pc.getLevel()) {
            short randomHp = CalcStat.calcStatHp(pc.getType(), pc.getBaseMaxHp(), pc.getBaseCon(),
                    pc.getOriginalHpup());
            short randomMp = CalcStat.calcStatMp(pc.getType(), pc.getBaseMaxMp(), pc.getBaseWis(),
                    pc.getOriginalMpup());
            pc.addBaseMaxHp(randomHp);
            pc.addBaseMaxMp(randomMp);
            ++i;
        }
    }

    public static String[] showInfoX(L1PcInstance pc, int mode) {
        int xmode = pc.get_otherList().get_uplevelList(13).intValue();
        int[] max = new int[6];

        switch (xmode) {
            case 0: {
                max = new int[]{ConfigAlt.POWER, ConfigAlt.POWER, ConfigAlt.POWER, ConfigAlt.POWER, ConfigAlt.POWER,
                        ConfigAlt.POWER};
                break;
            }
            case 1: {
                max = new int[]{ConfigAlt.POWERMEDICINE, ConfigAlt.POWERMEDICINE, ConfigAlt.POWERMEDICINE,
                        ConfigAlt.POWERMEDICINE, ConfigAlt.POWERMEDICINE, ConfigAlt.POWERMEDICINE};
                break;
            }
            case 2: {
                int type = pc.getType();
                switch (type) {
                    case 0: {
                        max = new int[]{20, 18, 18, 18, 18, 18};
                        break;
                    }
                    case 1: {
                        max = new int[]{20, 16, 18, 13, 12, 16};
                        break;
                    }
                    case 2: {
                        max = new int[]{18, 18, 18, 18, 18, 16};
                        break;
                    }
                    case 3: {
                        max = new int[]{20, 14, 18, 18, 18, 18};
                        break;
                    }
                    case 4: {
                        max = new int[]{18, 18, 18, 18, 18, 18};
                        break;
                    }
                    case 5: {
                        max = new int[]{19, 16, 18, 17, 17, 14};
                        break;
                    }
                    case 6: {
                        max = new int[]{19, 16, 18, 18, 18, 18};
                        break;
                    }
                }
            }

        }
        int elixirStats = pc.get_otherList().get_uplevelList(0).intValue();
        int originalStr = pc.get_otherList().get_uplevelList(1).intValue();
        int originalDex = pc.get_otherList().get_uplevelList(2).intValue();
        int originalCon = pc.get_otherList().get_uplevelList(3).intValue();
        int originalWis = pc.get_otherList().get_uplevelList(4).intValue();
        int originalInt = pc.get_otherList().get_uplevelList(5).intValue();
        int originalCha = pc.get_otherList().get_uplevelList(6).intValue();
        int addStr = pc.get_otherList().get_uplevelList(7).intValue();
        int addDex = pc.get_otherList().get_uplevelList(8).intValue();
        int addCon = pc.get_otherList().get_uplevelList(9).intValue();
        int addWis = pc.get_otherList().get_uplevelList(10).intValue();
        int addInt = pc.get_otherList().get_uplevelList(11).intValue();
        int addCha = pc.get_otherList().get_uplevelList(12).intValue();
        switch (mode) {
            case 1: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addStr > max[0] - originalStr || addStr < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(7, addStr);
                break;
            }
            case 2: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addStr > max[0] - originalStr || addStr < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(7, addStr);
                break;
            }
            case 3: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addDex > max[1] - originalDex || addDex < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(8, addDex);
                break;
            }
            case 4: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addDex > max[1] - originalDex || addDex < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(8, addDex);
                break;
            }
            case 5: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addCon > max[2] - originalCon || addCon < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(9, addCon);
                break;
            }
            case 6: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addCon > max[2] - originalCon || addCon < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(9, addCon);
                break;
            }
            case 7: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addWis > max[3] - originalWis || addWis < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(10, addWis);
                break;
            }
            case 8: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addWis > max[3] - originalWis || addWis < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(10, addWis);
                break;
            }
            case 9: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addInt > max[4] - originalInt || addInt < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(11, addInt);
                break;
            }
            case 10: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addInt > max[4] - originalInt || addInt < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(11, addInt);
                break;
            }
            case 11: {
                if (--elixirStats < 0) {
                    return null;
                }
                if (++addCha > max[5] - originalCha || addCha < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(12, addCha);
                break;
            }
            case 12: {
                if (++elixirStats < 0) {
                    return null;
                }
                if (--addCha > max[5] - originalCha || addCha < 0) {
                    return null;
                }
                pc.get_otherList().add_levelList(12, addCha);
                break;
            }
        }
        pc.get_otherList().add_levelList(0, elixirStats);
        return info(pc);
    }

    public static String[] showInfo(L1PcInstance pc, int mode) {
        int elixirStats = 0;
        int originalStr = 0;
        int originalDex = 0;
        int originalCon = 0;
        int originalWis = 0;
        int originalInt = 0;
        int originalCha = 0;
        int type = pc.getType();
        originalStr = C_CreateChar.ORIGINAL_STR[type];
        originalDex = C_CreateChar.ORIGINAL_DEX[type];
        originalCon = C_CreateChar.ORIGINAL_CON[type];
        originalWis = C_CreateChar.ORIGINAL_WIS[type];
        originalInt = C_CreateChar.ORIGINAL_INT[type];
        originalCha = C_CreateChar.ORIGINAL_CHA[type];
        switch (mode) {
            case 0: {
                if (pc.getBonusStats() > 0) {
                    elixirStats += pc.getBonusStats();
                }
                pc.get_otherList().add_levelList(13, 0);
                break;
            }
            case 1: {
                if (pc.getElixirStats() > 0) {
                    elixirStats += pc.getElixirStats();
                }
                originalStr = pc.get_otherList().get_uplevelList(1).intValue();
                originalDex = pc.get_otherList().get_uplevelList(2).intValue();
                originalCon = pc.get_otherList().get_uplevelList(3).intValue();
                originalWis = pc.get_otherList().get_uplevelList(4).intValue();
                originalInt = pc.get_otherList().get_uplevelList(5).intValue();
                originalCha = pc.get_otherList().get_uplevelList(6).intValue();
                pc.get_otherList().add_levelList(13, 1);
                break;
            }
            case 2: {
                elixirStats = C_CreateChar.ORIGINAL_AMOUNT[type];
                pc.get_otherList().add_levelList(13, 2);
                break;
            }
        }
        pc.get_otherList().add_levelList(0, elixirStats);
        switch (mode) {
            case 0: {
                int addStrS = pc.get_otherList().get_uplevelList(7).intValue();
                int addDexS = pc.get_otherList().get_uplevelList(8).intValue();
                int addConS = pc.get_otherList().get_uplevelList(9).intValue();
                int addWisS = pc.get_otherList().get_uplevelList(10).intValue();
                int addIntS = pc.get_otherList().get_uplevelList(11).intValue();
                int addChaS = pc.get_otherList().get_uplevelList(12).intValue();
                pc.get_otherList().add_levelList(1, originalStr + addStrS);
                pc.get_otherList().add_levelList(2, originalDex + addDexS);
                pc.get_otherList().add_levelList(3, originalCon + addConS);
                pc.get_otherList().add_levelList(4, originalWis + addWisS);
                pc.get_otherList().add_levelList(5, originalInt + addIntS);
                pc.get_otherList().add_levelList(6, originalCha + addChaS);
                pc.get_otherList().set_newPcOriginal(new int[]{originalStr + addStrS, originalDex + addDexS,
                        originalCon + addConS, originalWis + addWisS, originalInt + addIntS, originalCha + addChaS});
                break;
            }
            case 1: {
                int addStrR = pc.get_otherList().get_uplevelList(7).intValue();
                int addDexR = pc.get_otherList().get_uplevelList(8).intValue();
                int addConR = pc.get_otherList().get_uplevelList(9).intValue();
                int addWisR = pc.get_otherList().get_uplevelList(10).intValue();
                int addIntR = pc.get_otherList().get_uplevelList(11).intValue();
                int addChaR = pc.get_otherList().get_uplevelList(12).intValue();
                pc.get_otherList().add_levelList(1, originalStr + addStrR);
                pc.get_otherList().add_levelList(2, originalDex + addDexR);
                pc.get_otherList().add_levelList(3, originalCon + addConR);
                pc.get_otherList().add_levelList(4, originalWis + addWisR);
                pc.get_otherList().add_levelList(5, originalInt + addIntR);
                pc.get_otherList().add_levelList(6, originalCha + addChaR);
                break;
            }
            case 2: {
                pc.get_otherList().add_levelList(1, originalStr);
                pc.get_otherList().add_levelList(2, originalDex);
                pc.get_otherList().add_levelList(3, originalCon);
                pc.get_otherList().add_levelList(4, originalWis);
                pc.get_otherList().add_levelList(5, originalInt);
                pc.get_otherList().add_levelList(6, originalCha);
                break;
            }
        }
        final int addStr = 0;
        pc.get_otherList().add_levelList(7, addStr);
        final int addDex = 0;
        pc.get_otherList().add_levelList(8, addDex);
        final int addCon = 0;
        pc.get_otherList().add_levelList(9, addCon);
        final int addWis = 0;
        pc.get_otherList().add_levelList(10, addWis);
        final int addInt = 0;
        pc.get_otherList().add_levelList(11, addInt);
        final int addCha = 0;
        pc.get_otherList().add_levelList(12, addCha);
        return info(pc);
    }

    private static String[] info(L1PcInstance pc) {
        String[] info = null;
        int p1 = pc.get_otherList().get_uplevelList(0).intValue();
        int p2 = pc.get_otherList().get_uplevelList(1).intValue();
        int p3 = pc.get_otherList().get_uplevelList(7).intValue();
        int p4 = pc.get_otherList().get_uplevelList(2).intValue();
        int p5 = pc.get_otherList().get_uplevelList(8).intValue();
        int p6 = pc.get_otherList().get_uplevelList(3).intValue();
        int p7 = pc.get_otherList().get_uplevelList(9).intValue();
        int p8 = pc.get_otherList().get_uplevelList(4).intValue();
        int p9 = pc.get_otherList().get_uplevelList(10).intValue();
        int p10 = pc.get_otherList().get_uplevelList(5).intValue();
        int p11 = pc.get_otherList().get_uplevelList(11).intValue();
        int p12 = pc.get_otherList().get_uplevelList(6).intValue();
        int p13 = pc.get_otherList().get_uplevelList(12).intValue();
        int xmode = pc.get_otherList().get_uplevelList(13).intValue();
        if (xmode == 2) {
            int type = pc.getType();
            int elixirStats = C_CreateChar.ORIGINAL_AMOUNT[type];
            String nameid = "";
            switch (type) {
                case 0: {
                    nameid = "$1127";
                    break;
                }
                case 1: {
                    nameid = "$1128";
                    break;
                }
                case 2: {
                    nameid = "$1129";
                    break;
                }
                case 3: {
                    nameid = "$1130";
                    break;
                }
                case 4: {
                    nameid = "$2503";
                    break;
                }
                case 5: {
                    nameid = "$5889";
                    break;
                }
                case 6: {
                    nameid = "$5890";
                    break;
                }
            }
            String[] array = new String[15];
            array[0] = nameid;
            array[1] = String.valueOf(elixirStats);
            array[2] = String.valueOf(p1);
            array[3] = String.valueOf((p2 < 10) ? ("0" + p2) : Integer.valueOf(p2));
            array[4] = String.valueOf((p3 < 10) ? ("0" + p3) : Integer.valueOf(p3));
            array[5] = String.valueOf((p4 < 10) ? ("0" + p4) : Integer.valueOf(p4));
            array[6] = String.valueOf((p5 < 10) ? ("0" + p5) : Integer.valueOf(p5));
            array[7] = String.valueOf((p6 < 10) ? ("0" + p6) : Integer.valueOf(p6));
            array[8] = String.valueOf((p7 < 10) ? ("0" + p7) : Integer.valueOf(p7));
            array[9] = String.valueOf((p8 < 10) ? ("0" + p8) : Integer.valueOf(p8));
            array[10] = String.valueOf((p9 < 10) ? ("0" + p9) : Integer.valueOf(p9));
            array[11] = String.valueOf((p10 < 10) ? ("0" + p10) : Integer.valueOf(p10));
            array[12] = String.valueOf((p11 < 10) ? ("0" + p11) : Integer.valueOf(p11));
            array[13] = String.valueOf((p12 < 10) ? ("0" + p12) : Integer.valueOf(p12));
            array[14] = String.valueOf((p13 < 10) ? ("0" + p13) : Integer.valueOf(p13));
            info = array;
        } else {
            String[] array2 = new String[13];
            array2[0] = String.valueOf(p1);
            array2[1] = String.valueOf((p2 < 10) ? ("0" + p2) : Integer.valueOf(p2));
            array2[2] = String.valueOf((p3 < 10) ? ("0" + p3) : Integer.valueOf(p3));
            array2[3] = String.valueOf((p4 < 10) ? ("0" + p4) : Integer.valueOf(p4));
            array2[4] = String.valueOf((p5 < 10) ? ("0" + p5) : Integer.valueOf(p5));
            array2[5] = String.valueOf((p6 < 10) ? ("0" + p6) : Integer.valueOf(p6));
            array2[6] = String.valueOf((p7 < 10) ? ("0" + p7) : Integer.valueOf(p7));
            array2[7] = String.valueOf((p8 < 10) ? ("0" + p8) : Integer.valueOf(p8));
            array2[8] = String.valueOf((p9 < 10) ? ("0" + p9) : Integer.valueOf(p9));
            array2[9] = String.valueOf((p10 < 10) ? ("0" + p10) : Integer.valueOf(p10));
            array2[10] = String.valueOf((p11 < 10) ? ("0" + p11) : Integer.valueOf(p11));
            array2[11] = String.valueOf((p12 < 10) ? ("0" + p12) : Integer.valueOf(p12));
            array2[12] = String.valueOf((p13 < 10) ? ("0" + p13) : Integer.valueOf(p13));
            info = array2;
        }
        return info;
    }

    public static void stopSkill(L1PcInstance pc) {
        pc.getInventory().takeoffEquip(945);
        int skillNum = 1;
        while (skillNum <= 220) {
            if (!L1SkillMode.get().isNotCancelable(skillNum) || pc.isDead()) {
                pc.removeSkillEffect(skillNum);
            }
            ++skillNum;
        }
        pc.curePoison();
        pc.cureParalaysis();
        skillNum = STATUS_BRAVE3;
        while (skillNum <= 1026) {
            pc.removeSkillEffect(skillNum);
            ++skillNum;
        }
        skillNum = 3000;
        while (skillNum <= 3047) {
            if (!L1SkillMode.get().isNotCancelable(skillNum)) {
                pc.removeSkillEffect(skillNum);
            }
            ++skillNum;
        }
        if (pc.getHasteItemEquipped() > 0) {
            pc.setMoveSpeed(0);
            pc.sendPacketsAll(new S_SkillHaste(pc.getId(), 0, 0));
        }
        pc.sendPacketsAll(new S_CharVisualUpdate(pc));
    }

    @Override
    public int type() {
        return 3;
    }

    @Override
    public void talk(L1PcInstance pc, L1NpcInstance npc) {
        pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "yuleX1_1", null));
    }

    @Override
    public void action(L1PcInstance pc, L1NpcInstance npc, String cmd, long amount) {
        String[] info = null;
        if (cmd.equalsIgnoreCase("c")) {
            L1ItemInstance item = pc.getInventory().checkItemX(49142, 1L);
            if (item != null) {
                pc.get_otherList().clear_uplevelList();
                info = showInfo(pc, 2);
            } else {
                pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "ySrc_E", info));
            }
        } else if (cmd.equalsIgnoreCase("s1")) {
            info = showInfoX(pc, 1);
        } else if (cmd.equalsIgnoreCase("s2")) {
            info = showInfoX(pc, 2);
        } else if (cmd.equalsIgnoreCase("d1")) {
            info = showInfoX(pc, 3);
        } else if (cmd.equalsIgnoreCase("d2")) {
            info = showInfoX(pc, 4);
        } else if (cmd.equalsIgnoreCase("c1")) {
            info = showInfoX(pc, 5);
        } else if (cmd.equalsIgnoreCase("c2")) {
            info = showInfoX(pc, 6);
        } else if (cmd.equalsIgnoreCase("w1")) {
            info = showInfoX(pc, 7);
        } else if (cmd.equalsIgnoreCase("w2")) {
            info = showInfoX(pc, 8);
        } else if (cmd.equalsIgnoreCase("i1")) {
            info = showInfoX(pc, 9);
        } else if (cmd.equalsIgnoreCase("i2")) {
            info = showInfoX(pc, 10);
        } else if (cmd.equalsIgnoreCase("h1")) {
            info = showInfoX(pc, 11);
        } else if (cmd.equalsIgnoreCase("h2")) {
            info = showInfoX(pc, 12);
        } else if (cmd.equalsIgnoreCase("x")) {
            L1ItemInstance item = pc.getInventory().checkItemX(49142, 1L);
            if (item != null) {
                int elixirStats = pc.get_otherList().get_uplevelList(0).intValue();
                if (elixirStats > 0) {
                    pc.sendPackets(
                            new S_NPCTalkReturn(npc.getId(), "yuleX4", new String[]{String.valueOf(elixirStats)}));
                    return;
                }
                stopSkill(pc);
                info = showInfo(pc, 0);
            } else {
                pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "ySrc_E", info));
            }
        } else if (cmd.equalsIgnoreCase("b")) {
            L1ItemInstance item = pc.getInventory().checkItemX(49142, 1L);
            if (item != null) {
                int elixirStats = pc.get_otherList().get_uplevelList(0).intValue();
                if (elixirStats > 0) {
                    pc.sendPackets(
                            new S_NPCTalkReturn(npc.getId(), "yuleX4", new String[]{String.valueOf(elixirStats)}));
                    return;
                }
                stopSkill(pc);
                info = showInfo(pc, 1);
            } else {
                pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "ySrc_E", info));
            }
        } else if (cmd.equalsIgnoreCase("d")) {
            L1ItemInstance item = pc.getInventory().checkItemX(49142, 1L);
            if (item != null) {
                int elixirStats = pc.get_otherList().get_uplevelList(0).intValue();
                if (elixirStats > 0) {
                    pc.sendPackets(
                            new S_NPCTalkReturn(npc.getId(), "yuleX4", new String[]{String.valueOf(elixirStats)}));
                    return;
                }
                stopSkill(pc);
                pc.getInventory().removeItem(item, 1L);
                int hp = CalcInitHpMp.calcInitHp(pc);
                int mp = CalcInitHpMp.calcInitMp(pc);
                int baseStr = pc.getBaseStr();
                int baseDex = pc.getBaseDex();
                int baseCon = pc.getBaseCon();
                int baseWis = pc.getBaseWis();
                int baseInt = pc.getBaseInt();
                int baseCha = pc.getBaseCha();
                pc.addBaseStr(-baseStr);
                pc.addBaseDex(-baseDex);
                pc.addBaseCon(-baseCon);
                pc.addBaseWis(-baseWis);
                pc.addBaseInt(-baseInt);
                pc.addBaseCha(-baseCha);
                int originalStr = pc.get_otherList().get_uplevelList(1).intValue();
                int originalDex = pc.get_otherList().get_uplevelList(2).intValue();
                int originalCon = pc.get_otherList().get_uplevelList(3).intValue();
                int originalWis = pc.get_otherList().get_uplevelList(4).intValue();
                int originalInt = pc.get_otherList().get_uplevelList(5).intValue();
                int originalCha = pc.get_otherList().get_uplevelList(6).intValue();
                int addStr = pc.get_otherList().get_uplevelList(7).intValue();
                int addDex = pc.get_otherList().get_uplevelList(8).intValue();
                int addCon = pc.get_otherList().get_uplevelList(9).intValue();
                int addWis = pc.get_otherList().get_uplevelList(10).intValue();
                int addInt = pc.get_otherList().get_uplevelList(11).intValue();
                int addCha = pc.get_otherList().get_uplevelList(12).intValue();
                pc.addBaseStr((byte) (addStr + originalStr - 1));
                pc.addBaseDex((byte) (addDex + originalDex - 1));
                pc.addBaseCon((byte) (addCon + originalCon - 1));
                pc.addBaseWis((byte) (addWis + originalWis - 1));
                pc.addBaseInt((byte) (addInt + originalInt - 1));
                pc.addBaseCha((byte) (addCha + originalCha - 1));
                pc.setOriginalStr(pc.get_otherList().get_newPcOriginal()[0]);
                pc.setOriginalDex(pc.get_otherList().get_newPcOriginal()[1]);
                pc.setOriginalCon(pc.get_otherList().get_newPcOriginal()[2]);
                pc.setOriginalWis(pc.get_otherList().get_newPcOriginal()[3]);
                pc.setOriginalInt(pc.get_otherList().get_newPcOriginal()[4]);
                pc.setOriginalCha(pc.get_otherList().get_newPcOriginal()[5]);
                pc.addMr(0 - pc.getMr());
                pc.addDmgup(0 - pc.getDmgup());
                pc.addHitup(0 - pc.getHitup());
                pc.addBaseMaxHp((short) (hp - pc.getBaseMaxHp()));
                pc.addBaseMaxMp((short) (mp - pc.getBaseMaxMp()));
                if (pc.getOriginalAc() > 0) {
                    pc.addAc(pc.getOriginalAc());
                }
                if (pc.getOriginalMr() > 0) {
                    pc.addMr(0 - pc.getOriginalMr());
                }
                pc.refresh();
                setHpMp(pc);
                pc.setCurrentHp(pc.getMaxHp());
                pc.setCurrentMp(pc.getMaxMp());
                try {
                    CharacterTable.saveCharStatus(pc);
                    pc.sendPackets(new S_OwnCharStatus2(pc));
                    pc.sendPackets(new S_OwnCharAttrDef(pc));
                    pc.sendPackets(new S_OwnCharStatus(pc));
                    pc.sendPackets(new S_SPMR(pc));
                    pc.save();
                } catch (Exception e) {
                    Npc_LevelUp._log.error(e.getLocalizedMessage(), e);
                }
                pc.sendPacketsX8(new S_SkillSound(pc.getId(), 7625));
            } else {
                pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "ySrc_E", info));
            }
            pc.sendPackets(new S_CloseList(pc.getId()));
            pc.get_otherList().clear_uplevelList();
        }
        if (info != null) {
            int xmode = pc.get_otherList().get_uplevelList(13).intValue();
            switch (xmode) {
                case 0: {
                    pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "yuleX2", info));
                    break;
                }
                case 1: {
                    pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "yuleX2_1", info));
                    break;
                }
                case 2: {
                    pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "ySrc_1", info));
                    break;
                }
            }
        }
    }
}
