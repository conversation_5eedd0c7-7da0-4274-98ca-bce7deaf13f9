package com.lineage.data.npc;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.data.executor.NpcExecutor;

public class Npc_Nerva extends NpcExecutor {
	public static NpcExecutor get() {
		return new Npc_Nerva();
	}

	@Override
	public int type() {
		return 3;
	}

	@Override
	public void talk(final L1PcInstance pc, final L1NpcInstance npc) {
		if (pc.getLevel() >= 70) {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva2"));
		} else {
			pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva1"));
		}
	}

	@Override
	public void action(final L1PcInstance pc, final L1NpcInstance npc, final String cmd, final long amount) {
		if (cmd.equalsIgnoreCase("A")) {
			if (pc.getInventory().checkItem(49477, 1L) && pc.getInventory().checkItem(40087, 70L)
					&& pc.getInventory().checkItem(49928, 1L)) {
				pc.getInventory().consumeItem(49477, 1L);
				pc.getInventory().consumeItem(40087, 70L);
				pc.getInventory().consumeItem(49928, 1L);
				final L1ItemInstance item = pc.getInventory().storeItem(49871, 1L);
				pc.sendPackets(new S_ServerMessage(143, npc.getNpcTemplate().get_name(), item.getItem().getName()));
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva3"));
			} else {
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva4"));
			}
		} else if (cmd.equalsIgnoreCase("B")) {
			if (pc.getInventory().checkItem(49477, 1L) && pc.getInventory().checkItem(40087, 70L)
					&& pc.getInventory().checkItem(49928, 1L)) {
				pc.getInventory().consumeItem(49477, 1L);
				pc.getInventory().consumeItem(40087, 70L);
				pc.getInventory().consumeItem(49928, 1L);
				final L1ItemInstance item = pc.getInventory().storeItem(49872, 1L);
				pc.sendPackets(new S_ServerMessage(143, npc.getNpcTemplate().get_name(), item.getItem().getName()));
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva3"));
			} else {
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva4"));
			}
		} else if (cmd.equalsIgnoreCase("C")) {
			if (pc.getInventory().checkItem(49477, 1L) && pc.getInventory().checkItem(40087, 70L)
					&& pc.getInventory().checkItem(49928, 1L)) {
				pc.getInventory().consumeItem(49477, 1L);
				pc.getInventory().consumeItem(40087, 70L);
				pc.getInventory().consumeItem(49928, 1L);
				final L1ItemInstance item = pc.getInventory().storeItem(49873, 1L);
				pc.sendPackets(new S_ServerMessage(143, npc.getNpcTemplate().get_name(), item.getItem().getName()));
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva3"));
			} else {
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva4"));
			}
		} else if (cmd.equalsIgnoreCase("D")) {
			if (pc.getInventory().checkItem(49477, 1L) && pc.getInventory().checkItem(40087, 70L)
					&& pc.getInventory().checkItem(49928, 1L)) {
				pc.getInventory().consumeItem(49477, 1L);
				pc.getInventory().consumeItem(40087, 70L);
				pc.getInventory().consumeItem(49928, 1L);
				final L1ItemInstance item = pc.getInventory().storeItem(49874, 1L);
				pc.sendPackets(new S_ServerMessage(143, npc.getNpcTemplate().get_name(), item.getItem().getName()));
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva3"));
			} else {
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva4"));
			}
		} else if (cmd.equalsIgnoreCase("E")) {
			if (pc.getInventory().checkItem(49477, 1L) && pc.getInventory().checkItem(40087, 70L)
					&& pc.getInventory().checkItem(49928, 1L)) {
				pc.getInventory().consumeItem(49477, 1L);
				pc.getInventory().consumeItem(40087, 70L);
				pc.getInventory().consumeItem(49928, 1L);
				final L1ItemInstance item = pc.getInventory().storeItem(49875, 1L);
				pc.sendPackets(new S_ServerMessage(143, npc.getNpcTemplate().get_name(), item.getItem().getName()));
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva3"));
			} else {
				pc.sendPackets(new S_NPCTalkReturn(npc.getId(), "nerva4"));
			}
		}
	}
}
