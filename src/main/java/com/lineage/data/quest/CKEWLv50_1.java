package com.lineage.data.quest;

import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.L1Quest;
import org.apache.commons.logging.Log;
import com.lineage.data.executor.QuestExecutor;

public class CKEWLv50_1 extends QuestExecutor {
	private static final Log _log;
	public static L1Quest QUEST;
	public static final int MAPID = 2000;
	public static final int USER = 15;
	private static final String _html = "y_q_ckew50_1";

	static {
		_log = LogFactory.getLog(CKEWLv50_1.class);
	}

	private CKEWLv50_1() {
	}

	public static QuestExecutor get() {
		return new CKEWLv50_1();
	}

	@Override
	public void execute(final L1Quest quest) {
		try {
			CKEWLv50_1.QUEST = quest;
		} catch (Exception e) {
			CKEWLv50_1._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void startQuest(final L1PcInstance pc) {
		try {
			if (CKEWLv50_1.QUEST.check(pc)) {
				if (pc.getLevel() >= CKEWLv50_1.QUEST.get_questlevel()) {
					if (pc.getQuest().get_step(CKEWLv50_1.QUEST.get_id()) != 1) {
						pc.getQuest().set_step(CKEWLv50_1.QUEST.get_id(), 1);
					}
				} else {
					pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_q_not1"));
				}
			} else {
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_q_not2"));
			}
		} catch (Exception e) {
			CKEWLv50_1._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void endQuest(final L1PcInstance pc) {
		try {
			if (!pc.getQuest().isEnd(CKEWLv50_1.QUEST.get_id())) {
				pc.getQuest().set_end(CKEWLv50_1.QUEST.get_id());
				final String questName = CKEWLv50_1.QUEST.get_questname();
				pc.sendPackets(new S_ServerMessage("\\fT" + questName + "任務完成！"));
				if (CKEWLv50_1.QUEST.is_del()) {
					pc.sendPackets(new S_ServerMessage("\\fT請注意這個任務可以重複執行，需要重複任務，請在任務管理員中執行解除。"));
				} else {
					new S_ServerMessage("\\fR請注意這個任務不能重複執行，無法在任務管理員中解除執行。");
				}
			}
		} catch (Exception e) {
			CKEWLv50_1._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void showQuest(final L1PcInstance pc) {
		try {
			if ("y_q_ckew50_1" != null) {
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_q_ckew50_1"));
			}
		} catch (Exception e) {
			CKEWLv50_1._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void stopQuest(final L1PcInstance pc) {
	}
}
