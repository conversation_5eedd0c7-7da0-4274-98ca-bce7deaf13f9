package com.lineage.server.beans.errors;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class LogicError extends RuntimeException {
    private static final Log _log = LogFactory.getLog(LogicError.class);
    private static final long serialVersionUID = 1L;

    private String msg;

    public LogicError() {

    }

    public LogicError(String msg) {
        _log.error(msg);
        setMsg(msg);
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
