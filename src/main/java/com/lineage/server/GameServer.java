package com.lineage.server;

import com.add.*;
import com.add.system.*;
import com.eric.RandomMobTable;
import com.eric.StartCheckWarTime;
import com.lineage.DatabaseFactory;
import com.lineage.config.ConfigAlt;
import com.lineage.config.ConfigClan;
import com.lineage.config.ConfigOther;
import com.lineage.config.ConfigQuest;
import com.lineage.data.event.ClanSkillDBSet;
import com.lineage.data.event.NowTimeSpawn;
import com.lineage.echo.PacketHandler;
import com.lineage.list.BadNamesList;
import com.lineage.list.L1Karma_Pc;
import com.lineage.server.Instance.MapLimitItemInstance;
import com.lineage.server.Instance.MapLimitSettingInstance;
import com.lineage.server.Instance.character.CharacterPunishInstance;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.*;
import com.lineage.server.datatables.sql.CharacterQuestTable;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.datatables.sql.L1MonTable;
import com.lineage.server.datatables.sql.MailTable;
import com.lineage.server.model.Instance.L1DoorInstance;
import com.lineage.server.model.Instance.L1ItemPower;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1AttackList;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.model.L1ClanMatching;
import com.lineage.server.model.gametime.L1GameTimeClock;
import com.lineage.server.model.map.L1WorldMap;
import com.lineage.server.model.skill.L1SkillMode;
import com.lineage.server.templates.L1MapTile;
import com.lineage.server.templates.L1PcOther;
import com.lineage.server.thread.DeAiThreadPool;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.thread.NpcAiThreadPool;
import com.lineage.server.thread.PcOtherThreadPool;
import com.lineage.server.timecontroller.*;
import com.lineage.server.timecontroller.event.ranking.RankingHeroTimer;
import com.lineage.server.timecontroller.event.ranking.RankingHeroTimerlv;
import com.lineage.server.utils.DBClearAllUtil.DBClearQuesemap;
import com.lineage.server.utils.PerformanceTimer;
import com.lineage.server.utils.SQLUtil;
import com.lineage.server.world.*;
import com.lineage.william.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Calendar;
import java.util.Collection;
import java.util.Iterator;

public class GameServer {
    private static final Log _log;
    private static GameServer _instance;

    static {
        _log = LogFactory.getLog(GameServer.class);
    }

    public static GameServer getInstance() {
        if (GameServer._instance == null) {
            GameServer._instance = new GameServer();
        }
        return GameServer._instance;
    }

    public static void deleteData(int questid) {
        Connection co = null;
        PreparedStatement pm = null;
        try {
            co = DatabaseFactory.get().getConnection();
            pm = co.prepareStatement("DELETE FROM `character_quests` WHERE `quest_id`=?");
            pm.setInt(1, questid);
            pm.execute();
        } catch (SQLException ignored) {
        } finally {
            SQLUtil.close(pm);
            SQLUtil.close(co);
        }
    }

    public static String getIp() throws IOException {
        URL whatismyip = new URL("http://icanhazip.com");
        BufferedReader in = new BufferedReader(new InputStreamReader(whatismyip.openStream()));
        return in.readLine();
    }

    public void initialize() throws Exception {
        PerformanceTimer timer = new PerformanceTimer();
        try {
            L1Karma_Pc.main_check(1L);

            PacketHandler.load();
            ServerReading.get().load();
            IdFactory.get().load();
            CharObjidTable.get().load();
            AccountReading.get().load();
            GeneralThreadPool.get();
            PcOtherThreadPool.get();
            NpcAiThreadPool.get();
            DeAiThreadPool.get();
            L1SystemMessageTable.get().loadSystemMessage();
            ExpTable.get().load();
            SprTable.get().load();
            MapsTable.get().load();
            MapLevelTable.get().load();
            ItemTimeTable.get().load();
            L1WorldMap.get().load();
            L1GameTimeClock.init();
            NpcTable.get().load();
            NpcScoreTable.get().load();
            CharacterTable.loadAllCharName();
            CharacterTable.clearOnlineStatus();
            CharacterTable.clearSpeedError();
            World.get();
            WorldCrown.get();
            WorldDe.get();
            WorldKnight.get();
            WorldElf.get();
            WorldWizard.get();
            WorldDarkelf.get();
            WorldDragonKnight.get();
            WorldIllusionist.get();
            WorldPet.get();
            WorldSummons.get();
            TrapTable.get().load();
            TrapsSpawn.get().load();
            ItemTable.get().load();
            DropTable.get().load();
            DropMapTable.get().load();
            DropItemTable.get().load();
            DropItemEnchantTable.get().load();
            SkillsTable.get().load();
            SkillsItemTable.get().load();
            MobGroupTable.get().load();
            NPCTalkDataTable.get().load();
            NpcActionTable.load();
            SpawnTable.get().load();
            PolyTable.get().load();
            ShopTable.get().load();
            ShopCnTable.get().load();
            DungeonTable.get().load();
            DungeonRTable.get().load();
            NpcSpawnTable.get().load();
            DwarfForClanReading.get().load();
            ClanReading.get().load();
            ClanEmblemReading.get().load();
            L1ClanMatching.getInstance().loadClanMatching();
            if (ClanSkillDBSet.START) {
                ClanReading.get().load();
            }
            RandomMobTable.getInstance().startRandomMob();
            StartCheckWarTime.getInstance();
            if (NowTimeSpawn.START) {
                com.lineage.william.NowTimeSpawn.get();
            }
            Special.getStart();
            CastleReading.get().load();
            L1CastleLocation.setCastleTaxRate();
            GetBackRestartTable.get().load();
            DoorSpawnTable.get().load();
            WeaponSkillTable.get().load();
            WeaponSkillPowerTable.get().load();
            GetbackTable.loadGetBack();
            PetTypeTable.load();
            PetItemTable.get().load();
            ItemBoxTable.get().load();
            ResolventTable.get().load();
            NpcTeleportTable.get().load();
            NpcChatTable.get().load();
            ArmorSetTable.get().load();
            ItemTeleportTable.get().load();
            ItemPowerUpdateTable.get().load();
            CommandsTable.get().load();
            BeginnerTable.get().load();
            ItemRestrictionsTable.get().load();
            ServerFriendItemShopTable.get().load();
            ItemdropTable.get().load();
            ItemdropdeadTable.get().load();
            SpawnBossReading.get().load();
            HouseReading.get().load();
            IpReading.get().load();
            TownReading.get().load();
            MailTable.get().load();
            AuctionBoardReading.get().load();
            BoardReading.get().load();
            CharBuffReading.get().load();
            CharSkillReading.get().load();
            CharacterConfigReading.get().load();
            BuddyReading.get().load();
            CharBookReading.get().load();
            CharBookConfigReading.get().load();
            CharOtherReading.get().load();
            CharOtherReading1.get().load();
            CharOtherReading2.get().load();
            CharOtherReading3.get().load();
            QuizSetTable.getInstance().updateAllPcQuizSet();
            if (ConfigOther.monsec == 0) {
                cleanMissionStatus();
                GameServer._log.info(">>>>>>>>(每次重啟)角色任務媽祖欄位清理完畢。");
            } else if (ConfigOther.monsec > 0 && Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == ConfigOther.monsec) {
                cleanMissionStatus();
                GameServer._log.info(">>>>>>>>(時間重啟)角色任務媽祖欄位清理完畢。");
            }
            if (ConfigClan.clanskill) {
                clanaddskill();
                clanaddskill1();
                clanaddskill2();
                clanaddskill3();
                GameServer._log.info(">>>>>>>>(每次重啟)角色任務血盟貢獻清理完畢。");
            } else if (Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == ConfigClan.clanresttime) {
                clanaddskill();
                clanaddskill1();
                clanaddskill2();
                clanaddskill3();
                GameServer._log.info(">>>>>>>>(時間重啟)角色任務血盟貢獻清理完畢。");
            }
            if (ConfigOther.restday == 0) {
                day();
                GameServer._log.info(">>>>>>>>(每次重啟)角色每日簽到欄位清理完畢。");
            } else if (ConfigOther.restday > 0 && Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == ConfigOther.restday) {
                day();
                GameServer._log.info(">>>>>>>>(時間重啟)角色每日簽到欄位清理完畢。");
            }
            if (ConfigOther.shopitemrest == 0) {
                for (int questid : ShopTable._DailyItem.keySet()) {
                    deleteData(questid);
                }
                for (int questid : ShopCnTable._DailyCnItem.keySet()) {
                    deleteData(questid);
                }
            } else if (ConfigOther.shopitemrest > 0 && Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == ConfigOther.shopitemrest) {
                for (Integer integer : ShopTable._DailyItem.keySet()) {
                    int questid = integer;
                    deleteData(questid);
                }
                for (Integer integer : ShopCnTable._DailyCnItem.keySet()) {
                    int questid = integer;
                    deleteData(questid);
                }
            }
            NewAutoPractice.get().load();
            NewAutoPractice.get().load2();
            NewAutoPractice.get().load3();
            if (ConfigQuest.Time > 0 && Calendar.getInstance().get(11) == ConfigQuest.Time) {
                chcekQuest();
            } else if (ConfigQuest.Time == 0) {
                chcekQuest();
            } else if (ConfigQuest.Time == -1) {
                GameServer._log.info("任務重置已停用（Time = -1）");
            } else {
                GameServer._log.info("任務重置設定值: " + ConfigQuest.Time + "，不執行重置");
            }
            if (ConfigQuest.QuestMap_Time > 0 && Calendar.getInstance().get(11) == ConfigQuest.QuestMap_Time) {
                DBClearQuesemap.start();
                GameServer._log.info("執行任務地圖資料清空，時間: " + ConfigQuest.QuestMap_Time);
            } else if (ConfigQuest.QuestMap_Time == 0) {
                DBClearQuesemap.start();
                GameServer._log.info("執行任務地圖資料清空（每次啟動）");
            } else if (ConfigQuest.QuestMap_Time == -1) {
                GameServer._log.info("任務地圖資料清空已停用（QuestMap_Time = -1）");
            } else {
                GameServer._log.info("任務地圖資料清空設定值: " + ConfigQuest.QuestMap_Time + "，不執行清空");
            }
            CharacterQuestReading.get().load();
            BadNamesList.get().load();
            SceneryTable.get().load();
            L1SkillMode.get().load();
            L1AttackList.load();
            L1ItemPower.load();
            L1PcInstance.load();
            CharItemsReading.get().load();
            DwarfReading.get().load();
            DwarfForElfReading.get().load();
            DwarfForChaReading.get().load(); //角色專屬倉庫讀取
            DollPowerTable.get().load();
            PetReading.get().load();
            CharItemsTimeReading.get().load();
            L1PcOther.load();
            EventTable.get().load();
            if (EventTable.get().size() > 0) {
                EventSpawnTable.get().load();
            }
            QuestMapTable.get().load();
            FurnitureSpawnReading.get().load();
            ItemMsgTable.get().load();
            WeaponPowerTable.get().load();
            FishingTable.get().load();
            ExtraMeteAbilityTable.getInstance().load();
            Acc_use_Item.get();
            Char_use_Item.get();
            ExtraAttrWeaponTable.getInstance().load();
            Collection<L1MapTile> mapTile = MapTileTable.get().getList();
            if (!mapTile.isEmpty()) {
                for (L1MapTile tgMap : mapTile) {
                    L1WorldMap.get().getMap((short) tgMap.getMapid()).setTestTile(tgMap.getX(), tgMap.getY(),
                            tgMap.getTile());
                }
            }
            if (ConfigOther.RankLevel) {
                RankingHeroTimerlv.load();
            } else {
                RankingHeroTimer.load();
            }
            L1BlendTable.getInstance().loadBlendTable();
            L1BlendTable_1.getInstance().loadBlendTable();
            EnchantOrginal.getInstance();
            EnchantAccessory.getInstance();
            L1FireSmithCrystalTable.get().load();
            if (ConfigAlt.QUIZ_SET_SWITCH) {
                QuizSetTable.getInstance().load();
            }
            ItemVIPTable.get();
            ServerItemGiveTable.get();
            ItemHtmlTable.get();
            SetBossTable.get().loadSetQuest();
            CardSetTable.get().load();
            ACardTable.get().load();
            NpcHierarchTable.get();
            StartTimer_Server startTimer = new StartTimer_Server();
            startTimer.start();
            StartTimer_Pc pcTimer = new StartTimer_Pc();
            pcTimer.start();
            StartTimer_Npc npcTimer = new StartTimer_Npc();
            npcTimer.start();
            StartTimer_Pet petTimer = new StartTimer_Pet();
            petTimer.start();
            StartTimer_Skill skillTimer = new StartTimer_Skill();
            skillTimer.start();
            Runtime.getRuntime().addShutdownHook(Shutdown.getInstance());
            DeNameTable.get().load();
            DeClanTable.get().loadIcon();
            DeClanTable.get().load();
            DeTitleTable.get().load();
            DeShopChatTable.get().load();
            DeGlobalChatTable.get().load();
            DeShopItemTable.get().load();
            L1DoorInstance.openDoor();
            MapHprMprTable.get();
            MapHprMprRangeTable.get(); //Willie Kevin新增地圖範圍 回血魔
            MapLimitSettingInstance.get(); //Willie Kevin新增地圖限定設定
            MapLimitItemInstance.get(); //Willie Kevin新增地圖限定道具設定
            // 停用舊的ArmorEffect系統，已優化為新的GfxTimer系統
            // new ArmorEffect();
            ArmorSkillSound.getInstance(); // 初始化裝備特效系統
            WilliamItemMessage.getData();
            GfxIdOrginal.getInstance();
            GfxIdOrginalpoly.getInstance();
            NpcTalk10.load();
            ClanOriginal.getInstance();
            Explogpcpower.get().load();
            CheckTimeController.getInstance().start();
            MapsGroupTable.get().load();
            CharMapTimeReading.get().load();
            DropMobTable.get().load();
            L1MonTable.get().load();
            ItemLimitTable.get().load();
            AutoAddSkillTable.get();
            Taketreasure.getInstance().load();
            ItemIntegration.getInstance();
            PowerItemTable.get();
            ItemSpecialAttributeTable.get().load();
            ItemSpecialAttributeCharTable.get().load();
            if (ConfigOther.onlydaypre) {
                L1DayPresentTimer.initialize();
            }

            ClanAllianceReading.get().load();// 血盟同盟資料 丹 Kevin Willie

            ExcavateTable.get();
            WilliamBuff.load();
            ServerItemDropTable.get();
            LaBarGameTable.get();
            ServerQuestMaPTable.get();
            ExtraMagicWeaponTable.getInstance().load();
            CharWeaponTimeReading.get().load();
            SkillsProbabilityTable.get();
            ItemUseEXTable.get();
            BlendTable.getInstance().load();
            ItemTimeTableadd.get().load();
            //紋樣系統
            YiwaTable.get();// 伊娃紋樣
            shahaTable.get();// 沙哈紋樣
            mapuleTable.get();// 馬普勒紋樣
            pageliaoTable.get();// 帕格里奧紋樣
            yinhaisaTable.get();// 殷海薩紋樣
            if (ConfigOther.Reset_Map) {
                CharMapTimeReading.get().clearAllTime();
            } else if (Calendar.getInstance().get(Calendar.HOUR_OF_DAY) == ConfigOther.Reset_Map_Time) {
                CharMapTimeReading.get().clearAllTime();
            }
            EchoServerTimer.get().start();
        } catch (Exception e) {
            GameServer._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            final String ver =


                    "\n\r--------------------------------------------------"
                            + "\n\r       【目前版本:3.81】"
                            + "\n\r       【核心到期日:無限制】"
                            + "\n\r       【核心輸出:Schung】"
                            + "\n\r       【版本維護: Schung - Core 】"
                            + "\n\r       【原始開發團隊:日本】"
                            + "\n\r       【絕無侵權或惡意抄襲、篡改其他遊戲內容問題，若有雷同純屬巧合！】"
                            + "\n\r--------------------------------------------------";
            _log.info(ver);

            CmdEcho cmdEcho = new CmdEcho(timer.get());
            cmdEcho.runCmd();
        }
    }

    private void cleanMissionStatus() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 9955");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void cleanclanorg() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8599");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void day() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8991");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
        }finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void guaji_hu() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 95322");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void chcekQuest() {
        if (ConfigQuest.Quest1 > 0) {
            deleteData(ConfigQuest.Quest1);
        }
        if (ConfigQuest.Quest2 > 0) {
            deleteData(ConfigQuest.Quest2);
        }
        if (ConfigQuest.Quest3 > 0) {
            deleteData(ConfigQuest.Quest3);
        }
        if (ConfigQuest.Quest4 > 0) {
            deleteData(ConfigQuest.Quest4);
        }
        if (ConfigQuest.Quest5 > 0) {
            deleteData(ConfigQuest.Quest5);
        }
        if (ConfigQuest.Quest6 > 0) {
            deleteData(ConfigQuest.Quest6);
        }
        if (ConfigQuest.Quest7 > 0) {
            deleteData(ConfigQuest.Quest7);
        }
        if (ConfigQuest.Quest8 > 0) {
            deleteData(ConfigQuest.Quest8);
        }
        if (ConfigQuest.Quest9 > 0) {
            deleteData(ConfigQuest.Quest9);
        }
        if (ConfigQuest.Quest10 > 0) {
            deleteData(ConfigQuest.Quest10);
        }
        if (ConfigQuest.Quest11 > 0) {
            deleteData(ConfigQuest.Quest11);
        }
        if (ConfigQuest.Quest12 > 0) {
            deleteData(ConfigQuest.Quest12);
        }
        if (ConfigQuest.Quest13 > 0) {
            deleteData(ConfigQuest.Quest13);
        }
        if (ConfigQuest.Quest14 > 0) {
            deleteData(ConfigQuest.Quest14);
        }
        if (ConfigQuest.Quest15 > 0) {
            deleteData(ConfigQuest.Quest15);
        }
        if (ConfigQuest.Quest16 > 0) {
            deleteData(ConfigQuest.Quest16);
        }
        if (ConfigQuest.Quest17 > 0) {
            deleteData(ConfigQuest.Quest17);
        }
        if (ConfigQuest.Quest18 > 0) {
            deleteData(ConfigQuest.Quest18);
        }
        if (ConfigQuest.Quest19 > 0) {
            deleteData(ConfigQuest.Quest19);
        }
        if (ConfigQuest.Quest20 > 0) {
            deleteData(ConfigQuest.Quest20);
        }
        if (ConfigQuest.Quest21 > 0) {
            deleteData(ConfigQuest.Quest21);
        }
        if (ConfigQuest.Quest22 > 0) {
            deleteData(ConfigQuest.Quest22);
        }
        if (ConfigQuest.Quest23 > 0) {
            deleteData(ConfigQuest.Quest23);
        }
        if (ConfigQuest.Quest24 > 0) {
            deleteData(ConfigQuest.Quest24);
        }
        if (ConfigQuest.Quest25 > 0) {
            deleteData(ConfigQuest.Quest25);
        }
        if (ConfigQuest.Quest26 > 0) {
            deleteData(ConfigQuest.Quest26);
        }
        if (ConfigQuest.Quest27 > 0) {
            deleteData(ConfigQuest.Quest27);
        }
        if (ConfigQuest.Quest28 > 0) {
            deleteData(ConfigQuest.Quest28);
        }
        if (ConfigQuest.Quest29 > 0) {
            deleteData(ConfigQuest.Quest29);
        }
        if (ConfigQuest.Quest30 > 0) {
            deleteData(ConfigQuest.Quest30);
        }
        if (ConfigQuest.Quest31 > 0) {
            deleteData(ConfigQuest.Quest31);
        }
        if (ConfigQuest.Quest32 > 0) {
            deleteData(ConfigQuest.Quest32);
        }
        if (ConfigQuest.Quest33 > 0) {
            deleteData(ConfigQuest.Quest33);
        }
        if (ConfigQuest.Quest34 > 0) {
            deleteData(ConfigQuest.Quest34);
        }
        if (ConfigQuest.Quest35 > 0) {
            deleteData(ConfigQuest.Quest35);
        }
        if (ConfigQuest.Quest36 > 0) {
            deleteData(ConfigQuest.Quest36);
        }
        if (ConfigQuest.Quest37 > 0) {
            deleteData(ConfigQuest.Quest37);
        }
        if (ConfigQuest.Quest38 > 0) {
            deleteData(ConfigQuest.Quest38);
        }
        if (ConfigQuest.Quest39 > 0) {
            deleteData(ConfigQuest.Quest39);
        }
        if (ConfigQuest.Quest40 > 0) {
            deleteData(ConfigQuest.Quest40);
        }
        if (ConfigQuest.Quest41 > 0) {
            deleteData(ConfigQuest.Quest41);
        }
        if (ConfigQuest.Quest42 > 0) {
            deleteData(ConfigQuest.Quest42);
        }
        if (ConfigQuest.Quest43 > 0) {
            deleteData(ConfigQuest.Quest43);
        }
        if (ConfigQuest.Quest44 > 0) {
            deleteData(ConfigQuest.Quest44);
        }
        if (ConfigQuest.Quest45 > 0) {
            deleteData(ConfigQuest.Quest45);
        }
        if (ConfigQuest.Quest46 > 0) {
            deleteData(ConfigQuest.Quest46);
        }
        if (ConfigQuest.Quest47 > 0) {
            deleteData(ConfigQuest.Quest47);
        }
        if (ConfigQuest.Quest48 > 0) {
            deleteData(ConfigQuest.Quest48);
        }
        if (ConfigQuest.Quest49 > 0) {
            deleteData(ConfigQuest.Quest49);
        }
        if (ConfigQuest.Quest50 > 0) {
            deleteData(ConfigQuest.Quest50);
        }
    }

    private void clanaddskill() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8541");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        }finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void clanaddskill1() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8544");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void clanaddskill2() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8545");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void clanaddskill3() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 8546");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }

    private void deleclan() {
        Connection con = null;
        PreparedStatement pstm = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement("DELETE FROM character_quests WHERE quest_id = 50988");
            pstm.execute();
        } catch (SQLException e) {
            CharacterQuestTable._log.error(e.getLocalizedMessage(), e);
            return;
        } finally {
            try {
                if (pstm != null) {
                    pstm.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 PreparedStatement 時發生錯誤", e);
            }

            try {
                if (con != null) {
                    con.close();
                }
            } catch (SQLException e) {
                _log.warn("關閉 Connection 時發生錯誤", e);
            }
        }
    }
}
