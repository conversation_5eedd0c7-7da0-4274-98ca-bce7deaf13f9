package com.lineage.server.thread;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.Timer;
import java.util.TimerTask;
import com.lineage.server.model.monitor.L1PcMonitor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executor;

public class GeneralThreadPool {
	private static GeneralThreadPool _instance;
	private static final int SCHEDULED_CORE_POOL_SIZE = 100;
	private Executor _executor;
	private ScheduledExecutorService _scheduler;
	private ScheduledExecutorService _pcScheduler;
	private ScheduledExecutorService _aiScheduler;
	private final int _pcSchedulerPoolSize = 4;

	public static GeneralThreadPool get() {
		if (GeneralThreadPool._instance == null) {
			GeneralThreadPool._instance = new GeneralThreadPool();
		}
		return GeneralThreadPool._instance;
	}

	private GeneralThreadPool() {
		this._executor = Executors.newCachedThreadPool();
		this._scheduler = Executors.newScheduledThreadPool(100, new PriorityThreadFactory("GSTPool", 5));
		this._pcScheduler = Executors.newScheduledThreadPool(4, new PriorityThreadFactory("PSTPool", 5));
		this._aiScheduler = Executors.newScheduledThreadPool(4, new PriorityThreadFactory("AITPool", 5));
	}

	public void execute(final Runnable r) {
		try {
			if (this._executor == null) {
				final Thread t = new Thread(r);
				t.start();
			} else {
				this._executor.execute(r);
			}
		} catch (RejectedExecutionException e) {
			System.err.println("任務被拒絕執行: " + e.getMessage());
		} catch (Exception e) {
			System.err.println("執行任務失敗: " + e.getMessage());
		}
	}

	public void execute(final Thread t) {
		try {
			t.start();
		} catch (Exception e) {
			System.err.println("執行執行緒失敗: " + e.getMessage());
		}
	}

	public ScheduledFuture<?> schedule(final Runnable r, final long delay) {
		try {
			if (delay <= 0L) {
				this._executor.execute(r);
				return null;
			}
			return this._scheduler.schedule(r, delay, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("排程任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public ScheduledFuture<?> scheduleAtFixedRate(final Runnable r, final long initialDelay, final long period) {
		try {
			return this._scheduler.scheduleAtFixedRate(r, initialDelay, period, TimeUnit.MILLISECONDS);
		} catch (Exception e) {
			System.err.println("排程固定頻率任務失敗: " + e.getMessage());
			return null;
		}
	}

	public ScheduledFuture<?> pcSchedule(final L1PcMonitor r, final long delay) {
		try {
			if (delay <= 0L) {
				this._executor.execute(r);
				return null;
			}
			return this._pcScheduler.schedule(r, delay, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("PC排程任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public ScheduledFuture<?> scheduleAtFixedRate(final TimerTask command, final long initialDelay, final long period) {
		try {
			return this._aiScheduler.scheduleAtFixedRate(command, initialDelay, period, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("AI排程任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public void cancel(final ScheduledFuture<?> future, final boolean mayInterruptIfRunning) {
		try {
			future.cancel(mayInterruptIfRunning);
		} catch (RejectedExecutionException e) {
			System.err.println("取消任務失敗: " + e.getMessage());
		}
	}

	public Timer aiScheduleAtFixedRate(final TimerTask task, final long delay, final long period) {
		try {
			final Timer timer = new Timer();
			timer.scheduleAtFixedRate(task, delay, period);
			return timer;
		} catch (RejectedExecutionException e) {
			System.err.println("AI定時任務排程失敗: " + e.getMessage());
			return null;
		}
	}

	public void cancel(final TimerTask task) {
		try {
			task.cancel();
		} catch (Exception e) {
			System.err.println("取消TimerTask失敗: " + e.getMessage());
		}
	}

	private class PriorityThreadFactory implements ThreadFactory {
		private final int _prio;
		private final String _name;
		private final AtomicInteger _threadNumber;
		private final ThreadGroup _group;

		public PriorityThreadFactory(final String name, final int prio) {
			this._threadNumber = new AtomicInteger(1);
			this._prio = prio;
			this._name = name;
			this._group = new ThreadGroup(this._name);
		}

		@Override
		public Thread newThread(final Runnable r) {
			final Thread t = new Thread(this._group, r);
			t.setName(String.valueOf(this._name) + "-" + this._threadNumber.getAndIncrement());
			t.setPriority(this._prio);
			return t;
		}

		public ThreadGroup getGroup() {
			return this._group;
		}
	}
}
