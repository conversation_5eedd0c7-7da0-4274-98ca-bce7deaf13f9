package com.lineage.server.thread;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.TimerTask;
import com.lineage.server.model.monitor.L1PcMonitor;
import java.util.concurrent.RejectedExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executor;

public class PcOtherThreadPool {
	private static PcOtherThreadPool _instance;
	private Executor _executor;
	private ScheduledExecutorService _scheduler;
	private final int _pcSchedulerPoolSize = 4;

	public static PcOtherThreadPool get() {
		if (PcOtherThreadPool._instance == null) {
			PcOtherThreadPool._instance = new PcOtherThreadPool();
		}
		return PcOtherThreadPool._instance;
	}

	private PcOtherThreadPool() {
		this._scheduler = Executors.newScheduledThreadPool(4, new PriorityThreadFactory("PcOth", 5));
	}

	public void execute(final Runnable r) {
		try {
			if (this._executor == null) {
				final Thread t = new Thread(r);
				t.start();
			} else {
				this._executor.execute(r);
			}
		} catch (RejectedExecutionException e) {
			System.err.println("PcOther任務被拒絕執行: " + e.getMessage());
		} catch (Exception e) {
			System.err.println("PcOther執行任務失敗: " + e.getMessage());
		}
	}

	public ScheduledFuture<?> schedule(final Runnable r, final long delay) {
		try {
			if (delay <= 0L) {
				this._executor.execute(r);
				return null;
			}
			return this._scheduler.schedule(r, delay, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("PcOther排程任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public ScheduledFuture<?> pcSchedule(final L1PcMonitor r, final long delay) {
		try {
			if (delay <= 0L) {
				this._executor.execute(r);
				return null;
			}
			return this._scheduler.schedule(r, delay, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("PcOther PC排程任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public ScheduledFuture<?> scheduleAtFixedRate(final TimerTask command, final long initialDelay, final long period) {
		try {
			return this._scheduler.scheduleAtFixedRate(command, initialDelay, period, TimeUnit.MILLISECONDS);
		} catch (RejectedExecutionException e) {
			System.err.println("PcOther固定頻率任務被拒絕: " + e.getMessage());
			return null;
		}
	}

	public void cancel(final ScheduledFuture<?> future, final boolean mayInterruptIfRunning) {
		try {
			future.cancel(mayInterruptIfRunning);
		} catch (RejectedExecutionException e) {
			System.err.println("PcOther取消任務失敗: " + e.getMessage());
		}
	}

	public void cancel(final TimerTask task) {
		try {
			task.cancel();
		} catch (Exception e) {
			System.err.println("PcOther取消TimerTask失敗: " + e.getMessage());
		}
	}

	private class PriorityThreadFactory implements ThreadFactory {
		private final int _prio;
		private final String _name;
		private final AtomicInteger _threadNumber;
		private final ThreadGroup _group;

		public PriorityThreadFactory(final String name, final int prio) {
			this._threadNumber = new AtomicInteger(1);
			this._prio = prio;
			this._name = name;
			this._group = new ThreadGroup(this._name);
		}

		@Override
		public Thread newThread(final Runnable r) {
			final Thread t = new Thread(this._group, r);
			t.setName(String.valueOf(this._name) + "-" + this._threadNumber.getAndIncrement());
			t.setPriority(this._prio);
			return t;
		}

		public ThreadGroup getGroup() {
			return this._group;
		}
	}
}
