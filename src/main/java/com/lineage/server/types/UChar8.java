package com.lineage.server.types;

public class UChar8 {
	public static char[] fromArray(final long[] buff) {
		final char[] charBuff = new char[buff.length * 4];
		int i = 0;
		while (i < buff.length) {
			charBuff[i * 4 + 0] = (char) (int) (buff[i] & 0xFFL);
			charBuff[i * 4 + 1] = (char) (int) (buff[i] >> 8 & 0xFFL);
			charBuff[i * 4 + 2] = (char) (int) (buff[i] >> 16 & 0xFFL);
			charBuff[i * 4 + 3] = (char) (int) (buff[i] >> 24 & 0xFFL);
			++i;
		}
		return charBuff;
	}

	public static char[] fromArray(final byte[] buff) {
		final char[] charBuff = new char[buff.length];
		int i = 0;
		while (i < buff.length) {
			charBuff[i] = (char) (buff[i] & 0xFF);
			++i;
		}
		return charBuff;
	}

	public static char[] fromArray(final byte[] buff, final int length) {
		final char[] charBuff = new char[length];
		int i = 0;
		while (i < length) {
			charBuff[i] = (char) (buff[i] & 0xFF);
			++i;
		}
		return charBuff;
	}

	public static char fromUByte8(final byte b) {
		return (char) (b & 0xFF);
	}

	public static char[] fromULong32(final long l) {
		final char[] charBuff = { (char) (int) (l & 0xFFL), (char) (int) (l >> 8 & 0xFFL),
				(char) (int) (l >> 16 & 0xFFL), (char) (int) (l >> 24 & 0xFFL) };
		return charBuff;
	}
}
