package com.lineage.server.command.executor;

import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.command.GMCommands;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import java.util.StringTokenizer;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.util.Map;
import org.apache.commons.logging.Log;

public class L1Favorite implements L1CommandExecutor {
	private static final Log _log;
	private static final Map<Integer, String> _faviCom;

	static {
		_log = LogFactory.getLog(L1Favorite.class);
		_faviCom = new HashMap();
	}

	private L1Favorite() {
	}

	public static L1CommandExecutor getInstance() {
		return new L1Favorite();
	}

	@Override
	public void execute(final L1PcInstance pc, final String cmdName, final String arg) {
		try {
			if (!L1Favorite._faviCom.containsKey(Integer.valueOf(pc.getId()))) {
				L1Favorite._faviCom.put(Integer.valueOf(pc.getId()), "");
			}
			String faviCom = L1Favorite._faviCom.get(Integer.valueOf(pc.getId()));
			if (arg.startsWith("set")) {
				final StringTokenizer st = new StringTokenizer(arg);
				st.nextToken();
				if (!st.hasMoreTokens()) {
					pc.sendPackets(new S_SystemMessage("紀錄質不能為空白"));
					return;
				}
				final StringBuilder cmd = new StringBuilder();
				final String temp = st.nextToken();
				if (temp.equalsIgnoreCase(cmdName)) {
					pc.sendPackets(new S_SystemMessage(String.valueOf(cmdName) + " 紀錄質異常。"));
					return;
				}
				cmd.append(String.valueOf(temp) + " ");
				while (st.hasMoreTokens()) {
					cmd.append(String.valueOf(st.nextToken()) + " ");
				}
				faviCom = cmd.toString().trim();
				L1Favorite._faviCom.put(Integer.valueOf(pc.getId()), faviCom);
				pc.sendPackets(new S_SystemMessage(String.valueOf(faviCom) + " 指令紀錄完成!"));
			} else if (arg.startsWith("show")) {
				pc.sendPackets(new S_SystemMessage("目前紀錄的指令: " + faviCom));
			} else if (faviCom.isEmpty()) {
				pc.sendPackets(new S_SystemMessage("目前無紀錄指令!"));
			} else {
				final StringBuilder cmd2 = new StringBuilder();
				final StringTokenizer st2 = new StringTokenizer(arg);
				final StringTokenizer st3 = new StringTokenizer(faviCom);
				while (st3.hasMoreTokens()) {
					final String temp2 = st3.nextToken();
					if (temp2.startsWith("%")) {
						cmd2.append(String.valueOf(st2.nextToken()) + " ");
					} else {
						cmd2.append(String.valueOf(temp2) + " ");
					}
				}
				while (st2.hasMoreTokens()) {
					cmd2.append(String.valueOf(st2.nextToken()) + " ");
				}
				pc.sendPackets(new S_SystemMessage(cmd2 + " 指令執行。"));
				GMCommands.getInstance().handleCommands(pc, cmd2.toString());
			}
		} catch (Exception e) {
			L1Favorite._log.error("錯誤的GM指令格式: " + this.getClass().getSimpleName() + " 執行的GM:" + pc.getName());
			pc.sendPackets(new S_ServerMessage(261));
		}
	}
}
