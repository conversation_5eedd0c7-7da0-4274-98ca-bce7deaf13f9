package com.lineage.server.command.executor;

import com.lineage.server.templates.L1Skills;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.datatables.lock.CharSkillReading;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.serverpackets.S_AddSkill;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class L1AddSkill implements L1CommandExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(L1AddSkill.class);
	}

	private L1AddSkill() {
	}

	public static L1CommandExecutor getInstance() {
		return new L1AddSkill();
	}

	@Override
	public void execute(final L1PcInstance pc, final String cmdName, final String arg) {
		try {
			int cnt = 0;
			String skill_name = "";
			int skill_id = 0;
			final int object_id = pc.getId();
			pc.sendPacketsX8(new S_SkillSound(object_id, 227));
			if (pc.isCrown()) {
				pc.sendPackets(new S_AddSkill(pc, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 0, 0, 0, 0, 0, 0,
						0, 0, 0, 0, 0, 0, 0));
				cnt = 1;
				while (cnt <= 16) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
				cnt = 113;
				while (cnt <= 120) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isKnight()) {
				pc.sendPackets(new S_AddSkill(pc, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 192, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
						0, 0, 0, 0, 0, 0));
				cnt = 1;
				while (cnt <= 8) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
				cnt = 87;
				while (cnt <= 91) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isElf()) {
				pc.sendPackets(new S_AddSkill(pc, 255, 255, 127, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 127, 3,
						255, 255, 255, 255, 0, 0, 0, 0, 0, 0));
				cnt = 1;
				while (cnt <= 48) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
				cnt = 129;
				while (cnt <= 176) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isWizard()) {
				pc.sendPackets(new S_AddSkill(pc, 255, 255, 127, 255, 255, 255, 255, 255, 255, 255, 0, 0, 0, 0, 0, 0, 0,
						0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0));
				cnt = 1;
				while (cnt <= 80) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isDarkelf()) {
				pc.sendPackets(new S_AddSkill(pc, 255, 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 255, 127, 0, 0, 0, 0, 0, 0, 0,
						0, 0, 0, 0, 0, 0, 0));
				cnt = 1;
				while (cnt <= 16) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
				cnt = 97;
				while (cnt <= 111) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isDragonKnight()) {
				pc.sendPackets(new S_AddSkill(pc, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 240,
						255, 7, 0, 0, 0));
				cnt = 181;
				while (cnt <= 195) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			} else if (pc.isIllusionist()) {
				pc.sendPackets(new S_AddSkill(pc, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
						0, 0, 255, 255, 15));
				cnt = 201;
				while (cnt <= 220) {
					final L1Skills l1skills = SkillsTable.get().getTemplate(cnt);
					skill_name = l1skills.getName();
					skill_id = l1skills.getSkillId();
					CharSkillReading.get().spellMastery(object_id, skill_id, skill_name, 0, 0);
					++cnt;
				}
			}
		} catch (Exception e) {
			L1AddSkill._log.error("錯誤的GM指令格式: " + this.getClass().getSimpleName() + " 執行的GM:" + pc.getName());
			pc.sendPackets(new S_ServerMessage(261));
		}
	}
}
