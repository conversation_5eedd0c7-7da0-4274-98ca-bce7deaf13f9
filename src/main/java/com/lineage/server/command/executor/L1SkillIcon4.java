package com.lineage.server.command.executor;

import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_PacketBoxIcon2;
import java.util.StringTokenizer;
import com.lineage.server.model.Instance.L1PcInstance;

public class L1SkillIcon4 implements L1CommandExecutor {
	public static L1CommandExecutor getInstance() {
		return new L1SkillIcon4();
	}

	@Override
	public void execute(final L1PcInstance pc, final String cmdName, final String arg) {
		try {
			final StringTokenizer st = new StringTokenizer(arg);
			int type = 0;
			if (st.hasMoreTokens()) {
				type = Integer.parseInt(st.nextToken());
			}
			switch (type) {
			case 1: {
				int i = 0;
				while (i < 1000) {
					pc.sendPackets(new S_PacketBoxIcon2(0 + i, 0, 0, 0));
					++i;
				}
				break;
			}
			case 2: {
				int i = 0;
				while (i < 1000) {
					pc.sendPackets(new S_PacketBoxIcon2(0, 0 + i, 0, 0));
					++i;
				}
				break;
			}
			case 3: {
				int i = 0;
				while (i < 1000) {
					pc.sendPackets(new S_PacketBoxIcon2(0, 0, 0 + i, 0));
					++i;
				}
				break;
			}
			case 4: {
				int i = 0;
				while (i < 1000) {
					pc.sendPackets(new S_PacketBoxIcon2(0, 0, 0, 0 + i));
					++i;
				}
				break;
			}
			}
		} catch (Exception e) {
			pc.sendPackets(new S_SystemMessage(String.valueOf(cmdName) + " Skillicon 請輸入 id 編碼。"));
		}
	}
}
