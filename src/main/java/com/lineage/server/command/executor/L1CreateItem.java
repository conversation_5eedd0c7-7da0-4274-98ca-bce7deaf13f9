package com.lineage.server.command.executor;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.templates.L1Item;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.datatables.ItemTable;
import java.util.StringTokenizer;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class L1CreateItem implements L1CommandExecutor {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(L1CreateItem.class);
	}

	private L1CreateItem() {
	}

	public static L1CommandExecutor getInstance() {
		return new L1CreateItem();
	}

	@Override
	public void execute(final L1PcInstance pc, final String cmdName, final String arg) {
		try {
			final StringTokenizer st = new StringTokenizer(arg);
			final String nameid = st.nextToken();
			long count = 1L;
			if (st.hasMoreTokens()) {
				count = Long.parseLong(st.nextToken());
			}
			int enchant = 0;
			if (st.hasMoreTokens()) {
				enchant = Integer.parseInt(st.nextToken());
			}
			int itemid = 0;
			try {
				itemid = Integer.parseInt(nameid);
			} catch (NumberFormatException e) {
				itemid = ItemTable.get().findItemIdByNameWithoutSpace(nameid);
				if (itemid == 0) {
					pc.sendPackets(new S_SystemMessage("沒有找到條件吻合的物品。"));
					return;
				}
			}
			final L1Item temp = ItemTable.get().getTemplate(itemid);
			if (temp != null) {
				if (temp.isStackable()) {
					final L1ItemInstance item = ItemTable.get().createItem(itemid);
					item.setEnchantLevel(0);
					item.setCount(count);
					item.setIdentified(false);
					if (pc.getInventory().checkAddItem(item, count) == 0) {
						pc.getInventory().storeItem(item);
						pc.sendPackets(
								new S_ServerMessage(403, String.valueOf(item.getLogName()) + "(ID:" + itemid + ")"));
					}
				} else {
					if (count > 10L) {
						pc.sendPackets(new S_SystemMessage("不可以堆疊的物品一次創造數量禁止超過10"));
						return;
					}
					L1ItemInstance item = null;
					int createCount = 0;
					while (createCount < count) {
						item = ItemTable.get().createItem(itemid);
						item.setEnchantLevel(enchant);
						item.setIdentified(false);
						if (pc.getInventory().checkAddItem(item, 1L) != 0) {
							break;
						}
						pc.getInventory().storeItem(item);
						++createCount;
					}
					if (createCount > 0) {
						pc.sendPackets(
								new S_ServerMessage(403, String.valueOf(item.getLogName()) + "(ID:" + itemid + ")"));
					}
				}
			} else {
				pc.sendPackets(new S_SystemMessage("指定ID不存在"));
			}
		} catch (Exception e2) {
			L1CreateItem._log.error("錯誤的GM指令格式: " + this.getClass().getSimpleName() + " 執行的GM:" + pc.getName());
			pc.sendPackets(new S_ServerMessage(261));
		}
	}
}
