package com.lineage.server.templates;

public class L1ItemSpecialAttribute {
	private int _id;
	private String _colour;
	private int _type;
	private String _name;
	private int _dmg_small;
	private int _dmg_large;
	private int _hitmodifier;
	private int _dmgmodifier;
	private int _add_str;
	private int _add_con;
	private int _add_dex;
	private int _add_int;
	private int _add_wis;
	private int _add_cha;
	private int _add_hp;
	private int _add_mp;
	private int _add_hpr;
	private int _add_mpr;
	private int _add_sp;
	private int _add_rand;
	private int _add_m_def;
	private int _add_drain_min_hp;
	private int _add_drain_max_hp;
	private int _drain_hp_rand;
	private int _add_drain_min_mp;
	private int _add_drain_max_mp;
	private int _drain_mp_rand;
	private int _add_skill_rand;
	private int _add_skill_gfxid;
	private int _add_skill_dmg;
	private int _魔法格檔;
	private int _物理格檔;
	private int _add_pvp_dmg;
	private int _add_pvp_redmg;
	private int _add_potion_heal;
	private String _msg;
	private String _msg1;
	private int _add_dmgR;

	public int get_id() {
		return this._id;
	}

	public void set_id(final int id) {
		this._id = id;
	}

	public String get_colour() {
		return this._colour;
	}

	public void set_colour(final String colour) {
		this._colour = colour;
	}

	public int get_type() {
		return this._type;
	}

	public void set_type(final int type) {
		this._type = type;
	}

	public String get_name() {
		return this._name;
	}

	public void set_name(final String name) {
		this._name = name;
	}

	public int get_dmg_small() {
		return this._dmg_small;
	}

	public void set_dmg_small(final int dmg_small) {
		this._dmg_small = dmg_small;
	}

	public int get_dmg_large() {
		return this._dmg_large;
	}

	public void set_dmg_large(final int dmg_large) {
		this._dmg_large = dmg_large;
	}

	public int get_hitmodifier() {
		return this._hitmodifier;
	}

	public void set_hitmodifier(final int hitmodifier) {
		this._hitmodifier = hitmodifier;
	}

	public int get_dmgmodifier() {
		return this._dmgmodifier;
	}

	public void set_dmgmodifier(final int dmgmodifier) {
		this._dmgmodifier = dmgmodifier;
	}

	public int get_add_str() {
		return this._add_str;
	}

	public void set_add_str(final int add_str) {
		this._add_str = add_str;
	}

	public int get_add_con() {
		return this._add_con;
	}

	public void set_add_con(final int add_con) {
		this._add_con = add_con;
	}

	public int get_add_dex() {
		return this._add_dex;
	}

	public void set_add_dex(final int add_dex) {
		this._add_dex = add_dex;
	}

	public int get_add_int() {
		return this._add_int;
	}

	public void set_add_int(final int add_int) {
		this._add_int = add_int;
	}

	public int get_add_wis() {
		return this._add_wis;
	}

	public void set_add_wis(final int add_wis) {
		this._add_wis = add_wis;
	}

	public int get_add_cha() {
		return this._add_cha;
	}

	public void set_add_cha(final int add_cha) {
		this._add_cha = add_cha;
	}

	public int get_add_hp() {
		return this._add_hp;
	}

	public void set_add_hp(final int add_hp) {
		this._add_hp = add_hp;
	}

	public int get_add_mp() {
		return this._add_mp;
	}

	public void set_add_mp(final int add_mp) {
		this._add_mp = add_mp;
	}

	public int get_add_hpr() {
		return this._add_hpr;
	}

	public void set_add_hpr(final int add_hpr) {
		this._add_hpr = add_hpr;
	}

	public int get_add_mpr() {
		return this._add_mpr;
	}

	public void set_add_mpr(final int add_mpr) {
		this._add_mpr = add_mpr;
	}

	public int get_add_sp() {
		return this._add_sp;
	}

	public void set_add_sp(final int add_sp) {
		this._add_sp = add_sp;
	}

	public int get_add_rand() {
		return this._add_rand;
	}

	public void set_add_rand(final int add_rand) {
		this._add_rand = add_rand;
	}

	public int get_add_m_def() {
		return this._add_m_def;
	}

	public void set_add_m_def(final int add_m_def) {
		this._add_m_def = add_m_def;
	}

	public int get_add_drain_min_hp() {
		return this._add_drain_min_hp;
	}

	public void set_add_drain_min_hp(final int drain_min_hp) {
		this._add_drain_min_hp = drain_min_hp;
	}

	public int get_add_drain_max_hp() {
		return this._add_drain_max_hp;
	}

	public void set_add_drain_max_hp(final int drain_max_hp) {
		this._add_drain_max_hp = drain_max_hp;
	}

	public int get_drain_hp_rand() {
		return this._drain_hp_rand;
	}

	public void set_drain_hp_rand(final int drain_hp_rand) {
		this._drain_hp_rand = drain_hp_rand;
	}

	public int get_add_drain_min_mp() {
		return this._add_drain_min_mp;
	}

	public void set_add_drain_min_mp(final int drain_min_mp) {
		this._add_drain_min_mp = drain_min_mp;
	}

	public int get_add_drain_max_mp() {
		return this._add_drain_max_mp;
	}

	public void set_add_drain_max_mp(final int drain_max_mp) {
		this._add_drain_max_mp = drain_max_mp;
	}

	public int get_drain_mp_rand() {
		return this._drain_mp_rand;
	}

	public void set_drain_mp_rand(final int drain_mp_rand) {
		this._drain_mp_rand = drain_mp_rand;
	}

	public int get_add_skill_rand() {
		return this._add_skill_rand;
	}

	public void set_add_skill_rand(final int skill_rand) {
		this._add_skill_rand = skill_rand;
	}

	public int get_add_skill_gfxid() {
		return this._add_skill_gfxid;
	}

	public void set_add_skill_gfxid(final int skill_gfxid) {
		this._add_skill_gfxid = skill_gfxid;
	}

	public int get_add_skill_dmg() {
		return this._add_skill_dmg;
	}

	public void set_add_skill_dmg(final int skill_dmg) {
		this._add_skill_dmg = skill_dmg;
	}

	public int get魔法格檔() {
		return this._魔法格檔;
	}

	public void add魔法格檔(final int i) {
		this._魔法格檔 = i;
	}

	public int get物理格檔() {
		return this._物理格檔;
	}

	public void add物理格檔(final int i) {
		this._物理格檔 = i;
	}

	public int get_add_pvp_dmg() {
		return this._add_pvp_dmg;
	}

	public void set_add_pvp_dmg(final int add_pvp_dmg) {
		this._add_pvp_dmg = add_pvp_dmg;
	}

	public int get_add_pvp_redmg() {
		return this._add_pvp_redmg;
	}

	public void set_add_pvp_redmg(final int add_pvp_redmg) {
		this._add_pvp_redmg = add_pvp_redmg;
	}

	public int get_add_potion_heal() {
		return this._add_potion_heal;
	}

	public void set_add_potion_heal(final int add_potion_heal) {
		this._add_potion_heal = add_potion_heal;
	}

	public String get_msg() {
		return this._msg;
	}

	public void set_msg(final String msg) {
		this._msg = msg;
	}

	public String get_msg1() {
		return this._msg1;
	}

	public void set_msg1(final String msg1) {
		this._msg1 = msg1;
	}

	public int get_add_dmgR() {
		return this._add_dmgR;
	}

	public void set_add_dmgR(final int add_dmgR) {
		this._add_dmgR = add_dmgR;
	}
}
