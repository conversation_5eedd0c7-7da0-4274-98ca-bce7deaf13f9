package com.lineage.server.templates;

public class L1Fishing {
	private int _itemid;
	private int _randomint;
	private int _random;
	private int _fishingpole;
	private int _fishingpole2;

	public int get_itemid() {
		return this._itemid;
	}

	public void set_itemid(final int _itemid) {
		this._itemid = _itemid;
	}

	public int get_randomint() {
		return this._randomint;
	}

	public void set_randomint(final int _randomint) {
		this._randomint = _randomint;
	}

	public int get_random() {
		return this._random;
	}

	public void set_random(final int _random) {
		this._random = _random;
	}

	public int get_fishingpole() {
		return this._fishingpole;
	}

	public void set_fishingpole(final int itemid) {
		this._fishingpole = itemid;
	}

	public int get_fishingpole2() {
		return this._fishingpole2;
	}

	public void set_fishingpole2(final int itemid) {
		this._fishingpole2 = itemid;
	}
}
