package com.lineage.server.templates;

public class MapData {
    public int mapId;
    public int startX;
    public int endX;
    public int startY;
    public int endY;
    public double monster_amount;
    public double dropRate;
    public boolean isUnderwater;
    public boolean markable;
    public boolean teleportable;
    public boolean escapable;
    public boolean isUseResurrection;
    public boolean isUsePainwand;
    public boolean isEnabledDeathPenalty;
    public boolean isTakePets;
    public boolean isRecallPets;
    public boolean isUsableItem;
    public boolean isUsableSkill;
    public boolean isGuaji;
    public boolean isClanPc;
    public boolean isPartyPc;
    public boolean isAlliancePc;
    public boolean isdropitem;
    public int isUsableShop;
    public int isStart_time;
    public int isEnd_time;
    public int isweek;
    public int maptime;
    public String location;
    public int CopyMapId;
    public int EXP;

    public MapData() {
        mapId = 0;
        startX = 0;
        endX = 0;
        startY = 0;
        endY = 0;
        monster_amount = 1.0;
        dropRate = 1.0;
        isUnderwater = false;
        markable = false;
        teleportable = false;
        escapable = false;
        isUseResurrection = false;
        isUsePainwand = false;
        isEnabledDeathPenalty = false;
        isTakePets = false;
        isRecallPets = false;
        isUsableItem = false;
        isUsableSkill = false;
        isGuaji = false;
        isClanPc = false;
        isPartyPc = false;
        isAlliancePc = false;
        isdropitem = false;
        maptime = 0;
        CopyMapId = -1;
        EXP = 0;
    }
}
