package com.lineage.server.templates;

public class L1MobSkill implements Cloneable {
	public static final int TYPE_NONE = 0;
	public static final int TYPE_PHYSICAL_ATTACK = 1;
	public static final int TYPE_MAGIC_ATTACK = 2;
	public static final int TYPE_SUMMON = 3;
	public static final int TYPE_POLY = 4;
	public static final int AHTHARAS_1 = 5;
	public static final int AHTHARAS_2 = 6;
	public static final int AHTHARAS_3 = 7;
	public static final int CHANGE_TARGET_NO = 0;
	public static final int CHANGE_TARGET_ME = 2;
	public static final int CHANGE_TARGET_RANDOM = 3;
	private final int skillSize;
	private boolean[] isSkillDelayIdx;
	private int mobid;
	private String mobName;
	private int[] type;
	private int[] triRnd;
	int[] triHp;
	int[] triCompanionHp;
	int[] triRange;
	int[] triCount;
	int[] changeTarget;
	int[] range;
	int[] areaWidth;
	int[] areaHeight;
	int[] leverage;
	int[] skillId;
	int[] gfxid;
	int[] actid;
	int[] summon;
	int[] summonMin;
	int[] summonMax;
	int[] polyId;
	int[] reuseDelay;

	@Override
	public L1MobSkill clone() {
		try {
			return (L1MobSkill) super.clone();
		} catch (CloneNotSupportedException e) {
			throw new InternalError(e.getMessage());
		}
	}

	public int getSkillSize() {
		return this.skillSize;
	}

	public L1MobSkill(final int sSize) {
		this.skillSize = sSize;
		this.type = new int[this.skillSize];
		this.triRnd = new int[this.skillSize];
		this.triHp = new int[this.skillSize];
		this.triCompanionHp = new int[this.skillSize];
		this.triRange = new int[this.skillSize];
		this.triCount = new int[this.skillSize];
		this.changeTarget = new int[this.skillSize];
		this.range = new int[this.skillSize];
		this.areaWidth = new int[this.skillSize];
		this.areaHeight = new int[this.skillSize];
		this.leverage = new int[this.skillSize];
		this.skillId = new int[this.skillSize];
		this.gfxid = new int[this.skillSize];
		this.actid = new int[this.skillSize];
		this.summon = new int[this.skillSize];
		this.summonMin = new int[this.skillSize];
		this.summonMax = new int[this.skillSize];
		this.polyId = new int[this.skillSize];
		this.reuseDelay = new int[this.skillSize];
		this.isSkillDelayIdx = new boolean[this.skillSize];
	}

	public void setSkillDelayIdx(final int idx, final boolean flag) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.isSkillDelayIdx[idx] = flag;
	}

	public boolean isSkillDelayIdx(final int idx) {
		return idx >= 0 && idx < this.getSkillSize() && this.isSkillDelayIdx[idx];
	}

	public int get_mobid() {
		return this.mobid;
	}

	public void set_mobid(final int i) {
		this.mobid = i;
	}

	public String getMobName() {
		return this.mobName;
	}

	public void setMobName(final String s) {
		this.mobName = s;
	}

	public int getType(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.type[idx];
	}

	public void setType(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.type[idx] = i;
	}

	public int getTriggerRandom(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.triRnd[idx];
	}

	public void setTriggerRandom(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.triRnd[idx] = i;
	}

	public int getTriggerHp(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.triHp[idx];
	}

	public void setTriggerHp(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.triHp[idx] = i;
	}

	public int getTriggerCompanionHp(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.triCompanionHp[idx];
	}

	public void setTriggerCompanionHp(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.triCompanionHp[idx] = i;
	}

	public int getTriggerRange(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		Math.abs(idx);
		return this.triRange[idx];
	}

	public void setTriggerRange(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.triRange[idx] = i;
	}

	public boolean isTriggerDistance(final int idx, final int distance) {
		final int triggerRange = this.getTriggerRange(idx);
		return (triggerRange < 0 && distance <= Math.abs(triggerRange))
				|| (triggerRange > 0 && distance >= triggerRange);
	}

	public int getTriggerCount(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.triCount[idx];
	}

	public void setTriggerCount(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.triCount[idx] = i;
	}

	public int getChangeTarget(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.changeTarget[idx];
	}

	public void setChangeTarget(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.changeTarget[idx] = i;
	}

	public int getRange(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.range[idx];
	}

	public void setRange(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.range[idx] = i;
	}

	public int getAreaWidth(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.areaWidth[idx];
	}

	public void setAreaWidth(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.areaWidth[idx] = i;
	}

	public int getAreaHeight(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.areaHeight[idx];
	}

	public void setAreaHeight(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.areaHeight[idx] = i;
	}

	public int getLeverage(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.leverage[idx];
	}

	public void setLeverage(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.leverage[idx] = i;
	}

	public int getSkillId(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.skillId[idx];
	}

	public void setSkillId(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.skillId[idx] = i;
	}

	public int getGfxid(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.gfxid[idx];
	}

	public void setGfxid(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.gfxid[idx] = i;
	}

	public int getActid(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.actid[idx];
	}

	public void setActid(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.actid[idx] = i;
	}

	public int getSummon(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.summon[idx];
	}

	public void setSummon(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.summon[idx] = i;
	}

	public int getSummonMin(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.summonMin[idx];
	}

	public void setSummonMin(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.summonMin[idx] = i;
	}

	public int getSummonMax(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.summonMax[idx];
	}

	public void setSummonMax(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.summonMax[idx] = i;
	}

	public int getPolyId(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.polyId[idx];
	}

	public void setPolyId(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.polyId[idx] = i;
	}

	public int getReuseDelay(final int idx) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return 0;
		}
		return this.reuseDelay[idx];
	}

	public void setReuseDelay(final int idx, final int i) {
		if (idx < 0 || idx >= this.getSkillSize()) {
			return;
		}
		this.reuseDelay[idx] = i;
	}
}
