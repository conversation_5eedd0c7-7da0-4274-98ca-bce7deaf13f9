package com.lineage.server.timecontroller.event;

import java.util.Collection;
import com.lineage.server.serverpackets.S_NpcChatShouting;
import com.lineage.server.model.Instance.L1GamblingInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.S_NpcChat;
import com.lineage.server.model.Instance.L1GamInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.Instance.L1DoorInstance;
import java.util.Iterator;
import com.lineage.server.templates.L1Gambling;
import com.lineage.data.npc.event.GamblingNpc;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.server.timecontroller.server.ServerRestartTimer;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.data.event.GamblingSet;
import com.lineage.server.datatables.lock.GamblingReading;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import java.util.Random;
import com.lineage.data.event.gambling.Gambling;
import org.apache.commons.logging.Log;
import java.util.TimerTask;
import com.lineage.server.serverpackets.S_NPCPack;

/**
 * 賭狗系統時序控制
 * 包含開局、倒數、廣播、開獎、同步等流程
 * 已加強流程註解與可讀性
 */
public class GamblingTime extends TimerTask {
	private static final Log _log;
	private static Gambling _gambling;
	private static double rate;
	private static boolean _issystem;
	private static boolean _isStart;
	private static boolean _isBettingStarted;
	private static int _gamblingNo;
	private static Random _random;
	private ScheduledFuture<?> _timer;
	private static final String[] _msg;
	public static boolean _canBetting; // 可否下注

	static {
		_log = LogFactory.getLog(GamblingTime.class);
		rate = 0.0;
		_issystem = true;
		_isStart = false;
		_isBettingStarted = false;
		_gamblingNo = 100;
		_random = new Random();
		_msg = new String[] { "剛剛喝到的是綠水嗎?等等跑就知道~^^~", "隔壁跑道聽說昨天踩到釘子.....", "買我啦!!看我的臉就知道我贏!!", "快點跑完我也想去打一下副本~~",
				"你在看我嗎??你可以在靠近一點...", "飛龍都不一定跑贏我!看我強壯的雞腿!", "那個誰誰誰!!等等不要跑超過我黑...", "有沒騎士在場阿?給瓶勇水喝喝~~", "地球是很危險的...",
				"誰給我來一下祝福!加持!加持~", "咦~~有一個參賽者是傳說中的跑道之王...", "沒事!沒事!!哥只是個傳說~~", "隔壁的~你剛剛喝什麼?你是不是作弊??", "肚子好餓...沒吃飯能贏嗎??",
				"哇靠~~今天感覺精力充沛耶!!", "隔壁的!!你控制一下不要一直放屁!!", "嗯......嗯......其他幾個是憋三,我會贏....", "我剛剛好像喝多了...頭還在暈...",
				"昨晚的妞真正丫，喝綠水算三小。", "肚子餓死了，跑不動了。", "輸贏都行啦，娛樂而已。", "小賭怡情，大賭傷身。", "我要放點水。經常贏都有點不好意思了。",
				"【強化勇氣的藥水】是幹嘛的？我這有一罐。", "昨晚被吵死了，現在都覺得好累。", "阿幹....不要看我啦!!會影響我心情!!", "說什麼呢~~你們不想我贏阿!!!",
				"小賭可以養家活口!!大賭可以興邦建國!!", "賭是不好的....不賭是萬萬不行的...." };
	}

	public void start() {
		GamblingTime._gamblingNo = GamblingReading.get().maxId();
		final int timeMillis = GamblingSet.GAMADENATIME * 60 * 1000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, timeMillis, timeMillis);
	}

	public static int get_gamblingNo() {
		return GamblingTime._gamblingNo;
	}

	public static Gambling get_gambling() {
		return GamblingTime._gambling;
	}

	public static boolean isStart() {
		return GamblingTime._isStart;
	}

	public static boolean isBettingStarted() {
		return GamblingTime._isBettingStarted;
	}

	public static boolean canBetting() {
		return GamblingTime._canBetting;
	}

	public static void set_status(final boolean b) {
		GamblingTime._issystem = b;
	}

	@Override
	public void run() {
		try {
			// 在開始新一輪之前，先清理上一輪的資源
			if (GamblingTime._gambling != null) {
				GamblingTime._gambling.delAllNpc();
				GamblingTime._gambling.clear();
				GamblingTime._gambling = null;
			}

			if (GamblingTime._issystem) {
				if (!ServerRestartTimer.isRtartTime()) {
					// 重置下注開始標記
					GamblingTime._isBettingStarted = false;
					
					// 在開始新比賽前，先清除上一場的NPC（如果存在）
					this.doorOpen(false);
					final long previous = 0L;
					final Iterator<L1PcInstance> iter = World.get().getAllPlayers().iterator();
					while (iter.hasNext()) {
						final L1PcInstance listner = iter.next();
						if (!listner.isShowWorldChat()) {
							continue;
						}
						listner.sendPackets(new S_ServerMessage("奇岩賭場比賽即將開始"));
					}
					
					// 創建新的Gambling實例，但不立即生成NPC
					GamblingTime._gambling = new Gambling();
					boolean isGameRunning = true;
					int timeS = 300; // 5分鐘 = 300秒
					boolean npcReady = false;
					GamblingTime._canBetting = true; // 開放下注
					
					while (isGameRunning) {
						Thread.sleep(1000L);
						switch (timeS) {
						case 90: // 開跑前90秒生成NPC
							GamblingTime._gambling.set_gmaNpc(previous);
							npcReady = true;
							this.npcChat(0, 7, null);
							break;
						case 10: // 開跑前10秒停止下注
							GamblingTime._canBetting = false;
							// 10秒時強制刷新NPC顯示
							if (GamblingTime._gambling != null) {
								for (final GamblingNpc gamblingNpc : GamblingTime._gambling.get_allNpc().values()) {
									if (gamblingNpc != null && gamblingNpc.get_npc() != null) {
										gamblingNpc.get_npc().broadcastPacketAll(new S_NPCPack(gamblingNpc.get_npc()));
									}
								}
							}
							this.npcChat(timeS, 1, null);
							break;
						case 120:
						case 60:
							// 原有的喊話邏輯可以保留或移除
							break;
						case 1: {
							// 1秒時標記比賽開始
							GamblingTime._isStart = true;
							this.npcChat(0, 2, null);
							break;
						}
						case 0: {
							this.doorOpen(true);
							this.npcChat(0, 3, null);
							if (npcReady) {
								GamblingTime._gambling.startGam(); // 起跑
								npcReady = false;
							}
							isGameRunning = false;
							break;
						}
						case 180:
						case 240:
						case 300: {
							this.npcChat(timeS / 60, 0, null);
							break;
						}
						}
						--timeS;
					}
					
					// 等待比賽結束
					GamblingTime._gambling.set_allRate();
					Thread.sleep(2000L);
					final Iterator<GamblingNpc> iterator = GamblingTime._gambling.get_allNpc().values().iterator();
					while (iterator.hasNext()) {
						final GamblingNpc gamblingNpc = iterator.next();
						this.npcChat(0, 4, gamblingNpc);
						Thread.sleep(1000L);
					}
					while (GamblingTime._gambling.get_oneNpc() == null) {
						Thread.sleep(100L);
					}
					if (GamblingTime._gambling.get_oneNpc() != null) {
						final GamblingNpc one = GamblingTime._gambling.get_oneNpc();
						this.npcChat(0, 5, one);
						final int npcid = GamblingTime._gambling.get_oneNpc().get_npc().getNpcId();
						final double rate = GamblingTime._gambling.get_oneNpc().get_rate();
						final long adena = GamblingTime._gambling.get_allAdena();
						final int outcount = (int) (GamblingTime._gambling.get_oneNpc().get_adena()
								/ GamblingSet.GAMADENA);
						final L1Gambling gambling = new L1Gambling();
						gambling.set_id(GamblingTime._gamblingNo);
						gambling.set_adena(adena);
						gambling.set_rate(rate);
						final String gamblingno = String.valueOf(GamblingTime._gamblingNo) + "-" + npcid;
						gambling.set_gamblingno(gamblingno);
						gambling.set_outcount(outcount);
						
						GamblingReading.get().add(gambling);
					}
					synchronized (this) {
						++GamblingTime._gamblingNo;
					}
					
					// 比賽結束後等待30秒才開始賣下一輪彩票
					int waitTime = 30; // 30秒等待時間
					while (waitTime > 0) {
						Thread.sleep(1000L);
						// 每10秒讓NPC說一次話
						if (waitTime % 10 == 0) {
							this.npcChat(waitTime, 6, null); // 使用新的說話模式6
						}
						--waitTime;
					}
					
					// 30秒後開始賣下一輪彩票
					GamblingTime._isBettingStarted = true;
					_log.info("比賽結束30秒後，開始賣下一輪彩票");
					
					// 注意：這裡不清除NPC，讓它們保持在終點位置
					// 只有在下一場比賽開始時才會清除
				}
			}
			return;
		} catch (Exception e) {
			GamblingTime._log.error("奇岩賭場時間軸異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final GamblingTime timerTask = new GamblingTime();
			timerTask.start();
			return;
		} finally {
			// 確保比賽狀態正確重置
			GamblingTime._isStart = false;
		}
	}

	private void doorOpen(final boolean isOpen) {
		L1DoorInstance.openGam(isOpen);
	}

	private void npcChat(final int i, final int mode, final GamblingNpc gamblingNpc) {
		final Collection<L1Object> allObj = World.get().getObject();
		final Iterator<L1Object> iterator = allObj.iterator();
		while (iterator.hasNext()) {
			final L1Object obj = iterator.next();
			if (!(obj instanceof L1GamInstance)) {
				continue;
			}
			final L1GamInstance npc = (L1GamInstance) obj;
			switch (mode) {
			case 0: {
				if (GamblingTime._random.nextInt(100) + 1 >= 80) {
					continue;
				}
				final String msg = GamblingTime._msg[GamblingTime._random.nextInt(GamblingTime._msg.length)];
				npc.broadcastPacketX10(new S_NpcChat(npc, msg));
			}
			default: {
				continue;
			}
			}
		}
		
		final Iterator<L1Object> iterator2 = allObj.iterator();
		while (iterator2.hasNext()) {
			final L1Object obj = iterator2.next();
			if (!(obj instanceof L1GamblingInstance)) {
				continue;
			}
			final L1GamblingInstance npc2 = (L1GamblingInstance) obj;
			final int npcId = npc2.getNpcId();
			
			switch (mode) {
			case 0: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] startMessages = {
						"$376 " + i + "$377",
						"比賽即將開始，大家準備好了嗎？",
						"今天的天氣真適合比賽呢！",
						"讓我們來一場精彩的比賽吧！",
						"各位觀眾，比賽馬上開始！",
						"歡迎來到奇岩賭場！今天的比賽即將開始！",
						"各位賭客們，準備好下注了嗎？",
						"今天的賽道狀況很好，選手們都躍躍欲試！",
						"讓我們一起見證奇蹟的時刻！",
						"賭場的氣氛真是熱鬧啊！",
						"各位，準備好迎接刺激的比賽了嗎？",
						"今天的選手們看起來都很強呢！",
						"讓我們為今天的冠軍歡呼吧！",
						"賭場的燈光已經準備就緒！",
						"各位觀眾，請準備好您的賭注！"
					};
					String message = startMessages[GamblingTime._random.nextInt(startMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			case 1: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] countdownMessages = {
						String.valueOf(i),
						"還有 " + i + " 秒！",
						"倒數 " + i + " 秒！",
						"準備！還有 " + i + " 秒！",
						"最後 " + i + " 秒！",
						"緊張時刻！還有 " + i + " 秒！",
						"選手們準備！" + i + " 秒後開始！",
						"倒數計時！" + i + " 秒！",
						"最後衝刺準備！" + i + " 秒！",
						"比賽即將開始！" + i + " 秒！",
						"各位準備！" + i + " 秒後見真章！",
						"緊張刺激的 " + i + " 秒！",
						"倒數 " + i + " 秒，準備開始！",
						"最後 " + i + " 秒，選手就位！",
						"激動人心的 " + i + " 秒！",
						"比賽倒數！" + i + " 秒！"
					};
					String message = countdownMessages[GamblingTime._random.nextInt(countdownMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			case 2: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] startRaceMessages = {
						"$363",
						"比賽開始！",
						"衝啊！",
						"讓我們開始吧！",
						"比賽正式開始！",
						"選手們衝出去了！",
						"比賽開始！選手們奮力向前！",
						"衝刺開始！讓我們看看誰會贏！",
						"比賽開始！緊張刺激的時刻到了！",
						"選手們如箭般衝出起點！",
						"比賽開始！讓我們為選手加油！",
						"衝啊！選手們！",
						"比賽正式開始！緊張時刻！",
						"選手們開始衝刺！",
						"比賽開始！讓我們見證奇蹟！",
						"衝啊！今天的冠軍會是誰？"
					};
					String message = startRaceMessages[GamblingTime._random.nextInt(startRaceMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			case 3: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] raceMessages = {
						"$364",
						"比賽進行中！",
						"加油！加油！",
						"精彩刺激的比賽！",
						"誰會是今天的冠軍呢？",
						"比賽正激烈進行中！",
						"選手們的表現真是精彩！",
						"緊張刺激的比賽現場！",
						"讓我們為選手們加油！",
						"比賽進行得如火如荼！",
						"選手們的實力都很強！",
						"精彩的比賽正在進行！",
						"讓我們看看誰會脫穎而出！",
						"比賽現場氣氛熱烈！",
						"選手們都在奮力衝刺！",
						"緊張刺激的比賽時刻！",
						"讓我們為今天的冠軍歡呼！",
						"比賽進行得相當激烈！",
						"選手們的表現令人驚嘆！",
						"讓我們一起見證奇蹟！",
						"比賽現場真是熱鬧非凡！"
					};
					String message = raceMessages[GamblingTime._random.nextInt(raceMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			case 4: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					if (gamblingNpc != null) {
						String[] participantMessages = {
							"參賽者：" + gamblingNpc.get_npc().getNameId(),
							"選手：" + gamblingNpc.get_npc().getNameId() + " 準備就緒！",
							"歡迎 " + gamblingNpc.get_npc().getNameId() + " 參賽！",
							"選手 " + gamblingNpc.get_npc().getNameId() + " 登場！",
							"讓我們歡迎 " + gamblingNpc.get_npc().getNameId() + "！",
							"參賽選手：" + gamblingNpc.get_npc().getNameId() + " 準備完畢！",
							"歡迎選手 " + gamblingNpc.get_npc().getNameId() + " 加入比賽！",
							"選手 " + gamblingNpc.get_npc().getNameId() + " 已經就位！",
							"讓我們為 " + gamblingNpc.get_npc().getNameId() + " 加油！",
							"參賽者 " + gamblingNpc.get_npc().getNameId() + " 準備就緒！",
							"選手 " + gamblingNpc.get_npc().getNameId() + " 登場亮相！",
							"歡迎 " + gamblingNpc.get_npc().getNameId() + " 加入這場比賽！",
							"選手 " + gamblingNpc.get_npc().getNameId() + " 已經準備好了！",
							"讓我們歡迎選手 " + gamblingNpc.get_npc().getNameId() + "！",
							"參賽者 " + gamblingNpc.get_npc().getNameId() + " 準備完畢！"
						};
						String message = participantMessages[GamblingTime._random.nextInt(participantMessages.length)];
						npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					}
					continue;
				}
				continue;
			}
			case 5: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					if (gamblingNpc != null) {
						final String onename = gamblingNpc.get_npc().getNameId();
						String[] winnerMessages = {
							"$375 " + GamblingTime._gamblingNo + "優勝者: " + onename + " [賠率]:" + GamblingTime._gambling.get_oneNpc().get_rate(),
							"恭喜 " + onename + " 獲得冠軍！",
							"第 " + GamblingTime._gamblingNo + " 場比賽冠軍：" + onename + "！",
							"優勝者是 " + onename + "！恭喜！",
							"今天的冠軍是 " + onename + "！",
							"恭喜選手 " + onename + " 獲得冠軍！",
							"第 " + GamblingTime._gamblingNo + " 場比賽的優勝者是 " + onename + "！",
							"讓我們恭喜 " + onename + " 獲得冠軍！",
							"今天的冠軍選手是 " + onename + "！",
							"恭喜 " + onename + " 成為今天的冠軍！",
							"第 " + GamblingTime._gamblingNo + " 場比賽冠軍誕生了！是 " + onename + "！",
							"優勝者誕生！恭喜 " + onename + "！",
							"今天的冠軍選手 " + onename + " 實至名歸！",
							"恭喜 " + onename + " 獲得第 " + GamblingTime._gamblingNo + " 場比賽冠軍！",
							"冠軍誕生！恭喜選手 " + onename + "！",
							"今天的優勝者是 " + onename + "！恭喜！",
							"第 " + GamblingTime._gamblingNo + " 場比賽結束！冠軍是 " + onename + "！",
							"恭喜 " + onename + " 成為今天的冠軍選手！",
							"優勝者誕生！恭喜 " + onename + " 獲得冠軍！",
							"今天的冠軍選手 " + onename + " 表現出色！"
						};
						String message = winnerMessages[GamblingTime._random.nextInt(winnerMessages.length)];
						npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					}
				}
				continue;
			}
			case 6: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] waitingMessages = {
						"比賽結束了！請稍等一下，下一場比賽即將開始！",
						"精彩的比賽結束了！請耐心等待下一場！",
						"比賽結果已經出來了！下一場比賽很快就會開始！",
						"請稍等一下，我們正在準備下一場比賽！",
						"比賽結束了！下一場比賽即將開始，請耐心等待！",
						"精彩的比賽結束了！下一場比賽很快就會開始！",
						"請稍等一下，我們正在準備下一場精彩的比賽！",
						"比賽結束了！下一場比賽即將開始，敬請期待！",
						"請耐心等待，下一場比賽很快就會開始！",
						"比賽結束了！下一場比賽即將開始，請稍等一下！",
						"精彩的比賽結束了！下一場比賽很快就會開始！",
						"請稍等一下，我們正在準備下一場比賽！",
						"比賽結束了！下一場比賽即將開始，敬請期待！",
						"請耐心等待，下一場比賽很快就會開始！",
						"比賽結束了！下一場比賽即將開始，請稍等一下！"
					};
					String message = waitingMessages[GamblingTime._random.nextInt(waitingMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			case 7: {
				if (npcId == 70041 || npcId == 70042 || npcId == 70043 || npcId == 70044 || npcId == 70045) {
					String[] entranceMessages = {
						"各位觀眾！選手們已經登場了！",
						"歡迎來到奇岩賭場！選手們準備就緒！",
						"各位賭客們！選手們已經就位！",
						"比賽即將開始！選手們已經登場！",
						"各位觀眾！讓我們歡迎今天的選手們！",
						"奇岩賭場的選手們已經準備就緒！",
						"各位賭客們！選手們已經登場亮相！",
						"比賽即將開始！選手們已經就位！",
						"各位觀眾！選手們已經準備就緒！",
						"奇岩賭場的選手們已經登場！",
						"各位賭客們！選手們已經就位！",
						"比賽即將開始！選手們已經準備就緒！",
						"各位觀眾！選手們已經登場亮相！",
						"奇岩賭場的選手們已經就位！",
						"各位賭客們！選手們已經準備就緒！",
						"比賽即將開始！選手們已經登場！",
						"各位觀眾！選手們已經就位！",
						"奇岩賭場的選手們已經準備就緒！",
						"各位賭客們！選手們已經登場！",
						"比賽即將開始！選手們已經就位！"
					};
					String message = entranceMessages[GamblingTime._random.nextInt(entranceMessages.length)];
					npc2.broadcastPacketX10(new S_NpcChatShouting(npc2, message));
					continue;
				}
				continue;
			}
			default: {
				continue;
			}
			}
		}
	}
}
