package com.lineage.server.timecontroller.server;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ItemStatus;
import com.lineage.server.datatables.sql.CharWeaponTimeTable;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.templates.L1Pet;
import com.lineage.server.model.L1Object;
import com.lineage.server.datatables.lock.CharItemsReading;
import com.lineage.server.world.WorldPet;
import com.lineage.server.datatables.lock.PetReading;
import com.lineage.server.datatables.lock.CharItemsTimeReading;
import com.lineage.server.datatables.sql.CharItemsTimeTable;
import com.lineage.server.datatables.ItemVIPTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.sql.Timestamp;
import com.lineage.server.world.WorldItem;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class ServerItemUserTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(ServerItemUserTimer.class);
	}

	public void start() {
		final int timeMillis = 60000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, 60000L, 60000L);
	}

	@Override
	public void run() {
		try {
			final Collection<L1ItemInstance> items = WorldItem.get().all();
			if (items.isEmpty()) {
				return;
			}
			final Timestamp ts = new Timestamp(System.currentTimeMillis());
			final Iterator<L1ItemInstance> iter = items.iterator();
			while (iter.hasNext()) {
				final L1ItemInstance item = iter.next();
				if (item.get_time() == null) {
					continue;
				}
				if (item.get_magic_weapon() != null && item.get_magic_weapon().getMaxUseTime() > 0) {
					final Timestamp date = item.get_time();
					if (date != null && date.before(ts)) {
						checkItem2(item, ts);
						continue;
					}
				}
				checkItem(item, ts);
				Thread.sleep(5L);
			}
		} catch (Exception e) {
			ServerItemUserTimer._log.error("物品使用期限计时时间轴异常重启", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final ServerItemUserTimer userTimer = new ServerItemUserTimer();
			userTimer.start();
		}
	}

	private static void checkItem(final L1ItemInstance item, final Timestamp ts) throws Exception {
		try {
			if (item.get_time().before(ts)) {
				final L1Object object = World.get().findObject(item.get_char_objid());
				if (object != null) {
					if (object instanceof L1PcInstance) {
						final L1PcInstance tgpc = (L1PcInstance) object;
						final int itemId = item.getItemId();
						if (item.isEquipped()) {
							tgpc.getInventory().setEquipped(item, false);
							Thread.sleep(1000L);
							if (ItemVIPTable.get().checkVIP(itemId)) {
								ItemVIPTable.get().deleItemVIP(tgpc, itemId);
								CharItemsTimeTable.delete(item.getId());
								CharItemsTimeReading.get().load();
							}
						}
						tgpc.getInventory().removeItem(item);
						Thread.sleep(500L);
						final L1Pet pet = PetReading.get().getTemplate(item.getId());
						if (pet != null) {
							final L1PetInstance tgpet = WorldPet.get().get(Integer.valueOf(pet.get_objid()));
							if (tgpet != null) {
								tgpet.dropItem();
								tgpet.deleteMe();
							}
						}
					}
				} else {
					CharItemsReading.get().deleteItem(item.get_char_objid(), item);
				}
			}
		} catch (Exception e) {
			ServerItemUserTimer._log.error(e.getLocalizedMessage(), e);
		}
	}

	private static void checkItem2(final L1ItemInstance item, final Timestamp ts) throws Exception {
		item.set_magic_weapon(null);
		item.set_time(null);
		CharWeaponTimeTable.delete(item.getId());
		final L1Object object = World.get().findObject(item.get_char_objid());
		if (object != null && object instanceof L1PcInstance) {
			final L1PcInstance tgpc = (L1PcInstance) object;
			tgpc.sendPackets(new S_ItemStatus(item));
		}
	}
}
