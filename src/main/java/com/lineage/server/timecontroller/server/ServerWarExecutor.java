package com.lineage.server.timecontroller.server;

import com.eric.StartCheckWarTime;
import com.lineage.config.Config;
import com.lineage.config.ConfigAlt;
import com.lineage.server.datatables.CastleWarGiftTable;
import com.lineage.server.datatables.DoorSpawnTable;
import com.lineage.server.datatables.lock.CastleReading;
import com.lineage.server.datatables.lock.CharOtherReading;
import com.lineage.server.model.Instance.*;
import com.lineage.server.model.*;
import com.lineage.server.serverpackets.S_PacketBox;
import com.lineage.server.serverpackets.S_PacketBoxWar;
import com.lineage.server.templates.L1Castle;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldWar;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;

public class ServerWarExecutor {
    private static final Log _log;
    private static ServerWarExecutor _instance;

    static {
        _log = LogFactory.getLog(ServerWarExecutor.class);
    }

    private final L1Castle[] _l1castle;
    private final Calendar[] _war_start_time;
    private final Calendar[] _war_end_time;
    private final boolean[] _is_now_war;
    private final String[] _castleName;

    private ServerWarExecutor() {
        _l1castle = new L1Castle[8];
        _war_start_time = new Calendar[8];
        _war_end_time = new Calendar[8];
        _is_now_war = new boolean[8];
        _castleName = new String[]{"肯特", "妖魔", "風木", "奇岩", "海音", "侏儒", "亞丁", "狄亞得要塞"};
        int i = 0;
        while (i < _l1castle.length) {
            _l1castle[i] = CastleReading.get().getCastleTable(i + 1);
            Calendar nowTime = getRealTime();
            Calendar oldTime = getRealTime();
            oldTime.setTime(_l1castle[i].getWarTime().getTime());
            _war_start_time[i] = _l1castle[i].getWarTime();
            oldTime.add(ConfigAlt.ALT_WAR_TIME_UNIT, ConfigAlt.ALT_WAR_TIME);
            if (StartCheckWarTime.getInstance().isActive(_l1castle[i].getId()) && nowTime.after(oldTime)) {
                if (i == 1) {
                    nowTime.add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL - 3);
                } else {
                    nowTime.add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL);
                }
                _l1castle[i].setWarTime(nowTime);
                CastleReading.get().updateCastle(_l1castle[i]);
            }
            (_war_end_time[i] = (Calendar) _l1castle[i].getWarTime().clone()).add(ConfigAlt.ALT_WAR_TIME_UNIT,
                    ConfigAlt.ALT_WAR_TIME);
            ++i;
        }
    }

    public static ServerWarExecutor get() {
        if (ServerWarExecutor._instance == null) {
            ServerWarExecutor._instance = new ServerWarExecutor();
        }
        return ServerWarExecutor._instance;
    }

    public Calendar getRealTime() {
        TimeZone _tz = TimeZone.getTimeZone(Config.TIME_ZONE);
        Calendar cal = Calendar.getInstance(_tz);
        return cal;
    }

    public boolean isNowWar(int castle_id) {
        return _is_now_war[castle_id - 1];
    }

    public void setWarTime(int castle_id, Calendar calendar) {
        _war_start_time[castle_id - 1] = (Calendar) calendar.clone();
    }

    public void setEndWarTime(int castle_id, Calendar calendar) {
        (_war_end_time[castle_id - 1] = (Calendar) calendar.clone()).add(ConfigAlt.ALT_WAR_TIME_UNIT,
                ConfigAlt.ALT_WAR_TIME);
    }

    public void checkCastleWar(L1PcInstance player) {
        int i = 0;
        while (i < 8) {
            if (_is_now_war[i]) {
                player.sendPackets(new S_PacketBoxWar(2, i + 1));
            }
            ++i;
        }
    }

    public int checkCastleWar() {
        int x = 0;
        int i = 0;
        while (i < 8) {
            if (_is_now_war[i]) {
                ++x;
            }
            ++i;
        }
        return x;
    }

    public boolean isNowWar() {
        return _is_now_war[0] || _is_now_war[1] || _is_now_war[2] || _is_now_war[3]
                || _is_now_war[4] || _is_now_war[5] || _is_now_war[6] || _is_now_war[7];
    }

    protected void checkWarTime() {
        try {
            int i = 0;
            while (i < 8) {
                Calendar now = getRealTime();
                if (_war_start_time[i].before(now) && _war_end_time[i].after(now)) {
                    if (!_is_now_war[i]) {
                        _is_now_war[i] = true;
                        CharOtherReading.get().tam();
                        L1WarSpawn warspawn = new L1WarSpawn();
                        warspawn.SpawnFlag(i + 1);
                        L1DoorInstance[] doorList;
                        int length = (doorList = DoorSpawnTable.get().getDoorList()).length;
                        int j = 0;
                        while (j < length) {
                            L1DoorInstance door = doorList[j];
                            if (L1CastleLocation.checkInWarArea(i + 1, door)) {
                                door.repairGate();
                            }
                            ++j;
                        }
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                        String time = sdf.format(_war_start_time[i].getTime());
                        ServerWarExecutor._log.info(String.valueOf(_castleName[i]) + " 的攻城戰開始。時間: " + time);
                        World.get().broadcastPacketToAll(new S_PacketBoxWar(0, i + 1));
                        int[] loc = new int[3];
                        Iterator<L1PcInstance> iterator = World.get().getAllPlayers().iterator();
                        while (iterator.hasNext()) {
                            L1PcInstance pc = iterator.next();
                            int castleId = i + 1;
                            if (L1CastleLocation.checkInWarArea(castleId, pc) && !pc.isGm()) {
                                L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                                if (clan != null && clan.getCastleId() == castleId) {
                                    continue;
                                }
                                loc = L1CastleLocation.getGetBackLoc(castleId);
                                L1Teleport.teleport(pc, loc[0], loc[1], (short) loc[2], 5, true);
                            }
                        }
                    }
                } else if (_war_end_time[i].before(now) && _is_now_war[i]) {
                    _is_now_war[i] = false;
                    World.get().broadcastPacketToAll(new S_PacketBoxWar(1, i + 1));
                    if (i == 1) {
                        _war_start_time[i].add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL - 3);
                        _war_end_time[i].add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL - 3);
                    } else {
                        _war_start_time[i].add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL);
                        _war_end_time[i].add(ConfigAlt.ALT_WAR_INTERVAL_UNIT, ConfigAlt.ALT_WAR_INTERVAL);
                    }
                    _l1castle[i].setTaxRate(10);
                    _l1castle[i].setPublicMoney(0L);
                    CastleReading.get().updateCastle(_l1castle[i]);
                    int castle_id = i + 1;
                    List<L1War> list = WorldWar.get().getWarList();
                    Iterator<L1War> iterator2 = list.iterator();
                    while (iterator2.hasNext()) {
                        L1War war = iterator2.next();
                        if (war.get_castleId() == castle_id) {
                            war.ceaseCastleWar();
                        }
                    }
                    Iterator<L1Object> iterator3 = World.get().getObject().iterator();
                    while (iterator3.hasNext()) {
                        L1Object l1object = iterator3.next();
                        if (l1object instanceof L1FieldObjectInstance) {
                            L1FieldObjectInstance flag = (L1FieldObjectInstance) l1object;
                            if (L1CastleLocation.checkInWarArea(castle_id, flag)) {
                                flag.deleteMe();
                            }
                        }
                        if (l1object instanceof L1CrownInstance) {
                            L1CrownInstance crown = (L1CrownInstance) l1object;
                            if (L1CastleLocation.checkInWarArea(castle_id, crown)) {
                                crown.deleteMe();
                            }
                        }
                        if (l1object instanceof L1TowerInstance) {
                            L1TowerInstance tower = (L1TowerInstance) l1object;
                            if (L1CastleLocation.checkInWarArea(castle_id, tower)) {
                                tower.deleteMe();
                            }
                        }
                        if (l1object instanceof L1CatapultInstance) {
                            L1CatapultInstance catapult = (L1CatapultInstance) l1object;
                            if (!L1CastleLocation.checkInWarArea(castle_id, catapult)) {
                                continue;
                            }
                            catapult.deleteMe();
                        }
                    }
                    L1WarSpawn warspawn2 = new L1WarSpawn();
                    warspawn2.spawnTower(castle_id);
                    warspawn2.SpawnCatapult(castle_id);
                    L1DoorInstance[] doorList2;
                    int length2 = (doorList2 = DoorSpawnTable.get().getDoorList()).length;
                    int k = 0;
                    while (k < length2) {
                        L1DoorInstance door2 = doorList2[k];
                        if (L1CastleLocation.checkInWarArea(castle_id, door2)) {
                            door2.repairGate();
                        }
                        ++k;
                    }
                    World.get().broadcastPacketToAll(new S_PacketBoxWar());
                    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
                    String time2 = sdf2.format(now.getTime());
                    ServerWarExecutor._log.info(String.valueOf(_castleName[i]) + " 的攻城戰結束。時間: " + time2);
                    CastleWarGiftTable.get().get_gift(castle_id);
                }
                ++i;
            }
        } catch (Exception e) {
            ServerWarExecutor._log.error(e.getLocalizedMessage(), e);
        }
    }

    public void sendIcon(int castle_id, L1PcInstance pc) {
        pc.sendPackets(new S_PacketBox(153,
                (int) ((_war_end_time[castle_id - 1].getTimeInMillis() - getRealTime().getTimeInMillis())
                        / 1000L)));
    }
}
