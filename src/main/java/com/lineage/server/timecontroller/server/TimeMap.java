package com.lineage.server.timecontroller.server;

import com.lineage.server.datatables.lock.CharMapTimeReading;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.serverpackets.S_PacketBoxGame;
import com.lineage.server.serverpackets.S_PacketBox;
import java.util.Iterator;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.TimerTask;

public class TimeMap extends TimerTask {
	CopyOnWriteArrayList<L1PcInstance> pclist;
	int mapid;
	int id;
	int maptime;
	int locx;
	int locy;
	int teleportmapid;
	String mapName;
	private static final Log _log;

	static {
		_log = LogFactory.getLog(TimeMap.class);
	}

	public TimeMap() {
		this.pclist = new CopyOnWriteArrayList();
	}

	public int getId() {
		return this.id;
	}

	public void setId(final int id) {
		this.id = id;
	}

	public String getMapName() {
		return this.mapName;
	}

	public void setMapName(final String mapName) {
		this.mapName = mapName;
	}

	public int getMapid() {
		return this.mapid;
	}

	public void setMapid(final int mapid) {
		this.mapid = mapid;
	}

	public int getMaptime() {
		return this.maptime;
	}

	public void setMaptime(final int maptime) {
		this.maptime = maptime;
	}

	public int getLocx() {
		return this.locx;
	}

	public void setLocx(final int locx) {
		this.locx = locx;
	}

	public int getLocy() {
		return this.locy;
	}

	public void setLocy(final int locy) {
		this.locy = locy;
	}

	public int getTeleportmapid() {
		return this.teleportmapid;
	}

	public void setTeleportmapid(final int teleportmapid) {
		this.teleportmapid = teleportmapid;
	}

	@Override
	public void run() {
		try {
			final Iterator<L1PcInstance> iterator = this.pclist.iterator();
			while (iterator.hasNext()) {
				final L1PcInstance _pc = iterator.next();
				if (_pc.isDead()) {
					return;
				}
				short map = _pc.getMapId();
				if (map >= 4001 && map <= 4050) {
					map = 4001;
				}
				if (map == this.mapid) {
					if (_pc.isTimeMap()) {
						final int time = _pc.getMapTime(map);
						if (time >= this.maptime) {
							_pc.stopTimeMap();
							this.showTime(_pc, 2);
							this.deluser(_pc);
							_pc.sendPackets(new S_SystemMessage("你已經達到此地圖的限制時間"));
							this.teleport(_pc);
							return;
						}
						_pc.updateMapTime(1);
						this.showTime(_pc, 1);
					} else {
						_pc.setTimeMap(true);
					}
				} else {
					this.showTime(_pc, 2);
					_pc.stopTimeMap();
					this.deluser(_pc);
				}
			}
		} catch (Throwable e) {
			TimeMap._log.error(e.getLocalizedMessage(), e);
		}
	}

	private void showTime(final L1PcInstance _pc, final int b) {
		if (this.mapid == 4001) {
			return;
		}
		if (b == 1) {
			final int time = _pc.getMapTime(_pc.getMapId());
			_pc.sendPackets(new S_PacketBox(153, this.maptime - time, null));
		} else if (b == 2) {
			_pc.sendPackets(new S_PacketBoxGame(72));
		}
	}

	private void teleport(final L1PcInstance _pc) {
		L1Teleport.teleport(_pc, this.locx, this.locy, (short) this.teleportmapid, 5, true);
	}

	public void adduser(final L1PcInstance _pc) {
		final Iterator<L1PcInstance> iterator = this.pclist.iterator();
		while (iterator.hasNext()) {
			final L1PcInstance pc = iterator.next();
			if (pc.getId() == _pc.getId()) {
				return;
			}
		}
		this.pclist.add(_pc);
	}

	private void deluser(final L1PcInstance _pc) {
		int i = 0;
		while (i < this.pclist.size()) {
			if (this.pclist.get(i).getId() == _pc.getId()) {
				if (this.mapid != 4001) {
					CharMapTimeReading.get().update(_pc.getId(), this.mapid, _pc.getMapTime(this.mapid));
				}
				this.pclist.remove(i);
			}
			++i;
		}
	}
}
