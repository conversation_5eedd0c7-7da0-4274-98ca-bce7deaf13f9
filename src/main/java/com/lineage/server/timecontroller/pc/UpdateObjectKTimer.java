package com.lineage.server.timecontroller.pc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.WorldKnight;
import com.lineage.server.thread.PcOtherThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class UpdateObjectKTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(UpdateObjectKTimer.class);
	}

	public void start() {
		final int timeMillis = 350;
		this._timer = PcOtherThreadPool.get().scheduleAtFixedRate(this, 350L, 350L);
	}

	@Override
	public void run() {
		try {
			final Collection<L1PcInstance> allPc = WorldKnight.get().all();
			if (allPc.isEmpty()) {
				return;
			}
			final Iterator<L1PcInstance> iter = allPc.iterator();
			while (iter.hasNext()) {
				final L1PcInstance tgpc = iter.next();
				if (UpdateObjectCheck.check(tgpc)) {
					tgpc.updateObject();
				}
			}
		} catch (Exception e) {
			UpdateObjectKTimer._log.error("Pc 可見物更新處理時間軸(騎士)異常重啟", e);
			PcOtherThreadPool.get().cancel(this._timer, false);
			final UpdateObjectKTimer objectKTimer = new UpdateObjectKTimer();
			objectKTimer.start();
		}
	}
}
