package com.lineage.server.timecontroller.pc;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.config.Config;
import java.util.Iterator;
import java.util.Collection;
import com.lineage.echo.ClientExecutor;
import com.lineage.list.OnlineUser;
import com.lineage.server.thread.PcOtherThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class PcAutoSaveTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(PcAutoSaveTimer.class);
	}

	public void start() {
		final int timeMillis = 60000;
		this._timer = PcOtherThreadPool.get().scheduleAtFixedRate(this, 60000L, 60000L);
	}

	@Override
	public void run() {
		try {
			final Collection<ClientExecutor> allClien = OnlineUser.get().all();
			if (allClien.isEmpty()) {
				return;
			}
			final Iterator<ClientExecutor> iter = allClien.iterator();
			while (iter.hasNext()) {
				final ClientExecutor client = iter.next();
				int time = client.get_savePc();
				if (time == -1) {
					continue;
				}
				--time;
				this.save(client, Integer.valueOf(time));
			}
		} catch (Exception e) {
			PcAutoSaveTimer._log.error("人物資料自動保存時間軸異常重啟", e);
			PcOtherThreadPool.get().cancel(this._timer, false);
			final PcAutoSaveTimer restart = new PcAutoSaveTimer();
			restart.start();
		}
	}

	private void save(final ClientExecutor client, final Integer time) {
		try {
			if (client.get_socket() == null) {
				return;
			}
			if (time.intValue() > 0) {
				client.set_savePc(time.intValue());
			} else {
				client.set_savePc(Config.AUTOSAVE_INTERVAL);
				final L1PcInstance pc = client.getActiveChar();
				if (pc != null) {
					pc.save();
				}
			}
		} catch (Exception e) {
			PcAutoSaveTimer._log.debug("執行人物資料存檔處理異常 帳號:" + client.getAccountName());
		}
	}
}
