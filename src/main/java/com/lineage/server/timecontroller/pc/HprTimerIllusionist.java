package com.lineage.server.timecontroller.pc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.WorldIllusionist;
import com.lineage.server.thread.PcOtherThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class HprTimerIllusionist extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(HprTimerIllusionist.class);
	}

	public void start() {
		final int timeMillis = 3000;
		this._timer = PcOtherThreadPool.get().scheduleAtFixedRate(this, 3000L, 3000L);
	}

	@Override
	public void run() {
		try {
			final Collection<L1PcInstance> allPc = WorldIllusionist.get().all();
			if (allPc.isEmpty()) {
				return;
			}
			final Iterator<L1PcInstance> iter = allPc.iterator();
			while (iter.hasNext()) {
				final L1PcInstance tgpc = iter.next();
				final HprExecutor hpr = HprExecutor.get();
				if (hpr.check(tgpc)) {
					hpr.checkRegenHp(tgpc);
					Thread.sleep(1L);
				}
			}
		} catch (Exception e) {
			HprTimerIllusionist._log.error("Pc(幻術) HP自然回復時間軸異常重啟", e);
			PcOtherThreadPool.get().cancel(this._timer, false);
			final HprTimerIllusionist hprIllusionist = new HprTimerIllusionist();
			hprIllusionist.start();
		}
	}
}
