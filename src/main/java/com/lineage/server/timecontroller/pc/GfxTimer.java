package com.lineage.server.timecontroller.pc;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.model.L1Trade;
import com.lineage.server.Shutdown;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.config.ConfigOther;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.server.thread.PcOtherThreadPool;
import java.util.concurrent.ScheduledFuture;
import java.util.TimerTask;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.william.ArmorSkillSound;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 裝備特效時序控制器優化版
 * 提供更好的效能管理和錯誤處理
 */
public class GfxTimer {
	private static final ConcurrentHashMap<Integer, ScheduledFuture<?>> _timerMap = new ConcurrentHashMap<>();
	private static int totalEffectsTriggered = 0;
	private static long lastMonitoringTime = System.currentTimeMillis();
	
	// 配置參數
	private static final long EFFECT_CHECK_INTERVAL = 2000L; // 特效檢查間隔 (毫秒)
	private static final int MAX_CONSECUTIVE_FAILURES = 5; // 最大連續失敗次數
	private static final int MAX_CONCURRENT_EFFECTS = 1000; // 最大同時特效數量
	private static final boolean ENABLE_PERFORMANCE_MONITORING = true; // 啟用效能監控
	private static final boolean ENABLE_DETAILED_ERROR_LOGGING = true; // 啟用詳細錯誤日誌
	private static final boolean ENABLE_VERBOSE_LOGGING = false; // 啟用詳細日誌

	/**
	 * 開始裝備特效計時器
	 */
	public static void startGfxTimer(L1PcInstance pc) {
		if (pc == null) {
			return;
		}
		
		try {
			if (_timerMap.containsKey(pc.getId())) {
				stopGfxTimer(pc);
			}
			
			if (_timerMap.size() >= MAX_CONCURRENT_EFFECTS) {
				System.out.println("警告: 達到最大同時特效數量限制: " + MAX_CONCURRENT_EFFECTS);
				return;
			}
			
			ScheduledFuture<?> timer = GeneralThreadPool.get().scheduleAtFixedRate(
				new GfxTimerTask(pc), 
				EFFECT_CHECK_INTERVAL,
				EFFECT_CHECK_INTERVAL
			);
			
			_timerMap.put(pc.getId(), timer);
			
			if (ENABLE_VERBOSE_LOGGING) {
				System.out.println("玩家 " + pc.getName() + " 裝備特效計時器已啟動");
			}
			
		} catch (Exception e) {
			if (ENABLE_DETAILED_ERROR_LOGGING) {
				System.err.println("啟動玩家 " + pc.getName() + " 裝備特效計時器失敗: " + e.getMessage());
				e.printStackTrace();
			} else {
				System.err.println("啟動玩家 " + pc.getName() + " 裝備特效計時器失敗: " + e.getMessage());
			}
		}
	}

	/**
	 * 停止裝備特效計時器
	 */
	public static void stopGfxTimer(L1PcInstance pc) {
		if (pc == null) {
			return;
		}
		
		try {
			ScheduledFuture<?> timer = _timerMap.remove(pc.getId());
			if (timer != null && !timer.isCancelled()) {
				timer.cancel(false);
				
				if (ENABLE_VERBOSE_LOGGING) {
					System.out.println("玩家 " + pc.getName() + " 裝備特效計時器已停止");
				}
			}
		} catch (Exception e) {
			if (ENABLE_DETAILED_ERROR_LOGGING) {
				System.err.println("停止玩家 " + pc.getName() + " 裝備特效計時器失敗: " + e.getMessage());
				e.printStackTrace();
			} else {
				System.err.println("停止玩家 " + pc.getName() + " 裝備特效計時器失敗: " + e.getMessage());
			}
		}
	}

	/**
	 * 檢查玩家是否有裝備特效計時器
	 */
	public static boolean hasGfxTimer(L1PcInstance pc) {
		if (pc == null) {
			return false;
		}
		
		ScheduledFuture<?> timer = _timerMap.get(pc.getId());
		return timer != null && !timer.isCancelled();
	}

	/**
	 * 清理所有計時器
	 */
	public static void clearAllTimers() {
		try {
			for (ScheduledFuture<?> timer : _timerMap.values()) {
				if (timer != null && !timer.isCancelled()) {
					timer.cancel(false);
				}
			}
			_timerMap.clear();
			System.out.println("所有裝備特效計時器已清理");
		} catch (Exception e) {
			if (ENABLE_DETAILED_ERROR_LOGGING) {
				System.err.println("清理裝備特效計時器失敗: " + e.getMessage());
				e.printStackTrace();
			} else {
				System.err.println("清理裝備特效計時器失敗: " + e.getMessage());
			}
		}
	}

	/**
	 * 獲取系統狀態資訊
	 */
	public static String getSystemStatus() {
		int activeTimers = 0;
		for (ScheduledFuture<?> timer : _timerMap.values()) {
			if (timer != null && !timer.isCancelled()) {
				activeTimers++;
			}
		}
		
		return String.format("裝備特效計時器狀態 - 活躍計時器: %d/%d, 總觸發次數: %d", 
						   activeTimers, _timerMap.size(), totalEffectsTriggered);
	}

	/**
	 * 裝備特效計時器任務
	 */
	private static class GfxTimerTask implements Runnable {
		private final L1PcInstance _pc;
		private int consecutiveFailures = 0;

		public GfxTimerTask(L1PcInstance pc) {
			this._pc = pc;
		}

		@Override
		public void run() {
			try {
				if (_pc == null || _pc.isDead() || _pc.getNetConnection() == null) {
					stopGfxTimer(_pc);
					return;
				}
				
				if (_pc.getNetConnection().get_socket() == null || _pc.getNetConnection().get_socket().isClosed()) {
					stopGfxTimer(_pc);
					return;
				}
				
				ArmorSkillSound.forArmorSkillSound(_pc);
				
				consecutiveFailures = 0;
				totalEffectsTriggered++;
				
				if (ENABLE_PERFORMANCE_MONITORING) {
					updatePerformanceMonitoring();
				}
				
			} catch (Exception e) {
				consecutiveFailures++;
				if (ENABLE_DETAILED_ERROR_LOGGING) {
					System.err.println("執行玩家 " + _pc.getName() + " 裝備特效時發生錯誤 (失敗次數: " + consecutiveFailures + "): " + e.getMessage());
					e.printStackTrace();
				} else {
					System.err.println("執行玩家 " + _pc.getName() + " 裝備特效時發生錯誤 (失敗次數: " + consecutiveFailures + "): " + e.getMessage());
				}
				
				if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
					System.out.println("警告: 玩家 " + _pc.getName() + " 裝備特效連續失敗 " + consecutiveFailures + " 次，停止計時器");
					stopGfxTimer(_pc);
				}
			}
		}
	}

	/**
	 * 更新效能監控
	 */
	private static void updatePerformanceMonitoring() {
		long currentTime = System.currentTimeMillis();
		if (currentTime - lastMonitoringTime > 60000) { // 每分鐘更新一次
			System.out.println("裝備特效效能監控 - " + getSystemStatus());
			lastMonitoringTime = currentTime;
		}
	}

	/**
	 * 觸發特效
	 */
	public static void triggerEffect(L1PcInstance pc) {
		if (pc == null) {
			return;
		}
		
		try {
			ArmorSkillSound.forArmorSkillSound(pc);
			totalEffectsTriggered++;
		} catch (Exception e) {
			System.err.println("觸發玩家 " + pc.getName() + " 特效失敗: " + e.getMessage());
		}
	}

	/**
	 * 重新載入系統
	 */
	public static void reloadSystem() {
		try {
			clearAllTimers();
			ArmorSkillSound.reload();
			System.out.println("裝備特效系統重新載入完成");
		} catch (Exception e) {
			System.err.println("重新載入裝備特效系統失敗: " + e.getMessage());
		}
	}
}
