package com.lineage.server.timecontroller.pet;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.world.WorldSummons;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class SummonMprTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;
	private static int _time;

	static {
		_log = LogFactory.getLog(SummonMprTimer.class);
		_time = 0;
	}

	public void start() {
		SummonMprTimer._time = 0;
		final int timeMillis = 1000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, 1000L, 1000L);
	}

	@Override
	public void run() {
		try {
			++SummonMprTimer._time;
			final Collection allPet = WorldSummons.get().all();
			if (allPet.isEmpty()) {
				return;
			}
			final Iterator iter = allPet.iterator();
			while (iter.hasNext()) {
				final L1SummonInstance summon = (L1SummonInstance) iter.next();
				if (MprPet.mpUpdate(summon, SummonMprTimer._time)) {
					Thread.sleep(5L);
				}
			}
		} catch (Exception e) {
			SummonMprTimer._log.error("Summon MP自然回復時間軸異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final SummonMprTimer summonMprTimer = new SummonMprTimer();
			summonMprTimer.start();
		}
	}
}
