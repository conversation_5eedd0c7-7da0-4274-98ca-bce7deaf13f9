package com.lineage.server.timecontroller.pet;

import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1DollInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class DollHprTimer extends TimerTask {
	private static final Log _log;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(DollHprTimer.class);
	}

	public void start() {
		final int timeMillis = 1000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, timeMillis, timeMillis);
	}

	@Override
	public void run() {
		try {
			final Collection<L1PcInstance> all = World.get().getAllPlayers();
			if (all.isEmpty()) {
				return;
			}
			final Iterator<L1PcInstance> iter = all.iterator();
			while (iter.hasNext()) {
				final L1PcInstance tgpc = iter.next();
				if (tgpc.get_doll_hpr_time_src() > 0 && checkErr(tgpc)) {
					final int newHp = tgpc.getCurrentHp() + tgpc.get_doll_hpr();
					tgpc.setCurrentHp(newHp);
					if (!tgpc.getDolls().isEmpty()) {
						final Iterator<L1DollInstance> iterator = tgpc.getDolls().values().iterator();
						while (iterator.hasNext()) {
							final L1DollInstance doll = iterator.next();
							doll.show_action(3);
						}
					}
					Thread.sleep(50L);
				}
			}
		} catch (Exception e) {
			DollHprTimer._log.error("魔法娃娃處理時間軸(HPR)異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final DollHprTimer dollTimer = new DollHprTimer();
			dollTimer.start();
		}
	}

	private static boolean checkErr(final L1PcInstance tgpc) {
		try {
			if (tgpc == null) {
				return false;
			}
			if (tgpc.getOnlineStatus() == 0) {
				return false;
			}
			if (tgpc.getNetConnection() == null) {
				return false;
			}
			if (tgpc.isTeleport()) {
				return false;
			}
			if (!tgpc.getHpRegeneration()) {
				return false;
			}
			if (tgpc.getCurrentHp() >= tgpc.getMaxHp()) {
				return false;
			}
			final int newtime = tgpc.get_doll_hpr_time() - 1;
			tgpc.set_doll_hpr_time(newtime);
			if (newtime <= 0) {
				tgpc.set_doll_hpr_time(tgpc.get_doll_hpr_time_src());
				return true;
			}
		} catch (Exception e) {
			return false;
		}
		return false;
	}
}
