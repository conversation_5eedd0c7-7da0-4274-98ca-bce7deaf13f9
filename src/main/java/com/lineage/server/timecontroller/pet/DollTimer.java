package com.lineage.server.timecontroller.pet;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_DoActionGFX;
import java.util.Iterator;
import java.util.Collection;
import com.lineage.server.model.Instance.L1DollInstance;
import com.lineage.server.world.WorldDoll;
import com.lineage.server.thread.GeneralThreadPool;
import org.apache.commons.logging.LogFactory;
import java.util.concurrent.ScheduledFuture;
import java.util.Random;
import org.apache.commons.logging.Log;
import java.util.TimerTask;

public class DollTimer extends TimerTask {
	private static final Log _log;
	private static Random _random;
	private ScheduledFuture<?> _timer;

	static {
		_log = LogFactory.getLog(DollTimer.class);
		_random = new Random();
	}

	public void start() {
		final int timeMillis = 30000;
		this._timer = GeneralThreadPool.get().scheduleAtFixedRate(this, timeMillis, timeMillis);
	}

	@Override
	public void run() {
		try {
			final Collection<L1DollInstance> allDoll = WorldDoll.get().all();
			if (allDoll.isEmpty()) {
				return;
			}
			final Iterator<?> iter = allDoll.iterator();
			while (iter.hasNext()) {
				final L1DollInstance doll = (L1DollInstance) iter.next();
				final int time = doll.get_time() - 30;
				if (time <= 0) {
					outDoll(doll);
				} else {
					doll.set_time(time);
					if (doll.isDead()) {
						continue;
					}
					this.checkAction(doll);
				}
				Thread.sleep(50L);
			}
		} catch (Exception e) {
			DollTimer._log.error("魔法娃娃處理時間軸異常重啟", e);
			GeneralThreadPool.get().cancel(this._timer, false);
			final DollTimer dollTimer = new DollTimer();
			dollTimer.start();
		}
	}

	private static void outDoll(final L1DollInstance doll) {
		try {
			if (doll != null) {
				if (doll.destroyed()) {
					return;
				}
				doll.deleteDoll();
			}
		} catch (Exception e) {
			DollTimer._log.error(e.getLocalizedMessage(), e);
		}
	}

	private void checkAction(final L1DollInstance doll) {
		try {
			if (doll.getX() == doll.get_olX() && doll.getY() == doll.get_olY()) {
				int actionCode;
				if (DollTimer._random.nextBoolean()) {
					actionCode = 66;
				} else {
					actionCode = 67;
				}
				doll.broadcastPacketAll(new S_DoActionGFX(doll.getId(), actionCode));
			}
			doll.set_olX(doll.getX());
			doll.set_olY(doll.getY());
		} catch (Exception e) {
			DollTimer._log.error(e.getLocalizedMessage(), e);
		}
	}
}
