package com.lineage.server.world;

import com.lineage.config.Config;
import com.lineage.server.datatables.DoorSpawnTable;
import com.lineage.server.datatables.MapsTable;
import com.lineage.server.model.Instance.*;
import com.lineage.server.model.L1GroundInventory;
import com.lineage.server.model.L1Location;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.map.L1Map;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.templates.L1Npc;
import com.lineage.server.types.Point;
import com.lineage.server.utils.collections.CTRAssignLock;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

public class World {
    private static final Log _log;
    //<PERSON> 丹 新增
    private static final Map<Integer, L1ItemInstance> pcFallingItemObjIdList = new ConcurrentHashMap<>();
    private static final int MAX_MAP_ID = 12000;
    private static World _instance;

    static {
        _log = LogFactory.getLog(World.class);
    }

    private final ConcurrentHashMap<String, L1PcInstance> _allPlayers;
    private final ConcurrentHashMap<Integer, L1Object> _allObjects;
    private final Map<Integer, ConcurrentHashMap<Integer, L1Object>> _visibleObjects;
    private Collection<L1Object> _allValues;
    private Collection<L1PcInstance> _allPlayerValues;
    private int _weather;
    private boolean _worldChatEnabled;
    private boolean _processingContributionTotal;

    public World() {
        _weather = 4;
        _worldChatEnabled = true;
        _processingContributionTotal = false;
        _allPlayers = new ConcurrentHashMap<>();
        _allObjects = new ConcurrentHashMap<>();
        _visibleObjects = new ConcurrentHashMap<>();
        Iterator<Integer> iterator = MapsTable.get().getMaps().keySet().iterator();
        while (iterator.hasNext()) {
            Integer mapid = iterator.next();
            ConcurrentHashMap<Integer, L1Object> map = new ConcurrentHashMap<>();
            _visibleObjects.put(mapid, map);
            CTRAssignLock.registerGrdIvnLock(mapid);
        }
        World._log.info("遊戲世界儲存中心建立完成!!!");
    }

    //Kevin 丹丹新增以下
    public static void addpcFallingItemObjIdList(L1ItemInstance item) {
        L1ItemInstance fallingItem = new L1ItemInstance(item.getItem(), item.getCount());
        fallingItem.set_char_objid(item.get_char_objid());
        pcFallingItemObjIdList.put(item.getId(), fallingItem);
    }

    public static boolean checkpcFallingItem(int objid) {
        return pcFallingItemObjIdList.containsKey(objid);
    }

    public static L1ItemInstance getPcFallingItem(int objid) {
        return pcFallingItemObjIdList.get(objid);
    }

    public static void removepcFallingItemObjId(int objid) {
        pcFallingItemObjIdList.remove(objid);
    }

    //Kevin 丹 以上
    public static World get() {
        if (World._instance == null) {
            World._instance = new World();
        }
        return World._instance;
    }

    private static boolean check(L1PcInstance tgpc) {
        try {
            if (tgpc == null) {
                return false;
            }
            if (tgpc.getOnlineStatus() == 0) {
                return false;
            }
            if (tgpc.getNetConnection() == null) {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    public Object getRegion(Object object) {
        return null;
    }

    public void clear() {
        World._instance = new World();
    }

    public void storeObject(L1Object object) {
        try {
            if (object == null) {
                throw new NullPointerException();
            }
            _allObjects.put(Integer.valueOf(object.getId()), object);
            if (object instanceof L1ItemInstance) {
                WorldItem.get().put(new Integer(object.getId()), (L1ItemInstance) object);
            }
            if (object instanceof L1PcInstance) {
                L1PcInstance pc = (L1PcInstance) object;
                if (pc.isCrown()) {
                    WorldCrown.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isKnight()) {
                    WorldKnight.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isElf()) {
                    WorldElf.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isWizard()) {
                    WorldWizard.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isDarkelf()) {
                    WorldDarkelf.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isDragonKnight()) {
                    WorldDragonKnight.get().put(new Integer(pc.getId()), pc);
                } else if (pc.isIllusionist()) {
                    WorldIllusionist.get().put(new Integer(pc.getId()), pc);
                }
                _allPlayers.put(pc.getName(), pc);
            }
            if (object instanceof L1DeInstance) {
                L1DeInstance de = (L1DeInstance) object;
                WorldDe.get().put(de.getNameId(), de);
            }
            if (object instanceof L1TrapInstance) {
                WorldTrap.get().put(new Integer(object.getId()), (L1TrapInstance) object);
            }
            if (object instanceof L1PetInstance) {
                WorldPet.get().put(new Integer(object.getId()), (L1PetInstance) object);
            }
            if (object instanceof L1SummonInstance) {
                WorldSummons.get().put(new Integer(object.getId()), (L1SummonInstance) object);
            }
            if (object instanceof L1DollInstance) {
                WorldDoll.get().put(new Integer(object.getId()), (L1DollInstance) object);
            }
            if (object instanceof L1EffectInstance) {
                WorldEffect.get().put(new Integer(object.getId()), (L1EffectInstance) object);
            }
            if (object instanceof L1MonsterInstance) {
                WorldMob.get().put(new Integer(object.getId()), (L1MonsterInstance) object);
            }
            if (object instanceof L1HierarchInstance) {
                WorldHierarch.get().put(new Integer(object.getId()), (L1HierarchInstance) object);
            }
            if (object instanceof L1BowInstance) {
                WorldBow.get().put(new Integer(object.getId()), (L1BowInstance) object);
            }
            if (object instanceof L1NpcInstance) {
                WorldNpc.get().put(new Integer(object.getId()), (L1NpcInstance) object);
            }
            if (object instanceof L1CrockInstance) {
                WorldCrock.get().put(new Integer(object.getId()).intValue(), (L1CrockInstance) object);
            }
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
        }
    }

    public void removeObject(L1Object object) {
        try {
            if (object == null) {
                throw new NullPointerException();
            }
            _allObjects.remove(Integer.valueOf(object.getId()));
            if (object instanceof L1ItemInstance) {
                WorldItem.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1PcInstance) {
                L1PcInstance pc = (L1PcInstance) object;
                if (pc.isCrown()) {
                    WorldCrown.get().remove(new Integer(pc.getId()));
                } else if (pc.isKnight()) {
                    WorldKnight.get().remove(new Integer(pc.getId()));
                } else if (pc.isElf()) {
                    WorldElf.get().remove(new Integer(pc.getId()));
                } else if (pc.isWizard()) {
                    WorldWizard.get().remove(new Integer(pc.getId()));
                } else if (pc.isDarkelf()) {
                    WorldDarkelf.get().remove(new Integer(pc.getId()));
                } else if (pc.isDragonKnight()) {
                    WorldDragonKnight.get().remove(new Integer(pc.getId()));
                } else if (pc.isIllusionist()) {
                    WorldIllusionist.get().remove(new Integer(pc.getId()));
                }
                _allPlayers.remove(pc.getName());
            }
            if (object instanceof L1DeInstance) {
                L1DeInstance de = (L1DeInstance) object;
                WorldDe.get().remove(de.getNameId());
            }
            if (object instanceof L1TrapInstance) {
                WorldTrap.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1PetInstance) {
                WorldPet.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1SummonInstance) {
                WorldSummons.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1DollInstance) {
                WorldDoll.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1EffectInstance) {
                WorldEffect.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1MonsterInstance) {
                WorldMob.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1BowInstance) {
                WorldBow.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1NpcInstance) {
                WorldNpc.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1HierarchInstance) {
                WorldHierarch.get().remove(new Integer(object.getId()));
            }
            if (object instanceof L1CrockInstance) {
                WorldCrock.get().remove(new Integer(object.getId()).intValue());
            }
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
        }
    }

    public L1Object findObject(int oID) {
        if (oID == 0) {
            return null;
        }
        return _allObjects.get(Integer.valueOf(oID));
    }

    public Collection<L1Object> getObject() {
        try {
            Collection<L1Object> vs = _allValues;
            return (vs != null) ? vs
                    : (_allValues = Collections.unmodifiableCollection(_allObjects.values()));
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
            return null;
        }
    }

    public L1GroundInventory getInventory(int x, int y, short map) {
        try {
            int inventoryKey = ((x - 30000) * 10000 + (y - 30000)) * -1;
            ConcurrentHashMap<Integer, L1Object> idmap = _visibleObjects.get(new Integer(map));
            if (idmap != null) {
                Object object = idmap.get(Integer.valueOf(inventoryKey));
                if (object == null) {
                    return new L1GroundInventory(inventoryKey, x, y, map);
                }
                return (L1GroundInventory) object;
            }
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + (int) map);
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    public L1GroundInventory getInventory(L1Location loc) {
        return getInventory(loc.getX(), loc.getY(), (short) loc.getMap().getId());
    }

    public void addVisibleObject(L1Object object) {
        if (object.getMapId() <= 0) {
            return;
        }
        ConcurrentHashMap<Integer, L1Object> map = _visibleObjects.get(Integer.valueOf(object.getMapId()));
        if (map == null) {
            map = new ConcurrentHashMap<>();
            _visibleObjects.put(Integer.valueOf(object.getMapId()), map);
        }
        map.put(object.getId(), object);
    }

    public void removeVisibleObject(L1Object object) {
        if (object.getMapId() <= 0) {
            return;
        }
        final ConcurrentHashMap<Integer, L1Object> map = _visibleObjects.get(Integer.valueOf(object.getMapId()));
        if (map != null) {
            map.remove(object.getId());
        }
    }

    public void moveVisibleObject(L1Object object, int newMapId) {
        if (object.getMapId() == newMapId) {
            return;
        }
        if (object.getMapId() <= 0) {
            return;
        }
        final int srcMapId = object.getMapId();
        final ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(Integer.valueOf(srcMapId));
        if (mapSrc != null) {
            mapSrc.remove(object.getId());
        }

        final ConcurrentHashMap<Integer, L1Object> map = _visibleObjects.get(Integer.valueOf(newMapId));
        if (map != null) {
            map.put(object.getId(), object);
        }
    }

    private Map<Integer, Integer> createLineMap(final Point src, final Point target) {
        final Map<Integer, Integer> lineMap = new ConcurrentHashMap<>();

        int x0 = src.getX();
        int y0 = src.getY();
        int x2 = target.getX();
        int y2 = target.getY();
        int sx = (x2 > x0) ? 1 : -1;
        int dx = (x2 > x0) ? (x2 - x0) : (x0 - x2);
        int sy = (y2 > y0) ? 1 : -1;
        int dy = (y2 > y0) ? (y2 - y0) : (y0 - y2);
        int x3 = x0;
        int y3 = y0;
        if (dx >= dy) {
            int E = -dx;
            int i = 0;
            while (i <= dx) {
                int key = (x3 << 16) + y3;
                lineMap.put(Integer.valueOf(key), Integer.valueOf(key));
                x3 += sx;
                E += dy << 1;
                if (E >= 0) {
                    y3 += sy;
                    E -= dx << 1;
                }
                ++i;
            }
        } else {
            int E = -dy;
            int i = 0;
            while (i <= dy) {
                int key = (x3 << 16) + y3;
                lineMap.put(Integer.valueOf(key), Integer.valueOf(key));
                y3 += sy;
                E += dx << 1;
                if (E >= 0) {
                    x3 += sx;
                    E -= dy << 1;
                }
                ++i;
            }
        }
        return lineMap;
    }

    public ArrayList<L1Object> getVisibleLineObjects(final L1Object src, final L1Object target) {
        final int map = src.getMapId();
        if (map != target.getMapId()) {
            return null;
        }
        final Map<Integer, Integer> lineMap = this.createLineMap(src.getLocation(), target.getLocation());

        final ArrayList<L1Object> result = new ArrayList<>();
        if (lineMap == null) {
            return result;
        }
        final ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(Integer.valueOf(map));
        if (mapSrc == null) {
            return result;
        }
        for (final L1Object element : mapSrc.values()) {
            if (element.equals(src)) {
                continue;
            }
            if (src.get_showId() != element.get_showId()) {
                continue;
            }
            int key = (element.getX() << 16) + element.getY();
            if (!lineMap.containsKey(Integer.valueOf(key))) {
                continue;
            }
            result.add(element);
        }
        return result;
    }

    public ArrayList<L1Object> getVisibleBoxObjects(L1Object object, int heading, int width,
                                                    int height) {
        int x = object.getX();
        int y = object.getY();
        int map = object.getMapId();
        L1Location location = object.getLocation();
        ArrayList<L1Object> result = new ArrayList();
        int[] headingRotate = {6, 7, 0, 1, 2, 3, 4, 5};
        double cosSita = Math.cos(headingRotate[heading] * 3.141592653589793 / 4.0);
        double sinSita = Math.sin(headingRotate[heading] * 3.141592653589793 / 4.0);
        ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(new Integer(map));
        if (mapSrc == null) {
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + map);
            return result;
        }
        Iterator<L1Object> iterator = mapSrc.values().iterator();
        while (iterator.hasNext()) {
            L1Object element = iterator.next();
            if (element.equals(object)) {
                continue;
            }
            if (object.get_showId() != element.get_showId()) {
                continue;
            }
            if (map != element.getMapId()) {
                continue;
            }
            if (location.isSamePoint(element.getLocation())) {
                result.add(element);
            } else {
                int distance = location.getTileLineDistance(element.getLocation());
                if (distance > height && distance > width) {
                    continue;
                }
                int x2 = element.getX() - x;
                int y2 = element.getY() - y;
                int rotX = (int) Math.round(x2 * cosSita + y2 * sinSita);
                int rotY = (int) Math.round((-x2) * sinSita + y2 * cosSita);
                int xmin = 0;
                int xmax = height;
                int ymin = -width;
                int ymax = width;
                if (rotX <= 0 || distance > xmax || rotY < ymin || rotY > ymax) {
                    continue;
                }
                result.add(element);
            }
        }
        return result;
    }

    public ArrayList<L1Object> getVisibleObjects(L1Object object) {
        return getVisibleObjects(object, -1);
    }

    public boolean getVisibleObjects(L1Object src, L1Object tg) {
        return src.get_showId() == tg.get_showId() && src.getMapId() == tg.getMapId()
                && src.getLocation().isInScreen(tg.getLocation());
    }

    public ArrayList<L1Object> getVisibleObjects(L1Object object, int radius) {
        L1Map map = object.getMap();
        Point pt = object.getLocation();
        ArrayList<L1Object> result = new ArrayList();
        ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(new Integer(map.getId()));
        if (mapSrc == null) {
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + map.getId());
            return result;
        }
        Iterator<L1Object> iterator = mapSrc.values().iterator();
        while (iterator.hasNext()) {
            L1Object element = iterator.next();
            if (element.equals(object)) {
                continue;
            }
            if (object.get_showId() != element.get_showId()) {
                boolean is = false;
                if (element instanceof L1MerchantInstance) {
                    is = true;
                }
                if (element instanceof L1DwarfInstance) {
                    is = true;
                }
                if (!is) {
                    continue;
                }
            }
            if (map != element.getMap()) {
                continue;
            }
            switch (radius) {
                case -1: {
                    if (pt.isInScreen(element.getLocation())) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
                case 0: {
                    if (pt.isSamePoint(element.getLocation())) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
                default: {
                    int r = pt.getTileLineDistance(element.getLocation());
                    if (r <= radius) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
            }
        }
        return result;
    }

    public ArrayList<L1Object> getVisiblePoint(L1Location loc, int radius, int showid) {
        ArrayList<L1Object> result = new ArrayList();
        int mapId = loc.getMapId();
        ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(new Integer(mapId));
        if (mapSrc == null) {
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + mapId);
            return result;
        }
        Iterator<L1Object> iterator = mapSrc.values().iterator();
        while (iterator.hasNext()) {
            L1Object element = iterator.next();
            if (mapId != element.getMapId()) {
                continue;
            }
            if (showid != element.get_showId()) {
                continue;
            }
            if (loc.getTileLineDistance(element.getLocation()) > radius) {
                continue;
            }
            result.add(element);
        }
        return result;
    }

    public ArrayList<L1PcInstance> getVisiblePc(L1Location loc) {
        ArrayList<L1PcInstance> result = new ArrayList();
        int mapId = loc.getMapId();
        ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(new Integer(mapId));
        if (mapSrc == null) {
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + mapId);
            return result;
        }
        Iterator<L1PcInstance> iterator = _allPlayers.values().iterator();
        while (iterator.hasNext()) {
            L1PcInstance element = iterator.next();
            if (mapId != element.getMapId()) {
                continue;
            }
            if (!loc.isInScreen(element.getLocation())) {
                continue;
            }
            result.add(element);
        }
        return result;
    }

    public ArrayList<L1PcInstance> getVisiblePlayer(L1Object object) {
        return getVisiblePlayer(object, -1);
    }

    public ArrayList<L1PcInstance> getVisiblePlayer(L1Object object, int radius) {
        int map = object.getMapId();
        Point pt = object.getLocation();
        ArrayList<L1PcInstance> result = new ArrayList();
        Iterator<L1PcInstance> iterator = _allPlayers.values().iterator();
        while (iterator.hasNext()) {
            L1PcInstance element = iterator.next();
            if (element.equals(object)) {
                continue;
            }
            if (map != element.getMapId()) {
                continue;
            }
            if (object.get_showId() != element.get_showId()) {
                continue;
            }
            switch (radius) {
                case -1: {
                    if (pt.isInScreen(element.getLocation())) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
                case 0: {
                    if (pt.isSamePoint(element.getLocation())) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
                default: {
                    if (pt.getTileLineDistance(element.getLocation()) <= radius) {
                        result.add(element);
                        continue;
                    }
                    continue;
                }
            }
        }
        return result;
    }

    public ArrayList<L1PcInstance> getVisiblePlayerExceptTargetSight(L1Object object, L1Object target) {
        int map = object.getMapId();
        Point objectPt = object.getLocation();
        Point targetPt = target.getLocation();
        ArrayList<L1PcInstance> result = new ArrayList();
        Iterator<L1PcInstance> iterator = _allPlayers.values().iterator();
        while (iterator.hasNext()) {
            L1PcInstance element = iterator.next();
            if (element.equals(object)) {
                continue;
            }
            if (map != element.getMapId()) {
                continue;
            }
            if (target.get_showId() != element.get_showId()) {
                continue;
            }
            if (Config.PC_RECOGNIZE_RANGE == -1) {
                if (!objectPt.isInScreen(element.getLocation()) || targetPt.isInScreen(element.getLocation())) {
                    continue;
                }
                result.add(element);
            } else {
                if (objectPt.getTileLineDistance(element.getLocation()) > Config.PC_RECOGNIZE_RANGE
                        || targetPt.getTileLineDistance(element.getLocation()) <= Config.PC_RECOGNIZE_RANGE) {
                    continue;
                }
                result.add(element);
            }
        }
        return result;
    }

    public ArrayList<L1PcInstance> getVisiblePlayerExceptTargetSight(L1Object object, L1Object target,
                                                                     int radius) {
        int map = object.getMapId();
        Point objectPt = object.getLocation();
        Point targetPt = target.getLocation();
        ArrayList<L1PcInstance> result = new ArrayList();
        Iterator<L1PcInstance> iterator = _allPlayers.values().iterator();
        while (iterator.hasNext()) {
            L1PcInstance element = iterator.next();
            if (element.equals(object)) {
                continue;
            }
            if (map != element.getMapId()) {
                continue;
            }
            if (target.get_showId() != element.get_showId()) {
                continue;
            }
            if (objectPt.getTileLineDistance(element.getLocation()) > radius
                    || targetPt.getTileLineDistance(element.getLocation()) > radius) {
                continue;
            }
            result.add(element);
        }
        return result;
    }

    public ArrayList<L1PcInstance> getRecognizePlayer(L1Object object) {
        return getVisiblePlayer(object, Config.PC_RECOGNIZE_RANGE);
    }

    public Collection<L1PcInstance> getAllPlayers() {
        try {
            Collection<L1PcInstance> vs = _allPlayerValues;
            return (vs != null) ? vs
                    : (_allPlayerValues = Collections.unmodifiableCollection(_allPlayers.values()));
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
            return null;
        }
    }

    //檢查同帳號目前在線上的玩家有幾個
    public int checkPcCountForAccount(String accountName) {
        int count = 0;
        Iterator<L1PcInstance> iterator = getAllPlayers().iterator();
        while (iterator.hasNext()) {
            L1PcInstance tgpc = iterator.next();
            if (tgpc.getAccountName().equals(accountName)) {
                count += 1;
            }
        }
        return count;
    }

    public L1PcInstance getPlayer(String name) {
        if (_allPlayers.contains(name)) {
            return _allPlayers.get(name);
        }
        Iterator<L1PcInstance> iterator = getAllPlayers().iterator();
        while (iterator.hasNext()) {
            L1PcInstance each = iterator.next();
            if (each.getName().equalsIgnoreCase(name)) {
                return each;
            }
        }
        return null;
    }

    public boolean get_pc(L1PcInstance pc, String name) {
        L1PcInstance tg = _allPlayers.get(name);
        return tg != null && pc.getLocation().isInScreen(tg.getLocation());
    }

    public final Map<Integer, L1Object> getAllVisibleObjects() {
        return _allObjects;
    }

    public final Map<Integer, ConcurrentHashMap<Integer, L1Object>> getVisibleObjects() {
        return _visibleObjects;
    }

    public final ConcurrentHashMap<Integer, L1Object> getVisibleObjects(int mapId) {
        return _visibleObjects.get(new Integer(mapId));
    }

    public int getWeather() {
        return _weather;
    }

    public void setWeather(int weather) {
        _weather = weather;
    }

    public void set_worldChatElabled(boolean flag) {
        _worldChatEnabled = flag;
    }

    public boolean isWorldChatElabled() {
        return _worldChatEnabled;
    }

    public boolean isProcessingContributionTotal() {
        return _processingContributionTotal;
    }

    public void setProcessingContributionTotal(boolean flag) {
        _processingContributionTotal = flag;
    }

    public void broadcastPacketToAll(ServerBasePacket packet) {
        Iterator<L1PcInstance> iter = getAllPlayers().iterator();
        while (iter.hasNext()) {
            L1PcInstance tgpc = iter.next();
            if (check(tgpc)) {
                tgpc.sendPackets(packet);
            }
        }
    }

    public void broadcastServerMessage(String message) {
        broadcastPacketToAll(new S_SystemMessage(message));
    }

    public ArrayList<L1Object> getVisiblePoint(L1Location loc, int radius) {
        ArrayList<L1Object> result = new ArrayList();
        int mapId = loc.getMapId();
        ConcurrentHashMap<Integer, L1Object> mapSrc = _visibleObjects.get(new Integer(mapId));
        if (mapSrc == null) {
            World._log.error("遊戲世界儲存中心並未建立該地圖編號資料檔案: " + mapId);
            return result;
        }
        if (mapId <= 12000) {
            Iterator<L1Object> iterator = mapSrc.values().iterator();
            while (iterator.hasNext()) {
                L1Object element = iterator.next();
                if (mapId != element.getMapId()) {
                    continue;
                }
                if (loc.getTileLineDistance(element.getLocation()) > radius) {
                    continue;
                }
                result.add(element);
            }
        }
        return result;
    }

    public void closeMap(int mapid) {
        Iterator<Entry<Integer, L1Object>> iterator = _allObjects.entrySet().iterator();
        while (iterator.hasNext()) {
            Entry<Integer, L1Object> entry = iterator.next();
            L1Object object = entry.getValue();
            if (object.getMapId() == mapid) {
                if (object instanceof L1DoorInstance) {
                    DoorSpawnTable.get().removeDoor((L1DoorInstance) object);
                }
                removeObject(object);
            }
        }
        ConcurrentHashMap<Integer, L1Object> map = new ConcurrentHashMap();
        _visibleObjects.put(Integer.valueOf(mapid), map);
    }

    public void broadcastPacketToAlldroplist(ServerBasePacket packet) {
        Iterator<L1PcInstance> iterator = getAllPlayers().iterator();
        while (iterator.hasNext()) {
            L1PcInstance pc = iterator.next();
            pc.sendPackets(packet);
        }
    }

    public List<L1PcInstance> getRandomPlayers(int count) {
        try {
            Collection<L1PcInstance> tempList = getAllPlayers();
            if (!tempList.isEmpty()) {
                L1PcInstance[] userList = tempList.toArray(new L1PcInstance[tempList.size()]);
                List<L1PcInstance> newList = new ArrayList();
                Random _random = new Random();
                while (newList.size() < count) {
                    L1PcInstance each_pc = userList[_random.nextInt(userList.length)];
                    if (each_pc != null && !newList.contains(each_pc)) {
                        newList.add(each_pc);
                    }
                }
                return newList;
            }
        } catch (Exception e) {
            World._log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    public void addMap(int mapid) {
        ConcurrentHashMap<Integer, L1Object> map = new ConcurrentHashMap();
        _visibleObjects.put(Integer.valueOf(mapid), map);
    }

    public L1PcInstance[] getAllPlayersToArray() {
        return _allPlayers.values().toArray(new L1PcInstance[_allPlayers.size()]);
    }

    public L1PcInstance getPlayer(int objid) {
        Iterator<L1PcInstance> iterator = getAllPlayers().iterator();
        while (iterator.hasNext()) {
            L1PcInstance each = iterator.next();
            if (each.getId() == objid) {
                return each;
            }
        }
        return null;
    }

    public int getObjId(L1Npc npc) {
        Iterator<L1Object> iterator = _allObjects.values().iterator();
        while (iterator.hasNext()) {
            L1Object obj = iterator.next();
            if (obj instanceof L1NpcInstance && ((L1NpcInstance) obj).getNpcTemplate().get_npcId() == npc.get_npcId()) {
                return obj.getId();
            }
        }
        return 0;
    }


}
