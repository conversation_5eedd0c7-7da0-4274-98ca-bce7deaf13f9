package com.lineage.server.serverpackets;


public class S_Bookmarks extends ServerBasePacket {
    private byte[] _byte;

    public S_Bookmarks(String name, int map, int x,
					   int y, int id) {
        _byte = null;
        buildPacket(name, map, x, y, id);
    }

    private void buildPacket(String name, int map, int x,
							 int y, int id) {
        writeC(S_OPCODE_BOOKMARKS);
//		this.writeS(name);
//		this.writeH(map);
//		this.writeD(id);
        writeS(name);
        writeH(map);
        writeH(x);
        writeH(y);
        writeD(id);
    }


    @Override
    public byte[] getContent() {
        if (_byte == null) {
            _byte = getBytes();
        }
        return _byte;
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
