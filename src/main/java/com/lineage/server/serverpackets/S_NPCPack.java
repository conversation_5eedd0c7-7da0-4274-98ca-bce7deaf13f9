package com.lineage.server.serverpackets;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;

public class S_NPCPack extends ServerBasePacket {
	private static final int STATUS_POISON = 1;
	private static final int STATUS_PC = 4;
	private byte[] _byte;

	public S_NPCPack(final L1NpcInstance npc, final L1PcInstance pc) {
		this._byte = null;
		this.writeC(87);
		this.writeH(npc.getX());
		this.writeH(npc.getY());
		this.writeD(npc.getId());
		if (npc.getTempCharGfx() == 0) {
			this.writeH(npc.getGfxId());
		} else {
			this.writeH(npc.getTempCharGfx());
		}
		if (npc.getNpcTemplate().is_doppel() && npc.getGfxId() != 31) {
			this.writeC(4);
		} else {
			this.writeC(npc.getStatus());
		}
		this.writeC(npc.getHeading());
		this.writeC(npc.getChaLightSize());
		this.writeC(npc.getMoveSpeed());
		this.writeD((int) npc.getExp());
		this.writeH(npc.getTempLawful());
		String levelname = npc.getNameId();
		if ((pc.getLevel() - npc.getLevel() >= 0 && pc.getLevel() - npc.getLevel() <= 5)
				|| (npc.getLevel() - pc.getLevel() >= 0 && npc.getLevel() - pc.getLevel() <= 5)) {
			levelname = "\\f2" + npc.getNameId();
		} else if (npc.getLevel() - pc.getLevel() >= 30) {
			levelname = "\\f3" + npc.getNameId();
		} else if (npc.getLevel() - pc.getLevel() >= 20) {
			levelname = "\\fC" + npc.getNameId();
		} else if (npc.getLevel() - pc.getLevel() >= 11) {
			levelname = "\\f:" + npc.getNameId();
		}
		this.writeS(levelname);
		this.writeS(npc.getTitle());
		int status = 0;
		if (npc.getPoison() != null && npc.getPoison().getEffectId() == 1) {
			status |= 0x1;
		}
		if (npc.getNpcTemplate().is_doppel() && npc.getNpcTemplate().get_npcId() != 81069) {
			status |= 0x4;
		}
		if (npc.getNpcTemplate().get_npcId() == 90024) {
			status |= 0x1;
		}
		this.writeC(status);
		this.writeD(0);
		this.writeS(null);
		this.writeS(null);
		this.writeC(0);
		this.writeC(255);
		this.writeC(0);
		this.writeC(0);
		this.writeC(0);
		this.writeC(255);
		this.writeC(255);
	}

	public S_NPCPack(final L1NpcInstance npc) {
		this._byte = null;
		this.writeC(87);
		this.writeH(npc.getX());
		this.writeH(npc.getY());
		this.writeD(npc.getId());
		if (npc.getTempCharGfx() == 0) {
			this.writeH(npc.getGfxId());
		} else {
			this.writeH(npc.getTempCharGfx());
		}
		if (npc.getNpcTemplate().is_doppel() && npc.getGfxId() != 31) {
			this.writeC(4);
		} else {
			this.writeC(npc.getStatus());
		}
		this.writeC(npc.getHeading());
		this.writeC(npc.getChaLightSize());
		this.writeC(npc.getMoveSpeed());
		this.writeD((int) npc.getExp());
		this.writeH(npc.getTempLawful());
		this.writeS(npc.getNameId());
		this.writeS(npc.getTitle());
		int status = 0;
		if (npc.getPoison() != null && npc.getPoison().getEffectId() == 1) {
			status |= 0x1;
		}
		if (npc.getNpcTemplate().is_doppel() && npc.getNpcTemplate().get_npcId() != 81069) {
			status |= 0x4;
		}
		if (npc.getNpcTemplate().get_npcId() == 90024) {
			status |= 0x1;
		}
		this.writeC(status);
		this.writeD(0);
		this.writeS(null);
		this.writeS(null);
		this.writeC(0);
		this.writeC(255);
		this.writeC(0);
		this.writeC(0);
		this.writeC(0);
		this.writeC(255);
		this.writeC(255);
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
