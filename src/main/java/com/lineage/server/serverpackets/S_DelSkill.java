package com.lineage.server.serverpackets;

import com.lineage.server.templates.L1Skills;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.model.Instance.L1PcInstance;

public class S_DelSkill extends ServerBasePacket {
	private byte[] _byte;

	public S_DelSkill(final L1PcInstance pc, final int skillid) {
		this._byte = null;
		final byte skill_list_values = 32;
		final int[] skillList = new int[32];
		final L1Skills skill = SkillsTable.get().getTemplate(skillid);
		if (skill.getSkillLevel() > 0) {
			final int[] array = skillList;
			final int n = skill.getSkillLevel() - 1;
			array[n] += skill.getId();
			this.writeC(160);
			this.writeC(32);
			final int[] array2;
			final int length = (array2 = skillList).length;
			int i = 0;
			while (i < length) {
				final int element = array2[i];
				this.writeC(element);
				++i;
			}
			if (pc != null) {
				pc.removeSkillMastery(skillid);
			}
		}
	}

	public S_DelSkill(final L1PcInstance pc, final int level1, final int level2, final int level3, final int level4,
			final int level5, final int level6, final int level7, final int level8, final int level9, final int level10,
			final int knight1, final int knight2, final int de1, final int de2, final int royal, final int un,
			final int elf1, final int elf2, final int elf3, final int elf4, final int elf5, final int elf6,
			final int k1, final int k2, final int k3, final int l1, final int l2, final int l3) {
		this._byte = null;
		final int i6 = level5 + level6 + level7 + level8;
		final int j6 = level9 + level10;
		this.writeC(160);
		if (i6 > 0 && j6 == 0) {
			this.writeC(50);
		} else if (j6 > 0) {
			this.writeC(100);
		} else {
			this.writeC(32);
		}
		this.writeC(level1);
		this.writeC(level2);
		this.writeC(level3);
		this.writeC(level4);
		this.writeC(level5);
		this.writeC(level6);
		this.writeC(level7);
		this.writeC(level8);
		this.writeC(level9);
		this.writeC(level10);
		this.writeC(knight1);
		this.writeC(knight2);
		this.writeC(de1);
		this.writeC(de2);
		this.writeC(royal);
		this.writeC(un);
		this.writeC(elf1);
		this.writeC(elf2);
		this.writeC(elf3);
		this.writeC(elf4);
		this.writeC(elf5);
		this.writeC(elf6);
		this.writeC(k1);
		this.writeC(k2);
		this.writeC(k3);
		this.writeC(l1);
		this.writeC(l2);
		this.writeC(l3);
		final int[] ix = { level1, level2, level3, level4, level5, level6, level7, level8, level9, level10, knight1,
				knight2, de1, de2, royal, un, elf1, elf2, elf3, elf4, elf5, elf6, k1, k2, k3, l1, l2, l3 };
		int m = 0;
		while (m < ix.length) {
			int type = ix[m];
			int rtType = 128;
			int rt = 0;
			int skillid = -1;
			while (rt < 8) {
				if (type - rtType >= 0) {
					type -= rtType;
					switch (rtType) {
					case 128: {
						skillid = (m << 3) + 8;
						break;
					}
					case 64: {
						skillid = (m << 3) + 7;
						break;
					}
					case 32: {
						skillid = (m << 3) + 6;
						break;
					}
					case 16: {
						skillid = (m << 3) + 5;
						break;
					}
					case 8: {
						skillid = (m << 3) + 4;
						break;
					}
					case 4: {
						skillid = (m << 3) + 3;
						break;
					}
					case 2: {
						skillid = (m << 3) + 2;
						break;
					}
					case 1: {
						skillid = (m << 3) + 1;
						break;
					}
					}
					if (skillid != -1 && pc != null) {
						pc.removeSkillMastery(skillid);
					}
				}
				++rt;
				rtType >>= 1;
			}
			++m;
		}
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
