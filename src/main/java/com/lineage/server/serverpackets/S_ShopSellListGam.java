package com.lineage.server.serverpackets;

import java.util.Iterator;
import com.lineage.server.templates.L1Item;
import java.util.Map;
import com.lineage.data.event.gambling.Gambling;
import com.lineage.data.event.GamblingSet;
import com.lineage.data.npc.event.GamblingNpc;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.timecontroller.event.GamblingTime;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.templates.L1ShopItem;

public class S_ShopSellListGam extends ServerBasePacket {
	private byte[] _byte;

	public S_ShopSellListGam(final L1PcInstance pc, final L1NpcInstance npc) {
		this._byte = null;
		this.writeC(70);
		this.writeD(npc.getId());
		final Gambling gambling = GamblingTime.get_gambling();
		final Map<Integer, GamblingNpc> list = gambling.get_allNpc();
		if (list.size() <= 0) {
			this.writeH(0);
			return;
		}
		this.writeH(list.size());
		final L1Item item = ItemTable.get().getTemplate(40309);
		int i = 0;
		final Iterator<GamblingNpc> iterator = list.values().iterator();
		while (iterator.hasNext()) {
			final GamblingNpc gamblingNpc = iterator.next();
			++i;
			pc.get_otherList().add_gamList(gamblingNpc, i);
			this.writeD(i);
			this.writeH(item.getGfxId());
			this.writeD(GamblingSet.GAMADENA);
			final int no = GamblingTime.get_gamblingNo();
			final StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.append(gamblingNpc.get_npc().getNameId());
			stringBuilder.append(" [" + no + "-" + gamblingNpc.get_npc().getNpcId() + "]");
			this.writeS(stringBuilder.toString());
			this.writeC(0);
		}
		this.writeH(6100);
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
