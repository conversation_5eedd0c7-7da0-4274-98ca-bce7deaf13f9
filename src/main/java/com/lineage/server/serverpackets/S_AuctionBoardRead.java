package com.lineage.server.serverpackets;

import java.util.Calendar;
import com.lineage.server.templates.L1AuctionBoardTmp;
import com.lineage.server.datatables.lock.AuctionBoardReading;

public class S_AuctionBoardRead extends ServerBasePacket {
	private byte[] _byte;

	public S_AuctionBoardRead(final int objectId, final String house_number) {
		this._byte = null;
		this.buildPacket(objectId, house_number);
	}

	private void buildPacket(final int objectId, final String house_number) {
		final int number = Integer.valueOf(house_number).intValue();
		final L1AuctionBoardTmp board = AuctionBoardReading.get().getAuctionBoardTable(number);
		this.writeC(39);
		this.writeD(objectId);
		this.writeS("agsel");
		this.writeS(house_number);
		this.writeH(9);
		this.writeS(board.getHouseName());
		this.writeS(String.valueOf(board.getLocation()) + "$1195");
		this.writeS(String.valueOf(board.getHouseArea()));
		this.writeS(board.getOldOwner());
		this.writeS(board.getBidder());
		this.writeS(String.valueOf(board.getPrice()));
		final Calendar cal = board.getDeadline();
		final int month = cal.get(2) + 1;
		final int day = cal.get(5);
		final int hour = cal.get(11);
		this.writeS(String.valueOf(month));
		this.writeS(String.valueOf(day));
		this.writeS(String.valueOf(hour));
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
