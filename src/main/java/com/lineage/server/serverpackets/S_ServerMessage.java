package com.lineage.server.serverpackets;

public class S_ServerMessage extends ServerBasePacket {
	private byte[] _byte;

	public S_ServerMessage(final String msg) {
		this._byte = null;
		this.writeC(81);
		this.writeC(0);
		this.writeD(0);
		this.writeS(msg);
	}

	public S_ServerMessage(final String name, final int color) {
		this._byte = null;
		this.writeC(243);
		this.writeC(color);
		this.writeS(name);
	}

	public S_ServerMessage(final int type) {
		this._byte = null;
		this.writeC(71);
		this.writeH(type);
		this.writeC(0);
	}

	public S_ServerMessage(final int type, final String msg1) {
		this._byte = null;
		this.buildPacket(type, new String[] { msg1 });
	}

	public S_ServerMessage(final int type, final String msg1, final String msg2) {
		this._byte = null;
		this.buildPacket(type, new String[] { msg1, msg2 });
	}

	public S_ServerMessage(final int type, final String msg1, final String msg2, final String msg3) {
		this._byte = null;
		this.buildPacket(type, new String[] { msg1, msg2, msg3 });
	}

	public S_ServerMessage(final int type, final String msg1, final String msg2, final String msg3, final String msg4) {
		this._byte = null;
		this.buildPacket(type, new String[] { msg1, msg2, msg3, msg4 });
	}

	public S_ServerMessage(final int type, final String msg1, final String msg2, final String msg3, final String msg4,
			final String msg5) {
		this._byte = null;
		this.buildPacket(type, new String[] { msg1, msg2, msg3, msg4, msg5 });
	}

	public S_ServerMessage(final int type, final String[] info) {
		this._byte = null;
		this.buildPacket(type, info);
	}

	private void buildPacket(final int type, final String[] info) {
		this.writeC(71);
		this.writeH(type);
		if (info == null) {
			this.writeC(0);
		} else {
			this.writeC(info.length);
			int i = 0;
			while (i < info.length) {
				this.writeS(info[i]);
				++i;
			}
		}
	}

	@Override
	public byte[] getContent() {
		if (this._byte == null) {
			this._byte = this.getBytes();
		}
		return this._byte;
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
