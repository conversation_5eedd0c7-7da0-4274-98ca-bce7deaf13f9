package com.lineage.server.optimization;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.optimization.memory.MemoryStats;

import java.util.HashMap;
import java.util.Map;

/**
 * 記憶體監控器
 * 監控系統記憶體使用情況
 */
public class MemoryMonitor {
    private static final Log _log = LogFactory.getLog(MemoryMonitor.class);
    
    private static final double MEMORY_WARNING_THRESHOLD = 0.8; // 80%
    private static final double MEMORY_CRITICAL_THRESHOLD = 0.9; // 90%
    
    private boolean _running = false;
    
    /**
     * 啟動記憶體監控
     */
    public void start() {
        if (_running) {
            _log.warn("記憶體監控已經啟動");
            return;
        }
        
        _running = true;
        _log.info("記憶體監控啟動完成");
    }
    
    /**
     * 停止記憶體監控
     */
    public void stop() {
        if (!_running) {
            _log.warn("記憶體監控尚未啟動");
            return;
        }
        
        _running = false;
        _log.info("記憶體監控停止完成");
    }
    
    /**
     * 檢查記憶體使用情況
     */
    public void checkMemory() {
        if (!_running) {
            return;
        }
        
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            double memoryUsage = (double) usedMemory / maxMemory;
            
            // 記錄記憶體使用情況
            if (memoryUsage > MEMORY_CRITICAL_THRESHOLD) {
                _log.error("記憶體使用率嚴重過高: " + formatPercentage(memoryUsage));
                emergencyMemoryCleanup();
            } else if (memoryUsage > MEMORY_WARNING_THRESHOLD) {
                _log.warn("記憶體使用率過高: " + formatPercentage(memoryUsage));
            }
            
        } catch (Exception e) {
            _log.error("檢查記憶體使用情況失敗", e);
        }
    }
    
    /**
     * 緊急記憶體清理
     */
    private void emergencyMemoryCleanup() {
        try {
            _log.warn("執行緊急記憶體清理...");
            System.gc();
            Thread.sleep(1000);
            _log.info("緊急記憶體清理完成");
        } catch (Exception e) {
            _log.error("緊急記憶體清理失敗", e);
        }
    }
    
    /**
     * 獲取記憶體統計資訊
     */
    public MemoryStats getStats() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        return new MemoryStats(
            usedMemory,
            maxMemory,
            0, // totalMemoryAllocated
            0, // totalMemoryFreed
            0, // gcCount
            System.currentTimeMillis() // lastGcTime
        );
    }
    
    /**
     * 格式化百分比
     */
    private String formatPercentage(double value) {
        return String.format("%.2f%%", value * 100);
    }
    
    /**
     * 格式化位元組
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 檢查是否正在運行
     */
    public boolean isRunning() {
        return _running;
    }
} 