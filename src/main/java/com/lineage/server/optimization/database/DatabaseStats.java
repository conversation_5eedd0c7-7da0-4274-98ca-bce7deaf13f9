package com.lineage.server.optimization.database;

import java.util.HashMap;
import java.util.Map;

/**
 * 資料庫統計資訊類別
 * 用於存儲資料庫操作的統計數據
 */
public class DatabaseStats {
    private final Map<String, Object> stats;
    
    public DatabaseStats() {
        this.stats = new HashMap<>();
        initializeDefaultStats();
    }
    
    private void initializeDefaultStats() {
        stats.put("totalQueries", 0L);
        stats.put("slowQueries", 0L);
        stats.put("connectionCount", 0);
        stats.put("maxConnections", 20);
        stats.put("activeConnections", 0);
        stats.put("idleConnections", 0);
        stats.put("averageQueryTime", 0.0);
        stats.put("totalQueryTime", 0L);
    }
    
    public void setTotalQueries(long totalQueries) {
        stats.put("totalQueries", totalQueries);
    }
    
    public void setSlowQueries(long slowQueries) {
        stats.put("slowQueries", slowQueries);
    }
    
    public void setConnectionCount(int connectionCount) {
        stats.put("connectionCount", connectionCount);
    }
    
    public void setMaxConnections(int maxConnections) {
        stats.put("maxConnections", maxConnections);
    }
    
    public void setActiveConnections(int activeConnections) {
        stats.put("activeConnections", activeConnections);
    }
    
    public void setIdleConnections(int idleConnections) {
        stats.put("idleConnections", idleConnections);
    }
    
    public void setTotalQueryTime(long totalQueryTime) {
        stats.put("totalQueryTime", totalQueryTime);
        updateAverageQueryTime();
    }
    
    private void updateAverageQueryTime() {
        long totalQueries = (Long) stats.get("totalQueries");
        long totalQueryTime = (Long) stats.get("totalQueryTime");
        
        if (totalQueries > 0) {
            double averageQueryTime = (double) totalQueryTime / totalQueries;
            stats.put("averageQueryTime", averageQueryTime);
        }
    }
    
    public long getTotalQueries() {
        return (Long) stats.get("totalQueries");
    }
    
    public long getSlowQueries() {
        return (Long) stats.get("slowQueries");
    }
    
    public int getConnectionCount() {
        return (Integer) stats.get("connectionCount");
    }
    
    public int getMaxConnections() {
        return (Integer) stats.get("maxConnections");
    }
    
    public int getActiveConnections() {
        return (Integer) stats.get("activeConnections");
    }
    
    public int getIdleConnections() {
        return (Integer) stats.get("idleConnections");
    }
    
    public double getAverageQueryTime() {
        return (Double) stats.get("averageQueryTime");
    }
    
    public long getTotalQueryTime() {
        return (Long) stats.get("totalQueryTime");
    }
    
    public Map<String, Object> getStatsMap() {
        return new HashMap<>(stats);
    }
    
    @Override
    public String toString() {
        return String.format("DatabaseStats{queries=%d, slowQueries=%d, avgTime=%.2fms, connections=%d/%d}", 
                           getTotalQueries(), getSlowQueries(), getAverageQueryTime(), 
                           getActiveConnections(), getMaxConnections());
    }
}
