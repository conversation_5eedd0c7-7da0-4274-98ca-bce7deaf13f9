package com.lineage.server.optimization.thread;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.Map;
import java.util.HashMap;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;
import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.lang.management.ThreadInfo;

/**
 * 執行緒池優化管理器
 * 提供執行緒池監控、效能優化和問題檢測功能
 */
public class ThreadPoolOptimizer {
    private static final Log _log = LogFactory.getLog(ThreadPoolOptimizer.class);
    
    private static ThreadPoolOptimizer _instance;
    private boolean _running = false;
    
    // 執行緒池監控
    private final Map<String, ThreadPoolInfo> threadPoolInfos = new ConcurrentHashMap<>();
    private final AtomicLong totalTasks = new AtomicLong(0);
    private final AtomicLong completedTasks = new AtomicLong(0);
    private final AtomicLong rejectedTasks = new AtomicLong(0);
    private final AtomicLong deadlockDetected = new AtomicLong(0);
    
    // 監控執行器
    private final ScheduledExecutorService monitorExecutor;
    
    // 效能閾值
    private static final double MEMORY_USAGE_THRESHOLD = 0.85; // 85%
    private static final double CPU_USAGE_THRESHOLD = 0.80; // 80%
    private static final int MAX_QUEUE_SIZE = 1000;
    private static final long TASK_TIMEOUT = 30000; // 30秒
    
    // 監控間隔
    private static final int MONITOR_INTERVAL = 30; // 30秒
    private static final int DEADLOCK_CHECK_INTERVAL = 60; // 60秒
    
    private ThreadPoolOptimizer() {
        this.monitorExecutor = Executors.newScheduledThreadPool(2);
    }
    
    public static ThreadPoolOptimizer getInstance() {
        if (_instance == null) {
            _instance = new ThreadPoolOptimizer();
        }
        return _instance;
    }
    
    /**
     * 啟動執行緒池優化
     */
    public void start() {
        if (_running) {
            _log.warn("執行緒池優化器已經啟動");
            return;
        }
        
        _running = true;
        _log.info("啟動執行緒池優化器...");
        
        // 啟動監控任務
        startMonitoringTasks();
        
        _log.info("執行緒池優化器啟動完成");
    }
    
    /**
     * 停止執行緒池優化
     */
    public void stop() {
        if (!_running) {
            _log.warn("執行緒池優化器尚未啟動");
            return;
        }
        
        _running = false;
        
        try {
            if (monitorExecutor != null && !monitorExecutor.isShutdown()) {
                monitorExecutor.shutdown();
                if (!monitorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            }
        } catch (InterruptedException e) {
            _log.error("停止監控執行器失敗", e);
        }
        
        _log.info("執行緒池優化器停止完成");
    }
    
    /**
     * 註冊執行緒池進行監控
     */
    public void registerThreadPool(String name, ExecutorService executor) {
        if (!_running) {
            _log.warn("執行緒池優化器尚未啟動，無法註冊執行緒池: " + name);
            return;
        }
        
        ThreadPoolInfo info = new ThreadPoolInfo(name, executor);
        threadPoolInfos.put(name, info);
        _log.info("註冊執行緒池進行監控: " + name);
    }
    
    /**
     * 記錄任務執行
     */
    public void recordTask(String poolName, long executionTime) {
        totalTasks.incrementAndGet();
        completedTasks.incrementAndGet();
        
        ThreadPoolInfo info = threadPoolInfos.get(poolName);
        if (info != null) {
            info.recordTask(executionTime);
        }
    }
    
    /**
     * 記錄任務被拒絕
     */
    public void recordRejectedTask(String poolName) {
        rejectedTasks.incrementAndGet();
        
        ThreadPoolInfo info = threadPoolInfos.get(poolName);
        if (info != null) {
            info.recordRejectedTask();
        }
    }
    
    /**
     * 啟動監控任務
     */
    private void startMonitoringTasks() {
        // 執行緒池狀態監控
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                monitorThreadPools();
            } catch (Exception e) {
                _log.error("執行緒池監控任務執行失敗", e);
            }
        }, MONITOR_INTERVAL, MONITOR_INTERVAL, TimeUnit.SECONDS);
        
        // 死鎖檢測
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                checkForDeadlocks();
            } catch (Exception e) {
                _log.error("死鎖檢測任務執行失敗", e);
            }
        }, DEADLOCK_CHECK_INTERVAL, DEADLOCK_CHECK_INTERVAL, TimeUnit.SECONDS);
        
        _log.info("監控任務啟動完成");
    }
    
    /**
     * 監控執行緒池狀態
     */
    private void monitorThreadPools() {
        for (ThreadPoolInfo info : threadPoolInfos.values()) {
            try {
                ExecutorService executor = info.getExecutor();
                
                if (executor instanceof ThreadPoolExecutor) {
                    ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                    
                    // 檢查執行緒池狀態
                    int activeCount = tpe.getActiveCount();
                    int poolSize = tpe.getPoolSize();
                    int corePoolSize = tpe.getCorePoolSize();
                    int maximumPoolSize = tpe.getMaximumPoolSize();
                    long completedTaskCount = tpe.getCompletedTaskCount();
                    int queueSize = tpe.getQueue().size();
                    
                    // 更新統計資訊
                    info.updateStats(activeCount, poolSize, corePoolSize, maximumPoolSize, 
                                   completedTaskCount, queueSize);
                    
                    // 檢查效能問題
                    checkPerformanceIssues(info);
                    
                }
                
            } catch (Exception e) {
                _log.error("監控執行緒池失敗: " + info.getName(), e);
            }
        }
    }
    
    /**
     * 檢查效能問題
     */
    private void checkPerformanceIssues(ThreadPoolInfo info) {
        // 檢查記憶體使用率
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        double memoryUsage = (double) (totalMemory - freeMemory) / totalMemory;
        
        if (memoryUsage > MEMORY_USAGE_THRESHOLD) {
            _log.warn("記憶體使用率過高: " + String.format("%.2f%%", memoryUsage * 100));
            suggestMemoryOptimization();
        }
        
        // 檢查佇列大小
        if (info.getQueueSize() > MAX_QUEUE_SIZE) {
            _log.warn("執行緒池佇列過大: " + info.getName() + " - " + info.getQueueSize());
            suggestQueueOptimization(info);
        }
        
        // 檢查執行緒池飽和度
        double saturation = (double) info.getActiveCount() / info.getMaximumPoolSize();
        if (saturation > 0.9) {
            _log.warn("執行緒池飽和度過高: " + info.getName() + " - " + String.format("%.2f%%", saturation * 100));
            suggestThreadPoolOptimization(info);
        }
    }
    
    /**
     * 檢查死鎖
     */
    private void checkForDeadlocks() {
        try {
            ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
            long[] deadlockedThreads = threadBean.findDeadlockedThreads();
            
            if (deadlockedThreads != null && deadlockedThreads.length > 0) {
                deadlockDetected.incrementAndGet();
                _log.error("檢測到死鎖! 死鎖執行緒數量: " + deadlockedThreads.length);
                
                // 記錄死鎖執行緒資訊
                for (long threadId : deadlockedThreads) {
                    ThreadInfo threadInfo = threadBean.getThreadInfo(threadId);
                    if (threadInfo != null) {
                        _log.error("死鎖執行緒: " + threadInfo.getThreadName() + 
                                 " - 狀態: " + threadInfo.getThreadState());
                    }
                }
                
                // 建議解決方案
                suggestDeadlockResolution();
            }
            
        } catch (Exception e) {
            _log.error("死鎖檢測失敗", e);
        }
    }
    
    /**
     * 建議記憶體優化
     */
    private void suggestMemoryOptimization() {
        _log.info("記憶體優化建議:");
        _log.info("1. 檢查是否有記憶體洩漏");
        _log.info("2. 考慮增加堆記憶體大小");
        _log.info("3. 優化物件創建和回收");
        _log.info("4. 檢查快取使用情況");
    }
    
    /**
     * 建議佇列優化
     */
    private void suggestQueueOptimization(ThreadPoolInfo info) {
        _log.info("佇列優化建議 - " + info.getName() + ":");
        _log.info("1. 增加執行緒池大小");
        _log.info("2. 優化任務處理邏輯");
        _log.info("3. 考慮使用有界佇列");
        _log.info("4. 檢查任務是否過於耗時");
    }
    
    /**
     * 建議執行緒池優化
     */
    private void suggestThreadPoolOptimization(ThreadPoolInfo info) {
        _log.info("執行緒池優化建議 - " + info.getName() + ":");
        _log.info("1. 增加最大執行緒數");
        _log.info("2. 優化任務分配策略");
        _log.info("3. 檢查是否有長時間運行的任務");
        _log.info("4. 考慮使用不同的執行緒池");
    }
    
    /**
     * 建議死鎖解決方案
     */
    private void suggestDeadlockResolution() {
        _log.info("死鎖解決建議:");
        _log.info("1. 檢查鎖的獲取順序");
        _log.info("2. 使用超時機制");
        _log.info("3. 避免嵌套鎖");
        _log.info("4. 考慮使用無鎖資料結構");
    }
    
    /**
     * 獲取優化報告
     */
    public ThreadPoolOptimizationReport generateReport() {
        ThreadPoolOptimizationReport report = new ThreadPoolOptimizationReport();
        
        report.setTotalTasks(totalTasks.get());
        report.setCompletedTasks(completedTasks.get());
        report.setRejectedTasks(rejectedTasks.get());
        report.setDeadlockDetected(deadlockDetected.get());
        
        // 收集各執行緒池統計
        Map<String, ThreadPoolStats> poolStats = new HashMap<>();
        for (ThreadPoolInfo info : threadPoolInfos.values()) {
            poolStats.put(info.getName(), info.getStats());
        }
        report.setThreadPoolStats(poolStats);
        
        // 系統資源使用情況
        Runtime runtime = Runtime.getRuntime();
        report.setTotalMemory(runtime.totalMemory());
        report.setFreeMemory(runtime.freeMemory());
        report.setUsedMemory(runtime.totalMemory() - runtime.freeMemory());
        report.setAvailableProcessors(runtime.availableProcessors());
        
        return report;
    }
    
    /**
     * 執行緊急優化
     */
    public void emergencyOptimization() {
        _log.warn("執行緊急執行緒池優化...");
        
        try {
            // 強制垃圾回收
            System.gc();
            
            // 檢查並重啟有問題的執行緒池
            for (ThreadPoolInfo info : threadPoolInfos.values()) {
                if (info.getQueueSize() > MAX_QUEUE_SIZE * 2) {
                    _log.warn("重啟執行緒池: " + info.getName());
                    // 這裡可以添加重啟邏輯
                }
            }
            
            _log.info("緊急優化完成");
            
        } catch (Exception e) {
            _log.error("緊急優化失敗", e);
        }
    }
    
    /**
     * 檢查是否正在運行
     */
    public boolean isRunning() {
        return _running;
    }
    
    /**
     * 獲取執行緒池資訊
     */
    public List<ThreadPoolInfo> getThreadPoolInfos() {
        return new ArrayList<>(threadPoolInfos.values());
    }
} 