package com.lineage.server.optimization.thread;

import java.util.Map;
import java.util.HashMap;

/**
 * 執行緒池優化報告類別
 * 用於生成執行緒池優化的詳細報告
 */
public class ThreadPoolOptimizationReport {
    private long totalTasks;
    private long completedTasks;
    private long rejectedTasks;
    private long deadlockDetected;
    private Map<String, ThreadPoolStats> threadPoolStats;
    private long totalMemory;
    private long freeMemory;
    private long usedMemory;
    private int availableProcessors;
    private long timestamp;
    
    public ThreadPoolOptimizationReport() {
        this.threadPoolStats = new HashMap<>();
        this.timestamp = System.currentTimeMillis();
    }
    
    // Getter 和 Setter 方法
    public long getTotalTasks() {
        return totalTasks;
    }
    
    public void setTotalTasks(long totalTasks) {
        this.totalTasks = totalTasks;
    }
    
    public long getCompletedTasks() {
        return completedTasks;
    }
    
    public void setCompletedTasks(long completedTasks) {
        this.completedTasks = completedTasks;
    }
    
    public long getRejectedTasks() {
        return rejectedTasks;
    }
    
    public void setRejectedTasks(long rejectedTasks) {
        this.rejectedTasks = rejectedTasks;
    }
    
    public long getDeadlockDetected() {
        return deadlockDetected;
    }
    
    public void setDeadlockDetected(long deadlockDetected) {
        this.deadlockDetected = deadlockDetected;
    }
    
    public Map<String, ThreadPoolStats> getThreadPoolStats() {
        return threadPoolStats;
    }
    
    public void setThreadPoolStats(Map<String, ThreadPoolStats> threadPoolStats) {
        this.threadPoolStats = threadPoolStats;
    }
    
    public long getTotalMemory() {
        return totalMemory;
    }
    
    public void setTotalMemory(long totalMemory) {
        this.totalMemory = totalMemory;
    }
    
    public long getFreeMemory() {
        return freeMemory;
    }
    
    public void setFreeMemory(long freeMemory) {
        this.freeMemory = freeMemory;
    }
    
    public long getUsedMemory() {
        return usedMemory;
    }
    
    public void setUsedMemory(long usedMemory) {
        this.usedMemory = usedMemory;
    }
    
    public int getAvailableProcessors() {
        return availableProcessors;
    }
    
    public void setAvailableProcessors(int availableProcessors) {
        this.availableProcessors = availableProcessors;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }
    
    /**
     * 獲取記憶體使用率
     */
    public double getMemoryUsage() {
        if (totalMemory > 0) {
            return (double) usedMemory / totalMemory;
        }
        return 0.0;
    }
    
    /**
     * 獲取任務完成率
     */
    public double getTaskCompletionRate() {
        if (totalTasks > 0) {
            return (double) completedTasks / totalTasks;
        }
        return 0.0;
    }
    
    /**
     * 獲取任務拒絕率
     */
    public double getTaskRejectionRate() {
        if (totalTasks > 0) {
            return (double) rejectedTasks / totalTasks;
        }
        return 0.0;
    }
    
    /**
     * 獲取整體執行緒池利用率
     */
    public double getOverallUtilization() {
        if (threadPoolStats.isEmpty()) {
            return 0.0;
        }
        
        double totalUtilization = 0.0;
        int poolCount = 0;
        
        for (ThreadPoolStats stats : threadPoolStats.values()) {
            totalUtilization += stats.getUtilization();
            poolCount++;
        }
        
        return poolCount > 0 ? totalUtilization / poolCount : 0.0;
    }
    
    /**
     * 檢查是否有效能問題
     */
    public boolean hasPerformanceIssues() {
        // 檢查記憶體使用率
        if (getMemoryUsage() > 0.85) {
            return true;
        }
        
        // 檢查任務拒絕率
        if (getTaskRejectionRate() > 0.1) {
            return true;
        }
        
        // 檢查死鎖
        if (deadlockDetected > 0) {
            return true;
        }
        
        // 檢查各執行緒池的佇列大小
        for (ThreadPoolStats stats : threadPoolStats.values()) {
            if (stats.getQueueSize() > 1000) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 生成優化建議
     */
    public String generateOptimizationSuggestions() {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("執行緒池優化建議:\n\n");
        
        // 記憶體優化建議
        if (getMemoryUsage() > 0.85) {
            suggestions.append("🔴 記憶體使用率過高 (").append(String.format("%.2f%%", getMemoryUsage() * 100)).append("):\n");
            suggestions.append("   - 檢查是否有記憶體洩漏\n");
            suggestions.append("   - 考慮增加堆記憶體大小\n");
            suggestions.append("   - 優化物件創建和回收\n");
            suggestions.append("   - 檢查快取使用情況\n\n");
        }
        
        // 任務拒絕率建議
        if (getTaskRejectionRate() > 0.1) {
            suggestions.append("🔴 任務拒絕率過高 (").append(String.format("%.2f%%", getTaskRejectionRate() * 100)).append("):\n");
            suggestions.append("   - 增加執行緒池大小\n");
            suggestions.append("   - 優化任務處理邏輯\n");
            suggestions.append("   - 考慮使用有界佇列\n");
            suggestions.append("   - 檢查任務是否過於耗時\n\n");
        }
        
        // 死鎖檢測建議
        if (deadlockDetected > 0) {
            suggestions.append("🔴 檢測到死鎖 (").append(deadlockDetected).append(" 次):\n");
            suggestions.append("   - 檢查鎖的獲取順序\n");
            suggestions.append("   - 使用超時機制\n");
            suggestions.append("   - 避免嵌套鎖\n");
            suggestions.append("   - 考慮使用無鎖資料結構\n\n");
        }
        
        // 各執行緒池建議
        for (ThreadPoolStats stats : threadPoolStats.values()) {
            if (stats.getQueueSize() > 1000) {
                suggestions.append("🟡 執行緒池 ").append(stats.getName()).append(" 佇列過大 (").append(stats.getQueueSize()).append("):\n");
                suggestions.append("   - 增加執行緒池大小\n");
                suggestions.append("   - 優化任務分配策略\n");
                suggestions.append("   - 檢查是否有長時間運行的任務\n\n");
            }
            
            if (stats.getUtilization() > 0.9) {
                suggestions.append("🟡 執行緒池 ").append(stats.getName()).append(" 利用率過高 (").append(String.format("%.2f%%", stats.getUtilization() * 100)).append("):\n");
                suggestions.append("   - 增加最大執行緒數\n");
                suggestions.append("   - 優化任務處理邏輯\n");
                suggestions.append("   - 考慮使用不同的執行緒池\n\n");
            }
        }
        
        if (suggestions.toString().equals("執行緒池優化建議:\n\n")) {
            suggestions.append("✅ 系統運行良好，無需優化\n");
        }
        
        return suggestions.toString();
    }
    
    @Override
    public String toString() {
        StringBuilder report = new StringBuilder();
        report.append("=== 執行緒池優化報告 ===\n");
        report.append("生成時間: ").append(new java.util.Date(timestamp)).append("\n\n");
        
        // 系統概況
        report.append("系統概況:\n");
        report.append("  總任務數: ").append(totalTasks).append("\n");
        report.append("  已完成任務: ").append(completedTasks).append("\n");
        report.append("  被拒絕任務: ").append(rejectedTasks).append("\n");
        report.append("  任務完成率: ").append(String.format("%.2f%%", getTaskCompletionRate() * 100)).append("\n");
        report.append("  任務拒絕率: ").append(String.format("%.2f%%", getTaskRejectionRate() * 100)).append("\n");
        report.append("  死鎖檢測次數: ").append(deadlockDetected).append("\n\n");
        
        // 記憶體使用情況
        report.append("記憶體使用情況:\n");
        report.append("  總記憶體: ").append(formatMemory(totalMemory)).append("\n");
        report.append("  已使用記憶體: ").append(formatMemory(usedMemory)).append("\n");
        report.append("  可用記憶體: ").append(formatMemory(freeMemory)).append("\n");
        report.append("  記憶體使用率: ").append(String.format("%.2f%%", getMemoryUsage() * 100)).append("\n");
        report.append("  可用處理器: ").append(availableProcessors).append("\n\n");
        
        // 各執行緒池統計
        report.append("執行緒池統計:\n");
        for (ThreadPoolStats stats : threadPoolStats.values()) {
            report.append(stats.toString()).append("\n");
        }
        
        // 優化建議
        report.append("\n").append(generateOptimizationSuggestions());
        
        return report.toString();
    }
    
    /**
     * 格式化記憶體大小
     */
    private String formatMemory(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
} 