package com.lineage.server.clientpackets;

import com.lineage.data.event.TimeTrade;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.datatables.ItemRestrictionsTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.L1Trade;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class C_TradeAddItem extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_TradeAddItem.class);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();
            if (pc.isGhost()) {
            }
            if (pc.isDead() || pc.isTeleport()) {
                return;
            }
            int itemObjid = readD();
            long itemcount = readD();
            if (itemcount > 2147483647L) {
                itemcount = 2147483647L;
            }
            itemcount = Math.max(0L, itemcount);
            L1ItemInstance item = pc.getInventory().getItem(itemObjid);
            if (item == null) {
                return;
            }
            if (item.getCount() <= 0L) {
                return;
            }
            if ((item.getItemId() == 40308 || item.getItemId() == 44070) && pc.getLevel() < 35) {
                pc.sendPackets(new S_ServerMessage("預防作弊啟動，35級以下無法做此動作"));
                return;
            }
            if (item.getItemId() == 47011 && pc.getLevel() < 52) {
                return;
            }
            if (!pc.isGm()) {
                if (!item.getItem().isTradable()) {
                    pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                    return;
                }
                if (item.getBless() >= 128) {
                    pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                    return;
                }
                if (TimeTrade.START && item.get_time() != null) {
                    pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                    return;
                }
                if (ItemRestrictionsTable.get().checkItemRestrictions(item.getItemId(), pc)) {
                    pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                    return; //丹丹 Kevin新增限制親友
                }

            }
            if (item.isEquipped()) {
                pc.sendPackets(new S_ServerMessage(141));
                return;
            }
            Object[] petlist = pc.getPetList().values().toArray();
            Object[] array;
            int length = (array = petlist).length;
            int i = 0;
            while (i < length) {
                Object petObject = array[i];
                if (petObject instanceof L1PetInstance) {
                    L1PetInstance pet = (L1PetInstance) petObject;
                    if (item.getId() == pet.getItemObjId()) {
                        pc.sendPackets(new S_ServerMessage(1187));
                        return;
                    }
                }
                ++i;
            }
            if (item.getId() == pc.getQuest().get_step(74)) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getName()));
                return;
            }
            if (pc.getDoll(item.getId()) != null) {
                pc.sendPackets(new S_ServerMessage(1181));
                return;
            }
            if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                pc.sendPackets(new S_ServerMessage(1181));
            }
            L1PcInstance tradingPartner = (L1PcInstance) World.get().findObject(pc.getTradeID());
            if (tradingPartner == null) {
                return;
            }
            if (pc.getTradeOk()) {
                return;
            }
            if (tradingPartner.getInventory().checkAddItem(item, itemcount) != 0) {
                tradingPartner.sendPackets(new S_ServerMessage(270));
                pc.sendPackets(new S_ServerMessage(271));
                return;
            }
            L1Trade trade = new L1Trade();
            if (itemcount <= 0L) {
                C_TradeAddItem._log.error("要求增加交易物品傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                return;
            }
            trade.tradeAddItem(pc, itemObjid, (int) itemcount);
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }

}
