package com.lineage.server.clientpackets;

import com.lineage.config.Config;
import com.lineage.config.ConfigOther;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.datatables.DungeonRTable;
import com.lineage.server.datatables.DungeonTable;
import com.lineage.server.datatables.MapCheckTable;
import com.lineage.server.datatables.MapsTable;
import com.lineage.server.model.Instance.*;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.model.L1Trade;
import com.lineage.server.model.map.L1WorldMap;
import com.lineage.server.model.skill.L1SkillId;
import com.lineage.server.serverpackets.S_MoveCharPacket;
import com.lineage.server.serverpackets.S_NPCPack_Skin;
import com.lineage.server.serverpackets.S_NewMaster;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.templates.L1MapCheck;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.utils.Teleportation;
import com.lineage.server.utils.log.PlayerLogUtil;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldTrap;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Timestamp;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import static com.lineage.server.model.Instance.L1PcInstance.REGENSTATE_MOVE;
import static com.lineage.server.model.skill.L1SkillId.ABSOLUTE_BARRIER;
import static com.lineage.server.model.skill.L1SkillId.MEDITATION;

public class C_MoveChar extends ClientBasePacket {
    private static final Log _log;
    private static final byte[] HEADING_TABLE_X;
    private static final byte[] HEADING_TABLE_Y;

    static {
        _log = LogFactory.getLog(C_MoveChar.class);
        HEADING_TABLE_X = new byte[]{0, 1, 1, 1, 0, -1, -1, -1};
        HEADING_TABLE_Y = new byte[]{-1, -1, 0, 1, 1, 1, 0, -1};
    }

    public static void 玩家穿透(String info) {
        PlayerLogUtil.writeLog("[玩家穿透紀錄]", info);
//        try {
//            BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[玩家穿透紀錄].txt", true));
//            out.write(String.valueOf(info) + "\r\n");
//            out.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    public static void movexy(String info) {
        PlayerLogUtil.writeLog("玩家開通座標紀錄", info);
//        try {
//            BufferedWriter out = new BufferedWriter(new FileWriter("玩家紀錄/玩家開通座標紀錄.txt", true));
//            out.write(String.valueOf(info) + "\r\n");
//            out.close();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();
            if (pc.isDead() || pc.isTeleport()) {
                return;
            }
            if (pc.getTradeID() != 0) {
                L1Trade trade = new L1Trade();
                trade.tradeCancel(pc);
            }
            int locx = 0;
            int locy = 0;
            int heading = 0;
            try {
                locx = readH();
                locy = readH();
                heading = readC();
            } catch (Exception e2) {
                // 座標取回失敗
                return;
            }
            pc.killSkillEffectTimer(MEDITATION);// 解除冥想術
            pc.setCallClanId(0); // 人物移動呼喚盟友無效

            if (!pc.hasSkillEffect(ABSOLUTE_BARRIER)) { // 絕對屏障狀態
                pc.setRegenState(REGENSTATE_MOVE);
            }
            // 解除舊座標障礙宣告
            pc.getMap().setPassable(pc.getLocation(), true);

            //繁體中文版本
            if (Config.CLIENT_LANGUAGE == 3) {
                if (heading > 7) { // Taiwan Only
                    heading ^= 0x49;// 換位
                    locx = pc.getX();
                    locy = pc.getY();
                }
                heading = Math.min(heading, 7);
            }

            // 移動前位置
            int oleLocx = pc.getX();
            int oleLocy = pc.getY();

            // 移動後位置
            int newlocx = locx + HEADING_TABLE_X[heading];
            int newlocy = locy + HEADING_TABLE_Y[heading];

            try {
                // 不允許穿過該點
                boolean isError = false;

                // 異位判斷(封包數據 與 核心數據 不吻合)
                if ((locx != oleLocx) && (locy != oleLocy)) {
                    isError = true;
                }

                // 商店村模式
                if (pc.isPrivateShop()) {
                    isError = true;
                }

                // 無法攻擊/使用道具/技能/回城的狀態
                if (pc.isParalyzedX()) {
                    isError = true;
                }

                if (pc.hasSkillEffect(L1SkillId.MOVE_STOP)) {
                    isError = true;
                }

                int minX = MapsTable.get().getStartX(pc.getMapId());
                int minY = MapsTable.get().getStartY(pc.getMapId());
                int maxX = MapsTable.get().getEndX(pc.getMapId());
                int maxY = MapsTable.get().getEndY(pc.getMapId());
                if (newlocx < minX || newlocx > maxX || newlocy < minY || newlocy > maxY) {
                    isError = true;
                }

                boolean isPassable = pc.getMap().isOriginalTile(newlocx, newlocy);
                if (!isPassable) {
                    int tile = 0;
                    if (pc.getMap().isCombatZone(oleLocx, oleLocy)) {
                        tile = 47;
                    } else if (pc.getMap().isNormalZone(oleLocx, oleLocy)) {
                        tile = 15;
                    } else if (pc.getMap().isSafetyZone(oleLocx, oleLocy)) {
                        tile = 31;
                    }
                    L1WorldMap.get().getMap(pc.getMapId()).setTestTile(newlocx, newlocy, tile);
                    //MapTileTable.get().addData(pc.getMapId(), newlocx, newlocy, tile);
                    movexy("IP(" + pc.getNetConnection().getIp() + ")" + "玩家採點座標地圖:" + "【 " + (int) pc.getMapId()
                            + " 】 " + "X座標:" + "[" + newlocx + "] Y座標:[" + newlocy + "]區域(" + tile + ")" + " 】"
                            + new Timestamp(System.currentTimeMillis()) + ")。");
                    if (pc.isGm()) {
                        pc.sendPackets(new S_ServerMessage("已解除障礙，該點設為可通行。"));
                    }
                }
                if (!pc.isGm()) {
                    boolean isCheck = false;
                    List<L1MapCheck> check = MapCheckTable.get().getList();
                    Iterator<L1MapCheck> iterator = check.iterator();
                    while (iterator.hasNext()) {
                        L1MapCheck temp = iterator.next();
                        if (pc.getMapId() == temp.getMapid()) {
                            if (temp.getStartX() == 0) {
                                isCheck = true;
                                break;
                            }
                            if (pc.getX() >= temp.getStartX() && pc.getX() <= temp.getEndX()
                                    && pc.getY() >= temp.getStartY() && pc.getY() <= temp.getEndY()) {
                                isCheck = true;
                                break;
                            }
                            continue;
                        }
                    }
                    if (isCheck) {
                        boolean isPassable2 = pc.getMap().isOriginalTile(newlocx, newlocy);
                        if (!isPassable2) {
                            isError = true;
                        }
                    }
                }
                if (pc.checkPassable(newlocx, newlocy)) {
                    isError = true;
                }
                if (isError) {
                    pc.setTeleportX(pc.getOleLocX());
                    pc.setTeleportY(pc.getOleLocY());
                    pc.setTeleportMapId(pc.getMapId());
                    pc.setTeleportHeading(pc.getHeading());
                    Teleportation.teleportation(pc);
                    return;
                }
            } catch (Exception e) {
                C_MoveChar._log.error(e.getLocalizedMessage(), e);
            }
            int result = pc.speed_Attack()
                    .checkInterval(com.lineage.server.clientpackets.AcceleratorChecker.ACT_TYPE.MOVE);
            if (result == 2) {
                C_MoveChar._log.error("要求角色移動:速度異常(" + pc.getName() + ")");
            }
            CheckUtil.isUserMap(pc);
            if (DungeonTable.get().dg(newlocx, newlocy, pc.getMap().getId(), pc)) {
                return;
            }
            if (DungeonRTable.get().dg(newlocx, newlocy, pc.getMap().getId(), pc)) {
                return;
            }
            // 記錄移動前座標
            pc.setOleLocX(oleLocx);
            pc.setOleLocY(oleLocy);

            // 設置新作標點
            pc.getLocation().set(newlocx, newlocy);

            // 設置新面向
            pc.setHeading(heading);

            if (!pc.isGmInvis() && !pc.isGhost() && !pc.isInvisble()) {
                pc.broadcastPacketAll(new S_MoveCharPacket(pc));
            }
            pc.setNpcSpeed();

            setNpcSpeed(pc);
            pc.getMap().setPassable(pc.getLocation(), false);
            WorldTrap.get().onPlayerMoved(pc);
            if (pc.getHierarchs() != null && L1CastleLocation.checkInAllWarArea(pc.getX(), pc.getY(), pc.getMapId())) {
                pc.getHierarchs().deleteHierarch();
            }
            if (!ConfigOther.war_pet_summ && !pc.getPetList().isEmpty()
                    && L1CastleLocation.checkInAllWarArea(pc.getX(), pc.getY(), pc.getMapId())) {
                Object[] array;
                int length = (array = pc.getPetList().values().toArray()).length;
                int i = 0;
                while (i < length) {
                    Object obj = array[i];
                    L1NpcInstance petObject = (L1NpcInstance) obj;
                    if (petObject != null) {
                        if (petObject instanceof L1PetInstance) {
                            L1PetInstance pet = (L1PetInstance) petObject;
                            pet.collect(true);
                            pc.removePet(pet);
                            pet.deleteMe();
                        }
                        if (petObject instanceof L1SummonInstance) {
                            L1SummonInstance summon = (L1SummonInstance) petObject;
                            S_NewMaster packet = new S_NewMaster(summon);
                            if (summon != null) {
                                if (summon.destroyed()) {
                                    return;
                                }
                                summon.Death(null);
                            }
                        }
                    }
                    ++i;
                }
            }
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    private void setNpcSpeed(L1PcInstance pc) {
        Map<Integer, L1SkinInstance> skinList = pc.getSkins();
        if (skinList.size() > 0) {
            Iterator<Integer> iterator = skinList.keySet().iterator();
            while (iterator.hasNext()) {
                Integer gfxid = iterator.next();
                L1SkinInstance skin = skinList.get(gfxid);
                skin.setX(pc.getX());
                skin.setY(pc.getY());
                skin.setMap(pc.getMap());
                skin.setHeading(pc.getHeading());
                if (skin.getMoveType() == 0) {
                    Iterator<L1PcInstance> iterator2 = World.get().getVisiblePlayer(skin).iterator();
                    while (iterator2.hasNext()) {
                        L1PcInstance visiblePc = iterator2.next();
                        visiblePc.removeKnownObject(skin);
                    }
                    skin.broadcastPacketAll(new S_NPCPack_Skin(skin));
                } else {
                    skin.setNpcMoveSpeed();
                    skin.broadcastPacketAll(new S_MoveCharPacket(skin));
                }
            }
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
