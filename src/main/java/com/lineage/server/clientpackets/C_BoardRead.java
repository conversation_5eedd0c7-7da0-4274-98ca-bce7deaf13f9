package com.lineage.server.clientpackets;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.WorldNpc;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_BoardRead extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_BoardRead.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final int objId = this.readD();
			final int topicNumber = this.readD();
			final L1NpcInstance npc = WorldNpc.get().map().get(Integer.valueOf(objId));
			if (npc == null) {
				return;
			}
			final L1PcInstance pc = client.getActiveChar();
			if (pc == null) {
				return;
			}
			if (npc.ACTION != null) {
				npc.ACTION.action(pc, npc, "r", topicNumber);
			}
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
