package com.lineage.server.clientpackets;

import java.util.Iterator;
import java.util.List;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_FixWeaponList;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.ArrayList;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_FixWeaponList extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_FixWeaponList.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			final L1PcInstance pc = client.getActiveChar();
			final List<L1ItemInstance> weaponList = new ArrayList();
			final List<L1ItemInstance> itemList = pc.getInventory().getItems();
			final Iterator<L1ItemInstance> iterator = itemList.iterator();
			while (iterator.hasNext()) {
				final L1ItemInstance item = iterator.next();
				switch (item.getItem().getType2()) {
				case 1: {
					if (item.get_durability() <= 0) {
						continue;
					}
					weaponList.add(item);
				}
				default: {
					continue;
				}
				}
			}
			pc.sendPackets(new S_FixWeaponList(weaponList));
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
