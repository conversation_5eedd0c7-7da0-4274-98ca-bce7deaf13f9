package com.lineage.server.clientpackets;

import com.lineage.config.ConfigOther;
import com.lineage.data.event.ShopXSet;
import com.lineage.data.npc.shop.Npc_ShopX;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.Instance.character.CharacterPunishInstance;
import com.lineage.server.Shutdown;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.*;
import com.lineage.server.model.Instance.*;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.shop.L1Shop;
import com.lineage.server.model.shop.L1ShopBuyOrderList;
import com.lineage.server.model.shop.L1ShopSellOrderList;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1Item;
import com.lineage.server.templates.L1ItemUpdate;
import com.lineage.server.templates.L1PrivateShopBuyList;
import com.lineage.server.templates.L1PrivateShopSellList;
import com.lineage.server.timecontroller.event.GamblingTime;
import com.lineage.server.timecontroller.server.ServerRestartTimer;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;

public class C_Result extends ClientBasePacket {
    public static final Log _log;
    public static final Random _random;

    static {
        _log = LogFactory.getLog(C_Result.class);
        _random = new Random();
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();

            if (pc.isGhost()) { // 鬼魂模式
                return;
            }

            if (pc.isDead()) { // 死亡
                return;
            }

            if (pc.isTeleport()) { // 傳送中
                return;
            }

            if (pc.isPrivateShop()) { // 商店村模式
                return;
            }

            //檢查同帳號連線數
            if (World.get().checkPcCountForAccount(pc.getAccountName()) > 1) {
                pc.sendPackets(new S_Disconnect());
                pc.getNetConnection().kick();// 中斷
                return;
            }

            int npcObjectId = readD();
            int resultType = readC();
            int size = readC();
            int unknown = readC();
            int npcId = 0;
            boolean isPrivateShop = false;
            L1Object findObject = World.get().findObject(npcObjectId);
            if (findObject != null) {
                boolean isStop = false;
                if (findObject instanceof L1NpcInstance) {
                    L1NpcInstance targetNpc = (L1NpcInstance) findObject;
                    npcId = targetNpc.getNpcTemplate().get_npcId();
                    isStop = true;
                } else if (findObject instanceof L1PcInstance) {
                    isPrivateShop = true;
                    isStop = true;
                }
                if (isStop) {
                    int diffLocX = Math.abs(pc.getX() - findObject.getX());
                    Math.abs(pc.getY() - findObject.getY());
                }
            }
            switch (resultType) {
                case 0: {
                    if (size <= 0) {
                        break;
                    }
                    if (findObject instanceof L1MerchantInstance) {
                        switch (npcId) {
                            case 70535: {
                                mode_shopS(pc, size);
                                break;
                            }
                            case 210109: {
                                mode_shopS(pc, size);
                                break;
                            }
                            default: {
                                mode_buy(pc, npcId, size);
                                break;
                            }
                        }
                        return;
                    }
                    if (findObject instanceof L1GamblingInstance) {
                        mode_gambling(pc, npcId, size, true);
                        return;
                    }
                    if (findObject instanceof L1CnInstance) {
                        mode_cn_buy(pc, size);
                        return;
                    }
                    if (findObject instanceof L1DeInstance) {
                        L1DeInstance de = (L1DeInstance) findObject;
                        mode_buyde(pc, de, size);
                        return;
                    }
                    if (pc.equals(findObject)) {
                        mode_cn_buy(pc, size);
                        return;
                    }
                    if (findObject instanceof L1PcInstance && isPrivateShop) {
                        L1PcInstance targetPc = (L1PcInstance) findObject;
                        mode_buypc(pc, targetPc, size);
                        return;
                    }
                    break;
                }
                case 1: {
                    if (size <= 0) {
                        break;
                    }
                    if (findObject instanceof L1MerchantInstance) {
                        switch (npcId) {
                            case 70023: {
                                mode_sellall(pc, size);
                                break;
                            }
                            case 111414: {
                                mode_firecrystal(pc, size);
                                break;
                            }
                            default: {
                                mode_sell(pc, npcId, size);
                                break;
                            }
                        }
                        return;
                    }
                    if (findObject instanceof L1CnInstance) {
                        mode_cn_sell(pc, size);
                        return;
                    }
                    if (findObject instanceof L1GamblingInstance) {
                        mode_gambling(pc, npcId, size, false);
                        return;
                    }
                    if (findObject instanceof L1DeInstance) {
                        if (findObject instanceof L1DeInstance) {
                            L1DeInstance de = (L1DeInstance) findObject;
                            mode_sellde(pc, de, size);
                        }
                        return;
                    }
                    if (findObject instanceof L1PcInstance && isPrivateShop) {
                        L1PcInstance targetPc = (L1PcInstance) findObject;
                        mode_sellpc(pc, targetPc, size);
                        break;
                    }
                    break;
                }
                case 2: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= ConfigOther.warehouselevel) {
                        mode_warehouse_in(pc, npcId, size);
                        break;
                    } else { //Kevin ConfigOther設定warehouse等級限制
                        pc.sendPackets(new S_ServerMessage("等級低於" + ConfigOther.warehouselevel + "級無法使用帳號倉庫"));
                    }
                    break;
                }
                case 3: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= 5) {
                        mode_warehouse_out(pc, npcId, size);
                        break;
                    }
                    break;
                }
                case 4: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= ConfigOther.warehouselevel) {
                        mode_warehouse_clan_in(pc, npcId, size);
                        break;
                    } else { //Kevin ConfigOther設定warehouse等級限制
                        pc.sendPackets(new S_ServerMessage("等級低於" + ConfigOther.warehouselevel + "級無法使用血盟倉庫"));
                    }
                    break;
                }
                case 5: {
                    if (size > 0) {
                        if (findObject instanceof L1DwarfInstance) {
                            int level = pc.getLevel();
                            if (level >= 5) {
                                mode_warehouse_clan_out(pc, npcId, size);
                            }
                        }
                    } else {
                        L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                        if (clan != null) {
                            clan.setWarehouseUsingChar(0);
                        }
                    }
                }
                case 6: {
                    return;
                }
                case 8: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= ConfigOther.warehouselevel && pc.isElf()) {
                        mode_warehouse_elf_in(pc, npcId, size);
                        break;
                    } else { //Kevin ConfigOther設定warehouse等級限制
                        pc.sendPackets(new S_ServerMessage("等級低於" + ConfigOther.warehouselevel + "級無法使用妖精倉庫"));
                    }
                    break;
                }
                case 9: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= 5 && pc.isElf()) {
                        mode_warehouse_elf_out(pc, npcId, size);
                        break;
                    }
                    break;
                }
                case 10: {
                    if (size > 0) {
                        switch (npcId) {
                            case 111410: {
                                mode_update_item(pc, size, npcObjectId);
                                return;
                            }
                        }
                    }
                }
                case 12: {
                    if (size > 0) {
                        switch (npcId) {
                            case 70535: {
                                mode_shop_item(pc, size, npcObjectId);
                                break;
                            }
                        }
                        break;
                    }
                    break;
                }
                case 17: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    //Kevin 存入角色專屬倉庫防呆機制 需有95313道具才能使用
                    if (pc.getInventory().checkItem(95313)) {
                        if (level >= ConfigOther.warehouselevel) {
                            mode_warehouse_cha_in(pc, npcId, size);
                            break;
                        } else { //Kevin ConfigOther設定warehouse等級限制
                            pc.sendPackets(new S_ServerMessage("等級低於" + ConfigOther.warehouselevel + "級無法使用專屬倉庫"));
                        }
                    } else {
                        pc.sendPackets(new S_ServerMessage("\\aE你沒有鑰匙無法使用專屬倉庫"));
                    }
                    break;
                }
                case 18: {
                    if (size <= 0 || !(findObject instanceof L1DwarfInstance)) {
                        break;
                    }
                    int level = pc.getLevel();
                    if (level >= 5) {
                        mode_warehouse_cha_out(pc, npcId, size);
                        break;
                    }
                    break;
                }
            }
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    private void mode_update_item(L1PcInstance pc, int size, int npcObjectId) {
        try {
            if (size != 1) {
                pc.sendPackets(new S_ServerMessage("\\fR你只能選取一樣裝備用來升級。"));
                pc.sendPackets(new S_CloseList(pc.getId()));
                return;
            }
            int orderId = readD();
            int count = Math.max(0, readD());
            if (count != 1) {
                pc.sendPackets(new S_CloseList(pc.getId()));
                return;
            }
            L1ItemInstance item = pc.getInventory().getItem(orderId);
            ArrayList<L1ItemUpdate> items = ItemUpdateTable.get().get(item.getItemId());
            String[] names = new String[items.size()];
            int index = 0;
            while (index < items.size()) {
                int toid = items.get(index).get_toid();
                L1Item tgitem = ItemTable.get().getTemplate(toid);
                if (tgitem != null) {
                    names[index] = tgitem.getName();
                }
                ++index;
            }
            pc.set_mode_id(orderId);
            pc.sendPackets(new S_NPCTalkReturn(npcObjectId, "y_up_i1", names));
        } catch (Exception e) {
            _log.error("升級裝備物品數據異常: " + pc.getName());
        }
    }

    private void mode_shopS(L1PcInstance pc, int size) {
        try {
            Map<Integer, Integer> sellScoreMapMap = new HashMap();
            int i = 0;
            while (i < size) {
                int orderId = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                } else {
                    if (Shutdown.isSHUTDOWN) {
                        pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                        return;
                    }
                    sellScoreMapMap.put(new Integer(orderId), new Integer(count));
                }
                ++i;
            }
            pc.get_otherList().get_buyCnS(sellScoreMapMap);
        } catch (Exception e) {
            _log.error("購買人物託售物品數據異常: " + pc.getName());
        }
    }

    private void mode_shop_item(L1PcInstance pc, int size, int npcObjectId) {
        try {
            if (size == 1) {
                int objid = readD();
                L1Object object = pc.getInventory().getItem(objid);
                boolean isError = false;
                if (object instanceof L1ItemInstance) {
                    L1ItemInstance item = (L1ItemInstance) object;
                    if (!item.getItem().isTradable()) {
                        isError = true;
                    }
                    if (item.isEquipped()) {
                        isError = true;
                    }
                    if (!item.isIdentified()) {
                        isError = true;
                    }
                    if (item.getItem().getMaxUseTime() != 0) {
                        isError = true;
                    }
                    if (item.get_time() != null) {
                        isError = true;
                    }
                    if (ShopXTable.get().getTemplate(item.getItem().getItemId()) != null) {
                        isError = true;
                    }
                    Object[] petlist = pc.getPetList().values().toArray();
                    Object[] array;
                    int length = (array = petlist).length;
                    int i = 0;
                    while (i < length) {
                        Object petObject = array[i];
                        if (petObject instanceof L1PetInstance) {
                            L1PetInstance pet = (L1PetInstance) petObject;
                            if (item.getId() == pet.getItemObjId()) {
                                isError = true;
                            }
                        }
                        ++i;
                    }
                    if (pc.getDoll(item.getId()) != null) {
                        isError = true;
                    }
                    if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                        isError = true;
                    }
                    if (item.getraceGamNo() != null) {
                        isError = true;
                    }
                    if (item.getEnchantLevel() < 0) {
                        isError = true;
                    }
                    if (item.getItem().getMaxChargeCount() != 0 && item.getChargeCount() <= 0) {
                        isError = true;
                    }
                    if (isError) {
                        pc.sendPackets(new S_NPCTalkReturn(npcObjectId, "y_x_e1"));
                    } else {
                        L1ItemInstance itemT = pc.getInventory().checkItemX(Npc_ShopX._itemid, ShopXSet.ADENA);
                        if (itemT == null) {
                            pc.sendPackets(
                                    new S_ServerMessage(337, ItemTable.get().getTemplate(Npc_ShopX._itemid).getName()));
                            pc.sendPackets(new S_CloseList(pc.getId()));
                            return;
                        }
                        pc.get_other().set_item(item);
                        pc.sendPackets(new S_CnsSell(npcObjectId, "y_x_3", "ma"));
                    }
                }
            } else {
                pc.sendPackets(new S_NPCTalkReturn(npcObjectId, "y_x_e"));
            }
        } catch (Exception e) {
            _log.error("人物託售物品數據異常: " + pc.getName());
        }
    }

    private void mode_sellall(L1PcInstance pc, int size) {
        try {
            Map<Integer, Integer> sellallMap = new HashMap();
            int i = 0;
            while (i < size) {
                int objid = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                sellallMap.put(new Integer(objid), new Integer(count));
                ++i;
            }
            pc.get_otherList().sellall(sellallMap);
        } catch (Exception e) {
            _log.error("玩家賣出物品給予萬物回收商數據異常: " + pc.getName());
        }
    }

    private void mode_cn_buy(L1PcInstance pc, int size) {
        try {
            Map<Integer, Integer> cnMap = new HashMap();
            int i = 0;
            while (i < size) {
                int orderId = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (count > 9999) {
                    _log.error("要求列表物品取得傳回數量大於9999: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                cnMap.put(new Integer(orderId), new Integer(count));
                ++i;
            }
            pc.get_otherList().get_buyCn(cnMap);
        } catch (Exception e) {
            _log.error("玩家買入商城物品數據異常: " + pc.getName());
        }
    }

    private void mode_cn_sell(L1PcInstance pc, int size) {
        try {
            Map<Integer, Integer> cnsellMap = new HashMap();
            int i = 0;
            while (i < size) {
                int objid = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (count > 9999) {
                    _log.error("要求列表物品取得傳回數量大於9999: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                cnsellMap.put(new Integer(objid), new Integer(count));
                ++i;
            }
            pc.get_otherList().sellcnitem(cnsellMap);
        } catch (Exception e) {
            _log.error("玩家賣出物品給予商城道具回收專員數據異常: " + pc.getName());
        }
    }

    private void mode_firecrystal(L1PcInstance pc, int size) {
        try {
            Map<Integer, Integer> FCMap = new HashMap();
            int i = 0;
            while (i < size) {
                int objid = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                FCMap.put(new Integer(objid), new Integer(count));
                ++i;
            }
            pc.get_otherList().sellforfirecrystal(FCMap);
        } catch (Exception e) {
            _log.error("玩家賣出物品取得火神結晶數據異常: " + pc.getName());
        }
    }

    private void mode_gambling(L1PcInstance pc, int npcId, int size, boolean isShop) {
        if (isShop) {
            if (GamblingTime.isStart()) {
                pc.sendPackets(new S_CloseList(pc.getId()));
                return;
            }
            Map<Integer, Integer> gamMap = new HashMap();
            int i = 0;
            while (i < size) {
                int orderId = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                    return;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                gamMap.put(new Integer(orderId), new Integer(count));
                ++i;
            }
            pc.get_otherList().get_buyGam(gamMap);
        } else {
            int j = 0;
            while (j < size) {
                int objid = readD();
                int count2 = Math.max(0, readD());
                if (count2 <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                } else {
                    pc.get_otherList().get_sellGam(objid, count2);
                }
                ++j;
            }
        }
    }

    private void mode_warehouse_elf_out(L1PcInstance pc, int npcId, int size) {
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求精靈倉庫取出傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }
            L1ItemInstance item = pc.getDwarfForElfInventory().getItem(objectId);
            if (item == null) {
                _log.error("精靈倉庫取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (!DwarfForElfReading.get().getUserItems(pc.getAccountName(), objectId, count)) {
                _log.error("精靈倉庫取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            if (pc.getInventory().checkDBResultItemCount(pc.getId(), item.getId(), count, 2)) {
                return;
            }
            if (!isAvailableTrade(pc, objectId, item, count)) {
                break;
            }
            if (objectId != item.getId()) {
                pc.sendPackets(new S_Disconnect());
                break;
            }
            if (!item.isStackable() && count != 1) {
                pc.sendPackets(new S_Disconnect());
                break;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (item.getCount() > 10000000L) {
                break;
            }
            if (count > 10000000) {
                break;
            }
            if (count > item.getCount()) {
                count = (int) item.getCount();
            }
            if (pc.getInventory().checkAddItem(item, count) != 0) {
                pc.sendPackets(new S_ServerMessage(270));
                break;
            }
            if (!pc.getInventory().consumeItem(40494, 2L)) {
                pc.sendPackets(new S_ServerMessage(337, "$767"));
                break;
            }
            RecordTable.get().recordeWarehouse_elf(pc.getName(), "領取", "妖倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            pc.getDwarfForElfInventory().tradeItem(item, count, pc.getInventory());
            ++i;
        }
    }

    private void mode_warehouse_elf_in(L1PcInstance pc, int npcId, int size) {
        if (ServerRestartTimer.isRtartTime()) {
            pc.sendPackets(new S_SystemMessage("伺服器即將關機 無法使用"));
            return;
        }
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求精靈倉庫存入傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }
            L1Object object = pc.getInventory().getItem(objectId);
            if (object == null) {
                _log.error("人物背包資料取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (!CharItemsReading.get().getUserItems(pc.getId(), objectId, count)) {
                _log.error("人物背包資料取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            L1ItemInstance item = (L1ItemInstance) object;
            //Kevin 原本44070無法存進倉庫
            /*if (item.getItemId() == 44070) {
                pc.sendPackets(new S_ServerMessage("元寶無法存放倉庫"));
                return;
            }*/
            //Kevin Willie 限制240205不受Trade限制可存入倉庫
            if (item.getItemId() == 40308) {
                pc.sendPackets(new S_ServerMessage("金幣無法存入倉庫"));
                return;
            }
            if (item.getItemId() != 240205 && !item.getItem().isTradable()) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                return;
            }
            if (item.get_time() != null) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                return;
            }
            if (!isAvailableTrade(pc, objectId, item, count)) {
                return;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (item.getCount() > 100000000L) {
                break;
            }
            if (count > 100000000) {
                break;
            }
            Object[] petlist = pc.getPetList().values().toArray();
            Object[] array;
            int length = (array = petlist).length;
            int j = 0;
            while (j < length) {
                Object petObject = array[j];
                if (petObject instanceof L1PetInstance) {
                    L1PetInstance pet = (L1PetInstance) petObject;
                    if (item.getId() == pet.getItemObjId()) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        break;
                    }
                }
                ++j;
            }
            if (pc.getDoll(item.getId()) != null) {
                pc.sendPackets(new S_ServerMessage(1181));
                return;
            }
            if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                pc.sendPackets(new S_ServerMessage(1181));
                break;
            }
            if (pc.getDwarfForElfInventory().checkAddItemToWarehouse(item, count, 0) == 1) {
                pc.sendPackets(new S_ServerMessage(75));
                break;
            }
            RecordTable.get().recordeWarehouse_elf(pc.getName(), "存入", "妖倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            pc.getInventory().tradeItem(objectId, count, pc.getDwarfForElfInventory());
            ++i;
        }
    }

    private void mode_warehouse_clan_out(L1PcInstance pc, int npcId, int size) {
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        L1Clan clan = WorldClan.get().getClan(pc.getClanname());
        try {
            if (clan != null) {
                int i = 0;
                while (i < size) {
                    int objectId = readD();
                    int count = Math.max(0, readD());
                    if (count <= 0) {
                        _log.error("要求血盟倉庫取出傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                        return;
                    }
                    L1ItemInstance item = clan.getDwarfForClanInventory().getItem(objectId);
                    if (item == null) {
                        _log.error("血盟倉庫取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                                + pc.getNetConnection().kick());
                        return;
                    }
                    if (!DwarfForClanReading.get().getUserItems(pc.getClanname(), objectId, count)) {
                        _log.error("血盟倉庫取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/"
                                + pc.getNetConnection().hashCode() + pc.getNetConnection().kick());
                        return;
                    }
                    if (Shutdown.isSHUTDOWN) {
                        pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                        return;
                    }
                    if (clan.getWarehouseUsingChar() != pc.getId()) {
                        pc.sendPackets(new S_ServerMessage("血盟倉庫有人正在使用中。"));
                        return;
                    }
                    if (pc.getInventory().checkDBResultItemCount(pc.getId(), item.getId(), count, 2)) {
                        return;
                    }
                    if (item == null || item.getCount() < count || count <= 0) {
                        break;
                    }
                    if (item.getCount() <= 0L) {
                        break;
                    }
                    if (!isAvailableTrade(pc, objectId, item, count)) {
                        break;
                    }
                    if (pc.getInventory().checkAddItem(item, count) != 0) {
                        pc.sendPackets(new S_ServerMessage(270));
                        break;
                    }
                    if (!pc.getInventory().consumeItem(40308, 30L)) {
                        pc.sendPackets(new S_ServerMessage(189));
                        break;
                    }
                    RecordTable.get().recordeWarehouse_clan(pc.getName(), "領取", "盟倉", item.getAllName(), count,
                            item.getId(), pc.getIp());
                    clan.getDwarfForClanInventory().tradeItem(item, count, pc.getInventory());
                    clan.getDwarfForClanInventory().writeHistory(pc, item, count, 1);
                    ++i;
                }
                if (clan != null) {
                    clan.setWarehouseUsingChar(0);
                }
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    private void mode_warehouse_clan_in(L1PcInstance pc, int npcId, int size) {
        if (ServerRestartTimer.isRtartTime()) {
            pc.sendPackets(new S_SystemMessage("伺服器即將關機 無法使用"));
            return;
        }
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        L1Clan clan = WorldClan.get().getClan(pc.getClanname());
        try {
            if (clan != null) {
                int i = 0;
                while (i < size) {
                    int objectId = readD();
                    int count = Math.max(0, readD());
                    if (count <= 0) {
                        _log.error("要求血盟倉庫存入傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                        return;
                    }
                    L1Object object = pc.getInventory().getItem(objectId);
                    if (object == null) {
                        _log.error("人物背包資料取出數據異常(物品為空): " + pc.getName() + "/"
                                + pc.getNetConnection().hashCode() + pc.getNetConnection().kick());
                        return;
                    }
                    if (!CharItemsReading.get().getUserItems(pc.getId(), objectId, count)) {
                        _log.error("人物背包資料取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/"
                                + pc.getNetConnection().hashCode() + pc.getNetConnection().kick());
                        return;
                    }
                    if (Shutdown.isSHUTDOWN) {
                        pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                        return;
                    }
                    L1ItemInstance item = (L1ItemInstance) object;
                    //Kevin 原本44070無法存進倉庫
                    /*if (item.getItemId() == 44070) {
                        pc.sendPackets(new S_ServerMessage("元寶無法存放倉庫"));
                        break;
                    }*/
                    //Kevin Willie 限制240205不受Trade限制可存入倉庫
                    if (item.getItemId() == 40308) {
                        pc.sendPackets(new S_ServerMessage("金幣無法存入倉庫"));
                        return;
                    }
                    if (item.getItemId() != 240205 && !item.getItem().isTradable()) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        return;
                    }
                    if (item.get_time() != null) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        return;
                    }

                    if (ItemRestrictionsTable.get().checkItemRestrictions(item.getItemId(), pc)) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        return; //丹丹 Kevin新增限制親友
                    }
                    if (!isAvailableTrade(pc, objectId, item, count)) {
                        return;
                    }
                    if (item == null || item.getCount() < count || count <= 0) {
                        break;
                    }
                    if (item.getCount() <= 0L) {
                        break;
                    }
                    if (item.getCount() > 100000000L) {
                        break;
                    }
                    if (count > 100000000) {
                        break;
                    }
                    Object[] petlist = pc.getPetList().values().toArray();
                    Object[] array;
                    int length = (array = petlist).length;
                    int j = 0;
                    while (j < length) {
                        Object petObject = array[j];
                        if (petObject instanceof L1PetInstance) {
                            L1PetInstance pet = (L1PetInstance) petObject;
                            if (item.getId() == pet.getItemObjId()) {
                                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                                break;
                            }
                        }
                        ++j;
                    }
                    if (pc.getDoll(item.getId()) != null) {
                        pc.sendPackets(new S_ServerMessage(1181));
                        return;
                    }
                    if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                        pc.sendPackets(new S_ServerMessage(1181));
                        break;
                    }
                    if (clan.getDwarfForClanInventory().checkAddItemToWarehouse(item, count, 1) == 1) {
                        pc.sendPackets(new S_ServerMessage(75));
                        return;
                    }
                    RecordTable.get().recordeWarehouse_clan(pc.getName(), "存入", "盟倉", item.getAllName(), count,
                            item.getId(), pc.getIp());
                    pc.getInventory().tradeItem(objectId, count, clan.getDwarfForClanInventory());
                    clan.getDwarfForClanInventory().writeHistory(pc, item, count, 0);
                    ++i;
                }
            } else {
                pc.sendPackets(new S_ServerMessage(208));
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    private void mode_warehouse_out(L1PcInstance pc, int npcId, int size) {
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求帳號倉庫取出傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }
            L1ItemInstance item = pc.getDwarfInventory().getItem(objectId);
            if (item == null) {
                _log.error("帳號倉庫取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            if (pc.getInventory().checkDBResultItemCount(pc.getId(), item.getId(), count, 3)) {
                return;
            }
            if (pc.getInventory().checkDBResultItemCount(pc.getId(), item.getId(), count, 2)) {
                return;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (!isAvailableTrade(pc, objectId, item, count)) {
                break;
            }
            if (pc.getInventory().checkAddItem(item, count) != 0) {
                pc.sendPackets(new S_ServerMessage(270));
                break;
            }
            if (!pc.getInventory().consumeItem(40308, 30L)) {
                pc.sendPackets(new S_ServerMessage(189));
                break;
            }
            RecordTable.get().recordeWarehouse_pc(pc.getName(), "領取", "個倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            pc.getDwarfInventory().tradeItem(item, count, pc.getInventory());
            ++i;
        }

    }

    private void mode_warehouse_in(L1PcInstance pc, int npcId, int size) {
        if (ServerRestartTimer.isRtartTime()) {
            pc.sendPackets(new S_SystemMessage("伺服器即將關機 無法使用"));
            return;
        }
        if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
            return;
        }
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求帳號倉庫存入傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }

            L1Object object = pc.getInventory().getItem(objectId);
            if (object == null) {
                _log.error("人物背包資料取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (!CharItemsReading.get().getUserItems(pc.getId(), objectId, count)) {
                _log.error("人物背包資料取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            L1ItemInstance item = (L1ItemInstance) object;
            if (item.getCount() <= 0L) {
                break;
            }

            if (item.getItemId() == 40308) {
                pc.sendPackets(new S_ServerMessage("金幣無法存入倉庫"));
                return;
            }
            //Kevin 原本44070無法存進倉庫
            /*if (item.getItemId() == 44070) {
                pc.sendPackets(new S_ServerMessage("元寶無法存放倉庫"));
                break;
            }*/
            if (!isAvailableTrade(pc, objectId, item, count)) {
                return;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (item.getCount() > 100000000L) {
                break;
            }
            if (count > 100000000) {
                break;
            }
            //Kevin Willie 新增 240205可以存進倉庫，不受Trade限制
            if (item.getItemId() != 240205 && !item.getItem().isTradable()) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                break;
            }
            if (item.get_time() != null) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                break;
            }
            Object[] petlist = pc.getPetList().values().toArray();
            Object[] array;
            int length = (array = petlist).length;
            int j = 0;
            while (j < length) {
                Object petObject = array[j];
                if (petObject instanceof L1PetInstance) {
                    L1PetInstance pet = (L1PetInstance) petObject;
                    if (item.getId() == pet.getItemObjId()) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        break;
                    }
                }
                ++j;
            }
            if (pc.getDoll(item.getId()) != null) {
                pc.sendPackets(new S_ServerMessage(1181));
                break;
            }
            if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                pc.sendPackets(new S_ServerMessage(1181));
                break;
            }
            if (pc.getDwarfInventory().checkAddItemToWarehouse(item, count, 0) == 1) {
                pc.sendPackets(new S_ServerMessage(75));
                break;
            }


            pc.getInventory().tradeItem(objectId, count, pc.getDwarfInventory());
            RecordTable.get().recordeWarehouse_pc(pc.getName(), "存入", "個倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            ++i;
        }
    }

    private void mode_warehouse_cha_out(L1PcInstance pc, int npcId, int size) {
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求角色專屬倉庫取出傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }
            L1ItemInstance item = pc.getDwarfForChaInventory().getItem(objectId);
            if (item == null) {
                _log.error("角色專屬倉庫取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (!DwarfForChaReading.get().getUserItems(pc.getName(), objectId, count)) {
                _log.error("角色專屬倉庫取出數據異常(該倉庫指定數據有誤): " + pc.getName() + "/" + pc.getNetConnection().hashCode()
                        + pc.getNetConnection().kick());
                return;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            if (pc.getInventory().checkDBResultItemCount(pc.getId(), item.getId(), count, 1)) {
                return;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (!isAvailableTrade(pc, objectId, item, count)) {
                break;
            }
            if (pc.getInventory().checkAddItem(item, count) != 0) {
                pc.sendPackets(new S_ServerMessage(270));
                break;
            }
            if (!pc.getInventory().consumeItem(40308, 30L)) {
                pc.sendPackets(new S_ServerMessage(189));
                break;
            }
            RecordTable.get().recordWarehouse_char_pc(pc.getName(), "領取", "角倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            pc.getDwarfForChaInventory().tradeItem(item, count, pc.getInventory());
            ++i;
        }
    }

    private void mode_warehouse_cha_in(L1PcInstance pc, int npcId, int size) {
        if (ServerRestartTimer.isRtartTime()) {
            pc.sendPackets(new S_SystemMessage("伺服器即將關機 無法使用"));
            return;
        }
        int i = 0;
        while (i < size) {
            int objectId = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求角色專屬倉庫存入傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                return;
            }

            L1Object object = pc.getInventory().getItem(objectId);
            if (object == null) {
                _log.error("人物背包資料取出數據異常(物品為空): " + pc.getName() + "/" + pc.getNetConnection().hashCode());
                return;
            }
            if (!CharItemsReading.get().getUserItems(pc.getId(), objectId, count)) {
                _log
                        .error("人物背包資料取出數據異常(該人物背包指定數據有誤): " + pc.getName() + "/" + pc.getNetConnection().hashCode());
                return;
            }
            L1ItemInstance item = (L1ItemInstance) object;
            if (item.getCount() <= 0L) {
                return;
            }
            if (!isAvailableTrade(pc, objectId, item, count)) {
                return;
            }
            if (item == null || item.getCount() < count || count <= 0) {
                break;
            }
            if (item.getCount() <= 0L) {
                break;
            }
            if (item.getCount() > 100000000L) {
                break;
            }
            if (count > 100000000) {
                break;
            }
            if (Shutdown.isSHUTDOWN) {
                pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                return;
            }
            //Kevin 原本44070無法存進倉庫
           /* if (item.getItemId() == 44070) {
                pc.sendPackets(new S_ServerMessage("元寶無法存放倉庫"));
                return;
            }*/
            //Kevin Willie 限制240205不受Trade限制可存入倉庫
            if (item.getItemId() == 40308) {
                pc.sendPackets(new S_ServerMessage("金幣無法存入倉庫"));
                return;
            }
            if (item.getItemId() != 240205 && !item.getItem().isTradable()) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                break;
            }
            if (item.get_time() != null) {
                pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                break;
            }
            Object[] petlist = pc.getPetList().values().toArray();
            Object[] array;
            int length = (array = petlist).length;
            int j = 0;
            while (j < length) {
                Object petObject = array[j];
                if (petObject instanceof L1PetInstance) {
                    L1PetInstance pet = (L1PetInstance) petObject;
                    if (item.getId() == pet.getItemObjId()) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                        break;
                    }
                }
                ++j;
            }
            if (pc.getDoll(item.getId()) != null) {
                pc.sendPackets(new S_ServerMessage(1181));
                break;
            }
            if (pc.get_power_doll() != null && pc.get_power_doll().getItemObjId() == item.getId()) {
                pc.sendPackets(new S_ServerMessage(1181));
                break;
            }
            if (pc.getDwarfForChaInventory().checkAddItemToWarehouse(item, count, 0) == 1) {
                pc.sendPackets(new S_ServerMessage(75));
                break;
            }
            RecordTable.get().recordWarehouse_char_pc(pc.getName(), "存入", "角倉", item.getAllName(), count, item.getId(),
                    pc.getIp());
            pc.getInventory().tradeItem(objectId, count, pc.getDwarfForChaInventory());
            ++i;
        }
    }

    private void mode_sellpc(L1PcInstance pc, L1PcInstance targetPc, int size) {
        ArrayList<L1PrivateShopBuyList> buyList = targetPc.getBuyList();
        boolean[] isRemoveFromList = new boolean[8];
        if (targetPc.isTradingInPrivateShop()) {
            return;
        }
        int adenaItemId = targetPc.getMap().isUsableShop();
        if (adenaItemId <= 0 || ItemTable.get().getTemplate(adenaItemId) == null) {
            return;
        }
        targetPc.setTradingInPrivateShop(true);
        int i = 0;
        while (i < size) {
            int itemObjectId = readD();
            int count = readCH();
            count = Math.max(0, count);
            if (count <= 0) {
                _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                break;
            }
            int order = readC();
            L1ItemInstance item = pc.getInventory().getItem(itemObjectId);

            if (item != null) {
                if (item.get_time() == null) {
                    L1PrivateShopBuyList psbl = buyList.get(order);
                    int buyItemObjectId = psbl.getItemObjectId();
                    long buyPrice = psbl.getBuyPrice();
                    int buyTotalCount = psbl.getBuyTotalCount();
                    int buyCount = psbl.getBuyCount();
                    if (count > buyTotalCount - buyCount) {
                        count = buyTotalCount - buyCount;
                    }
                    if (item.isEquipped()) {
                        pc.sendPackets(new S_ServerMessage(905));
                    } else {
                        L1ItemInstance srcItem = targetPc.getInventory().getItem(buyItemObjectId);
                        if (srcItem.get_time() == null) {
                            if (item.getItemId() != srcItem.getItemId()
                                    || item.getEnchantLevel() != srcItem.getEnchantLevel()) {
                                _log.error("可能使用bug進行交易 人物名稱(賣出道具給予個人商店/交易條件不吻合): " + pc.getName() + " objid:"
                                        + pc.getId());
                                return;
                            }
                            if (targetPc.getInventory().checkAddItem(item, count) == 0) {
                                int j = 0;
                                while (j < count) {
                                    if (buyPrice * j > 2000000000L) {
                                    }
                                    ++j;
                                }
                                if (!targetPc.getInventory().checkItem(adenaItemId, count * buyPrice)) {
                                    targetPc.sendPackets(new S_ServerMessage(189));
                                    break;
                                }
                                L1ItemInstance adena = targetPc.getInventory().findItemId(adenaItemId);
                                if (adena == null) {
                                    targetPc.sendPackets(new S_ServerMessage(904, "2000000000"));
                                    return;
                                }
                                if (!pc.getInventory().checkDBItemCount(pc.getId(), item.getItemId(), count)) {
                                    targetPc.sendPackets(new S_ServerMessage(904, "2000000000"));
                                    return;
                                }
                                if (item.getCount() < count) {
                                    pc.sendPackets(new S_ServerMessage(989));
                                    _log.error("可能使用bug進行交易 人物名稱(賣出道具給予個人商店/交易數量不吻合): " + pc.getName()
                                            + " objid:" + pc.getId());
                                    targetPc.sendPackets(new S_ServerMessage(904, "2000000000"));
                                    return;
                                }
                                OtherUserSellReading.get().add(item.getItem().getName(), item.getId(), (int) buyPrice,
                                        count, pc.getId(), pc.getName(), targetPc.getId(), targetPc.getName());
                                targetPc.getInventory().tradeItem(adena, count * buyPrice, pc.getInventory());
                                pc.getInventory().tradeItem(item, count, targetPc.getInventory());
                                String message = String.valueOf(item.getItem().getNameId()) + " ("
                                        + String.valueOf(count) + ")";
                                pc.sendPackets(new S_ServerMessage(877, targetPc.getName(), message));
                                psbl.setBuyCount(count + buyCount);
                                buyList.set(order, psbl);
                                if (psbl.getBuyCount() == psbl.getBuyTotalCount()) {
                                    isRemoveFromList[order] = true;
                                }
                            }
                            pc.sendPackets(new S_ServerMessage(271));
                            break;
                        }
                    }
                }
            }

            ++i;
        }
        i = 7;
        while (i >= 0) {
            if (isRemoveFromList[i]) {
                buyList.remove(i);
            }
            --i;
        }
        targetPc.setTradingInPrivateShop(false);
    }

    private void mode_buypc(L1PcInstance pc, L1PcInstance targetPc, int size) {
        ArrayList<L1PrivateShopSellList> sellList = targetPc.getSellList();
        boolean[] isRemoveFromList = new boolean[8];
        // 正在執行交易
        if (targetPc.isTradingInPrivateShop()) {
            return;
        }
        int adenaItemId = targetPc.getMap().isUsableShop();
        if (adenaItemId <= 0 || ItemTable.get().getTemplate(adenaItemId) == null) {
            return;
        }

        // 擺放個人商店的全部物品種類清單
        sellList = targetPc.getSellList();
        synchronized (sellList) {
            // 售出數量異常
            if (pc.getPartnersPrivateShopItemCount() != sellList.size()) {
                return;
            }

            targetPc.setTradingInPrivateShop(true);

            for (int i = 0; i < size; i++) {
                int order = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求買入個人商店物品傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                    break;
                }
                if (Shutdown.isSHUTDOWN) {
                    pc.sendPackets(new S_SystemMessage("目前服務器準備關機狀態"));
                    return;
                }
                // 取回商店賣出的道具資訊
                L1PrivateShopSellList pssl = sellList.get(order);
                int itemObjectId = pssl.getItemObjectId();// 物品objid
                int sellPrice = pssl.getSellPrice();// 售價
                int sellTotalCount = pssl.getSellTotalCount(); // 預計賣出的數量
                int sellCount = pssl.getSellCount();// 已經賣出數量的累計

                // 取回賣出物品資料
                L1ItemInstance item = targetPc.getInventory().getItem(itemObjectId);
                if (item == null) {
                    // 該物件為空的狀態
                    continue;
                }
                if (item.get_time() != null) {
                    // 具有時間
                    continue;
                }

                // 賣出物品 買方選取超出數量
                if (count > sellTotalCount - sellCount) {
                    count = sellTotalCount - sellCount;
                }

                // 賣出物品數量為零
                if (count <= 0) {
                    continue;
                }

                if (pc.getInventory().checkAddItem(item, count) == 0) {
                    for (int j = 0; j < count; j++) { // 計算收入
                        if (sellPrice * j > 2000000000) {
                            // 總共販賣價格無法超過 %d金幣。
                            pc.sendPackets(new S_ServerMessage(904, "2000000000"));
                            targetPc.setTradingInPrivateShop(false);
                            return;
                        }
                    }
                    // 所需花費
                    int price = count * sellPrice;

                    // 取回金幣資料
                    L1ItemInstance adena = pc.getInventory().findItemId(adenaItemId);

                    if (adena == null) {
                        // \f1金幣不足。
                        pc.sendPackets(new S_ServerMessage(189));
                        continue;
                    }
                    // 買入物品玩家金幣數量不足
                    if (adena.getCount() < price) {
                        // \f1金幣不足。
                        pc.sendPackets(new S_ServerMessage(189));
                        continue;
                    }


                    if (adena == null) {
                        pc.sendPackets(new S_ServerMessage(337,
                                ItemTable.get().getTemplate(adenaItemId).getName()));
                        pc.sendPackets(new S_ServerMessage(904, "2000000000"));
                        targetPc.setTradingInPrivateShop(false);
                        return;
                    }
                    if (adena.getCount() < price) {
                        pc.sendPackets(new S_ServerMessage(337,
                                ItemTable.get().getTemplate(adenaItemId).getName()));
                        pc.sendPackets(new S_ServerMessage(904, "2000000000"));
                        targetPc.setTradingInPrivateShop(false);
                        return;
                    }

                    // 買入個人商店物品紀錄
                    OtherUserBuyReading.get().add(item.getItem().getName(), item.getId(), sellPrice, count,
                            pc.getId(), pc.getName(), targetPc.getId(), targetPc.getName());

                    // 轉移道具給予購買者
                    targetPc.getInventory().tradeItem(item, count, pc.getInventory());
                    // 把花費的金幣轉移給設置為商店的玩家
                    pc.getInventory().tradeItem(adena, price, targetPc.getInventory());
                    // 產生訊息
                    String message = String.valueOf(item.getItem().getNameId()) + " ("
                            + String.valueOf(count) + ")";
                    targetPc.sendPackets(new S_ServerMessage(877, pc.getName(), message));

                    // 設置物品已賣出數量
                    pssl.setSellCount(count + sellCount);
                    sellList.set(order, pssl);
                    // 販賣物件已售完
                    if (pssl.getSellCount() == pssl.getSellTotalCount()) {
                        isRemoveFromList[order] = true;
                    }
                } else {
                    pc.sendPackets(new S_ServerMessage(270));
                }
            }
            // 該道具販賣結束移出販賣清單（道具最多擺放8格）
            for (int i = 7; i >= 0; i--) {
                if (isRemoveFromList[i]) {
                    sellList.remove(i);
                }
            }
            targetPc.setTradingInPrivateShop(false);
        }
    }

    private void mode_sell(L1PcInstance pc, int npcId, int size) {
        L1Shop shop = ShopTable.get().get(npcId);
        if (shop != null) {
            L1ShopSellOrderList orderList = shop.newSellOrderList(pc);
            int i = 0;
            while (i < size) {
                int objid = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                } else {
                    orderList.add(objid, count);
                }
                ++i;
            }
            shop.buyItems(pc, orderList);
        } else {
            pc.sendPackets(new S_CloseList(pc.getId()));
        }
    }

    private void mode_buy(L1PcInstance pc, int npcId, int size) {
        L1Shop shop = ShopTable.get().get(npcId);
        if (shop != null) {
            L1ShopBuyOrderList orderList = shop.newBuyOrderList();
            int i = 0;
            while (i < size) {
                int orderId = readD();
                int count = Math.max(0, readD());
                if (count <= 0) {
                    _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
                } else {
                    orderList.add(orderId, count);
                }
                ++i;
            }
            shop.sellItems(pc, orderList);
        } else {
            pc.sendPackets(new S_CloseList(pc.getId()));
        }
    }

    private void mode_sellde(L1PcInstance pc, L1DeInstance de, int size) {
        Map<Integer, int[]> buyList = de.get_buyList();
        Queue<Integer> removeList = new ConcurrentLinkedQueue();
        int i = 0;
        while (i < size) {
            int itemObjectId = readD();
            int count = readCH();
            count = Math.max(0, count);
            if (count <= 0) {
                _log.error("要求列表物品取得傳回數量小於等於0: " + pc.getName() + ":" + pc.getNetConnection().kick());
            } else {
                L1ItemInstance item = pc.getInventory().getItem(itemObjectId);
                if (item != null) {
                    if (item.get_time() != null) {
                        pc.sendPackets(new S_ServerMessage(210, item.getItem().getNameId()));
                    } else if (item.isEquipped()) {
                        pc.sendPackets(new S_ServerMessage(905));
                    } else {
                        int[] sellX = buyList.get(Integer.valueOf(item.getItemId()));
                        if (sellX != null) {
                            int price = sellX[0];
                            int enchantlvl = sellX[1];
                            int buycount = sellX[2];
                            if (item.getEnchantLevel() == enchantlvl && buycount >= count) {
                                if (pc.getInventory().removeItem(itemObjectId, count) == count) {
                                    pc.getInventory().storeItem(40308, price * count);
                                } else {
                                    pc.sendPackets(new S_ServerMessage(989));
                                }
                                int newcount = buycount - count;
                                if (newcount <= 0) {
                                    removeList.add(Integer.valueOf(item.getItemId()));
                                } else {
                                    int[] newSellX = {price, enchantlvl, newcount};
                                    de.updateBuyList(Integer.valueOf(item.getItemId()), newSellX);
                                }
                            }
                        }
                    }
                }
            }
            ++i;
        }
        if (!removeList.isEmpty()) {
            Iterator<Integer> iter = removeList.iterator();
            while (iter.hasNext()) {
                Integer reitem = iter.next();
                iter.remove();
                buyList.remove(reitem);
            }
        }
        pc.get_otherList().DELIST.clear();
    }

    private void mode_buyde(L1PcInstance pc, L1DeInstance de, int size) {
        Map<L1ItemInstance, Integer> sellList = de.get_sellList();
        Queue<L1ItemInstance> removeList = new ConcurrentLinkedQueue();
        int i = 0;
        while (i < size) {
            int order = readD();
            int count = Math.max(0, readD());
            if (count <= 0) {
                _log.error("要求買入個人商店物品傳回數量小於等於0: " + pc.getName() + pc.getNetConnection().kick());
                break;
            }
            Map<Integer, L1ItemInstance> list = pc.get_otherList().DELIST;
            Iterator<Integer> iterator = list.keySet().iterator();
            while (iterator.hasNext()) {
                int key = iterator.next().intValue();
                if (order == key) {
                    L1ItemInstance item = list.get(Integer.valueOf(key));
                    if (item == null) {
                        continue;
                    }
                    if (item.getCount() < count) {
                        continue;
                    }
                    if (pc.getInventory().checkAddItem(item, count) != 0) {
                        pc.sendPackets(new S_ServerMessage(270));
                        break;
                    }
                    long price = count * sellList.get(item).intValue();
                    if (!pc.getInventory().consumeItem(40308, price)) {
                        pc.sendPackets(new S_ServerMessage(189));
                        break;
                    }
                    if (item.isStackable()) {
                        pc.getInventory().storeItem(item.getItemId(), count);
                        long newCount = item.getCount() - count;
                        if (newCount > 0L) {
                            item.setCount(newCount);
                        } else {
                            removeList.add(item);
                        }
                    } else {
                        pc.getInventory().storeTradeItem(item);
                        removeList.add(item);
                    }
                }
            }
            ++i;
        }
        if (!removeList.isEmpty()) {
            Iterator<L1ItemInstance> iter = removeList.iterator();
            while (iter.hasNext()) {
                L1ItemInstance reitem = iter.next();
                iter.remove();
                sellList.remove(reitem);
            }
        }
        pc.get_otherList().DELIST.clear();
    }

    private boolean isAvailableTrade(L1PcInstance pc, int objectId, L1ItemInstance item,
                                     int count) {
        boolean result = true;
        if (item == null) {
            result = false;
        }
        if (objectId != item.getId()) {
            result = false;
        }
        if (!item.isStackable() && count != 1) {
            result = false;
        }
        if (item.getCount() <= 0L || item.getCount() > 2000000000L) {
            result = false;
        }
        if (count <= 0 || count > 2000000000) {
            result = false;
        }
        if (!result) {
            pc.sendPackets(new S_Disconnect());
        }
        return result;
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
