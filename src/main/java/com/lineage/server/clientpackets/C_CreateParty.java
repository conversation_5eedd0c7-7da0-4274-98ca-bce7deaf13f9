package com.lineage.server.clientpackets;

import java.util.Iterator;
import com.lineage.server.model.Instance.L1DeInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.L1Party;
import java.util.ArrayList;
import java.util.HashMap;
import com.lineage.server.serverpackets.S_Message_YN;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.types.Point;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_CreateParty extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_CreateParty.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final L1PcInstance pc = client.getActiveChar();
			if (!pc.isGhost()) {
				if (!pc.isDead()) {
					if (!pc.isTeleport()) {
						final int type = this.readC();
						switch (type) {
						case 0:
						case 1: {
							final int targetId = this.readD();
							final L1Object temp = World.get().findObject(targetId);
							if (!(temp instanceof L1PcInstance)) {
								break;
							}
							final L1PcInstance targetPc = (L1PcInstance) temp;
							if (pc.getId() == targetPc.getId()) {
								return;
							}
							if (!pc.getLocation().isInScreen(targetPc.getLocation())
									|| pc.getLocation().getTileLineDistance(targetPc.getLocation()) > 7) {
								pc.sendPackets(new S_ServerMessage(952));
								return;
							}
							if (targetPc.isInParty()) {
								pc.sendPackets(new S_ServerMessage(415));
								return;
							}
							if (!pc.isInParty()) {
								targetPc.setPartyID(pc.getId());
								targetPc.sendPackets(new S_Message_YN(953, pc.getName()));
								break;
							}
							if (pc.getParty().isLeader(pc)) {
								targetPc.setPartyID(pc.getId());
								targetPc.sendPackets(new S_Message_YN(953, pc.getName()));
								break;
							}
							pc.sendPackets(new S_ServerMessage(416));
							break;
						}
						case 2: {
							final String name = this.readS();
							final L1PcInstance targetPc2 = World.get().getPlayer(name);
							L1DeInstance de = null;
							if (targetPc2 == null) {
								de = C_ChatWhisper.getDe(name);
							}
							if (targetPc2 == null && de == null) {
								pc.sendPackets(new S_ServerMessage(109));
								return;
							}
							if (targetPc2 == null) {
								pc.sendPackets(new S_ServerMessage(109));
								return;
							}
							if (pc.getId() == targetPc2.getId()) {
								return;
							}
							if (!pc.getLocation().isInScreen(targetPc2.getLocation())
									|| pc.getLocation().getTileLineDistance(targetPc2.getLocation()) > 7) {
								pc.sendPackets(new S_ServerMessage(952));
								return;
							}
							if (targetPc2.isInChatParty()) {
								pc.sendPackets(new S_ServerMessage(415));
								return;
							}
							if (!pc.isInChatParty()) {
								targetPc2.setPartyID(pc.getId());
								targetPc2.sendPackets(new S_Message_YN(951, pc.getName()));
								break;
							}
							if (pc.getChatParty().isLeader(pc)) {
								targetPc2.setPartyID(pc.getId());
								targetPc2.sendPackets(new S_Message_YN(951, pc.getName()));
								break;
							}
							pc.sendPackets(new S_ServerMessage(416));
							break;
						}
						case 3: {
							if (!pc.isInParty()) {
								break;
							}
							if (!pc.getParty().isLeader(pc)) {
								pc.sendPackets(new S_ServerMessage(1697));
								break;
							}
							final int objid = this.readD();
							final L1Object object = World.get().findObject(objid);
							if (!(object instanceof L1PcInstance)) {
								break;
							}
							final L1PcInstance tgpc = (L1PcInstance) object;
							if (tgpc.getMapId() != pc.getMapId()) {
								pc.sendPackets(new S_Message_YN(1695));
							}
							if (pc.getLocation().isInScreen(tgpc.getLocation())) {
								final HashMap<Integer, L1PcInstance> map = new HashMap();
								map.putAll(pc.getParty().partyUsers());
								final ArrayList<L1PcInstance> newList = new ArrayList();
								final Iterator<L1PcInstance> iterator = map.values().iterator();
								while (iterator.hasNext()) {
									final L1PcInstance newpc = iterator.next();
									if (!newpc.equals(tgpc)) {
										newList.add(newpc);
									}
								}
								map.clear();
								pc.getParty().breakup();
								final L1Party party = new L1Party();
								party.addMember(tgpc);
								final Iterator<L1PcInstance> iterator2 = newList.iterator();
								while (iterator2.hasNext()) {
									final L1PcInstance newpc2 = iterator2.next();
									party.addMember(newpc2);
									tgpc.sendPackets(new S_ServerMessage(424, newpc2.getName()));
								}
								party.msgToAll();
								newList.clear();
								break;
							}
							pc.sendPackets(new S_ServerMessage(1695));
							break;
						}
						}
						return;
					}
				}
			}
			return;
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
