package com.lineage.server.clientpackets;

import com.add.system.L1ItemNpc;
import com.lineage.DatabaseFactory;
import com.lineage.config.ConfigOther;
import com.lineage.data.ItemClass;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.Instance.MapLimitInstance;
import com.lineage.server.datatables.*;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.*;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1Box;
import com.lineage.server.templates.L1Doll;
import com.lineage.server.templates.L1EtcItem;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.utils.SQLUtil;
import com.lineage.server.world.World;
import com.lineage.william.ItemIntegration;
import com.lineage.william.ArmorSkillSound;
import com.lineage.server.timecontroller.pc.GfxTimer;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Calendar;

public class C_ItemUSe extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_ItemUSe.class);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();
            if (!pc.isGhost()) {
                if (!pc.isDead()) {
                    if (!pc.isFreezeAtion()) {
                        if (pc.isTeleport()) {
                            pc.setTeleport(false);
                            pc.sendPackets(new S_Paralysis(7, false));
                        } else if (pc.get_isTeleportToOk()) {
                            pc.setTeleport(false);
                            pc.sendPackets(new S_Paralysis(7, false));
                        } else {
                            int itemObjid = readD();
                            L1ItemInstance useItem = pc.getInventory().getItem(itemObjid);
                            if (useItem == null) {
                                return;
                            }
                            int[] bow_GFX_Arrow = ConfigOther.WAR_DISABLE_ITEM;
                            boolean castle_area = L1CastleLocation.checkInAllWarArea(pc.getX(), pc.getY(),
                                    pc.getMapId());
                            int[] array;
                            int length = (array = bow_GFX_Arrow).length;
                            int i = 0;
                            while (i < length) {
                                int gfx = array[i];
                                if (useItem.getItemId() == gfx && castle_area && pc.castleWarResult()) {
                                    pc.sendPackets(new S_ServerMessage("攻城中禁止使用此道具"));
                                    return;
                                }
                                ++i;
                            }
                            int[] MAP_IDSKILL = ConfigOther.MAP_IDITEM;
                            int[] MAP_SKILL = ConfigOther.MAP_ITEM;
                            int[] array2;
                            int length2 = (array2 = MAP_IDSKILL).length;
                            int j = 0;
                            while (j < length2) {
                                int mapid = array2[j];
                                int[] array3;
                                int length3 = (array3 = MAP_SKILL).length;
                                int k = 0;
                                while (k < length3) {
                                    int mapskill = array3[k];
                                    if (pc.getMapId() == mapid && useItem.getItemId() == mapskill) {
                                        pc.sendPackets(new S_ServerMessage("此地圖無法使用此道具"));
                                        return;
                                    }
                                    ++k;
                                }
                                ++j;
                            }
                            if (useItem.getItem().getacccount() == 1) {
                                if (!Acc_use_Item.get().checkAreaArmorOff(pc, useItem.getItemId())) {
                                    pc.sendPackets(new S_ServerMessage("該帳號已使用過:" + useItem.getAllName(), 17));
                                    return;
                                }
                                Acc_use_Item.get().Add(pc, useItem.getItemId());
                                Acc_use_Item.get().load();
                            }
                            if (useItem.getItem().getcharcount() == 1) {
                                if (!Char_use_Item.get().checkAreaArmorOff(pc, useItem.getItemId())) {
                                    pc.sendPackets(new S_ServerMessage("該腳色已使用過:" + useItem.getAllName(), 17));
                                    return;
                                }
                                Char_use_Item.get().Add(pc, useItem.getItemId());
                                Char_use_Item.get().load();
                            }
                            useItem.set_char_objid(pc.getId());
                            boolean isStop = false;
                            boolean isStop2 = false;
                            if (pc.hasSkillEffect(78)) {
                                pc.killSkillEffectTimer(78);
                                pc.startHpRegeneration();
                                pc.startMpRegeneration();
                            }
                            L1ItemNpc.forRequestItemUSe(client, useItem);
                            if (!pc.getMap().isUsableItem()) {
                                pc.sendPackets(new S_ServerMessage(563));
                                isStop = true;
                            }
                            if (pc.isParalyzedX() && !isStop) {
                                isStop = true;
                            }

                            if (!isStop) {
                                switch (pc.getMapId()) {
                                    case 22: {
                                        switch (useItem.getItemId()) {
                                            case 30:
                                            case 40017: {
                                                break;
                                            }
                                            default: {
                                                pc.sendPackets(new S_ServerMessage(563));
                                                isStop = true;
                                                break;
                                            }
                                        }
                                    }
                                }
                            }

                            if (!CheckUtil.getUseItemAll(pc) && !isStop) {
                                isStop = true;
                            }
                            if (pc.isPrivateShop() && !isStop) {
                                isStop = true;
                            }
                            if (pc.hasSkillEffect(8872)) {
                                pc.sendPackets(new S_SystemMessage("\\fY白息之光狀態下無法使用"));
                                isStop = true;
                            }
                            if (pc.hasSkillEffect(9990) && pc.getAccessLevel() == 0) {
                                isStop2 = true;
                            }
                            if (isStop) {
                                pc.sendPackets(new S_Paralysis(7, false));
                                return;
                            }
                            if (isStop2) {
                                pc.sendPackets(new S_Paralysis(7, false));
                                pc.sendPackets(new S_SystemMessage("目前受到監禁狀態中無法使用道具。"));
                                return;
                            }
                            if (pc.getCurrentHp() <= 0) {
                                return;
                            }
                            int delay_id = 0;
                            if (useItem.getItem().getType2() == 0) {
                                delay_id = ((L1EtcItem) useItem.getItem()).get_delayid();
                                if (delay_id != 0 && pc.hasItemDelay(delay_id)) {
                                    pc.sendPackets(new S_Paralysis(7, false));
                                    return;
                                }
                            }
                            if (useItem.getCount() <= 0L) {
                                pc.sendPackets(new S_ServerMessage(329, useItem.getLogName()));
                                return;
                            }
                            int min = useItem.getItem().getMinLevel();
                            int max = useItem.getItem().getMaxLevel();
                            int minlogpcpower = useItem.getItem().getminlogpcpower();
                            int maxlogpcpower = useItem.getItem().getmaxlogpcpower();
                            if (min != 0 && min > pc.getLevel()) {
                                if (min < 50) {
                                    S_PacketBoxItemLv toUser = new S_PacketBoxItemLv(min, 0);
                                    pc.sendPackets(toUser);
                                } else {
                                    S_ServerMessage toUser2 = new S_ServerMessage(318, String.valueOf(min));
                                    pc.sendPackets(toUser2);
                                }
                                return;
                            }
                            if (max != 0 && max < pc.getLevel()
                                    && (useItem.getItem().getType2() == 0 || !useItem.isEquipped())) {
                                S_PacketBoxItemLv toUser = new S_PacketBoxItemLv(0, max);
                                pc.sendPackets(toUser);
                                return;
                            }
                            if (useItem.getItem().getType2() == 0 && minlogpcpower != -1
                                    && pc.getMeteLevel() < minlogpcpower) {
                                pc.sendPackets(new S_SystemMessage("您的轉生次數不夠~暫時無法使用此物品"));
                                return;
                            }
                            if (useItem.getItem().getType2() == 0 && maxlogpcpower != -1
                                    && pc.getMeteLevel() > maxlogpcpower) {
                                pc.sendPackets(new S_SystemMessage("您的轉生次太高~無法使用此物品唷"));
                                return;
                            }
                            boolean isDelayEffect = false;
                            if (useItem.getItem().getType2() == 0) {
                                int delayEffect = ((L1EtcItem) useItem.getItem()).get_delayEffect();
                                if (delayEffect > 0) {
                                    isDelayEffect = true;
                                    Timestamp lastUsed = useItem.getLastUsed();
                                    if (lastUsed != null) {
                                        Calendar cal = Calendar.getInstance();
                                        long useTime = (cal.getTimeInMillis() - lastUsed.getTime()) / 1000L;
                                        if (useTime <= delayEffect) {
                                            useTime = delayEffect - useTime;
                                            String useTimeS = String.valueOf(useTime / 60L);
                                            if (useTime / 60L > 0L) {
                                                pc.sendPackets(new S_ServerMessage(String.valueOf(useItem.getLogName())
                                                        + " " + useTimeS + "分鐘之內無法使用。"));
                                            } else {
                                                pc.sendPackets(new S_ServerMessage(String.valueOf(useItem.getLogName())
                                                        + " " + useTime + "秒之內無法使用。"));
                                            }
                                            return;
                                        }
                                    }
                                }
                            }
                            if (!CheckUtil.getUseItemAll(pc) && !isStop) {
                                isStop = true;
                            }
                            if (pc.isPrivateShop() && !isStop) {
                                isStop = true;
                            }
                            int use_type = useItem.getItem().getUseType();
                            boolean isClass = false;
                            String className = useItem.getItem().getclassname();
                            if (!className.equals("0")) {
                                isClass = true;
                            }


                            switch (use_type) {
                                case -11: {
                                    L1Doll doll = DollPowerTable.get().get_type(useItem.getItemId());
                                    int maxusetime = useItem.getItem().getMaxUseTime();
                                    if (doll != null && maxusetime > 0 && useItem.getRemainingTime() <= 0) {
                                        pc.sendPackets(new S_ServerMessage("\\fY必須先使用能量石進行充電。"));
                                        return;
                                    }
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -10: {
                                    if (!CheckUtil.getUseItem(pc)) {
                                        return;
                                    }
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -9: {
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -8: {
                                    if (!isClass) {
                                        break;
                                    }
                                    try {
                                        int[] newData = {readC(), readC()};
                                        ItemClass.get().item(newData, pc, useItem);
                                        break;
                                    } catch (Exception e3) {
                                        return;
                                    }
                                }
                                case -7: {
                                    if (!CheckUtil.getUseItem(pc)) {
                                        return;
                                    }
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -6: {
                                    if (!CheckUtil.getUseItem(pc)) {
                                        return;
                                    }
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -4: {
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case -3: {
                                    pc.getInventory().setSting(useItem.getItemId());
                                    pc.sendPackets(new S_ServerMessage(452, useItem.getLogName()));
                                    break;
                                }
                                case -2: {
                                    pc.getInventory().setArrow(useItem.getItemId());
                                    pc.sendPackets(new S_ServerMessage(452, useItem.getLogName()));
                                    break;
                                }
                                case -12:
                                case -5:
                                case -1: {
                                    pc.sendPackets(new S_ServerMessage(74, useItem.getLogName()));
                                    break;
                                }
                                case 0: {
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                }
                                case 1: {
                                    if (useItem(pc, useItem)) {
                                        useWeapon(pc, useItem);
                                    }
                                    break;
                                }
                                case 2:
                                case 18:
                                case 19:
                                case 20:
                                case 21:
                                case 22:
                                case 23:
                                case 24:
                                case 25:
                                case 37:
                                case 40:
                                case 43:
                                case 44:
                                case 45:
                                case 47:
                                case 48:
                                case 49:
                                case 51: {
                                    if (useItem.getItemId() >= 21157 && useItem.getItemId() <= 21178
                                            && useItem.get_time() == null) {
                                        pc.sendPackets(new S_ServerMessage("\\fY必須先使用魔法氣息解除封印。"));
                                        return;
                                    }
                                    if (useItem.getItemId() >= 301060 && useItem.getItemId() <= 301099
                                            && useItem.getRemainingTime() <= 0) {
                                        pc.sendPackets(new S_ServerMessage("\\fY必須先使用龍的血液解除封印。"));
                                        return;
                                    }
                                    if (useItem(pc, useItem)) {
                                        useArmor(pc, useItem);
                                    }
                                    break;
                                }
                                case 3:// 創造怪物魔杖(無須選取目標) (無數量:沒有任何事情發生)
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;
                                case 4:// 希望魔杖(無須選取目標)(有數量:你想要什麼 / 無數量:沒有任何事情發生)
                                    break;
                                case 5:// 魔杖類型(須選取目標)
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[3];
                                            newData[0] = readD();// 選取目標的OBJID
                                            newData[1] = readH();// X座標
                                            newData[2] = readH();// Y座標
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

//                                case 6:// 瞬間移動卷軸
//                                    if (isClass) {
//                                        try {
//                                            int[] newData = new int[2];
//                                            newData[1] = readH();
//                                            newData[0] = readD();
//                                            ItemClass.get().item(newData, pc, useItem);
//                                            pc.sendPackets(new S_Paralysis(S_Paralysis.TYPE_TELEPORT_UNLOCK, false));
//
//                                        } catch (Exception e) {
//                                            return;
//                                        }
//                                    }
//                                    break;

                                case 7:// 鑑定卷軸
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readD();// 選取物件的OBJID
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 8:// 復活卷軸
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readD();// 選取目標的OBJID
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 9:// 傳送回家的卷軸 / 血盟傳送卷軸
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 10:// 照明道具
                                    // 取得道具編號
                                    if ((useItem.getRemainingTime() <= 0) && (useItem.getItemId() != 40004)) {
                                        return;
                                    }
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 14:// 請選擇一個物品(道具欄位) 燈油/磨刀石/膠水
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readD();// 選取物件的OBJID
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 15:// 哨子
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 16:// 變形卷軸
                                    if (isClass) {
                                        String cmd = readS();
                                        pc.setText(cmd);// 選取的變身命令
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 17:// 選取目標 (近距離)
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[3];
                                            newData[0] = readD();// 選取目標的OBJID
                                            newData[1] = readH();// X座標
                                            newData[2] = readH();// Y座標
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;
                                case 27:// 對盔甲施法的卷軸
                                case 26:// 對武器施法的卷軸
                                case 46:// 飾品強化捲軸
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            // 選取目標的OBJID
                                            newData[0] = readD();
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 28:// 空的魔法卷軸
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readC();
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;
                                case 6:
                                case 29:// 瞬間移動卷軸(祝福)
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[3];
                                            newData[0] = readH();
                                            newData[1] = readH();
                                            newData[2] = readH();
                                            ItemClass.get().item(newData, pc, useItem);
                                            pc.sendPackets(new S_Paralysis(
                                                    S_Paralysis.TYPE_TELEPORT_UNLOCK, false));

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 30:// 選取目標 (Ctrl 遠距離)
                                    if (isClass) {
                                        try {
                                            int obj = readD();// 選取目標的OBJID
                                            int[] newData = new int[]{obj};
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 12:// 信紙
                                case 31:// 聖誕卡片
                                case 33:// 情人節卡片
                                case 35:// 白色情人節卡片
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readH();
                                            pc.setText(readS());
                                            pc.setTextByte(readByte());
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 13:// 信紙(打開)
                                case 32:// 聖誕卡片(打開)
                                case 34:// 情人節卡片(打開)
                                case 36:// 白色情人節卡片(打開)
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 38:// 食物
                                    if (isClass) {
                                        ItemClass.get().item(null, pc, useItem);
                                    }
                                    break;

                                case 39:// 選取目標 (遠距離)
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[3];
                                            newData[0] = readD();// 選取目標的OBJID
                                            newData[1] = readH();// X座標
                                            newData[2] = readH();// Y座標
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 42:// 釣魚杆
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[3];
                                            newData[0] = readH();// X座標
                                            newData[1] = readH();// Y座標
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                case 55:// 魔法娃娃成長藥劑
                                    if (isClass) {
                                        try {
                                            int[] newData = new int[1];
                                            newData[0] = readD();// 選取物件的OBJID
                                            ItemClass.get().item(newData, pc, useItem);

                                        } catch (Exception e) {
                                            return;
                                        }
                                    }
                                    break;

                                default:// 測試
                                    _log.info("未處理的物品分類: " + use_type);
                                    break;
                            }


                            try {
                                if (useItem.getItem().getType() == 20 && use_type == 0) {
                                    int npcId = Integer.parseInt(useItem.getItem().getName());
                                    pc.sendPackets(new S_ScrollShopSellList(NpcTable.get().getTemplate(npcId)));
                                    return;
                                }
                            } catch (NumberFormatException e) {
                                System.out.println("商店卷軸有誤 ID : " + useItem.getItem().getItemId() + " 。");
                                e.printStackTrace();
                            }

                            if (useItem.getItem().getType2() == 0 && use_type == 0) {
                                int itemId = useItem.getItem().getItemId();
                                switch (itemId) {
                                    case 40630: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "diegodiary"));
                                        break;
                                    }
                                    case 40663: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "sonsletter"));
                                        break;
                                    }
                                    case 40701: {
                                        if (pc.getQuest().get_step(23) == 1) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "firsttmap"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 2) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "secondtmapa"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 3) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "secondtmapb"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 4) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "secondtmapc"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 5) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmapd"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 6) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmape"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 7) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmapf"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 8) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmapg"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 9) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmaph"));
                                            break;
                                        }
                                        if (pc.getQuest().get_step(23) == 10) {
                                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "thirdtmapi"));
                                            break;
                                        }
                                        break;
                                    }
                                    case 41007: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "erisscroll"));
                                        break;
                                    }
                                    case 41009: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "erisscroll2"));
                                        break;
                                    }
                                    case 41060: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "nonames"));
                                        break;
                                    }
                                    case 41061: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "kames"));
                                        break;
                                    }
                                    case 41062: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "bakumos"));
                                        break;
                                    }
                                    case 41063: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "bukas"));
                                        break;
                                    }
                                    case 41064: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "huwoomos"));
                                        break;
                                    }
                                    case 41065: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "noas"));
                                        break;
                                    }
                                    case 41317: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "rarson"));
                                        break;
                                    }
                                    case 41318: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "kuen"));
                                        break;
                                    }
                                    case 41329: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "anirequest"));
                                        break;
                                    }
                                    case 41340: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "tion"));
                                        break;
                                    }
                                    case 41356: {
                                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "rparum3"));
                                        break;
                                    }

                                }
                            }
                            if (useItem.getItem().getType2() == 0 && useItem.getItem().getType() == 16) {
                                if (pc.getInventory().getSize() >= 160) {
                                    pc.sendPackets(new S_ServerMessage("角色身上攜帶超過160個道具時無法開啟寶箱。"));
                                    return;
                                }
                                if (pc.getInventory().getWeight240() >= 180) {
                                    pc.sendPackets(new S_ServerMessage(82));
                                    return;
                                }
                                ArrayList<L1Box> list = ItemBoxTable.get().get(pc, useItem);
                                if (list == null) {
                                    ItemBoxTable.get().get_all(pc, useItem);
                                }
                            }
                            if (isDelayEffect) {
                                Timestamp ts = new Timestamp(System.currentTimeMillis());
                                useItem.setLastUsed(ts);
                                pc.getInventory().updateItem(useItem, 32);
                                pc.getInventory().saveItem(useItem, 32);
                            }
                            try {
                                L1ItemDelay.onItemUse(client, useItem);
                            } catch (Exception e2) {
                                C_ItemUSe._log.error("分類道具使用延遲異常:" + useItem.getItemId(), e2);
                            }
                            int itemId = useItem.getItem().getItemId();
                            if (!isClass) {
                                int l = 0;
                                int blanksc_skillid = 0;
                                int spellsc_objid = 0;
                                int spellsc_x = 0;
                                int spellsc_y = 0;
                                int resid = 0;
                                int letterCode = 0;
                                String letterReceiver = "";
                                byte[] letterText = null;
                                int cookStatus = 0;
                                int cookNo = 0;
                                int fishX = 0;
                                int fishY = 0;
                                int mapid2 = 0;
                                //TODO Kevin 刪除NPC魔杖試做(參考8.15版本原碼) 道具編號:95315
                                //TODO Kevin Catch未完成
                                //TODO Kevin 斷點測試結果有跑進來，但是findobject為null，就不執行以下直接跳開
                                if (itemId == 95315) {
                                    L1Object target = World.get().findObject(spellsc_objid);
                                    if (target instanceof L1NpcInstance) {
                                        L1NpcInstance npc = (L1NpcInstance) target;
                                        Connection con = null;
                                        PreparedStatement pstm = null;
                                        try {
                                            con = DatabaseFactory.get().getConnection();
                                            pstm = con.prepareStatement("DELETE FROM spawnlist_npc WHERE npc_templateid=? AND locx=? AND locy=? AND mapid=?");
                                            if (target instanceof L1MonsterInstance) {
                                                pstm = con.prepareStatement("DELETE FROM spawnlist WHERE npc_templateid=? AND locx=? AND locy=? AND mapid=?");
                                            }
                                            pstm.setInt(1, npc.getNpcId());
                                            pstm.setInt(2, npc.getX());
                                            pstm.setInt(3, npc.getY());
                                            pstm.setInt(4, npc.getMapId());
                                            pstm.execute();
                                        } catch (SQLException e) {
                                            //TODO Kevin 這裡不知道怎麼接

                                        } finally {
                                            SQLUtil.close(pstm);
                                            SQLUtil.close(con);
                                        }
                                        npc.teleport(0, 0, 0);
                                        pc.sendPackets(new S_SystemMessage("npcid:" + npc.getNpcId() + " = " + npc.getNpcTemplate().get_name()));
                                    }
                                }
                                if (itemId == 40126 || itemId == 40098 || itemId == 41029 || itemId == 40317
                                        || itemId == 41036 || itemId == 41245 || (itemId >= 41048 && itemId <= 41057)
                                        || (itemId >= 40925 && itemId <= 40929) || (itemId >= 40931 && itemId <= 40958)
                                        || itemId == 40964 || itemId == 41426 || itemId == 41427 || itemId == 40075
                                        || itemId == 70370 || useItem.getItem().getType() == 19
                                        || useItem.getItem().getType() == 20 || useItem.getItem().getType() == 21
                                        || useItem.getItem().getType() == 24) {
                                    l = readD();
                                } else if (itemId == 40090 || itemId == 40091 || itemId == 40092 || itemId == 40093
                                        || itemId == 40094) {
                                    blanksc_skillid = readC();
                                } else if (use_type == 30 || itemId == 40870 || itemId == 40879) {
                                    spellsc_objid = readD();
                                } else if (use_type == 5 || use_type == 17) {
                                    spellsc_objid = readD();
                                    spellsc_x = readH();
                                    spellsc_y = readH();
                                } else if (itemId == 40089 || itemId == 140089) {
                                    resid = readD();
                                } else if (itemId == 40310 || itemId == 40311 || itemId == 40730 || itemId == 40731
                                        || itemId == 40732) {
                                    letterCode = readH();
                                    letterReceiver = readS();
                                    letterText = readByte();
                                } else if (itemId >= 41255 && itemId <= 41259) {
                                    cookStatus = readC();
                                    cookNo = readC();
                                } else if (itemId == 41293 || itemId == 41294 || itemId == 70533) {
                                    fishX = readH();
                                    fishY = readH();
                                } else if (use_type == 9) {
                                    mapid2 = readH();
                                } else {
                                    l = readC();
                                }
                                L1ItemInstance tgItem = pc.getInventory().getItem(l);
                                int itemType = useItem.getItem().getType();
                                switch (itemType) {
                                    case 19: {
                                        ItemIntegration.forItemIntegration(pc, useItem, tgItem);
                                        break;
                                    }
                                }
                                return;
                            }
                            return;
                        }
                    }
                }
            }
            return;
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    private boolean useItem(L1PcInstance pc, L1ItemInstance useItem) {
        if (!MapLimitInstance.get().canEquippedItem(Integer.valueOf(pc.getMapId()), useItem.getItemId())) {
            pc.sendPackets(new S_ServerMessage("此地圖無法使用此道具"));
            return Boolean.FALSE;
        }

        boolean isEquipped = false;
        if (pc.isCrown()) {
            if (useItem.getItem().isUseRoyal()) {
                isEquipped = true;
            }
        } else if (pc.isKnight()) {
            if (useItem.getItem().isUseKnight()) {
                isEquipped = true;
            }
        } else if (pc.isElf()) {
            if (useItem.getItem().isUseElf()) {
                isEquipped = true;
            }
        } else if (pc.isWizard()) {
            if (useItem.getItem().isUseMage()) {
                isEquipped = true;
            }
        } else if (pc.isDarkelf()) {
            if (useItem.getItem().isUseDarkelf()) {
                isEquipped = true;
            }
        } else if (pc.isDragonKnight()) {
            if (useItem.getItem().isUseDragonknight()) {
                isEquipped = true;
            }
        } else if (pc.isIllusionist() && useItem.getItem().isUseIllusionist()) {
            isEquipped = true;
        }
        if (!isEquipped) {
            pc.sendPackets(new S_ServerMessage(264));
        }
        return isEquipped;
    }

    private void useArmor(L1PcInstance pc, L1ItemInstance armor) {
        int itemid = armor.getItem().getItemId();
        int type = armor.getItem().getType();
        L1PcInventory pcInventory = pc.getInventory();
        if (pc.getLevel() < 15 && armor.getItem().get_addcon() < 0) {
            pc.sendPackets(new S_SystemMessage("未滿 15 級的人物將無法裝備會減少「體質」點數的道具。"));
            return;
        }
        boolean equipeSpace;
        if (type == 9) {
            equipeSpace = (pcInventory.getTypeEquipped(2, 9) <= 3);
        } else if (type == 12) {
            equipeSpace = (pcInventory.getTypeEquipped(2, 12) <= 1);
        } else {
            equipeSpace = (pcInventory.getTypeEquipped(2, type) <= 0);
        }
        if (equipeSpace && !armor.isEquipped()) {
            if (type == 9 && pcInventory.getTypeEquipped(2, 9) == 2 && pc.getQuest().get_step(58003) != 1) {
                pc.sendPackets(new S_ServerMessage(3253));
                return;
            }
            if (type == 9 && pcInventory.getTypeEquipped(2, 9) == 3 && pc.getQuest().get_step(58002) != 1) {
                pc.sendPackets(new S_ServerMessage(3253));
                return;
            }
            if (pcInventory.getTypeAndItemIdEquipped(2, 9, itemid) == 2) {
                pc.sendPackets(new S_ServerMessage(3278));
                return;
            }
            if (type == 12 && pcInventory.getTypeEquipped(2, 12) == 1 && pc.getQuest().get_step(58001) != 1) {
                pc.sendPackets(new S_ServerMessage(3253));
                return;
            }
            //TODO 同Group只能裝備一個
            if (armor.getItem().getgroup() != 0 && pcInventory.getTypeAndItemIdEquippedgroup(armor.getItem().getgroup()) > 0) {
                pc.sendPackets(new S_ServerMessage("同系列的裝備無法再重複穿戴"));
                return;
            }
            if (pcInventory.getTypeAndItemIdEquipped(2, 12, itemid) == 1) {
                pc.sendPackets(new S_ServerMessage(3278));
                return;
            }

            int polyid = pc.getTempCharGfx();
            if (!L1PolyMorph.isEquipableArmor(polyid, type)) {
                return;
            }
            if ((type == 7 && pcInventory.getTypeEquipped(2, 13) >= 1)
                    || (type == 13 && pcInventory.getTypeEquipped(2, 7) >= 1)) {
                pc.sendPackets(new S_ServerMessage(124));
                return;
            }
            if (type == 7 && pc.getWeapon() != null && pc.getWeapon().getItem().isTwohandedWeapon()) {
                pc.sendPackets(new S_ServerMessage(129));
                return;
            }
            if (type == 3 && pcInventory.getTypeEquipped(2, 4) >= 1) {
                pc.sendPackets(new S_ServerMessage(126, "$224", "$225"));
                return;
            }
            if (type == 3 && pcInventory.getTypeEquipped(2, 2) >= 1) {
                pc.sendPackets(new S_ServerMessage(126, "$224", "$226"));
                return;
            }
            if (type == 2 && pcInventory.getTypeEquipped(2, 4) >= 1) {
                pc.sendPackets(new S_ServerMessage(126, "$226", "$225"));
                return;
            }
            if ((armor.getItemId() == 20134 || armor.getItemId() == 20211 || armor.getItemId() == 400044)
                    && pc.getClassId() != 1) {
                pc.sendPackets(new S_ServerMessage(264));
                return;
            }
            if (armor.getItem().getType2() == 2) {
                if (pc.hasSkillEffect(170)) {
                    pc.killSkillEffectTimer(170);
                }
            }
            pcInventory.setEquipped(armor, true);
            // 檢查是否需要啟動特效計時器
            checkAndManageGfxTimer(pc);
        } else if (armor.isEquipped()) {
            if (armor.getItem().getBless() == 2) {
                pc.sendPackets(new S_ServerMessage(150));
                return;
            }
            if (type == 3 && pcInventory.getTypeEquipped(2, 2) >= 1) {
                pc.sendPackets(new S_ServerMessage(127));
                return;
            }
            if ((type == 2 || type == 3) && pcInventory.getTypeEquipped(2, 4) >= 1) {
                pc.sendPackets(new S_ServerMessage(127));
                return;
            }
            if ((type == 7 || type == 13) && pc.hasSkillEffect(90)) {
                pc.removeSkillEffect(90);
            }
            pcInventory.setEquipped(armor, false);
            // 檢查是否需要停止特效計時器
            checkAndManageGfxTimer(pc);
        } else {
            if (armor.getItem().getUseType() == 23) {
                pc.sendPackets(new S_SystemMessage("你已經戴著四個戒指。"));
                return;
            }
            pc.sendPackets(new S_ServerMessage(124));
            return;
        }
        pc.setCurrentHp(pc.getCurrentHp());
        pc.setCurrentMp(pc.getCurrentMp());
        pc.sendPackets(new S_OwnCharAttrDef(pc));
        pc.sendPackets(new S_OwnCharStatus(pc));
        pc.sendPackets(new S_SPMR(pc));
    }

    /**
     * 檢查並管理玩家的裝備特效計時器
     * @param pc 玩家實例
     */
    private void checkAndManageGfxTimer(L1PcInstance pc) {
        if (pc == null) {
            return;
        }

        // 假設 ArmorSkillSound 提供了一個靜態方法來檢查玩家是否裝備了任何帶有持續特效的物品
        // 注意：這需要 ArmorSkillSound 類別的配合，可能需要修改該類別
        if (ArmorSkillSound.hasContinuousEffectArmor(pc)) {
            GfxTimer.startGfxTimer(pc);
        } else {
            GfxTimer.stopGfxTimer(pc);
        }
    }

    private void useWeapon(L1PcInstance pc, L1ItemInstance weapon) {
        // 基本檢查
        if (weapon == null || weapon.getItem() == null) {
            pc.sendPackets(new S_ServerMessage(79)); // 無效的物品
            return;
        }
        
        // 等級檢查
        if (pc.getLevel() < 15 && weapon.getItem().get_addcon() < 0) {
            pc.sendPackets(new S_SystemMessage("未滿 15 級的人物將無法裝備會減少「體質」點數的道具。"));
            return;
        }
        
        // 特殊武器檢查
        switch (weapon.getItemId()) {
            case 65:
            case 133:
            case 191:
            case 192: {
                if (pc.getMapId() != 2000 && (pc.getWeapon() == null || !pc.getWeapon().equals(weapon))) {
                    pc.sendPackets(new S_ServerMessage(563));
                    return;
                }
                break;
            }
            default: {
                if (pc.hasSkillEffect(4007)) {
                    pc.sendPackets(new S_ServerMessage(563));
                    return;
                }
                break;
            }
        }
        
        L1PcInventory pcInventory = pc.getInventory();
        L1ItemInstance currentWeapon = pc.getWeapon();
        
        // 檢查是否可以裝備新武器
        if (currentWeapon == null || !currentWeapon.equals(weapon)) {
            int weapon_type = weapon.getItem().getType();
            int polyid = pc.getTempCharGfx();
            if (!L1PolyMorph.isEquipableWeapon(polyid, weapon_type)) {
                return;
            }
            if (weapon.getItem().isTwohandedWeapon() && pcInventory.getTypeEquipped(2, 7) >= 1) {
                pc.sendPackets(new S_ServerMessage(128));
                return;
            }
        }
        
        // 處理技能效果
        if (pc.hasSkillEffect(78)) {
            pc.killSkillEffectTimer(78);
            pc.startHpRegeneration();
            pc.startMpRegeneration();
        }
        
        // 裝備邏輯
        if (currentWeapon != null) {
            // 檢查當前武器是否為祝福武器
            if (currentWeapon.getItem() != null && currentWeapon.getItem().getBless() == 2) {
                pc.sendPackets(new S_ServerMessage(150));
                return;
            }
            
            if (currentWeapon.equals(weapon)) {
                // 卸下相同武器
                pcInventory.setEquipped(currentWeapon, false, false, false);
                ArmorSkillSound.forArmorSkillSound(pc);
            } else {
                // 卸下當前武器，裝備新武器
                pcInventory.setEquipped(currentWeapon, false, false, false);
                pcInventory.setEquipped(weapon, true, false, false);
                ArmorSkillSound.forArmorSkillSound(pc);
            }
        } else {
            // 直接裝備武器
            pcInventory.setEquipped(weapon, true, false, false);
            ArmorSkillSound.forArmorSkillSound(pc);
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
