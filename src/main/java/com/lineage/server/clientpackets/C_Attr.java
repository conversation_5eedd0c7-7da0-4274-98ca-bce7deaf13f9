package com.lineage.server.clientpackets;

import com.add.L1PcUnlock;
import com.lineage.config.ConfigAlt;
import com.lineage.config.ConfigClan;
import com.lineage.config.ConfigOther;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.ClanAllianceReading;
import com.lineage.server.datatables.lock.HouseReading;
import com.lineage.server.datatables.lock.PetReading;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.model.Instance.L1EffectInstance;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.*;
import com.lineage.server.model.map.L1Map;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1House;
import com.lineage.server.templates.L1Npc;
import com.lineage.server.templates.L1Pet;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.timecontroller.server.ServerWarExecutor;
import com.lineage.server.utils.FaceToFace;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldWar;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Iterator;
import java.util.regex.Matcher;

public class C_Attr extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_Attr.class);
    }

    private static void renamePet(L1PetInstance pet, String name) {
        if (pet == null || name == null) {
            throw new NullPointerException();
        }
        int petItemObjId = pet.getItemObjId();
        L1Pet petTemplate = PetReading.get().getTemplate(petItemObjId);
        if (petTemplate == null) {
            throw new NullPointerException();
        }
        L1PcInstance pc = (L1PcInstance) pet.getMaster();
        if (PetReading.get().isNameExists(name)) {
            pc.sendPackets(new S_ServerMessage(327));
            return;
        }
        L1Npc l1npc = NpcTable.get().getTemplate(pet.getNpcId());
        if (!pet.getName().equalsIgnoreCase(l1npc.get_name())) {
            pc.sendPackets(new S_ServerMessage(326));
            return;
        }
        pet.setName(name);
        petTemplate.set_name(name);
        PetReading.get().storePet(petTemplate);
        L1ItemInstance item = pc.getInventory().getItem(pet.getItemObjId());
        pc.getInventory().updateItem(item);
        pc.sendPacketsAll(new S_ChangeName(pet.getId(), name));
    }

    private static boolean isInWarAreaAndWarTime(L1PcInstance pc) {
        int castleId = L1CastleLocation.getCastleIdByArea(pc);
        return castleId != 0 && ServerWarExecutor.get().isNowWar(castleId);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();
            if (pc.isGhost() || pc.isTeleport()) {
                return;
            }
            int mode = readH();
            int tgobjid = 0;
            if (mode == 0) {
                tgobjid = readD();
                mode = readH();
            }
            switch (mode) {
                case 97: {
                    int c = readC();
                    L1PcInstance joinPc = (L1PcInstance) World.get().findObject(pc.getTempID());
                    pc.setTempID(0);
                    if (joinPc == null) {
                        break;
                    }
                    if (c == 0) {
                        joinPc.sendPackets(new S_ServerMessage(96, pc.getName()));
                        break;
                    }
                    if (c == 1) {
                        L1ClanJoin.getInstance().ClanJoin(pc, joinPc);
                        break;
                    }
                    break;
                }
                case 1906: {
                    int c = readC();
                    if (c == 0) {
                        leaveClan(pc, false);
                        break;
                    }
                    boolean loginLeader = false;
                    String clan_name = pc.getClanname();
                    L1Clan clan = WorldClan.get().getClan(clan_name);
                    L1PcInstance[] clanMember = clan.getOnlineClanMember();
                    int i = 0;
                    while (i < clanMember.length) {
                        if (clanMember[i].getClanRank() == 4 || clanMember[i].getClanRank() == 3
                                || clanMember[i].getClanRank() == 10) {
                            clanMember[i].setTempID(pc.getId());
                            clanMember[i].sendPackets(new S_Message_YN(1908, pc.getName()));
                            loginLeader = true;
                        }
                        ++i;
                    }
                    if (loginLeader) {
                        pc.sendPackets(new S_ServerMessage(1907));
                        break;
                    }
                    pc.sendPackets(new S_Message_YN(1914));
                    break;
                }
                case 1908: {
                    L1PcInstance leavePc = (L1PcInstance) World.get().findObject(pc.getTempID());
                    int c = readC();
                    if (c == 0) {
                        String clan_name = pc.getClanname();
                        L1Clan clan = WorldClan.get().getClan(clan_name);
                        L1PcInstance[] clanMember = clan.getOnlineClanMember();
                        int j = 0;
                        while (j < clanMember.length) {
                            clanMember[j].sendPackets(new S_ServerMessage(1917, pc.getName(), leavePc.getName()));
                            ++j;
                        }
                        leavePc.sendPackets(new S_Message_YN(1912));
                        break;
                    }
                    String clan_name = pc.getClanname();
                    L1Clan clan = WorldClan.get().getClan(clan_name);
                    L1PcInstance[] clanMember = clan.getOnlineClanMember();
                    int j = 0;
                    while (j < clanMember.length) {
                        clanMember[j].sendPackets(new S_ServerMessage(1916, pc.getName(), leavePc.getName()));
                        ++j;
                    }
                    leaveClan(leavePc, true);
                    break;
                }
                case 1912:
                case 1914: {
                    int c = readC();
                    if (c == 0) {
                        return;
                    }
                    leaveClan(pc, false);
                    break;
                }
                case 217:
                case 221:
                case 222: {
                    int c = readC();
                    L1PcInstance enemyLeader = (L1PcInstance) World.get().findObject(pc.getTempID());
                    if (enemyLeader == null) {
                        return;
                    }
                    pc.setTempID(0);
                    String clanName = pc.getClanname();
                    String enemyClanName = enemyLeader.getClanname();
                    if (c == 0) {
                        if (mode == 217) {
                            enemyLeader.sendPackets(new S_ServerMessage(236, clanName));
                            break;
                        }
                        if (mode == 221 || mode == 222) {
                            enemyLeader.sendPackets(new S_ServerMessage(237, clanName));
                            break;
                        }
                        break;
                    } else {
                        if (c != 1) {
                            break;
                        }
                        if (mode == 217) {
                            L1War war = new L1War();
                            war.handleCommands(2, enemyClanName, clanName);
                            break;
                        }
                        if (mode == 221 || mode == 222) {
                            Iterator<L1War> iterator = WorldWar.get().getWarList().iterator();
                            while (iterator.hasNext()) {
                                L1War war = iterator.next();
                                if (war.checkClanInWar(clanName)) {
                                    if (mode == 221) {
                                        war.surrenderWar(enemyClanName, clanName);
                                        break;
                                    }
                                    if (mode == 222) {
                                        war.ceaseWar(enemyClanName, clanName);
                                        break;
                                    }
                                    break;
                                }
                            }
                            break;
                        }
                        break;
                    }
                }
                case 252: {
                    int c = readC();
                    L1PcInstance trading_partner = (L1PcInstance) World.get().findObject(pc.getTradeID());
                    L1PcInstance target_partner = FaceToFace.faceToFace(pc);
                    if (trading_partner != null && target_partner != null
                            && target_partner.getId() == trading_partner.getId()) {
                        if (c == 0) {
                            if (pc.hasSkillEffect(51234)) {
                                trading_partner.sendPackets(new S_ServerMessage(253, "尿尿商人"));
                            } else {
                                trading_partner.sendPackets(new S_ServerMessage(253, pc.getName()));
                            }
                            pc.setTradeID(0);
                            trading_partner.setTradeID(0);
                            break;
                        }
                        if (c != 1) {
                            break;
                        }
                        if (trading_partner.hasSkillEffect(51234)) {
                            pc.sendPackets(new S_Trade("尿尿商人"));
                            trading_partner.sendPackets(new S_Trade(pc.getName()));
                            break;
                        }
                        pc.sendPackets(new S_Trade(trading_partner.getName()));
                        trading_partner.sendPackets(new S_Trade(pc.getName()));
                        break;
                    } else {
                        pc.setTradeID(0);
                        if (trading_partner != null) {
                            trading_partner.setTradeID(0);
                            break;
                        }
                        break;
                    }
                }
                case 321: {
                    int c = readC();
                    L1PcInstance resusepc1 = (L1PcInstance) World.get().findObject(pc.getTempID());
                    pc.setTempID(0);
                    if (resusepc1 == null) {
                        break;
                    }
                    if (c == 0) {
                        break;
                    }
                    if (c == 1) {
                        L1EffectInstance tomb = pc.get_tomb();
                        if (tomb != null) {
                            tomb.broadcastPacketAll(new S_DoActionGFX(tomb.getId(), 8));
                            tomb.deleteMe();
                        }
                        pc.sendPacketsX8(new S_SkillSound(pc.getId(), 230));
                        pc.resurrect(pc.getMaxHp() / 2);
                        pc.setCurrentHp(pc.getMaxHp() / 2);
                        pc.startHpRegeneration();
                        pc.startMpRegeneration();
                        pc.stopPcDeleteTimer();
                        pc.sendPacketsAll(new S_Resurrection(pc, resusepc1, 0));
                        pc.sendPacketsAll(new S_CharVisualUpdate(pc));
                        break;
                    }
                    break;
                }
                case 322: {
                    int c = readC();
                    L1PcInstance resusepc2 = (L1PcInstance) World.get().findObject(pc.getTempID());
                    pc.setTempID(0);
                    if (resusepc2 == null) {
                        break;
                    }
                    if (c == 0) {
                        break;
                    }
                    if (c != 1) {
                        break;
                    }
                    L1EffectInstance tomb2 = pc.get_tomb();
                    if (tomb2 != null) {
                        tomb2.broadcastPacketAll(new S_DoActionGFX(tomb2.getId(), 8));
                        tomb2.deleteMe();
                    }
                    pc.sendPacketsX8(new S_SkillSound(pc.getId(), 230));
                    pc.resurrect(pc.getMaxHp());
                    pc.setCurrentHp(pc.getMaxHp());
                    pc.startHpRegeneration();
                    pc.startMpRegeneration();
                    pc.stopPcDeleteTimer();
                    pc.sendPacketsAll(new S_Resurrection(pc, resusepc2, 0));
                    pc.sendPacketsAll(new S_CharVisualUpdate(pc));
                    if (pc.getExpRes() == 1 && pc.isGres() && pc.isGresValid()) {
                        pc.resExp();
                        pc.setExpRes(0);
                        pc.setGres(false);
                        break;
                    }
                    break;
                }
                case 325: {
                    int c = readC();
                    String petName = readS();
                    if (pc.is_rname()) {
                        String name = Matcher.quoteReplacement(petName);
                        name = name.replaceAll("\\s", "");
                        name = name.replaceAll("\u3000", "");
                        name = String.valueOf(name.substring(0, 1).toUpperCase()) + name.substring(1);
                        String[] banlist;
                        int length = (banlist = C_CreateChar.BANLIST).length;
                        int k = 0;
                        while (k < length) {
                            String ban = banlist[k];
                            if (name.indexOf(ban) != -1) {
                                name = "";
                            }
                            ++k;
                        }
                        if (name.length() == 0) {
                            pc.sendPackets(new S_ServerMessage(53));
                            return;
                        }
                        if (!C_CreateChar.isInvalidName(name)) {
                            pc.sendPackets(new S_ServerMessage(53));
                            return;
                        }
                        if (CharObjidTable.get().charObjid(name) != 0) {
                            pc.sendPackets(new S_ServerMessage(58));
                            return;
                        }
                        World.get().removeObject(pc);
                        pc.getInventory().consumeItem(41227, 1L);
                        RecordTable.get().recordPcChangeName(pc.getId(), pc.getAccountName(), pc.getName(), name,
                                pc.getIp());
                        pc.setName(name);
                        CharObjidTable.get().reChar(pc.getId(), name);
                        CharacterTable.get().newCharName(pc.getId(), name);
                        World.get().storeObject(pc);
                        pc.sendPacketsAll(new S_ChangeName(pc, true));
                        pc.sendPackets(new S_ServerMessage(166, "由於人物名稱異動!請重新登入遊戲!將於5秒後強制斷線!"));
                        KickPc kickPc = new KickPc(pc);
                        kickPc.start_cmd();
                    } else {
                        L1PetInstance pet = (L1PetInstance) World.get().findObject(pc.getTempID());
                        pc.setTempID(0);
                        renamePet(pet, petName);
                    }
                    pc.rename(false);
                    break;
                }
                case 512: {
                    int c = readC();
                    String houseName = readS();
                    int houseId = pc.getTempID();
                    pc.setTempID(0);
                    if (houseName.length() <= 16) {
                        L1House house = HouseReading.get().getHouseTable(houseId);
                        house.setHouseName(houseName);
                        HouseReading.get().updateHouse(house);
                        break;
                    }
                    pc.sendPackets(new S_ServerMessage(513));
                    break;
                }
                case 630: {
                    int c = readC();
                    L1PcInstance fightPc = (L1PcInstance) World.get().findObject(pc.getFightId());
                    if (c == 0) {
                        pc.setFightId(0);
                        fightPc.setFightId(0);
                        fightPc.sendPackets(new S_ServerMessage(631, pc.getName()));
                        break;
                    }
                    if (c == 1) {
                        fightPc.sendPackets(new S_PacketBox(5, fightPc.getFightId(), fightPc.getId()));
                        pc.sendPackets(new S_PacketBox(5, pc.getFightId(), pc.getId()));
                        break;
                    }
                    break;
                }
                case 653: {
                    int c = readC();
                    L1PcInstance target653 = (L1PcInstance) World.get().findObject(pc.getPartnerId());
                    if (c == 0) {
                        return;
                    }
                    if (c == 1) {
                        if (target653 != null) {
                            target653.setPartnerId(0);
                            target653.save();
                            target653.sendPackets(new S_ServerMessage(662));
                        } else {
                            CharacterTable.get();
                            CharacterTable.updatePartnerId(pc.getPartnerId());
                        }
                    }
                    pc.setPartnerId(0);
                    pc.save();
                    pc.sendPackets(new S_ServerMessage(662));
                    break;
                }
                case 654: {
                    int c = readC();
                    L1PcInstance partner = (L1PcInstance) World.get().findObject(pc.getTempID());
                    pc.setTempID(0);
                    if (partner == null) {
                        break;
                    }
                    if (c == 0) {
                        partner.sendPackets(new S_ServerMessage(656, pc.getName()));
                        break;
                    }
                    if (c == 1) {
                        pc.setPartnerId(partner.getId());
                        pc.save();
                        pc.sendPackets(new S_ServerMessage(790));
                        pc.sendPackets(new S_ServerMessage(655, partner.getName()));
                        partner.setPartnerId(pc.getId());
                        partner.save();
                        partner.sendPackets(new S_ServerMessage(790));
                        partner.sendPackets(new S_ServerMessage(655, pc.getName()));
                        Iterator<L1PcInstance> iterator2 = World.get().getAllPlayers().iterator();
                        while (iterator2.hasNext()) {
                            L1PcInstance allpc = iterator2.next();
                            if (allpc != null) {
                                allpc.sendPackets(
                                        new S_SystemMessage("恭喜 " + pc.getName() + " 與 " + partner.getName() + " 結為夫妻。"));
                                allpc.sendPackets(new S_SystemMessage("讓我們一起為這對新人送上最真摯的祝福。"));
                            }
                        }
                        break;
                    }
                    break;
                }
                case 729: {
                    int c = readC();
                    if (c == 0) {
                        int objId = pc.getTempID();
                        Collection<L1PcInstance> allPc = World.get().getAllPlayers();
                        Iterator<L1PcInstance> iterator3 = allPc.iterator();
                        while (iterator3.hasNext()) {
                            L1PcInstance each = iterator3.next();
                            if (each.getId() == objId) {
                                each.sendPackets(new S_ServerMessage("對方拒絕了你的呼喚。"));
                                break;
                            }
                        }
                        if (pc.getcallclanal() > 0) {
                            pc.setcallclanal(0);
                        }
                        pc.setTempID(0);
                        break;
                    }
                    if (c != 1) {
                        break;
                    }
                    if (pc.getcallclanal() <= 0) {
                        callClan(pc);
                        break;
                    }
                    if (!pc.getInventory().checkItem(ConfigOther.target_clan_itemid, ConfigOther.target_clan_count)) {
                        pc.sendPackets(new S_ServerMessage(ConfigOther.clanmsg2));
                        return;
                    }
                    if (!pc.getInventory().checkItem(ConfigOther.target_clan_itemid, ConfigOther.target_clan_count)) {
                        pc.sendPackets(new S_ServerMessage(ConfigOther.alliancemsg2));
                        return;
                    }
                    if (!pc.getMap().isAlliancePc()) {
                        pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
                        return;
                    }
                    if (!pc.getMap().isClanPc()) {
                        pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
                        return;
                    }
                    if (isInWarAreaAndWarTime(pc)) {
                        pc.sendPackets(new S_ServerMessage("旗子內禁止使用"));
                        return;
                    }
                    callClan1(pc);
                    break;
                }
                case 4000: {
                    int c = readC();
                    if (c == 0) {
                        int objId = pc.getTempID();
                        Collection<L1PcInstance> allPc = World.get().getAllPlayers();
                        Iterator<L1PcInstance> iterator4 = allPc.iterator();
                        while (iterator4.hasNext()) {
                            L1PcInstance each = iterator4.next();
                            if (each.getId() == objId) {
                                each.sendPackets(new S_ServerMessage("對方拒絕了你的呼喚。"));
                                break;
                            }
                        }
                        if (pc.getcallclanal() > 0) {
                            pc.setcallclanal(0);
                        }
                        pc.setTempID(0);
                        break;
                    }
                    if (c != 1 || pc.getcallclanal() <= 0) {
                        break;
                    }
                    if (!pc.getInventory().checkItem(ConfigOther.target_party_itemid, ConfigOther.target_party_count)) {
                        pc.sendPackets(new S_ServerMessage(ConfigOther.clanmsg6));
                        return;
                    }
                    if (!pc.getMap().isPartyPc()) {
                        pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
                        return;
                    }
                    if (isInWarAreaAndWarTime(pc)) {
                        pc.sendPackets(new S_ServerMessage("旗子內禁止使用"));
                        return;
                    }
                    callpartyall(pc);
                    break;
                }
                case 738: {
                    int c = readC();
                    if (c == 0) {
                        break;
                    }
                    if (c != 1 || pc.getExpRes() != 1) {
                        break;
                    }
                    int cost = 0;
                    int level = pc.getLevel();
                    int lawful = pc.getLawful();
                    if (level < 45) {
                        cost = level * level * 100;
                    } else {
                        cost = level * level * 200;
                    }
                    if (lawful >= 0) {
                        cost /= 2;
                    }
                    if (pc.getInventory().consumeItem(40308, cost)) {
                        pc.resExp();
                        pc.setExpRes(0);
                        break;
                    }
                    pc.sendPackets(new S_ServerMessage(189));
                    break;
                }
                case 223: {
                    L1PcInstance alliancePc = (L1PcInstance) World.get().findObject(pc.getTempID());
                    pc.setTempID(0);
                    if (alliancePc == null) {
                        return;
                    }
                    int c = readC();

                    if (c == 1) {
                        L1Clan user_clan = pc.getClan();
                        if (user_clan == null || pc.getId() != user_clan.getLeaderId()) {
                            // 血盟君主才可使用此命令。
                            pc.sendPackets(new S_ServerMessage(518));
                            return;
                        }

                        L1Clan target_clan = alliancePc.getClan();
                        if (target_clan == null) {
                            return;
                        }

                        L1Alliance alliance = ClanAllianceReading.get().getAlliance(user_clan.getClanId());
                        if (alliance == null) {
                            alliance = new L1Alliance(user_clan.getClanId(), user_clan, target_clan);
                            ClanAllianceReading.get().insertAlliance(alliance);

                        } else {
                            if (!alliance.checkSize()) {
                                alliancePc.sendPackets(new S_ServerMessage(1201));
                                return;
                            }
                            alliance.addAlliance(target_clan);
                            ClanAllianceReading.get().updateAlliance(alliance.getOrderId(), alliance.getTotalList());
                        }

                        World.get().broadcastPacketToAll(
                                new S_ServerMessage(224, user_clan.getClanName(), target_clan.getClanName()));

                        alliance.sendPacketsAll("", new S_ServerMessage(1200, target_clan.getClanName()));

                    } else if (c == 0) {
                        alliancePc.sendPackets(new S_ServerMessage(1198));
                    }
                    break;
                }
                /**
                 * 穿雲箭增加判斷玩家狀態限制
                 */
                case 748: {
                    //檢查傳送限制秒數
                    /*LocalDateTime nowTime = LocalDateTime.now();
                    long time = Duration.between(pc.getLastTimeAllCallTime(), nowTime).getSeconds();
                    if (time > ConfigOther.CALL_ALL_REPLY_SECOND) {
                        _log.warn("間隔超過了" + time + "秒");
                        pc.sendPackets(new S_ServerMessage("超過使用限制" + ConfigOther.CALL_ALL_REPLY_SECOND + "秒，取消傳送"));
                        break;
                    }*/

                    if (pc.hasSkillEffect(87)) {
                        pc.sendPackets(new S_ServerMessage("身上有衝擊之暈狀態，無法使用穿雲箭"));
                        break;
                    }
                    if (pc.hasSkillEffect(66)) {
                        pc.sendPackets(new S_ServerMessage("身上有沉睡之霧狀態，無法使用穿雲箭"));
                    }
                    if (!pc.hasSkillEffect(1692)) {
                        int newX = pc.getTeleportX();
                        int newY = pc.getTeleportY();
                        short mapId = pc.getTeleportMapId();
                        L1Teleport.teleport(pc, newX, newY, mapId, 5, true);
                        break;
                    }
                    if (pc.hasSkillEffect(50)) {
                        pc.sendPackets(new S_ServerMessage("身上有冰茅圍籬狀態，無法使用穿雲箭"));
                        break;
                    }
                    if (pc.hasSkillEffect(33)) {
                        pc.sendPackets(new S_ServerMessage("身上有木乃伊的詛咒狀態，無法使用穿雲箭"));
                        break;
                    }
                    if (pc.hasSkillEffect(157)) {
                        pc.sendPackets(new S_ServerMessage("身上有木乃伊的詛咒狀態，無法使用穿雲箭"));
                        break;
                    }

                    int c = readC();
                    if (c == 0) {
                        break;
                    }
                    if (c == 1) {
                        int newX = pc.getTeleportX();
                        int newY = pc.getTeleportY();
                        short mapId = pc.getTeleportMapId();
                        L1Teleport.teleport(pc, newX, newY, mapId, 5, true);
                        break;
                    }
                    break;
                }
                case 951: {
                    int c = readC();
                    L1PcInstance chatPc = (L1PcInstance) World.get().findObject(pc.getPartyID());
                    if (chatPc == null) {
                        break;
                    }
                    if (c == 0) {
                        chatPc.sendPackets(new S_ServerMessage(423, pc.getName()));
                        pc.setPartyID(0);
                        break;
                    }
                    if (c != 1) {
                        break;
                    }
                    if (!chatPc.isInChatParty()) {
                        L1ChatParty chatParty = new L1ChatParty();
                        chatParty.addMember(chatPc);
                        chatParty.addMember(pc);
                        chatPc.sendPackets(new S_ServerMessage(424, pc.getName()));
                        break;
                    }
                    if (chatPc.getChatParty().isVacancy() || chatPc.isGm()) {
                        chatPc.getChatParty().addMember(pc);
                        break;
                    }
                    chatPc.sendPackets(new S_ServerMessage(417));
                    break;
                }
                case 953: {
                    int c = readC();
                    L1PcInstance target654 = (L1PcInstance) World.get().findObject(pc.getPartyID());
                    if (target654 == null) {
                        break;
                    }
                    if (c == 0) {
                        target654.sendPackets(new S_ServerMessage(423, pc.getName()));
                        pc.setPartyID(0);
                        break;
                    }
                    if (c != 1) {
                        break;
                    }
                    if (!target654.isInParty()) {
                        L1Party party = new L1Party();
                        party.addMember(target654);
                        party.addMember(pc);
                        target654.sendPackets(new S_ServerMessage(424, pc.getName()));
                        break;
                    }
                    if (target654.getParty().isVacancy()) {
                        target654.getParty().addMember(pc);
                        break;
                    }
                    target654.sendPackets(new S_ServerMessage(417));
                    break;
                }
                case 479: {
                    int c = readC();
                    if (c != 1) {
                        break;
                    }
                    String s = readS();
                    if (pc.getLevel() - 50 <= pc.getBonusStats()) {
                        return;
                    }
                    if (s.equalsIgnoreCase("str")) {
                        if (pc.getBaseStr() < ConfigAlt.POWER) {
                            pc.addBaseStr(1);
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    } else if (s.equalsIgnoreCase("dex")) {
                        if (pc.getBaseDex() < ConfigAlt.POWER) {
                            pc.addBaseDex(1);
                            pc.resetBaseAc();
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    } else if (s.equalsIgnoreCase("con")) {
                        if (pc.getBaseCon() < ConfigAlt.POWER) {
                            pc.addBaseCon(1);
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    } else if (s.equalsIgnoreCase("int")) {
                        if (pc.getBaseInt() < ConfigAlt.POWER) {
                            pc.addBaseInt(1);
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    } else if (s.equalsIgnoreCase("wis")) {
                        if (pc.getBaseWis() < ConfigAlt.POWER) {
                            pc.addBaseWis(1);
                            pc.resetBaseMr();
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    } else if (s.equalsIgnoreCase("cha")) {
                        if (pc.getBaseCha() < ConfigAlt.POWER) {
                            pc.addBaseCha(1);
                            pc.setBonusStats(pc.getBonusStats() + 1);
                            pc.sendPackets(new S_OwnCharStatus2(pc));
                            pc.sendPackets(new S_CharVisualUpdate(pc));
                            pc.save();
                        } else {
                            pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWER + "。 請重試一次。"));
                        }
                    }
                    if (pc.power()) {
                        pc.sendPackets(new S_Bonusstats(pc.getId()));
                        break;
                    }
                    break;
                }
                case 1210: // 確定要退出同盟嗎? (Y/N)
                    if (readC() == 1) {
                        L1Clan user_clan = pc.getClan();
                        if (user_clan == null || pc.getId() != user_clan.getLeaderId()) {
                            // 血盟君主才可使用此命令。
                            pc.sendPackets(new S_ServerMessage(518));
                            return;
                        }

                        L1Alliance alliance = ClanAllianceReading.get().getAlliance(user_clan.getClanId());
                        if (alliance == null) {
                            return;
                        }

                        for (L1Clan l1clan : alliance.getTotalList()) {
                            if (l1clan.getClanId() == user_clan.getClanId()) {
                                alliance.getTotalList().remove(l1clan);
                                break;
                            }
                        }

                        if (alliance.getTotalList().size() < 2) {
                            ClanAllianceReading.get().deleteAlliance(alliance.getOrderId());

                        } else {
                            ClanAllianceReading.get().updateAlliance(alliance.getOrderId(), alliance.getTotalList());
                        }

                        World.get().broadcastPacketToAll(new S_ServerMessage(225,
                                alliance.getTotalList().get(0).getClanName(), user_clan.getClanName()));

                        alliance.sendPacketsAll("", new S_ServerMessage(1204, user_clan.getClanName()));

                    }
                    break;

                case 3312: {
                    int c = readC();
                    if (c == 1) {
                        pc.getInventory().consumeItem(ConfigOther.checkitem76, ConfigOther.checkitemcount76);
                        pc.getQuest().set_step(58003, 1);
                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "slot9"));
                        break;
                    }
                    break;
                }
                case 3313: {
                    int c = readC();
                    if (c == 1) {
                        pc.getInventory().consumeItem(ConfigOther.checkitem81, ConfigOther.checkitemcount81);
                        pc.getQuest().set_step(58002, 1);
                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "slot9"));
                        break;
                    }
                    break;
                }
                case 3589: {
                    int c = readC();
                    if (c == 1) {
                        pc.getInventory().consumeItem(ConfigOther.checkitem59, ConfigOther.checkitemcount59);
                        pc.getQuest().set_step(58001, 1);
                        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "slot9"));
                        break;
                    }
                    break;
                }
                case 1268: {
                    if (pc.getLevel() <= 29 || pc.getLevel() >= 52) {
                        pc.getLevel();
                        break;
                    }
                    break;
                }
            }
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    private void leaveClan(L1PcInstance leavePc, boolean isApproved) {
        String clan_name = leavePc.getClanname();
        L1Clan clan = WorldClan.get().getClan(clan_name);
        L1PcInstance[] clanMember = clan.getOnlineClanMember();
        int i = 0;
        while (i < clanMember.length) {
            clanMember[i].sendPackets(new S_ServerMessage(178, leavePc.getName(), clan_name));
            ++i;
        }
        if (clan.getWarehouseUsingChar() == leavePc.getId()) {
            clan.setWarehouseUsingChar(0);
        }
        try {
            long time = 0L;
            if (isApproved) {
                time = 0L;
            } else {
                time = 60000L; //Kevin 退出血盟後須等1分鐘才可以再加入
            }
            leavePc.setClanid(0);
            leavePc.setClanname("");
            leavePc.setClanRank(0);
            leavePc.setClanMemberId(0);
            leavePc.setClanMemberNotes("");
            if (ConfigClan.clandelt) {
                leavePc.setPcContribution(0);
            }
            leavePc.setClanContribution(0);
            leavePc.setClanNameContribution("");
            leavePc.sendPacketsAll(new S_CharTitle(leavePc.getId(), ""));
            leavePc.sendPacketsAll(new S_CharReset(leavePc.getId(), 0));
            leavePc.save();
            L1PcUnlock.Pc_Unlock(leavePc);
            clan.delMemberName(leavePc.getName());
            ClanMembersTable.getInstance().deleteMember(leavePc.getId());
            leavePc.setRejoinClanTime(new Timestamp(System.currentTimeMillis() + time));
        } catch (Exception e) {
            C_Attr._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void callClan(L1PcInstance pc) {
        L1PcInstance callClanPc = (L1PcInstance) World.get().findObject(pc.getTempID());
        pc.setTempID(0);
        if (pc.isParalyzedX()) {
            return;
        }
        if (callClanPc == null) {
            return;
        }
        if (!pc.getMap().isEscapable() && !pc.isGm()) {
            pc.sendPackets(new S_ServerMessage(647));
            return;
        }
        if (pc.getId() != callClanPc.getCallClanId()) {
            return;
        }
        boolean isInWarArea = false;
        int castleId = L1CastleLocation.getCastleIdByArea(callClanPc);
        if (castleId != 0) {
            isInWarArea = true;
            if (ServerWarExecutor.get().isNowWar(castleId)) {
                isInWarArea = false;
            }
        }
        short mapId = callClanPc.getMapId();
        if ((mapId != 0 && mapId != 4 && mapId != 304) || isInWarArea) {
            pc.sendPackets(new S_ServerMessage(629));
            return;
        }
        if (QuestMapTable.get().isQuestMap(pc.getMapId())) {
            pc.sendPackets(new S_ServerMessage(629));
            return;
        }
        int[] HEADING_TABLE_X = {0, 1, 1, 1, 0, -1, -1, -1};
        int[] HEADING_TABLE_Y = {-1, -1, 0, 1, 1, 1, 0, -1};
        L1Map map = callClanPc.getMap();
        int locX = callClanPc.getX();
        int locY = callClanPc.getY();
        int heading = callClanPc.getCallClanHeading();
        locX += HEADING_TABLE_X[heading];
        locY += HEADING_TABLE_Y[heading];
        heading = (heading + 4) % 4;
        boolean isExsistCharacter = false;
        Iterator<L1Object> iterator = World.get().getVisibleObjects(callClanPc, 1).iterator();
        while (iterator.hasNext()) {
            L1Object object = iterator.next();
            if (object instanceof L1Character) {
                L1Character cha = (L1Character) object;
                if (cha.getX() == locX && cha.getY() == locY && cha.getMapId() == mapId) {
                    isExsistCharacter = true;
                    break;
                }
                continue;
            }
        }
        if ((locX == 0 && locY == 0) || !map.isPassable(locX, locY, null) || isExsistCharacter) {
            pc.sendPackets(new S_ServerMessage(627));
            return;
        }
        L1Teleport.teleport(pc, locX, locY, mapId, heading, true, 3);
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }

    private void callClan1(L1PcInstance pc) {
        L1PcInstance callClanPc = (L1PcInstance) World.get().findObject(pc.getcallclanal());
        pc.setcallclanal(0);
        if (pc.isParalyzedX()) {
            return;
        }
        if (callClanPc == null) {
            return;
        }
        if (pc.isParalyzedX()) {
            return;
        }
        boolean isInWarArea = false;
        int castleId = L1CastleLocation.getCastleIdByArea(callClanPc);
        if (castleId != 0) {
            isInWarArea = true;
            if (ServerWarExecutor.get().isNowWar(castleId)) {
                isInWarArea = false;
            }
        }
        if (isInWarArea) {
            pc.sendPackets(new S_ServerMessage(626));
            return;
        }
        if (isInWarAreaAndWarTime(callClanPc)) {
            pc.sendPackets(new S_ServerMessage("對方旗子內,無法傳送"));
            return;
        }
        if (!callClanPc.getMap().isAlliancePc()) {
            pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
            return;
        }
        if (!callClanPc.getMap().isClanPc()) {
            pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
            return;
        }
        short mapId = callClanPc.getMapId();
        if (QuestMapTable.get().isQuestMap(pc.getMapId())) {
            pc.sendPackets(new S_ServerMessage(626));
            return;
        }
        pc.getInventory().consumeItem(ConfigOther.target_clan_itemid, ConfigOther.target_clan_count);
        int[] HEADING_TABLE_X = {0, 1, 1, 1, 0, -1, -1, -1};
        int[] HEADING_TABLE_Y = {-1, -1, 0, 1, 1, 1, 0, -1};
        L1Map map = callClanPc.getMap();
        int locX = callClanPc.getX();
        int locY = callClanPc.getY();
        int heading = callClanPc.getCallClanHeading();
        locX += HEADING_TABLE_X[heading];
        locY += HEADING_TABLE_Y[heading];
        heading = (heading + 4) % 4;
        L1Teleport.teleport(pc, locX, locY, mapId, heading, false);
    }

    private void callpartyall(L1PcInstance pc) {
        L1PcInstance callClanPc = (L1PcInstance) World.get().findObject(pc.getcallclanal());
        pc.setcallclanal(0);
        if (pc.isParalyzedX()) {
            return;
        }
        if (callClanPc == null) {
            return;
        }
        if (pc.isParalyzedX()) {
            return;
        }
        boolean isInWarArea = false;
        int castleId = L1CastleLocation.getCastleIdByArea(callClanPc);
        if (castleId != 0) {
            isInWarArea = true;
            if (ServerWarExecutor.get().isNowWar(castleId)) {
                isInWarArea = false;
            }
        }
        short mapId = callClanPc.getMapId();
        if (isInWarArea) {
            pc.sendPackets(new S_ServerMessage(626));
            return;
        }
        if (QuestMapTable.get().isQuestMap(pc.getMapId())) {
            pc.sendPackets(new S_ServerMessage(626));
            return;
        }
        if (isInWarAreaAndWarTime(callClanPc)) {
            pc.sendPackets(new S_ServerMessage("對方旗子內,無法傳送"));
            return;
        }
        if (!callClanPc.getMap().isPartyPc()) {
            pc.sendPackets(new S_ServerMessage("所在地圖無法進行傳送"));
            return;
        }
        int[] HEADING_TABLE_X = {0, 1, 1, 1, 0, -1, -1, -1};
        int[] HEADING_TABLE_Y = {-1, -1, 0, 1, 1, 1, 0, -1};
        L1Map map = callClanPc.getMap();
        int locX = callClanPc.getX();
        int locY = callClanPc.getY();
        int heading = callClanPc.getCallClanHeading();
        locX += HEADING_TABLE_X[heading];
        locY += HEADING_TABLE_Y[heading];
        heading = (heading + 4) % 4;
        L1Teleport.teleport(pc, locX, locY, mapId, heading, false);
    }

    private class KickPc implements Runnable {
        private final ClientExecutor _client;

        private KickPc(L1PcInstance pc) {
            _client = pc.getNetConnection();
        }

        private void start_cmd() {
            GeneralThreadPool.get().execute(this);
        }

        @Override
        public void run() {
            try {
                Thread.sleep(5000L);
                _client.kick();
            } catch (InterruptedException e) {
                C_Attr._log.error(e.getLocalizedMessage(), e);
            }
        }
    }
}
