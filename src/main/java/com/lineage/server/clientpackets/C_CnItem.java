package com.lineage.server.clientpackets;

import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_CnItem extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_CnItem.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			client.getActiveChar();
		} catch (Exception ignored) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
