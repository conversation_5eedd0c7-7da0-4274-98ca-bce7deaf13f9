package com.lineage.server.clientpackets;

import com.lineage.config.ConfigOther;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.Instance.character.CharacterPunishInstance;
import com.lineage.server.datatables.RecordTable;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Inventory;
import com.lineage.server.model.L1Object;
import com.lineage.server.serverpackets.*;
import com.lineage.server.world.World;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

public class C_PickUpItem extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_PickUpItem.class);
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            L1PcInstance pc = client.getActiveChar();

            if (CharacterPunishInstance.get().checkCharacter(pc.getId())) {
                return;
            }

            if (pc.isGhost()) {
            }
            if (pc.isDead() || pc.isTeleport() || pc.isPrivateShop() || pc.isInvisble() || pc.isInvisDelay()) {
                return;
            }
            int x = readH();
            int y = readH();
            int objectId = readD();
            long pickupCount = readD();
            if (pickupCount > 2147483647L) {
                pickupCount = 2147483647L;
            }
            pickupCount = Math.max(0L, pickupCount);
            L1Inventory groundInventory = World.get().getInventory(x, y, pc.getMapId());
            L1Object object = groundInventory.getItem(objectId);
            if (object != null && !pc.isDead()) {
                L1ItemInstance item = (L1ItemInstance) object;
                if (item.getCount() <= 0L) {
                    return;
                }
                if (item.getItemOwnerId() != 0 && pc.getId() != item.getItemOwnerId()) {
                    pc.sendPackets(new S_ServerMessage(623));
                    return;
                }
                if (pc.getLocation().getTileLineDistance(item.getLocation()) > 3) {
                    return;
                }
                item.set_showId(-1);
                if (pc.getInventory().checkAddItem(item, pickupCount) == 0 && item.getX() != 0 && item.getY() != 0) {
                    if (ConfigOther.Pickupitemtoall && item.getItem().getItemId() != 40515) {
                        World.get().broadcastPacketToAll(new S_ServerMessage(
                                "玩家[" + pc.getName() + "]撿起地上物品[" + item.getItem().getName() + "]"));
                    }
                    C_PickUpItem._log.info(
                            "人物:" + pc.getName() + " 撿起物品 " + item.getItem().getName() + " 物品OBJID:" + item.getId());
                    groundInventory.tradeItem(item, pickupCount, pc.getInventory());
                    pc.turnOnOffLight();
                    pc.setHeading(pc.targetDirection(item.getX(), item.getY()));
                    if (!pc.isGmInvis()) {
                        pc.broadcastPacketAll(new S_ChangeHeading(pc));
                        pc.sendPacketsAll(new S_DoActionGFX(pc.getId(), 15));
                    }
                    //Kevin 丹 以下新增
                    if (World.checkpcFallingItem(objectId)) {
                        L1ItemInstance fallingItem = World.getPcFallingItem(objectId);
                        String charName = CharacterTable.get().getCharName(fallingItem.get_char_objid());
                        String message = String.format("玩家【%s】，撿起了玩家【%s】的【%s】", pc.getName(), charName, item.getLogName());

//                        String message = "玩家【" + pc.getName() + "】" + "撿走了" + "【" + item.getLogName() + "】";
                        World.get().broadcastPacketToAll(new S_PacketBoxGree(2, "\\f=" + message));
                        World.get().broadcastPacketToAll((new S_SystemMessage("\\f=" + message)));
                        World.removepcFallingItemObjId(objectId);
                    }
                    //Kevin 丹 以上新增
                    RecordTable.get().recordtakeitem(pc.getName(), item.getAllName(), (int) pickupCount, item.getId(),
                            pc.getIp());
                }
            }
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
