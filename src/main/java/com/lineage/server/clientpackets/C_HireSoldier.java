package com.lineage.server.clientpackets;

import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_HireSoldier extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_HireSoldier.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final int something1 = this.readH();
			final int something2 = this.readH();
			final int something3 = this.readD();
			final int something4 = this.readD();
			this.readH();
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
