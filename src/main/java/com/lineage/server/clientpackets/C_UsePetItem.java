package com.lineage.server.clientpackets;

import com.lineage.server.serverpackets.S_ItemName;
import com.lineage.server.templates.L1PetItem;
import java.util.List;
import com.lineage.server.model.L1Inventory;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.datatables.PetItemTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.world.WorldPet;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_UsePetItem extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_UsePetItem.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			this.read(decrypt);
			final int data = this.readC();
			final int petId = this.readD();
			final int listNo = this.readC();
			final L1PcInstance pc = client.getActiveChar();
			if (pc == null) {
				return;
			}
			final L1PetInstance pet = WorldPet.get().get(Integer.valueOf(petId));
			if (pet == null) {
				return;
			}
			if (pc.getPetList().get(Integer.valueOf(petId)) == null) {
				return;
			}
			switch (pet.getNpcId()) {
			case 45034:
			case 45039:
			case 45040:
			case 45042:
			case 45043:
			case 45044:
			case 45046:
			case 45047:
			case 45048:
			case 45049:
			case 45053:
			case 45054:
			case 45313:
			case 45711:
			case 46042:
			case 46044: {
				return;
			}
			default: {
				final L1Inventory inventory = pet.getInventory();
				final List<L1ItemInstance> itemList = inventory.getItems();
				if (itemList.size() <= 0) {
					return;
				}
				final L1ItemInstance item = itemList.get(listNo);
				if (item == null) {
					return;
				}
				final int itemId = item.getItemId();
				final L1PetItem petItem = PetItemTable.get().getTemplate(itemId);
				if (petItem == null) {
					pc.sendPackets(new S_ServerMessage(79));
					break;
				}
				if (petItem.isWeapom()) {
					usePetWeapon(pc, pet, item, petItem);
					break;
				}
				usePetArmor(pc, pet, item, petItem);
				break;
			}
			}
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	public static void usePetWeapon(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance weapon,
			final L1PetItem petItem) {
		if (pet.getWeapon() == null) {
			setPetWeapon(pc, pet, weapon, petItem);
		} else if (pet.getWeapon().equals(weapon)) {
			removePetWeapon(pc, pet, pet.getWeapon(), petItem);
		} else {
			removePetWeapon(pc, pet, pet.getWeapon(), petItem);
			pc.sendPackets(new S_ItemName(pet.getWeapon()));
			setPetWeapon(pc, pet, weapon, petItem);
		}
	}

	public static void usePetArmor(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance armor,
			final L1PetItem petItem) {
		if (pet.getArmor() == null) {
			setPetArmor(pc, pet, armor, petItem);
		} else if (pet.getArmor().equals(armor)) {
			removePetArmor(pc, pet, pet.getArmor(), petItem);
		} else {
			removePetArmor(pc, pet, pet.getArmor(), petItem);
			pc.sendPackets(new S_ItemName(pet.getArmor()));
			setPetArmor(pc, pet, armor, petItem);
		}
	}

	private static void setPetWeapon(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance weapon,
			final L1PetItem petItem) {
		pet.setHitByWeapon(petItem.getHitModifier());
		pet.setDamageByWeapon(petItem.getDamageModifier());
		pet.addStr(petItem.getAddStr());
		pet.addCon(petItem.getAddCon());
		pet.addDex(petItem.getAddDex());
		pet.addInt(petItem.getAddInt());
		pet.addWis(petItem.getAddWis());
		pet.addMaxHp(petItem.getAddHp());
		pet.addMaxMp(petItem.getAddMp());
		pet.addSp(petItem.getAddSp());
		pet.addMr(petItem.getAddMr());
		pet.setWeapon(weapon);
		weapon.setEquipped(true);
	}

	private static void removePetWeapon(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance weapon,
			final L1PetItem petItem) {
		pet.setHitByWeapon(0);
		pet.setDamageByWeapon(0);
		pet.addStr(-petItem.getAddStr());
		pet.addCon(-petItem.getAddCon());
		pet.addDex(-petItem.getAddDex());
		pet.addInt(-petItem.getAddInt());
		pet.addWis(-petItem.getAddWis());
		pet.addMaxHp(-petItem.getAddHp());
		pet.addMaxMp(-petItem.getAddMp());
		pet.addSp(-petItem.getAddSp());
		pet.addMr(-petItem.getAddMr());
		pet.setWeapon(null);
		weapon.setEquipped(false);
	}

	private static void setPetArmor(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance armor,
			final L1PetItem petItem) {
		pet.addAc(petItem.getAddAc());
		pet.addStr(petItem.getAddStr());
		pet.addCon(petItem.getAddCon());
		pet.addDex(petItem.getAddDex());
		pet.addInt(petItem.getAddInt());
		pet.addWis(petItem.getAddWis());
		pet.addMaxHp(petItem.getAddHp());
		pet.addMaxMp(petItem.getAddMp());
		pet.addSp(petItem.getAddSp());
		pet.addMr(petItem.getAddMr());
		pet.setArmor(armor);
		armor.setEquipped(true);
	}

	private static void removePetArmor(final L1PcInstance pc, final L1PetInstance pet, final L1ItemInstance armor,
			final L1PetItem petItem) {
		pet.addAc(-petItem.getAddAc());
		pet.addStr(-petItem.getAddStr());
		pet.addCon(-petItem.getAddCon());
		pet.addDex(-petItem.getAddDex());
		pet.addInt(-petItem.getAddInt());
		pet.addWis(-petItem.getAddWis());
		pet.addMaxHp(-petItem.getAddHp());
		pet.addMaxMp(-petItem.getAddMp());
		pet.addSp(-petItem.getAddSp());
		pet.addMr(-petItem.getAddMr());
		pet.setArmor(null);
		armor.setEquipped(false);
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
