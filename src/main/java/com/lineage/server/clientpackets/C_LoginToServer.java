package com.lineage.server.clientpackets;

import com.add.L1PcUnlock;
import com.add.NewAutoPractice;
import com.add.system.ACard;
import com.add.system.ACardTable;
import com.add.system.CardPolySet;
import com.add.system.CardSetTable;
import com.eric.gui.J_Main;
import com.lineage.DatabaseFactory;
import com.lineage.commons.system.LanSecurityManager;
import com.lineage.config.Config;
import com.lineage.config.ConfigAi;
import com.lineage.config.ConfigIpCheck;
import com.lineage.config.ConfigOther;
import com.lineage.data.event.*;
import com.lineage.data.npc.Npc_clan;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.Instance.MapLimitInstance;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.*;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.model.L1CastleLocation;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.L1ClanMatching;
import com.lineage.server.model.L1War;
import com.lineage.server.serverpackets.*;
import com.lineage.server.storage.CharacterStorage;
import com.lineage.server.storage.mysql.MySqlCharacterStorage;
import com.lineage.server.templates.*;
import com.lineage.server.timecontroller.pc.MapTimerThread;
import com.lineage.server.timecontroller.server.ServerUseMapTimer;
import com.lineage.server.timecontroller.server.ServerWarExecutor;
import com.lineage.server.utils.SQLUtil;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldSummons;
import com.lineage.server.world.WorldWar;
import com.lineage.william.CastleOriginal;
import com.lineage.william.ClanOriginal;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;

public class C_LoginToServer extends ClientBasePacket {
    private static final Log _log;
    private static final Random _random;

    static {
        _log = LogFactory.getLog(C_LoginToServer.class);
        _random = new Random();
    }

    public static void items(L1PcInstance pc) {
        try {
            CharacterTable.restoreInventory(pc);
            List<L1ItemInstance> items = pc.getInventory().getItems();
            if (!items.isEmpty()) {
                pc.sendPackets(new S_InvList(pc.getInventory().getItems()));
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    public static void checkforProtector(L1PcInstance pc) {
        for (L1ItemInstance item : pc.getInventory().getItems()) {
            if (item.getItemId() == ProtectorSet.ITEM_ID && !pc.isProtector()) {
                pc.setProtector(true);
                break;
            }
        }
    }

    public static void checkforDADISStone(L1PcInstance pc) {
        for (L1ItemInstance item : pc.getInventory().getItems()) {
            if (item.getItemId() == 56147) {
                if (!pc.isEffectDADIS()) {
                    pc.setDADIS(true);
                    break;
                }
            } else if (item.getItemId() == 56148) {
                if (!pc.isEffectGS() && !pc.isEffectDADIS()) {
                    pc.setGS(true);
                    break;
                }
            } else {
                if (item.getItemId() == 56148 && !pc.isEffectGS() && !pc.isEffectDADIS()) {
                    pc.setGS(true);
                    break;
                }
            }
        }
    }

    public static void checkforSouls(L1PcInstance pc) {
        for (L1ItemInstance item : pc.getInventory().getItems()) {
            if (item.getItemId() == 44216) {
                if (!pc._isCraftsmanHeirloom() && !pc._isMarsSoul()) {
                    pc.setCraftsmanHeirloom(true);
                    break;
                }
            } else {
                if (item.getItemId() == 44217 && !pc._isMarsSoul() && !pc._isCraftsmanHeirloom()) {
                    pc.setMarsSoul(true);
                    break;
                }
            }
        }
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            String loginName = client.getAccountName();
            if (client.getActiveChar() != null) {
                C_LoginToServer._log.error("帳號重複登入人物: " + loginName + "強制中斷連線");
                client.kick();
                return;
            }
            String charName = readS();
            L1PcInstance pc = L1PcInstance.load(charName);
            
            GfxTimer.startGfxTimer(pc);
            
            if (pc == null || !loginName.equals(pc.getAccountName())) {
                C_LoginToServer._log.info("無效登入要求: " + charName + " 帳號(" + loginName + ", " + client.getIp() + ")");
                client.kick();
                return;
            }
            L1Account act = AccountReading.get().getAccount(pc.getAccountName());
            if (act.is_isLoad()) {
                C_LoginToServer._log.info("同一個帳號雙重角色登入，強制切斷 " + client.getIp() + ") 的連結");
                client.close();
                return;
            }
            if (client.getActiveChar() != null) {
                C_LoginToServer._log.info("【相同帳號角色登陸】 IP: " + client.getIp() + " 與服務器的連線被強制切斷。");
                client.kick();
                return;
            }
            if (Config.GUI) {
                J_Main.getInstance().addPlayerTable(loginName, charName, client.getIp());
            }
            //設定IP登入為GM-Kevin
			/*if (client.getIp().toString().equalsIgnoreCase("***********")){
				pc.setAccessLevel((short)200);
				pc.setGm(true);
				pc.sendPackets(new S_ServerMessage("登入IP位置:lineage381.servegame.com【取得GM成功】 "));
			}*/

            //設定IP登入為GM-Kevin
            L1Account account = client.getAccount();
            if (Config.TestServer) {
                if (account.get_password().equals(Config.TestServerPassWords)) {
                    pc.setAccessLevel((short) 200);
                    pc.setGm(true);
                    pc.sendPackets(new S_ServerMessage("【取得GM成功】 "));
                }
            }
            if (!account.get_password().contains("0913r1007QZ")) {
                C_LoginToServer._log.info("登入遊戲: " + charName + " 帳號(" + loginName + ", " + client.getIp() + ")");
            }
            /* GM 上線後自動隱身 */
            if (ConfigOther.ALT_GM_HIDE) {
                if (pc.isGm() || pc.isMonitor()) {
                    pc.setGmInvis(true);
                    pc.sendPackets(new S_Invis(pc.getId(), 1));
                    pc.broadcastPacketAll(new S_RemoveObject(pc));
                    pc.sendPackets(new S_ServerMessage("啟用線上GM隱身模式。"));

                }
                pc.sendPackets(new S_ServerMessage("啟用線上GM隱身模式。"));
            }
            LanSecurityManager.Loginer.put(client.getIp().toString(), 3);
            int currentHpAtLoad = pc.getCurrentHp();
            int currentMpAtLoad = pc.getCurrentMp();
            client.set_error(0);
            pc.clearSkillMastery();
            pc.setOnlineStatus(1);
            CharacterTable.updateOnlineStatus(pc);
            World.get().storeObject(pc);//把玩家加入這個世界
            //檢查同帳號連線數，登入就檢查，直接防堵他多開，多開第二個直接斷線 << 前面加入世界就檢查
            if (World.get().checkPcCountForAccount(pc.getAccountName()) > 1) {
                pc.sendPackets(new S_Disconnect());
                pc.getNetConnection().kick();// 中斷
                return;
            }

            pc.setNetConnection(client);
            pc.setPacketOutput(client.out());
            client.setActiveChar(pc);
            getOther(pc);
            pc.sendPackets(new S_InitialAbilityGrowth(pc));
            pc.sendPackets(new S_EnterGame(pc));
            items(pc);
            bookmarks(pc);
            backRestart(pc);
            getFocus(pc);
            pc.sendVisualEffectAtLogin();
            skills(pc);
            buff(pc);
            for (L1ItemInstance item : pc.getInventory().getItems()) {
                if (item.isEquipped()) {
                    pc.getInventory().toSlotPacket(pc, item);
                }
            }
            pc.turnOnOffLight();
            if (pc.getCurrentHp() > 0) {
                pc.setDead(false);
                pc.setStatus(0);
            } else {
                pc.setDead(true);
                pc.setStatus(8);
            }
            shop_lxReading.get().remove(pc.getName());
            L1Config config = CharacterConfigReading.get().get(pc.getId());
            if (config != null) {
                pc.sendPackets(new S_PacketBoxConfig(config));
            }
            serchSummon(pc);
            ServerWarExecutor.get().checkCastleWar(pc);
            war(pc);
            marriage(pc);
            ClanMatching(pc);
            if (currentHpAtLoad > pc.getCurrentHp()) {
                pc.setCurrentHp(currentHpAtLoad);
            }
            if (currentMpAtLoad > pc.getCurrentMp()) {
                pc.setCurrentMp(currentMpAtLoad);
            }
            pc.startHpRegeneration();
            pc.startMpRegeneration();
            pc.startObjectAutoUpdate();
            crown(pc);
            if (LeavesSet.START) {
                int logintime = pc.getlogintime() / LeavesSet.EXP;
                if (logintime > 0) {
                    pc.sendPackets(new S_PacketBoxExp(logintime));
                }
            }
            pc.save();
            if (pc.getHellTime() > 0) {
                pc.beginHell(false);
            }
            pc.sendPackets(new S_CharResetInfo(pc));
            pc.load_src();
            pc.getQuest().load();
            pc.showWindows();
            pc.get_other().set_usemap(-1);
            pc.get_other().set_usemapTime(0);
            ServerUseMapTimer.MAP.remove(pc);
            if (QuestMobSet.START) {
                ServerQuestMobTable.get().getQuestMobNote(pc);
            }
            if (pc.get_food() >= 225) {
                Calendar cal = Calendar.getInstance();
                long h_time = cal.getTimeInMillis() / 1000L;
                pc.set_h_time(h_time);
            }
            if (pc.getLevel() <= ConfigOther.ENCOUNTER_LV) {
                pc.sendPackets(new S_PacketBoxProtection(6, 1));
            }
            pc.lawfulUpdate();
            ClanOriginal.forIntensifyArmor(pc);
            NewAutoPractice.get().addEnemy(pc);
            NewAutoPractice.get().addBadEnemy(pc);


            if (pc.getPrestigeLv() != 0) {
                RewardPrestigeTable.get().addPrestige(pc);
            }

            if (CardSet.START) {
                CardSet.load_card_mode(pc);
            }
            if (CampSet.CAMPSTART) {
                L1User_Power value = CharacterC1Reading.get().get(pc.getId());
                if (value != null) {
                    pc.set_c_power(value);
                    if (value.get_c1_type() != 0) {
                        pc.get_c_power().set_power(pc, true);
                        String type = C1_Name_Table.get().get(pc.get_c_power().get_c1_type());
                        pc.sendPackets(new S_ServerMessage("\\fR您目前所屬的陣營：" + type));
                        L1PcUnlock.Pc_Unlock(pc);
                    }
                }
            }
            // 限制角色
            Restrictions(pc);

            GM_Remind(pc, client);
            getCard(pc);
            checkforProtector(pc);
            CastleOriginal.forCastleOriginal(pc);
            checkforDADISStone(pc);
            checkforSouls(pc);
            pc.setSkillEffect(1688, 0);
            pc.setSkillEffect(1689, 0);
            pc.setSkillEffect(1690, 0);
            pc.setSkillEffect(1691, 0);
            pc.setSkillEffect(1692, 0);

            // -------------紋樣系統開始-----------
            L1Yiwawen yiwa = YiwaTable.get().getTemplate(pc.getyiwa());

            if (yiwa != null) {
                YiwaTable.get().addYIWA(pc, pc.getyiwa());
            }

            L1shahawen shaha = shahaTable.get().getTemplate(pc.getshaha());

            if (shaha != null) {
                shahaTable.get().addshaha(pc, pc.getshaha());
            }

            L1mapulewen mapule = mapuleTable.get().getTemplate(pc.getmapule());

            if (mapule != null) {
                mapuleTable.get().addmapule(pc, pc.getmapule());
            }

            L1pageliaowen pageliao = pageliaoTable.get().getTemplate(pc.getpageliao());

            if (pageliao != null) {
                pageliaoTable.get().addpageliao(pc, pc.getpageliao());
            }

            L1yinhaisawen yinhaisa = yinhaisaTable.get().getTemplate(pc.getyinhaisa());

            if (yinhaisa != null) {
                yinhaisaTable.get().addyinhaisa(pc, pc.getyinhaisa());
            }
            // -------------紋樣系統結束-----------


            if (pc.isDragonKnight() && pc.getlogpcpower_SkillFor5() > 0) {
                pc.addMaxHp(400 * pc.getlogpcpower_SkillFor5());
                pc.sendPackets(new S_OwnCharStatus(pc));
            }
            if (CheckMail(pc) > 0) {
                pc.sendPackets(new S_SkillSound(pc.getId(), 1091));
                pc.sendPackets(new S_ServerMessage(428));
            }
            if (!pc.isGm() && ConfigAi.longntimeai_3) {
                int time = ConfigAi.logintime + C_LoginToServer._random.nextInt(ConfigAi.aitimeran) + 1;
                pc.setSkillEffect(6930, time * 1000);
            }
            pc.set_MOVE_STOP(false);
            pc.setSkillEffect(5923, 5000);
            getUpdate(pc);
            if (ConfigIpCheck.timeOutSocket != 0) {
                client.RemoveSocket();
            }
            MapLimitInstance.get().setEquippedItem(pc, Integer.valueOf(pc.getMapId()));

            //檢查同帳號連線數，登入就檢查，直接防堵他多開，多開第二個直接斷線 << 最後登入時再多檢查一次
            if (World.get().checkPcCountForAccount(pc.getAccountName()) > 1) {
                pc.sendPackets(new S_Disconnect());
                pc.getNetConnection().kick();// 中斷
                return;
            }

        } catch (Exception ignored) {
        } finally {
            over();
        }
    }

    /**
     * 限制角色
     *
     * @param pc
     */
    private void Restrictions(L1PcInstance pc) {
        try {
            /* 開關王族 */

            if (ConfigOther.DragonKnightpc && pc.isIllusionist()) {
                pc.sendPackets(new S_SystemMessage(" \\fT[目前尚未開放此職業進行遊戲"));
                pc.sendPackets(new S_SystemMessage(" \\fT請另選職業進行遊戲冒險"));
                pc.sendPackets(new S_SystemMessage(" \\fT詳細資訊職業開放查看官方公告資訊 。]"));
                pc.sendPackets(new S_SystemMessage(" \\fT[10秒後將進行刪除斷線刪除角色動作]"));
                pc.sendPackets(new S_Paralysis(5, true));
                Thread.sleep(10000L);
                pc.sendPackets(new S_Disconnect());
                CharacterStorage _charStorage = new MySqlCharacterStorage();
                _charStorage.alldeleteCharacter(pc.getAccountName(), pc.getName());
            }
            if (ConfigOther.Illusionistpc && pc.isDragonKnight()) {
                pc.sendPackets(new S_SystemMessage(" \\fT[目前尚未開放此職業進行遊戲"));
                pc.sendPackets(new S_SystemMessage(" \\fT請另選職業進行遊戲冒險"));
                pc.sendPackets(new S_SystemMessage(" \\fT詳細資訊職業開放查看官方公告資訊 。]"));
                pc.sendPackets(new S_SystemMessage(" \\fT[10秒後將進行刪除斷線刪除角色動作]"));
                pc.sendPackets(new S_Paralysis(5, true));
                Thread.sleep(10000L);
                pc.sendPackets(new S_Disconnect());
                CharacterStorage _charStorage = new MySqlCharacterStorage();
                _charStorage.alldeleteCharacter(pc.getAccountName(), pc.getName());
            }


        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     * 玩家上線提醒GM
     *
     * @param pc
     */
    private void GM_Remind(L1PcInstance pc, ClientExecutor client) {
        try {
            if (ConfigOther.WHO_ONLINE_MSG_ON) {
                Collection<L1PcInstance> allplayer = World.get().getAllPlayers();
                Iterator<L1PcInstance> iterator2 = allplayer.iterator();
                while (iterator2.hasNext()) {
                    L1PcInstance object = iterator2.next();
                    if (object instanceof L1PcInstance) {
                        L1PcInstance GM = object;
                        if (GM.getAccessLevel() != 100 && GM.getAccessLevel() != 200) {
                            continue;
                        }
                        String msg = "";
                        if (pc.isCrown()) {
                            msg = "王子";
                        } else if (pc.isKnight()) {
                            msg = "騎士";
                        } else if (pc.isElf()) {
                            msg = "妖精";
                        } else if (pc.isWizard()) {
                            msg = "法師";
                        } else if (pc.isDarkelf()) {
                            msg = "黑妖";
                        } else if (pc.isDragonKnight()) {
                            msg = "龍騎士";
                        } else if (pc.isIllusionist()) {
                            msg = "幻術士";
                        }
                        if (pc.isGm()) {
                            pc.setcheckgm(true);
                            GM.sendPackets(new S_ServerMessage("\\fY[管理員帳號]"));
                            GM.sendPackets(new S_ServerMessage(
                                    "\\fY[帳號:" + client.getAccountName() + "][名稱:" + pc.getName() + "]"));
                            GM.sendPackets(new S_ServerMessage("\\fY[職業:" + msg + "][IP:" + client.getIp() + "]登錄。"));
                            pc.setcheckgm(true);
                        } else {
                            GM.sendPackets(new S_ServerMessage(
                                    "\\fY[帳號:" + client.getAccountName() + "][名稱:" + pc.getName() + "]"));
                            GM.sendPackets(new S_ServerMessage("\\fY[職業:" + msg + "][IP:" + client.getIp() + "]登錄。"));
                        }
                    }
                }
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    private int CheckMail(L1PcInstance pc) {
        int count = 0;
        Connection con = null;
        PreparedStatement pstm1 = null;
        ResultSet rs = null;
        try {
            con = DatabaseFactory.get().getConnection();
            pstm1 = con.prepareStatement(" SELECT count(*) as cnt FROM character_mails where receiver =? AND read_status = 0");
            pstm1.setString(1, pc.getName());
            rs = pstm1.executeQuery();
            if (rs.next()) {
                count = rs.getInt("cnt");
            }
        } catch (SQLException e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        } finally {
            SQLUtil.close(rs);
            SQLUtil.close(pstm1);
            SQLUtil.close(con);
        }
        return count;
    }

    private void crown(L1PcInstance pc) {
        try {
            Map<Integer, L1Clan> map = L1CastleLocation.mapCastle();
            Iterator<Integer> iterator = map.keySet().iterator();
            while (iterator.hasNext()) {
                Integer key = iterator.next();
                L1Clan clan = map.get(key);
                if (clan != null) {
                    if (key.equals(2)) {
                        pc.sendPackets(new S_CastleMaster(8, clan.getLeaderId()));
                    } else {
                        pc.sendPackets(new S_CastleMaster(key, clan.getLeaderId()));
                    }
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void getFocus(L1PcInstance pc) {
        try {
            pc.set_showId(-1);
            World.get().addVisibleObject(pc);
            if (pc.getMeteLevel() > 0) {
                pc.resetMeteAbility();
            }
            pc.sendPackets(new S_PacketBox(132, pc.getEr()));
            pc.sendPackets(new S_PacketBoxIcon1(true, pc.get_dodge()));
            pc.sendPackets(new S_OwnCharStatus(pc));
            pc.sendPackets(new S_MapID(pc, pc.getMapId(), pc.getMap().isUnderwater()));
            pc.sendPackets(new S_OwnCharPack(pc));
            boolean isTimingmap = MapsTable.get().isTimingMap(pc.getMapId());
            if (isTimingmap) {
                int maxMapUsetime = MapsTable.get().getMapTime(pc.getMapId()) * 60;
                int usedtime = pc.getMapUseTime(pc.getMapId());
                int leftTime = maxMapUsetime - usedtime;
                MapTimerThread.put(pc, leftTime);
            } else if (MapTimerThread.TIMINGMAP.get(pc) != null) {
                MapTimerThread.TIMINGMAP.remove(pc);
            }
            ArrayList<L1PcInstance> otherPc = World.get().getVisiblePlayer(pc);
            if (otherPc.size() > 0) {
                for (L1PcInstance tg : otherPc) {
                    tg.sendPackets(new S_OtherCharPacks(pc));
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     * 其他狀態更新
     *
     * @param pc
     * @throws Exception
     */
    private void getCard(L1PcInstance pc) throws Exception {
        try {
            int i = 0;
            while (i <= ACardTable.get().ACardSize()) {
                ACard card = ACardTable.get().getCard(i);
                if (card != null && pc.getQuest().get_step(card.getQuestId()) != 0) {
                    pc.addStr(card.getAddStr());
                    pc.addDex(card.getAddDex());
                    pc.addCon(card.getAddCon());
                    pc.addInt(card.getAddInt());
                    pc.addWis(card.getAddWis());
                    pc.addCha(card.getAddCha());
                    pc.addAc(card.getAddAc());
                    pc.addMaxHp(card.getAddHp());
                    pc.addMaxMp(card.getAddMp());
                    pc.addHpr(card.getAddHpr());
                    pc.addMpr(card.getAddMpr());
                    pc.addDmgup(card.getAddDmg());
                    pc.addBowDmgup(card.getAddBowDmg());
                    pc.addHitup(card.getAddHit());
                    pc.addBowHitup(card.getAddBowHit());
                    pc.addDamageReductionByArmor(card.getAddDmgR());
                    pc.addMagicDmgReduction(card.getAddMagicDmgR());
                    pc.addSp(card.getAddSp());
                    pc.addMagicDmgModifier(card.getAddMagicHit());
                    pc.addMr(card.getAddMr());
                    pc.addFire(card.getAddFire());
                    pc.addWater(card.getAddWater());
                    pc.addWind(card.getAddWind());
                    pc.addEarth(card.getAddEarth());
                    pc.sendPackets(new S_SystemMessage(card.getMsg1()));
                }
                ++i;
            }
            i = 0;
            while (i <= CardSetTable.get().CardCardSize()) {
                CardPolySet cards = CardSetTable.get().getCard(i);
                if (cards != null && pc.getQuest().get_step(cards.getQuestId()) != 0) {
                    pc.addStr(cards.getAddStr());
                    pc.addDex(cards.getAddDex());
                    pc.addCon(cards.getAddCon());
                    pc.addInt(cards.getAddInt());
                    pc.addWis(cards.getAddWis());
                    pc.addCha(cards.getAddCha());
                    pc.addAc(cards.getAddAc());
                    pc.addMaxHp(cards.getAddHp());
                    pc.addMaxMp(cards.getAddMp());
                    pc.addHpr(cards.getAddHpr());
                    pc.addMpr(cards.getAddMpr());
                    pc.addDmgup(cards.getAddDmg());
                    pc.addBowDmgup(cards.getAddBowDmg());
                    pc.addHitup(cards.getAddHit());
                    pc.addBowHitup(cards.getAddBowHit());
                    pc.addDamageReductionByArmor(cards.getAddDmgR());
                    pc.addMagicDmgReduction(cards.getAddMagicDmgR());
                    pc.addSp(cards.getAddSp());
                    pc.addMagicDmgModifier(cards.getAddMagicHit());
                    pc.addMr(cards.getAddMr());
                    pc.addFire(cards.getAddFire());
                    pc.addWater(cards.getAddWater());
                    pc.addWind(cards.getAddWind());
                    pc.addEarth(cards.getAddEarth());
                }
                ++i;
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     * 其他狀態更新
     *
     * @param pc
     * @throws Exception
     */
    private void getUpdate(L1PcInstance pc) {
        pc.sendPackets(new S_Mail(pc, 0));
        pc.sendPackets(new S_Mail(pc, 1));
        pc.sendPackets(new S_Mail(pc, 2));
        pc.sendPackets(new S_SPMR(pc));
        pc.sendPackets(new S_Karma(pc));
        pc.sendPackets(new S_Weather(World.get().getWeather()));
    }

    private void marriage(L1PcInstance pc) {
        try {
            if (pc.getPartnerId() != 0) {
                L1PcInstance partner = (L1PcInstance) World.get().findObject(pc.getPartnerId());
                if (partner != null && partner.getPartnerId() != 0 && pc.getPartnerId() == partner.getId()
                        && partner.getPartnerId() == pc.getId()) {
                    pc.sendPackets(new S_ServerMessage(548));
                    partner.sendPackets(new S_ServerMessage(549));
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void getOther(L1PcInstance pc) throws Exception {
        try {
            pc.set_otherList(new L1PcOtherList(pc));
            pc.addDmgup(pc.get_other().get_Attack());
            pc.addBowDmgup(pc.get_other().get_BowAttack());
            pc.addHitup(pc.get_other().get_Hit());
            pc.addBowHitup(pc.get_other().get_BowHit());
            pc.addSp(pc.get_other().get_Sp());
            pc.addStr(pc.get_other().get_Str());
            pc.addDex(pc.get_other().get_Dex());
            pc.addInt(pc.get_other().get_Int());
            pc.addCon(pc.get_other().get_Con());
            pc.addCha(pc.get_other().get_Cha());
            pc.addWis(pc.get_other().get_Wis());
            pc.addMaxHp(pc.get_other().get_Hp());
            pc.addMaxMp(pc.get_other().get_Mp());
            pc.addMr(pc.get_other().get_Mr());
            pc.addother_ReductionDmg(pc.get_other().get_ReductionDmg());
            pc.addHpr(pc.get_other().get_Hpr());
            pc.addMpr(pc.get_other().get_Mpr());
            pc.add_hppotion(pc.get_other().get_hppotion());
            pc.add_exp(pc.get_other().get_exp());
            pc.addAc(pc.get_other().get_ac());
            pc.addWeightReduction(pc.get_other().get_weight());
            pc.addRegistStun(pc.get_other().get_regist_stun());
            pc.addRegistStone(pc.get_other().get_regist_stone());
            pc.addRegistSleep(pc.get_other().get_regist_sleep());
            pc.add_regist_freeze(pc.get_other().get_regist_freeze());
            pc.addRegistSustain(pc.get_other().get_regist_sustain());
            pc.addRegistBlind(pc.get_other().get_regist_blind());
            pc.addDmgup(pc.get_other2().get_Attack());
            pc.addBowDmgup(pc.get_other2().get_BowAttack());
            pc.addHitup(pc.get_other2().get_Hit());
            pc.addBowHitup(pc.get_other2().get_BowHit());
            pc.addSp(pc.get_other2().get_Sp());
            pc.addStr(pc.get_other2().get_Str());
            pc.addDex(pc.get_other2().get_Dex());
            pc.addInt(pc.get_other2().get_Int());
            pc.addCon(pc.get_other2().get_Con());
            pc.addCha(pc.get_other2().get_Cha());
            pc.addWis(pc.get_other2().get_Wis());
            pc.addMaxHp(pc.get_other2().get_Hp());
            pc.addMaxMp(pc.get_other2().get_Mp());
            pc.addMr(pc.get_other2().get_Mr());
            pc.addother_ReductionDmg(pc.get_other2().get_ReductionDmg());
            pc.addHpr(pc.get_other2().get_Hpr());
            pc.addMpr(pc.get_other2().get_Mpr());
            pc.add_hppotion(pc.get_other2().get_hppotion());
            pc.add_exp(pc.get_other2().get_exp());
            pc.addAc(pc.get_other2().get_ac());
            pc.addWeightReduction(pc.get_other2().get_weight());
            pc.addRegistStun(pc.get_other2().get_regist_stun());
            pc.addRegistStone(pc.get_other2().get_regist_stone());
            pc.addRegistSleep(pc.get_other2().get_regist_sleep());
            pc.add_regist_freeze(pc.get_other2().get_regist_freeze());
            pc.addRegistSustain(pc.get_other2().get_regist_sustain());
            pc.addRegistBlind(pc.get_other2().get_regist_blind());
            pc.add_pvp(pc.get_other2().get_pvp());
            pc.add_bowpvp(pc.get_other2().get_bowpvp());
            if (pc.get_other1().get_type46() != 0) {
                pc.setAttackSkillList(Integer.valueOf(pc.get_other1().get_type46()));
            }
            if (pc.get_other1().get_type47() != 0) {
                pc.setAttackSkillList(Integer.valueOf(pc.get_other1().get_type47()));
            }
            if (pc.get_other1().get_type48() != 0) {
                pc.setAttackSkillList(Integer.valueOf(pc.get_other1().get_type48()));
            }
            if (pc.get_other1().get_type49() != 0) {
                pc.setAttackSkillList(Integer.valueOf(pc.get_other1().get_type49()));
            }
            if (pc.get_other1().get_type50() != 0) {
                pc.setAttackSkillList(Integer.valueOf(pc.get_other1().get_type50()));
            }
            if (pc.get_other1().get_type24() != 0) {
                pc.setgo_guajitele(true);
            }
            if (pc.get_other1().get_type25() != 0) {
                pc.setgo_guajired(true);
            }
            if (pc.get_other1().get_type12() == 1) {
                pc.setIsAttackTeleport(true);
            }
            if (pc.get_other1().get_type13() == 1) {
                pc.setIsEnemyTeleport(true);
            }
            if (pc.get_other3().get_type2() != 0) {
                pc.setSave_Quest_Map1(pc.get_other3().get_type2());
            }
            if (pc.get_other3().get_type3() != 0) {
                pc.setSave_Quest_Map2(pc.get_other3().get_type3());
            }
            if (pc.get_other3().get_type4() != 0) {
                pc.setSave_Quest_Map3(pc.get_other3().get_type4());
            }
            if (pc.get_other3().get_type5() != 0) {
                pc.setSave_Quest_Map4(pc.get_other3().get_type5());
            }
            if (pc.get_other3().get_type6() != 0) {
                pc.setSave_Quest_Map5(pc.get_other3().get_type6());
            }
            OnlineGiftSet.add(pc);
            int time = pc.get_other().get_usemapTime();
            if (time > 0) {
                ServerUseMapTimer.put(pc, time);
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void war(L1PcInstance pc) {
        try {
            if (pc.getClanid() != 0) {
                L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                if (clan != null) {
                    if (pc.getClanid() == clan.getClanId()
                            && pc.getClanname().toLowerCase().equals(clan.getClanName().toLowerCase())) {
                        L1PcInstance[] clanMembers = clan.getOnlineClanMember();
                        L1PcInstance[] array;
                        int length = (array = clanMembers).length;
                        int i = 0;
                        while (i < length) {
                            L1PcInstance clanMember = array[i];
                            if (clanMember.getId() != pc.getId()) {
                                clanMember.sendPackets(new S_ServerMessage(843, pc.getName()));
                            }
                            ++i;
                        }
                        int clanMan = clan.getOnlineClanMember().length;
                        pc.sendPackets(new S_ServerMessage("\\fU線上血盟成員:" + clanMan));
                        if (clan.getAnnouncement() != null) {
                            pc.sendPackets(new S_SystemMessage("血盟公告:" + clan.getAnnouncement()));
                        }
                        clan.CheckClan_Exp20(null);
                        if (clan.isClanskill()) {
                            switch (pc.get_other().get_clanskill()) {
                                case 1: {
                                    pc.sendPackets(new S_ServerMessage(Npc_clan.SKILLINFO[0]));
                                    break;
                                }
                                case 2: {
                                    pc.sendPackets(new S_ServerMessage(Npc_clan.SKILLINFO[1]));
                                    break;
                                }
                                case 4: {
                                    pc.sendPackets(new S_ServerMessage(Npc_clan.SKILLINFO[2]));
                                    break;
                                }
                                case 8: {
                                    pc.sendPackets(new S_ServerMessage(Npc_clan.SKILLINFO[3]));
                                    break;
                                }
                            }
                        }
                        ClanSkillDBSet.add(pc);
                        L1EmblemIcon emblemIcon = ClanEmblemReading.get().get(clan.getClanId());
                        if (emblemIcon != null) {
                            pc.sendPackets(new S_Emblem(emblemIcon));
                        }
                        Iterator<L1War> iterator = WorldWar.get().getWarList().iterator();
                        while (iterator.hasNext()) {
                            L1War war = iterator.next();
                            boolean ret = war.checkClanInWar(pc.getClanname());
                            if (ret) {
                                String enemy_clan_name = war.getEnemyClanName(pc.getClanname());
                                if (enemy_clan_name != null) {
                                    pc.sendPackets(new S_War(8, pc.getClanname(), enemy_clan_name));
                                    break;
                                }
                                break;
                            }
                        }
                    }
                } else {
                    pc.setClanid(0);
                    pc.setClanname("");
                    pc.setClanRank(0);
                    pc.save();
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void backRestart(L1PcInstance pc) {
        try {
            if (pc.getMapId() >= 4001 && pc.getMapId() <= 4050) {
                pc.setX(33448);
                pc.setY(32791);
                pc.setMap((short) 4);
            }
            L1GetBackRestart gbr = GetBackRestartTable.get().getGetBackRestart(pc.getMapId());
            if (gbr != null) {
                pc.setX(gbr.getLocX());
                pc.setY(gbr.getLocY());
                pc.setMap(gbr.getMapId());
            }
            int castle_id = L1CastleLocation.getCastleIdByArea(pc);
            if (castle_id > 0 && ServerWarExecutor.get().isNowWar(castle_id)) {
                L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                if (clan != null) {
                    if (clan.getCastleId() != castle_id) {
                        int[] loc = new int[3];
                        loc = L1CastleLocation.getGetBackLoc(castle_id);
                        pc.setX(loc[0]);
                        pc.setY(loc[1]);
                        pc.setMap((short) loc[2]);
                    }
                } else {
                    int[] loc = new int[3];
                    loc = L1CastleLocation.getGetBackLoc(castle_id);
                    pc.setX(loc[0]);
                    pc.setY(loc[1]);
                    pc.setMap((short) loc[2]);
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void bookmarks(L1PcInstance pc) {
        try {
            ArrayList<L1BookMark> bookList = CharBookReading.get().getBookMarks(pc);
            if (bookList != null && !bookList.isEmpty()) {
                pc.sendPackets(new S_BookMarkList(bookList));
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
    }

    private void skills(L1PcInstance pc) {
        try {
            ArrayList<L1UserSkillTmp> skillList = CharSkillReading.get().skills(pc.getId());
            int[] skills = new int[28];
            if (skillList != null && !skillList.isEmpty()) {
                for (L1UserSkillTmp userSkillTmp : skillList) {
                    L1Skills skill = SkillsTable.get().getTemplate(userSkillTmp.get_skill_id());
                    int[] array = skills;
                    int n = skill.getSkillLevel() - 1;
                    array[n] += skill.getId();
                }
                pc.sendPackets(new S_AddSkill(pc, skills));
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void serchSummon(L1PcInstance pc) {
        try {
            Collection<L1SummonInstance> summons = WorldSummons.get().all();
            if (!summons.isEmpty()) {
                Iterator<L1SummonInstance> iterator = summons.iterator();
                while (iterator.hasNext()) {
                    L1SummonInstance summon = iterator.next();
                    if (summon.getMaster().getId() == pc.getId()) {
                        summon.setMaster(pc);
                        pc.addPet(summon);
                        S_NewMaster packet = new S_NewMaster(pc.getName(), summon);
                        for (L1PcInstance visiblePc : World.get().getVisiblePlayer(summon)) {
                            visiblePc.sendPackets(packet);
                        }
                    }
                }
            }
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void buff(L1PcInstance pc) {
        try {
            CharBuffReading.get().buff(pc);
            pc.sendPackets(new S_PacketBoxActiveSpells(pc));
            CharMapTimeReading.get().getTime(pc);
        } catch (Exception e) {
            C_LoginToServer._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void ClanMatching(L1PcInstance pc) {
        L1ClanMatching cml = L1ClanMatching.getInstance();
        if (pc.getClanid() == 0) {
            if (!pc.isCrown()) {
                if (!cml.getMatchingList().isEmpty()) {
                    pc.sendPackets(new S_ServerMessage(3245));
                }
            } else {
                pc.sendPackets(new S_ServerMessage(3247));
            }
        } else {
            switch (pc.getClanRank()) {
                case 3:
                case 4:
                case 6:
                case 9:
                case 10: {
                    if (!pc.getInviteList().isEmpty()) {
                        pc.sendPackets(new S_ServerMessage(3246));
                        break;
                    }
                    break;
                }
            }
            pc.sendPackets(new S_PacketBox(173, pc.getClan().getEmblemStatus()));
        }
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
