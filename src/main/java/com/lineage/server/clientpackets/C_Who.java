package com.lineage.server.clientpackets;

import com.lineage.config.ConfigAlt;
import com.lineage.config.ConfigRate;
import com.lineage.config.ConfigWho;
import com.lineage.echo.ClientExecutor;
import com.lineage.server.model.Instance.L1DeInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.pr_type_name;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.serverpackets.S_WhoCharinfo;
import com.lineage.server.timecontroller.server.ServerRestartTimer;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldNpc;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;

public class C_Who extends ClientBasePacket {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(C_Who.class);
    }

    public static int deCount() {
        int count = 0;
        Collection<L1NpcInstance> allObj = WorldNpc.get().all();
        if (allObj.isEmpty()) {
            return count;
        }
        for (L1NpcInstance obj : allObj) {
            if (obj instanceof L1DeInstance) {
                ++count;
            }
        }
        return count;
    }

    @Override
    public void start(byte[] decrypt, ClientExecutor client) {
        try {
            read(decrypt);
            String s = readS();
            L1Character find = World.get().getPlayer(s);
            L1PcInstance pc = client.getActiveChar();
            if (find == null) {
                find = getDe(s);
            }
            if (find != null) {
                S_WhoCharinfo whoChar = new S_WhoCharinfo(find, client);
                pc.sendPackets(whoChar);
            } else {
                int de = deCount();
                int count = World.get().getAllPlayers().size();
                String amount = String.valueOf((int) (count * ConfigAlt.ALT_WHO_COUNT) + de);
                if (pc.isGm()) {
                    pc.sendPackets(new S_ServerMessage("\\f=(GM顯示)目前真實人數: " + amount));
                    pc.sendPackets(new S_ServerMessage("\\f=(GM顯示)目前假人人數: " + de));
                }
                pc.sendPackets(new S_ServerMessage("\\f=目前人數: " + amount));
                if (ConfigAlt.ALT_WHO_COMMANDX) {
                    String nowDate = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
                    switch (ConfigAlt.ALT_WHO_TYPE) {
                        case 0: {
                            pc.sendPackets(
                                    new S_ServerMessage("\\fV啟動時間: " + String.valueOf(ServerRestartTimer.get_startTime())));
                            if (ConfigWho.check_who_exp) {
                                pc.sendPackets(
                                        new S_ServerMessage("\\fV經驗倍率: " + ConfigWho.whoexp * ConfigWho.RATE_XP_WHO));
                            } else {
                                pc.sendPackets(
                                        new S_ServerMessage("\\fV經驗倍率: " + ConfigRate.RATE_XP * ConfigWho.RATE_XP_WHO));
                            }
                            pc.sendPackets(new S_ServerMessage("\\fV金錢倍率: " + ConfigRate.RATE_DROP_ADENA));
                            if (ConfigWho.check_who_weapon) {
                                pc.sendPackets(new S_ServerMessage("\\fV衝武倍率: " + ConfigWho.whoweapon));
                            } else {
                                pc.sendPackets(new S_ServerMessage("\\fV衝武倍率: " + ConfigRate.ENCHANT_CHANCE_WEAPON));
                            }
                            if (ConfigWho.check_who_armor) {
                                pc.sendPackets(new S_ServerMessage("\\fV衝防倍率: " + ConfigWho.whoarmor));
                            } else {
                                pc.sendPackets(new S_ServerMessage("\\fV衝防倍率: " + ConfigRate.ENCHANT_CHANCE_ARMOR));
                            }
                            pc.sendPackets(new S_ServerMessage("\\fV現實時間: " + nowDate));
                            pc.sendPackets(new S_ServerMessage("\\fV重啟時間: " + ServerRestartTimer.get_restartTime()));
                            if (pc.hasSkillEffect(7596)) {
                                pc.sendPackets(new S_SystemMessage("請稍候在進行操作。"));
                                return;
                            }
                            pc.setSkillEffect(7596, 60000);
                            if (pc.getnewaicount_2() > 0) {
                                pc.sendPackets(new S_ServerMessage("\\fU您的驗證答案數字請輸入: " + pc.getnewaicount_2()));
                                break;
                            }
                            break;
                        }
                        case 1: {
                            String[] info = new String[14];
                            info[1] = String.valueOf(amount);
                            if (ConfigWho.check_who_exp) {
                                info[2] = String.valueOf(ConfigWho.whoexp * ConfigWho.RATE_XP_WHO);
                            } else {
                                info[2] = String.valueOf(ConfigRate.RATE_XP * ConfigWho.RATE_XP_WHO);
                            }
                            info[3] = String.valueOf(ConfigRate.RATE_DROP_ITEMS);
                            info[4] = String.valueOf(ConfigRate.RATE_DROP_ADENA);
                            if (ConfigWho.check_who_weapon) {
                                info[5] = String.valueOf(ConfigWho.whoweapon);
                            } else {
                                info[5] = String.valueOf(ConfigRate.ENCHANT_CHANCE_WEAPON);
                            }
                            if (ConfigWho.check_who_armor) {
                                info[6] = String.valueOf(ConfigWho.whoarmor);
                            } else {
                                info[6] = String.valueOf(ConfigRate.ENCHANT_CHANCE_ARMOR);
                            }
                            info[7] = String.valueOf(ConfigAlt.POWER);
                            info[8] = String.valueOf(ConfigAlt.POWERMEDICINE);
                            info[9] = String.valueOf(ConfigAlt.MEDICINE);
                            info[10] = String.valueOf(nowDate);
                            info[11] = String.valueOf(ServerRestartTimer.get_restartTime());
                            info[12] = String.valueOf(pc.getElixirStats());
                            info[13] = String.valueOf(pc.getguaji_count());
                            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_who", info));
                            if (pc.getPrestigeLv() != 0) {
                                pc.sendPackets(new S_ServerMessage("目前" + pr_type_name._1 + ":" + pc.getPrestige(), 11));
                            }
                            if (pc.hasSkillEffect(7596)) {
                                pc.sendPackets(new S_SystemMessage("請稍候在進行操作。"));
                                return;
                            }
                            pc.setSkillEffect(7596, 60000);
                            if (pc.getnewaicount_2() > 0) {
                                pc.sendPackets(new S_ServerMessage("\\fU您的驗證答案數字請輸入: " + pc.getnewaicount_2()));
                            }
                            if (pc.getguaji_count() > 0) {
                                pc.sendPackets(new S_ServerMessage("\\fU目前活力值: " + pc.getguaji_count()));
                                break;
                            }
                            break;
                        }
                    }
                }
            }
        } catch (Exception ex) {
        } finally {
            over();
        }
    }

    private L1DeInstance getDe(String s) {
        Collection<L1NpcInstance> allObj = WorldNpc.get().all();
        if (allObj.isEmpty()) {
            return null;
        }
        Iterator<L1NpcInstance> iterator = allObj.iterator();
        while (iterator.hasNext()) {
            L1NpcInstance obj = iterator.next();
            if (obj instanceof L1DeInstance) {
                L1DeInstance de = (L1DeInstance) obj;
                if (de.getNameId().equalsIgnoreCase(s)) {
                    return de;
                }
                continue;
            }
        }
        return null;
    }

    @Override
    public String getType() {
        return getClass().getSimpleName();
    }
}
