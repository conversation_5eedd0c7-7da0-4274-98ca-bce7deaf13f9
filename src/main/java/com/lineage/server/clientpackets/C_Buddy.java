package com.lineage.server.clientpackets;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_Buddy;
import com.lineage.echo.ClientExecutor;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C_Buddy extends ClientBasePacket {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(C_Buddy.class);
	}

	@Override
	public void start(final byte[] decrypt, final ClientExecutor client) {
		try {
			final L1PcInstance pc = client.getActiveChar();
			pc.sendPackets(new S_Buddy(pc.getId()));
		} catch (Exception ex) {
		} finally {
			this.over();
		}
	}

	@Override
	public String getType() {
		return this.getClass().getSimpleName();
	}
}
