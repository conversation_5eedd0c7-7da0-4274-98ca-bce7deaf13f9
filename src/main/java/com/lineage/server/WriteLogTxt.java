package com.lineage.server;

import java.util.Date;
import java.io.IOException;
import java.util.logging.Level;
import java.sql.Timestamp;
import java.io.FileOutputStream;
import java.io.File;
import java.util.Calendar;
import java.text.SimpleDateFormat;
import java.util.logging.Logger;

public class WriteLogTxt {
	private static Logger _log;

	static {
		_log = Logger.getLogger(WriteLogTxt.class.getName());
	}

	public static void Recording(final String name, final String info) {
		try {
			final SimpleDateFormat sdfmt = new SimpleDateFormat("yyyy-MM-dd");
			final Date d = Calendar.getInstance().getTime();
			final String date = " " + sdfmt.format(d);
			final String path = "玩家紀錄/萬物回收/時間" + date;
			final File file = new File(path);
			if (!file.exists()) {
				file.mkdir();
			}
			final FileOutputStream fos = new FileOutputStream(String.valueOf(path) + "/" + name + date + ".txt", true);
			fos.write((String.valueOf(info) + " 時間：" + new Timestamp(System.currentTimeMillis()) + "\r\n").getBytes());
			fos.close();
		} catch (IOException e) {
			WriteLogTxt._log.log(Level.SEVERE, e.getLocalizedMessage(), e);
		}
	}
}
