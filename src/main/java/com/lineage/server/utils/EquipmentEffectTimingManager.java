package com.lineage.server.utils;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.william.ArmorSkillSound;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * 裝備特效觸發時機管理器
 * 統一管理裝備穿脫時的特效觸發邏輯
 */
public class EquipmentEffectTimingManager {
    
    private static final Logger _log = Logger.getLogger(EquipmentEffectTimingManager.class.getName());
    
    // 玩家特效觸發時間戳記錄
    private static final ConcurrentHashMap<Integer, Long> _playerEffectTimestamps = new ConcurrentHashMap<>();
    
    // 配置參數
    private static boolean ENABLED = true;
    private static boolean TRIGGER_ON_EQUIP = true;
    private static boolean TRIGGER_ON_UNEQUIP = true;
    private static boolean TRIGGER_ON_SWITCH = true;
    private static boolean TRIGGER_ON_SET_CHANGE = true;
    private static long CHECK_DELAY = 100;
    private static long EFFECT_COOLDOWN = 2000;
    private static boolean DEBUG_MODE = false;
    private static boolean LOG_ENABLED = true;
    private static boolean PERFORMANCE_MONITORING = true;
    
    // 效能統計
    private static long totalEffectsTriggered = 0;
    private static long totalEquipEffects = 0;
    private static long totalUnequipEffects = 0;
    private static long totalSwitchEffects = 0;
    private static long totalSetChangeEffects = 0;
    
    /**
     * 初始化管理器
     */
    public static void initialize() {
        try {
            loadConfiguration();
            _log.info("裝備特效觸發時機管理器初始化完成");
        } catch (Exception e) {
            _log.warning("裝備特效觸發時機管理器初始化失敗: " + e.getMessage());
        }
    }
    
    /**
     * 載入配置
     */
    private static void loadConfiguration() {
        try {
            // 這裡可以載入配置檔案
            // 暫時使用預設值
            ENABLED = true;
            TRIGGER_ON_EQUIP = true;
            TRIGGER_ON_UNEQUIP = true;
            TRIGGER_ON_SWITCH = true;
            TRIGGER_ON_SET_CHANGE = true;
            CHECK_DELAY = 100;
            EFFECT_COOLDOWN = 2000;
            DEBUG_MODE = false;
            LOG_ENABLED = true;
            PERFORMANCE_MONITORING = true;
            
        } catch (Exception e) {
            _log.warning("載入配置失敗: " + e.getMessage());
        }
    }
    
    /**
     * 裝備穿上時觸發特效
     * @param pc 玩家
     * @param item 裝備物品
     */
    public static void onEquip(L1PcInstance pc, L1ItemInstance item) {
        if (!ENABLED || !TRIGGER_ON_EQUIP || pc == null || item == null) {
            return;
        }
        
        try {
            if (isEffectOnCooldown(pc)) {
                return;
            }
            
            // 延遲觸發特效
            scheduleEffectTrigger(pc, "裝備穿上", () -> {
                try {
                    ArmorSkillSound.forArmorSkillSound(pc);
                    totalEquipEffects++;
                    totalEffectsTriggered++;
                    
                    if (DEBUG_MODE) {
                        pc.sendPackets(new com.lineage.server.serverpackets.S_SystemMessage(
                            "裝備特效觸發: " + item.getName() + " (穿上)"
                        ));
                    }
                    
                    if (LOG_ENABLED) {
                        _log.info("玩家 " + pc.getName() + " 裝備穿上觸發特效: " + item.getName());
                    }
                    
                } catch (Exception e) {
                    _log.warning("裝備穿上觸發特效失敗: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            _log.warning("裝備穿上特效處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 裝備脫下時觸發特效
     * @param pc 玩家
     * @param item 裝備物品
     */
    public static void onUnequip(L1PcInstance pc, L1ItemInstance item) {
        if (!ENABLED || !TRIGGER_ON_UNEQUIP || pc == null || item == null) {
            return;
        }
        
        try {
            if (isEffectOnCooldown(pc)) {
                return;
            }
            
            // 延遲觸發特效
            scheduleEffectTrigger(pc, "裝備脫下", () -> {
                try {
                    ArmorSkillSound.forArmorSkillSound(pc);
                    totalUnequipEffects++;
                    totalEffectsTriggered++;
                    
                    if (DEBUG_MODE) {
                        pc.sendPackets(new com.lineage.server.serverpackets.S_SystemMessage(
                            "裝備特效觸發: " + item.getName() + " (脫下)"
                        ));
                    }
                    
                    if (LOG_ENABLED) {
                        _log.info("玩家 " + pc.getName() + " 裝備脫下觸發特效: " + item.getName());
                    }
                    
                } catch (Exception e) {
                    _log.warning("裝備脫下觸發特效失敗: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            _log.warning("裝備脫下特效處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 裝備切換時觸發特效
     * @param pc 玩家
     * @param oldItem 舊裝備
     * @param newItem 新裝備
     */
    public static void onSwitch(L1PcInstance pc, L1ItemInstance oldItem, L1ItemInstance newItem) {
        if (!ENABLED || !TRIGGER_ON_SWITCH || pc == null) {
            return;
        }
        
        try {
            if (isEffectOnCooldown(pc)) {
                return;
            }
            
            // 延遲觸發特效
            scheduleEffectTrigger(pc, "裝備切換", () -> {
                try {
                    ArmorSkillSound.forArmorSkillSound(pc);
                    totalSwitchEffects++;
                    totalEffectsTriggered++;
                    
                    if (DEBUG_MODE) {
                        String message = "裝備特效觸發: ";
                        if (oldItem != null) {
                            message += oldItem.getName() + " -> ";
                        }
                        if (newItem != null) {
                            message += newItem.getName();
                        }
                        pc.sendPackets(new com.lineage.server.serverpackets.S_SystemMessage(message));
                    }
                    
                    if (LOG_ENABLED) {
                        _log.info("玩家 " + pc.getName() + " 裝備切換觸發特效");
                    }
                    
                } catch (Exception e) {
                    _log.warning("裝備切換觸發特效失敗: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            _log.warning("裝備切換特效處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 裝備套裝變更時觸發特效
     * @param pc 玩家
     */
    public static void onSetChange(L1PcInstance pc) {
        if (!ENABLED || !TRIGGER_ON_SET_CHANGE || pc == null) {
            return;
        }
        
        try {
            if (isEffectOnCooldown(pc)) {
                return;
            }
            
            // 延遲觸發特效
            scheduleEffectTrigger(pc, "套裝變更", () -> {
                try {
                    ArmorSkillSound.forArmorSkillSound(pc);
                    totalSetChangeEffects++;
                    totalEffectsTriggered++;
                    
                    if (DEBUG_MODE) {
                        pc.sendPackets(new com.lineage.server.serverpackets.S_SystemMessage("裝備特效觸發: 套裝變更"));
                    }
                    
                    if (LOG_ENABLED) {
                        _log.info("玩家 " + pc.getName() + " 套裝變更觸發特效");
                    }
                    
                } catch (Exception e) {
                    _log.warning("套裝變更觸發特效失敗: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            _log.warning("套裝變更特效處理失敗: " + e.getMessage());
        }
    }
    
    /**
     * 檢查特效是否在冷卻中
     */
    private static boolean isEffectOnCooldown(L1PcInstance pc) {
        Long lastEffectTime = _playerEffectTimestamps.get(pc.getId());
        if (lastEffectTime == null) {
            return false;
        }
        
        return (System.currentTimeMillis() - lastEffectTime) < EFFECT_COOLDOWN;
    }
    
    /**
     * 排程特效觸發
     */
    private static void scheduleEffectTrigger(L1PcInstance pc, String action, Runnable effectTask) {
        try {
            // 更新玩家特效時間戳
            _playerEffectTimestamps.put(pc.getId(), System.currentTimeMillis());
            
            // 延遲執行特效任務
            com.lineage.server.thread.GeneralThreadPool.get().execute(() -> {
                try {
                    Thread.sleep(CHECK_DELAY);
                    effectTask.run();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    _log.warning("執行特效任務失敗: " + e.getMessage());
                }
            });
            
        } catch (Exception e) {
            _log.warning("排程特效觸發失敗: " + e.getMessage());
        }
    }
    
    /**
     * 獲取統計資訊
     * @return 統計資訊字串
     */
    public static String getStats() {
        try {
            return String.format(
                "裝備特效統計 - 總觸發: %d, 裝備穿上: %d, 裝備脫下: %d, 裝備切換: %d, 套裝變更: %d",
                totalEffectsTriggered, totalEquipEffects, totalUnequipEffects, 
                totalSwitchEffects, totalSetChangeEffects
            );
        } catch (Exception e) {
            _log.warning("獲取統計資訊失敗: " + e.getMessage());
            return "統計資訊獲取失敗";
        }
    }
    
    /**
     * 重置統計資訊
     */
    public static void resetStats() {
        totalEffectsTriggered = 0;
        totalEquipEffects = 0;
        totalUnequipEffects = 0;
        totalSwitchEffects = 0;
        totalSetChangeEffects = 0;
        _log.info("裝備特效統計資訊已重置");
    }
    
    /**
     * 重新載入配置
     */
    public static void reloadConfiguration() {
        try {
            loadConfiguration();
            _log.info("裝備特效觸發時機配置重新載入完成");
        } catch (Exception e) {
            _log.warning("重新載入配置失敗: " + e.getMessage());
        }
    }
} 