package com.lineage.server.utils;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.L1AttackPc;
import com.lineage.server.serverpackets.S_SystemMessage;
import java.util.logging.Logger;
import java.util.logging.Level;

/**
 * 簡化物理攻擊修復工具
 * 專注於核心問題的快速修復
 */
public class SimpleAttackFixer {
    
    private static final Logger _log = Logger.getLogger(SimpleAttackFixer.class.getName());
    
    /**
     * 快速檢測物理攻擊問題
     */
    public static String quickCheck(L1PcInstance pc, L1Character target) {
        StringBuilder result = new StringBuilder();
        
        try {
            result.append("=== 物理攻擊快速檢測 ===\n");
            
            // 1. 基本狀態檢查
            if (pc.isDead()) {
                result.append("❌ 玩家已死亡\n");
                return result.toString();
            }
            
            if (pc.isGhost()) {
                result.append("❌ 玩家為幽靈狀態\n");
                return result.toString();
            }
            
            if (pc.isTeleport()) {
                result.append("❌ 玩家正在傳送\n");
                return result.toString();
            }
            
            if (pc.isParalyzedX()) {
                result.append("❌ 玩家被麻痺\n");
                return result.toString();
            }
            
            if (pc.hasSkillEffect(8007)) {
                result.append("❌ 玩家有特殊技能效果\n");
                return result.toString();
            }
            
            if (pc.isInvisble() || pc.isInvisDelay()) {
                result.append("❌ 玩家處於隱身狀態\n");
                return result.toString();
            }
            
            // 2. 目標檢查
            if (target == null) {
                result.append("❌ 目標為空\n");
                return result.toString();
            }
            
            if (target.isDead()) {
                result.append("❌ 目標已死亡\n");
                return result.toString();
            }
            
            // 3. 距離檢查
            double distance = pc.getLocation().getLineDistance(target.getLocation());
            result.append("距離: ").append(String.format("%.2f", distance)).append("\n");
            
            // 4. 武器檢查
            if (pc.getWeapon() != null) {
                result.append("武器: ").append(pc.getWeapon().getName()).append("\n");
                result.append("武器範圍: ").append(pc.getWeapon().getItem().getRange()).append("\n");
            } else {
                result.append("武器: 空手\n");
            }
            
            result.append("✅ 基本檢查通過\n");
            
        } catch (Exception e) {
            result.append("❌ 檢測失敗: ").append(e.getMessage()).append("\n");
            _log.log(Level.WARNING, "物理攻擊快速檢測失敗", e);
        }
        
        return result.toString();
    }
    
    /**
     * 快速修復物理攻擊問題
     */
    public static String quickFix(L1PcInstance pc, L1Character target) {
        StringBuilder result = new StringBuilder();
        
        try {
            result.append("=== 物理攻擊快速修復 ===\n");
            
            // 1. 清除影響攻擊的狀態
            if (pc.hasSkillEffect(8007)) {
                pc.killSkillEffectTimer(8007);
                result.append("✅ 清除特殊技能效果\n");
            }
            
            if (pc.isParalyzedX()) {
                pc.setParalyzed(false);
                result.append("✅ 解除麻痺狀態\n");
            }
            
            if (pc.isInvisble() || pc.isInvisDelay()) {
                pc.delInvis();
                result.append("✅ 解除隱身狀態\n");
            }
            
            // 2. 重置攻擊方向
            pc.setHeading(pc.targetDirection(target.getX(), target.getY()));
            result.append("✅ 重置攻擊方向\n");
            
            // 3. 清除其他影響攻擊的技能
            pc.killSkillEffectTimer(32);
            result.append("✅ 清除其他技能效果\n");
            
            // 4. 測試攻擊
            result.append("\n測試攻擊:\n");
            L1AttackPc attack = new L1AttackPc(pc, target);
            
            boolean isHit = attack.calcHit();
            result.append("命中: ").append(isHit).append("\n");
            
            int damage = attack.calcDamage();
            result.append("傷害: ").append(damage).append("\n");
            
            attack.action();
            result.append("✅ 攻擊動作執行\n");
            
            attack.commit();
            result.append("✅ 攻擊完成\n");
            
        } catch (Exception e) {
            result.append("❌ 修復失敗: ").append(e.getMessage()).append("\n");
            _log.log(Level.WARNING, "物理攻擊快速修復失敗", e);
        }
        
        return result.toString();
    }
    
    /**
     * 檢查攻擊封包問題
     */
    public static String checkAttackPacket(L1PcInstance pc, L1Character target) {
        StringBuilder result = new StringBuilder();
        
        try {
            result.append("=== 攻擊封包檢查 ===\n");
            
            // 檢查攻擊速度
            int attackSpeed = pc.speed_Attack().getRightInterval(com.lineage.server.clientpackets.AcceleratorChecker.ACT_TYPE.ATTACK);
            result.append("攻擊速度: ").append(attackSpeed).append("ms\n");
            
            // 檢查重量
            int weight = pc.getInventory().getWeight240();
            result.append("重量: ").append(weight).append("/240\n");
            
            if (weight >= 197) {
                result.append("❌ 重量過重\n");
            } else {
                result.append("✅ 重量正常\n");
            }
            
            // 檢查地圖
            result.append("玩家地圖: ").append(pc.getMapId()).append("\n");
            result.append("目標地圖: ").append(target.getMapId()).append("\n");
            
            if (pc.getMapId() != target.getMapId()) {
                result.append("❌ 地圖不同\n");
            } else {
                result.append("✅ 地圖相同\n");
            }
            
        } catch (Exception e) {
            result.append("❌ 封包檢查失敗: ").append(e.getMessage()).append("\n");
            _log.log(Level.WARNING, "攻擊封包檢查失敗", e);
        }
        
        return result.toString();
    }
} 