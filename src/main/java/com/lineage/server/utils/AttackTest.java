package com.lineage.server.utils;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.world.World;

/**
 * Attack Test Utility Class
 * Used for testing attack-related functionality
 */
public class AttackTest {
    
    /**
     * Test player attack functionality
     * @param pc Player
     * @return Test result
     */
    public static String testPlayerAttack(L1PcInstance pc) {
        if (pc == null) {
            return "Player is null";
        }
        
        StringBuilder result = new StringBuilder();
        result.append("=== Attack Function Test ===\n");
        
        // Basic attack condition check
        result.append("1. Basic Attack Condition Check:\n");
        result.append("   Alive: ").append(!pc.isDead()).append("\n");
        result.append("   Not Ghost: ").append(!pc.isGhost()).append("\n");
        result.append("   Not Teleporting: ").append(!pc.isTeleport()).append("\n");
        result.append("   Not Invisible: ").append(!pc.isInvisble()).append("\n");
        result.append("   Not Paralyzed: ").append(!pc.isParalyzedX()).append("\n");
        result.append("   Weight Normal: ").append(pc.getInventory().getWeight240() < 197).append("\n");
        
        // Equipment check
        result.append("\n2. Equipment Check:\n");
        if (pc.getWeapon() != null) {
            result.append("   Weapon: ").append(pc.getWeapon().getName()).append("\n");
            result.append("   Weapon Type: ").append(pc.getWeapon().getItem().getType()).append("\n");
            result.append("   Weapon Enchant: +").append(pc.getWeapon().getEnchantLevel()).append("\n");
        } else {
            result.append("   Weapon: None\n");
        }
        
        // Attribute check
        result.append("\n3. Combat Attribute Check:\n");
        result.append("   Strength: ").append(pc.getStr()).append("\n");
        result.append("   Dexterity: ").append(pc.getDex()).append("\n");
        result.append("   Hit: ").append(pc.getHitup()).append("\n");
        result.append("   Damage: ").append(pc.getDmgup()).append("\n");
        
        // Attack feasibility check
        result.append("\n4. Attack Feasibility:\n");
        boolean canAttack = canPlayerAttack(pc);
        result.append("   Can Attack: ").append(canAttack).append("\n");
        
        return result.toString();
    }
    
    /**
     * Test target attack
     * @param pc Player
     * @param targetId Target ID
     * @return Test result
     */
    public static String testTargetAttack(L1PcInstance pc, int targetId) {
        if (pc == null) {
            return "Player is null";
        }
        
        StringBuilder result = new StringBuilder();
        result.append("=== Target Attack Test ===\n");
        
        // Find target
        com.lineage.server.model.L1Object targetObj = World.get().findObject(targetId);
        if (targetObj == null) {
            result.append("Target not found (ID: ").append(targetId).append(")\n");
            return result.toString();
        }
        
        if (!(targetObj instanceof L1Character)) {
            result.append("Target is not attackable (ID: ").append(targetId).append(")\n");
            return result.toString();
        }
        
        L1Character target = (L1Character) targetObj;
        
        result.append("Target: ").append(target.getName()).append(" (ID: ").append(targetId).append(")\n");
        result.append("Target Type: ").append(target.getClass().getSimpleName()).append("\n");
        result.append("Target Alive: ").append(!target.isDead()).append("\n");
        result.append("Target Map: ").append(target.getMapId()).append("\n");
        result.append("Player Map: ").append(pc.getMapId()).append("\n");
        
        // Distance check
        double distance = pc.getLocation().getLineDistance(target.getLocation());
        result.append("Distance: ").append(String.format("%.2f", distance)).append("\n");
        
        // Attack feasibility check
        boolean canAttackTarget = canAttackTarget(pc, target);
        result.append("Can Attack Target: ").append(canAttackTarget).append("\n");
        
        return result.toString();
    }
    
    /**
     * Send test message to player
     * @param pc Player
     * @param message Message
     */
    public static void sendTestMessage(L1PcInstance pc, String message) {
        if (pc != null && pc.getNetConnection() != null) {
            try {
                pc.sendPackets(new S_SystemMessage("\\fG[Test] " + message));
            } catch (Exception e) {
                System.err.println("Failed to send test message: " + e.getMessage());
            }
        }
    }
    
    /**
     * Run complete attack test
     * @param pc Player
     * @param targetId Target ID
     */
    public static void runFullAttackTest(L1PcInstance pc, int targetId) {
        if (pc == null) {
            return;
        }
        
        sendTestMessage(pc, "Starting attack function test...");
        
        // Player attack test
        String playerTest = testPlayerAttack(pc);
        sendTestMessage(pc, "Player attack test completed");
        
        // Target attack test
        String targetTest = testTargetAttack(pc, targetId);
        sendTestMessage(pc, "Target attack test completed");
        
        // Output complete result
        System.out.println("=== Complete Attack Test Result ===");
        System.out.println(playerTest);
        System.out.println(targetTest);
    }
    
    /**
     * Check if player can attack
     * @param pc Player
     * @return Can attack
     */
    public static boolean canPlayerAttack(L1PcInstance pc) {
        if (pc == null) {
            return false;
        }
        
        // Basic status check
        if (pc.isDead() || pc.isGhost() || pc.isTeleport() || 
            pc.isInvisble() || pc.isInvisDelay() || pc.isParalyzedX() || 
            pc.isPrivateShop()) {
            return false;
        }
        
        // Weight check
        if (pc.getInventory().getWeight240() >= 197) {
            return false;
        }
        
        // Skill effect check
        if (pc.hasSkillEffect(8007)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if can attack target
     * @param attacker Attacker
     * @param target Target
     * @return Can attack target
     */
    public static boolean canAttackTarget(L1PcInstance attacker, L1Character target) {
        if (attacker == null || target == null) {
            return false;
        }
        
        // Check if attacker can attack
        if (!canPlayerAttack(attacker)) {
            return false;
        }
        
        // Check target conditions
        if (target.isDead()) {
            return false;
        }
        
        if (target.getMapId() != attacker.getMapId()) {
            return false;
        }
        
        double distance = attacker.getLocation().getLineDistance(target.getLocation());
        if (distance > 20.0) {
            return false;
        }
        
        return true;
    }
} 