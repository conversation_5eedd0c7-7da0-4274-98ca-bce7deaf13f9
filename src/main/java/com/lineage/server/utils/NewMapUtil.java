package com.lineage.server.utils;

import java.io.IOException;
import java.nio.channels.FileChannel;
import java.io.FileInputStream;
import java.nio.ByteBuffer;
import java.io.FileFilter;
import java.io.File;
import com.lineage.server.templates.L1NewMap;
import java.util.ArrayList;
import java.util.concurrent.ConcurrentHashMap;

public class NewMapUtil {
	private static ConcurrentHashMap<Integer, ArrayList<L1NewMap>> M;

	static {
		M = new ConcurrentHashMap();
	}

	public static void load(final String path) throws IOException {
		final File f = new File(path);
		if (!f.isDirectory() || !f.exists()) {
			throw new NullPointerException("錯誤的地圖路徑或地圖不存在！");
		}
		FileChannel fc = null;
		final File[] listFiles;
		final int length = (listFiles = f.listFiles(new FileFilter() {
			@Override
			public final boolean accept(final File pathname) {
				return pathname.isDirectory();
			}
		})).length;
		int i = 0;
		while (i < length) {
			final File map = listFiles[i];
			int mapId = 0;
			try {
				mapId = Integer.parseInt(map.getName());
			} catch (NumberFormatException e) {
			}
			final ArrayList<L1NewMap> maps = new ArrayList();
			NewMapUtil.M.put(Integer.valueOf(mapId), maps);
			final File[] listFiles2;
			final int length2 = (listFiles2 = map.listFiles(new FileFilter() {
				@Override
				public final boolean accept(final File pathname) {
					return pathname.isFile() && pathname.getName().toLowerCase().endsWith(".bin");
				}
			})).length;
			int j = 0;
			while (j < length2) {
				final File fileMap = listFiles2[j];
				final ByteBuffer buf = ByteBuffer.allocate(4096);
				fc = new FileInputStream(fileMap).getChannel();
				fc.read(buf);
				fc.close();
				final int x = Integer.parseInt(fileMap.getName().substring(0, 4), 16) & 0xFFFF;
				final int y = Integer.parseInt(fileMap.getName().substring(4, 8), 16) & 0xFFFF;
				maps.add(new L1NewMap(x, y, buf.array()));
				++j;
			}
			++i;
		}
		System.out.println(String.valueOf(NewMapUtil.class.getName()) + ": 加載 " + NewMapUtil.M.size() + " 張地圖。");
	}

	public static ArrayList<L1NewMap> getBlock(final int mapId) {
		if (NewMapUtil.M.containsKey(Integer.valueOf(mapId))) {
			return NewMapUtil.M.get(Integer.valueOf(mapId));
		}
		return null;
	}
}
