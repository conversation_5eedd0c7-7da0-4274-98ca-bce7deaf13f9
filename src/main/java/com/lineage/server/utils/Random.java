package com.lineage.server.utils;

public class Random {
	private static final int _max = 32767;
	private static int _idx;
	private static double[] _value;
	private static int _nIndex;
	private static double[] _nArray;

	static {
		_idx = 0;
		_value = new double[32768];
		_idx = 0;
		while (_idx < 32768) {
			_value[_idx] = (Math.random() + Math.random() + Math.random() + Math.random() + Math.random()) % 1.0;
			++_idx;
		}
		_nIndex = 0;
		_nArray = new double[32768];
		do {
			_nArray[_nIndex] = Math.random();
		} while (getIndex() != 0);
	}

	public static int nextInt(final int n) {
		Random._idx &= 0x7FFF;
		return (int) (Random._value[Random._idx++] * n);
	}

	public static int nextInt(final int n, final int offset) {
		Random._idx &= 0x7FFF;
		return offset + (int) (Random._value[Random._idx++] * n);
	}

	public static boolean nextBoolean() {
		return nextInt(2) == 1;
	}

	public static byte nextByte() {
		return (byte) nextInt(256);
	}

	public static long nextLong() {
		final long l = nextInt(Integer.MAX_VALUE) << 32 + nextInt(Integer.MAX_VALUE);
		return l;
	}

	public static int getInt(final int rang) {
		return (int) (getValue() * rang);
	}

	private static double getValue() {
		return Random._nArray[getIndex()];
	}

	private static int getIndex() {
		return Random._nIndex = (0x7FFF & ++Random._nIndex);
	}
}
