package com.lineage.server.utils.log;

import com.lineage.server.utils.date.DateTimeUtil;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;

public class PlayerLogUtil {

    private PlayerLogUtil() {
    }

    public static void writeLog(String fileName, String info) {
        try {
            File dir = new File(String.format("./玩家紀錄/%s", getTodayDate()));
            dir.mkdir();
            BufferedWriter out = new BufferedWriter(new FileWriter(String.format("./玩家紀錄/%s/%s.txt", getTodayDate(), fileName), true));
            out.write(info + "\r\n");
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String getTodayDate() {
        return DateTimeUtil.format(new Date(), "yyyyMMdd");
    }
}
