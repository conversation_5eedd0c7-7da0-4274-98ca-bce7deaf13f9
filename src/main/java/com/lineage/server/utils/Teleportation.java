package com.lineage.server.utils;

import java.util.Map;
import java.util.ArrayList;
import com.lineage.server.templates.L1MapsLimitTime;
import com.lineage.server.model.L1Location;
import java.util.Iterator;
import com.lineage.server.model.L1Clan;
import com.lineage.server.model.map.L1Map;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.serverpackets.S_PacketBoxWindShackle;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.timecontroller.pc.MapTimerThread;
import com.lineage.server.datatables.MapsTable;
import com.lineage.server.timecontroller.server.ServerUseMapTimer;
import com.lineage.server.serverpackets.S_NPCPack_Hierarch;
import com.lineage.server.serverpackets.S_NPCPack_Skin;
import com.lineage.server.model.Instance.L1SkinInstance;
import com.lineage.server.serverpackets.S_NPCPack_Eff;
import com.lineage.server.model.Instance.L1EffectInstance;
import com.lineage.server.serverpackets.S_MapTimer;
import com.lineage.server.datatables.MapsGroupTable;
import com.lineage.server.serverpackets.S_NPCPack_Doll;
import com.lineage.server.model.Instance.L1DollInstance;
import com.lineage.server.serverpackets.S_NPCPack_Pet;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.serverpackets.S_NPCPack_Summon;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import java.util.HashSet;
import com.lineage.server.serverpackets.S_CharVisualUpdate;
import com.lineage.server.serverpackets.S_OwnCharPack;
import com.lineage.server.serverpackets.S_OtherCharPacks;
import com.lineage.server.serverpackets.S_MapID;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.world.WorldClan;
import com.lineage.server.model.map.L1WorldMap;
import com.lineage.server.types.Point;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import java.util.Random;
import org.apache.commons.logging.Log;

public class Teleportation {
	private static final Log _log;
	private static Random _random;

	static {
		_log = LogFactory.getLog(Teleportation.class);
		_random = new Random();
	}

	public static void teleportation(final L1PcInstance pc) {
		try {
			if (pc == null) {
				return;
			}
			if (pc.getOnlineStatus() == 0) {
				return;
			}
			if (pc.getNetConnection() == null) {
				return;
			}
			if (pc.isDead()) {
				return;
			}
			if (pc.isTeleport()) {
				return;
			}
			pc.getMap().setPassable(pc.getLocation(), true);
			short mapId = pc.getTeleportMapId();
			if (pc.isDead() && mapId != 9101) {
				return;
			}
			int x = pc.getTeleportX();
			int y = pc.getTeleportY();
			final int head = pc.getTeleportHeading();
			final short pc_mapId = pc.getMapId();
			final L1Map map = L1WorldMap.get().getMap(mapId);
			if (!map.isInMap(x, y) && !pc.isGm()) {
				x = pc.getX();
				y = pc.getY();
				mapId = pc.getMapId();
			}
			final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
			if (clan != null && clan.getWarehouseUsingChar() == pc.getId()) {
				clan.setWarehouseUsingChar(0);
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_who"));
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "y_who"));
			}
			World.get().moveVisibleObject(pc, mapId);
			pc.setLocation(x, y, mapId);
			pc.setHeading(head);
			pc.setOleLocX(x);
			pc.setOleLocY(y);
			final boolean isUnderwater = pc.getMap().isUnderwater();
			pc.sendPackets(new S_MapID(pc, pc.getMapId(), isUnderwater));
			if (!pc.isGhost() && !pc.isGmInvis() && !pc.isInvisble()) {
				pc.broadcastPacketAll(new S_OtherCharPacks(pc));
			}
			if (pc.isReserveGhost()) {
				pc.endGhost();
			}
			pc.sendPackets(new S_OwnCharPack(pc));
			pc.removeAllKnownObjects();
			pc.updateObject();
			pc.sendVisualEffectAtTeleport();
			pc.sendPackets(new S_CharVisualUpdate(pc));
			pc.killSkillEffectTimer(32);
			pc.setCallClanId(0);
			final HashSet<L1PcInstance> subjects = new HashSet();
			subjects.add(pc);
			if (!pc.isGhost()) {
				if (pc.getMap().isTakePets()) {
					final Iterator<L1NpcInstance> iterator = pc.getPetList().values().iterator();
					while (iterator.hasNext()) {
						final L1NpcInstance petNpc = iterator.next();
						final int nx = pc.getX();
						final int ny = pc.getY();
						petNpc.set_showId(pc.get_showId());
						teleport(petNpc, nx, ny, mapId, head);
						if (petNpc instanceof L1SummonInstance) {
							final L1SummonInstance summon = (L1SummonInstance) petNpc;
							pc.sendPackets(new S_NPCPack_Summon(summon, pc));
						} else if (petNpc instanceof L1PetInstance) {
							final L1PetInstance pet = (L1PetInstance) petNpc;
							pc.sendPackets(new S_NPCPack_Pet(pet, pc));
						}
						final Iterator<L1PcInstance> iterator2 = World.get().getVisiblePlayer(petNpc).iterator();
						while (iterator2.hasNext()) {
							final L1PcInstance visiblePc = iterator2.next();
							visiblePc.removeKnownObject(petNpc);
							if (visiblePc.get_showId() == petNpc.get_showId()) {
								subjects.add(visiblePc);
							}
						}
					}
				}
				if (!pc.getDolls().isEmpty()) {
					final L1Location loc = pc.getLocation().randomLocation(3, false);
					final int nx2 = loc.getX();
					final int ny2 = loc.getY();
					final Object[] dolls = pc.getDolls().values().toArray();
					final Object[] array;
					final int length = (array = dolls).length;
					int i = 0;
					while (i < length) {
						final Object obj = array[i];
						final L1DollInstance doll = (L1DollInstance) obj;
						teleport(doll, nx2, ny2, mapId, head);
						pc.sendPackets(new S_NPCPack_Doll(doll, pc));
						doll.set_showId(pc.get_showId());
						final Iterator<L1PcInstance> iterator3 = World.get().getVisiblePlayer(doll).iterator();
						while (iterator3.hasNext()) {
							final L1PcInstance visiblePc2 = iterator3.next();
							visiblePc2.removeKnownObject(doll);
							if (visiblePc2.get_showId() == doll.get_showId()) {
								subjects.add(visiblePc2);
							}
						}
						++i;
					}
				}
				if (pc_mapId != mapId) {
					final L1MapsLimitTime mapsLimitTime = MapsGroupTable.get().findGroupMap(mapId);
					if (mapsLimitTime != null) {
						final int order_id = mapsLimitTime.getOrderId();
						final int used_time = pc.getMapsTime(order_id);
						final int limit_time = mapsLimitTime.getLimitTime();
						if (used_time < limit_time) {
							pc.sendPackets(new S_MapTimer(limit_time - used_time));
						}
					}
				}
				final ArrayList<L1EffectInstance> effectlist = pc.get_TrueTargetEffectList();
				final Iterator<L1EffectInstance> iterator4 = effectlist.iterator();
				while (iterator4.hasNext()) {
					final L1EffectInstance effect = iterator4.next();
					final L1Location loc2 = pc.getLocation();
					final int nx3 = loc2.getX();
					final int ny3 = loc2.getY();
					teleport(effect, nx3, ny3, mapId, head);
					pc.sendPackets(new S_NPCPack_Eff(effect));
					effect.set_showId(pc.get_showId());
					final Iterator<L1PcInstance> iterator5 = effect.getKnownPlayers().iterator();
					while (iterator5.hasNext()) {
						final L1PcInstance knownPc = iterator5.next();
						knownPc.removeKnownObject(effect);
						if (knownPc.get_showId() == effect.get_showId()) {
							knownPc.addKnownObject(effect);
							knownPc.sendPackets(new S_NPCPack_Eff(effect));
						}
					}
				}
				if (pc.getSkins() != null) {
					final Map<Integer, L1SkinInstance> skinList = pc.getSkins();
					final Iterator<Integer> iterator6 = skinList.keySet().iterator();
					while (iterator6.hasNext()) {
						final Integer gfxid = iterator6.next();
						final L1SkinInstance skin = skinList.get(gfxid);
						teleport(skin, pc.getX(), pc.getY(), pc.getMapId(), pc.getHeading());
						pc.sendPackets(new S_NPCPack_Skin(skin));
						skin.set_showId(pc.get_showId());
						final Iterator<L1PcInstance> iterator7 = World.get().getVisiblePlayer(skin).iterator();
						while (iterator7.hasNext()) {
							final L1PcInstance visiblePc3 = iterator7.next();
							visiblePc3.removeKnownObject(skin);
							if (visiblePc3.get_showId() == skin.get_showId()) {
								subjects.add(visiblePc3);
							}
						}
					}
				}
				if (pc.getHierarchs() != null) {
					final int nx2 = pc.getX();
					final int ny2 = pc.getY();
					teleport(pc.getHierarchs(), nx2, ny2, mapId, head);
					pc.sendPackets(new S_NPCPack_Hierarch(pc.getHierarchs()));
					pc.getHierarchs().set_showId(pc.get_showId());
					final Iterator<L1PcInstance> iterator8 = World.get().getVisiblePlayer(pc.getHierarchs()).iterator();
					while (iterator8.hasNext()) {
						final L1PcInstance visiblePc4 = iterator8.next();
						visiblePc4.removeKnownObject(pc.getHierarchs());
						if (visiblePc4.get_showId() == pc.getHierarchs().get_showId()) {
							subjects.add(visiblePc4);
						}
					}
				}
			}
			final Iterator<L1PcInstance> iterator9 = subjects.iterator();
			while (iterator9.hasNext()) {
				final L1PcInstance updatePc = iterator9.next();
				updatePc.updateObject();
			}
			final Integer time = ServerUseMapTimer.MAP.get(pc);
			if (time != null) {
				ServerUseMapTimer.put(pc, time.intValue());
			}
			final boolean isTimingmap = MapsTable.get().isTimingMap(mapId);
			if (isTimingmap) {
				final int maxMapUsetime = MapsTable.get().getMapTime(mapId) * 60;
				final int usedtime = pc.getMapUseTime(mapId);
				final int leftTime = maxMapUsetime - usedtime;
				MapTimerThread.put(pc, leftTime);
			} else if (MapTimerThread.TIMINGMAP.get(pc) != null) {
				MapTimerThread.TIMINGMAP.remove(pc);
				System.out.println("移出清單");
			}
			if (pc.getoldMapId() != pc.getMapId()) {
				if (pc.isActived()) {
					pc.setActived(false);
					if (pc.get_fwgj() > 0) {
						pc.setlslocx(0);
						pc.setlslocy(0);
						pc.set_fwgj(0);
					}
					pc.killSkillEffectTimer(8853);
				}
				if (pc.getnpcdmg() > 0.0) {
					pc.sendPackets(new S_SystemMessage("\\fU目前攻擊累積傷害:" + pc.getnpcdmg()));
					pc.sendPackets(new S_SystemMessage("\\fU因您更新地圖,攻擊傷害累積重新計算"));
					pc.setnpcdmg(0.0);
				}
				pc.setoldMapId(pc.getMapId());
			}
			pc.setTeleport(false);
			if (pc.hasSkillEffect(167)) {
				pc.sendPackets(new S_PacketBoxWindShackle(pc.getId(), pc.getSkillEffectTimeSec(167)));
			}
			pc.sendPackets(new S_Paralysis(7, false));
			if (!pc.isGmInvis()) {
				pc.getMap().setPassable(pc.getLocation(), false);
			}
			pc.getPetModel();
		} catch (Exception e) {
			Teleportation._log.error(e.getLocalizedMessage(), e);
		}
	}

	public static void teleport(final L1NpcInstance npc, final int x, final int y, final short map, final int head) {
		try {
			World.get().moveVisibleObject(npc, map);
			L1WorldMap.get().getMap(npc.getMapId()).setPassable(npc.getX(), npc.getY(), true, 2);
			npc.setX(x);
			npc.setY(y);
			npc.setMap(map);
			npc.setHeading(head);
			L1WorldMap.get().getMap(npc.getMapId()).setPassable(npc.getX(), npc.getY(), false, 2);
		} catch (Exception e) {
			Teleportation._log.error(e.getLocalizedMessage(), e);
		}
	}
}
