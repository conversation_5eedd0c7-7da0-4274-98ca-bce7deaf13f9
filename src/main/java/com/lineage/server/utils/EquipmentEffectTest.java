package com.lineage.server.utils;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;

/**
 * 裝備穿脫效果測試類
 * 用於驗證裝備穿脫效果檢查功能
 */
public class EquipmentEffectTest {
    
    /**
     * 測試裝備效果檢查功能
     * @param pc 玩家
     * @param item 物品
     */
    public static void testEquipmentEffect(L1PcInstance pc, L1ItemInstance item) {
        System.out.println("=== 裝備效果檢查測試開始 ===");
        
        // 測試裝備狀態檢查
        System.out.println("1. 測試裝備狀態檢查:");
        String status = EquipmentEffectChecker.getEquipmentStatus(pc, item);
        System.out.println("   裝備狀態: " + status);
        
        // 測試裝備衝突檢查
        System.out.println("2. 測試裝備衝突檢查:");
        boolean hasConflict = EquipmentEffectChecker.hasEquipmentConflict(pc, item);
        System.out.println("   是否有衝突: " + hasConflict);
        
        // 測試裝備效果檢查
        System.out.println("3. 測試裝備效果檢查:");
        EquipmentEffectChecker.checkEquipmentEffect(pc, item, true);
        System.out.println("   裝備效果檢查完成");
        
        // 測試卸下效果檢查
        System.out.println("4. 測試卸下效果檢查:");
        EquipmentEffectChecker.checkEquipmentEffect(pc, item, false);
        System.out.println("   卸下效果檢查完成");
        
        System.out.println("=== 裝備效果檢查測試結束 ===");
    }
    
    /**
     * 測試裝備特效功能
     * @param pc 玩家
     */
    public static void testArmorSkillSound(L1PcInstance pc) {
        System.out.println("=== 裝備特效測試開始 ===");
        
        try {
            // 測試裝備特效
            System.out.println("1. 測試裝備特效:");
            com.lineage.william.ArmorSkillSound.forArmorSkillSound(pc);
            System.out.println("   裝備特效檢查完成");
            
        } catch (Exception e) {
            System.out.println("   裝備特效檢查失敗: " + e.getMessage());
        }
        
        System.out.println("=== 裝備特效測試結束 ===");
    }
    
    /**
     * 測試裝備穿脫流程
     * @param pc 玩家
     * @param item 物品
     */
    public static void testEquipmentProcess(L1PcInstance pc, L1ItemInstance item) {
        System.out.println("=== 裝備穿脫流程測試開始 ===");
        
        // 測試裝備前檢查
        System.out.println("1. 裝備前檢查:");
        testEquipmentEffect(pc, item);
        
        // 模擬裝備過程
        System.out.println("2. 模擬裝備過程:");
        EquipmentEffectChecker.checkEquipmentEffect(pc, item, true);
        
        // 測試裝備後檢查
        System.out.println("3. 裝備後檢查:");
        testEquipmentEffect(pc, item);
        
        // 模擬卸下過程
        System.out.println("4. 模擬卸下過程:");
        EquipmentEffectChecker.checkEquipmentEffect(pc, item, false);
        
        // 測試卸下後檢查
        System.out.println("5. 卸下後檢查:");
        testEquipmentEffect(pc, item);
        
        System.out.println("=== 裝備穿脫流程測試結束 ===");
    }
    
    /**
     * 執行完整測試
     * @param pc 玩家
     * @param item 物品
     */
    public static void runFullTest(L1PcInstance pc, L1ItemInstance item) {
        System.out.println("開始執行裝備穿脫效果完整測試...");
        
        testEquipmentEffect(pc, item);
        testArmorSkillSound(pc);
        testEquipmentProcess(pc, item);
        
        System.out.println("裝備穿脫效果完整測試完成！");
    }
} 