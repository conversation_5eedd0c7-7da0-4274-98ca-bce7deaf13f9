package com.lineage.server.datatables;

import java.util.Iterator;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import java.util.logging.Level;
import com.lineage.DatabaseFactory;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.sql.Timestamp;
import java.util.logging.Logger;

public class DropLimitTable {
	private static final Logger _log;
	private static DropLimitTable INS;
	private Timestamp ts;
	private final Map<Integer, Object[]> _data;

	static {
		_log = Logger.getLogger(DropLimitTable.class.getName());
	}

	public static DropLimitTable getInstance() {
		if (DropLimitTable.INS == null) {
			DropLimitTable.INS = new DropLimitTable();
		}
		return DropLimitTable.INS;
	}

	private DropLimitTable() {
		this._data = new ConcurrentHashMap();
		this.reload();
	}

	public final void reload() {
		this._data.clear();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM w_物品掉落限制二");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int id = rs.getInt(1);
				final Object[] data = { this.ts = rs.getTimestamp(6), Integer.valueOf(rs.getInt(3)),
						Integer.valueOf(rs.getInt(4)), Integer.valueOf(rs.getInt(5)) };
				if (data[0] == null) {
					data[0] = new Timestamp(System.currentTimeMillis() + rs.getInt(5) * 3600000L);
				} else {
					data[0] = this.ts;
				}
				this._data.put(Integer.valueOf(id), data);
			}
		} catch (SQLException e) {
			DropLimitTable._log.log(Level.SEVERE, e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs, pstm, con);
		}
		this.ts = new Timestamp(System.currentTimeMillis());
		final Iterator<Integer> iterator = this._data.keySet().iterator();
		while (iterator.hasNext()) {
			final int itemid = iterator.next().intValue();
			this.updateTime(itemid, this._data.get(Integer.valueOf(itemid)), this.ts);
		}
	}

	public boolean giveCheck(final int i, final int j) {
		this.ts.setTime(System.currentTimeMillis());
		boolean flag = false;
		if (this._data.containsKey(Integer.valueOf(i))) {
			synchronized (DropLimitTable.class) {
				if (this._data.containsKey(Integer.valueOf(i))) {
					final Object[] aobj = this._data.get(Integer.valueOf(i));
					if (((Integer) aobj[2]).intValue() >= ((Integer) aobj[1]).intValue()) {
						flag = true;
					} else {
						aobj[2] = Integer.valueOf(((Integer) aobj[2]).intValue() + j);
					}
					this.updateTime(i, aobj, this.ts);
				}
			}
		}
		return flag;
	}

	private void updateTime(final int itemid, final Object[] info, final Timestamp now) {
		final Timestamp ts = (Timestamp) info[0];
		if (now.after(ts)) {
			final int day = ((Integer) info[3]).intValue();
			info[0] = new Timestamp(System.currentTimeMillis() + day * 3600000L);
			info[2] = Integer.valueOf(0);
		}
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("UPDATE w_物品掉落限制二 SET give_count=?, time=? WHERE item_id=?");
			pstm.setObject(1, info[2]);
			pstm.setObject(2, info[0]);
			pstm.setInt(3, itemid);
			pstm.execute();
		} catch (SQLException e) {
			DropLimitTable._log.log(Level.SEVERE, e.getLocalizedMessage(), e);
			return;
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
	}
}
