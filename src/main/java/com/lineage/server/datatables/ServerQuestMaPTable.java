package com.lineage.server.datatables;

import java.util.StringTokenizer;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.world.World;
import com.lineage.server.templates.L1Item;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_CloseList;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.PerformanceTimer;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import org.apache.commons.logging.Log;
import java.sql.PreparedStatement;

public class ServerQuestMaPTable {
	private static final Log _log;
	private static ArrayList<ArrayList<Object>> _array;
	static boolean mustMobCountOnly;
	private static ServerQuestMaPTable _instance;

	static {
		_log = LogFactory.getLog(ServerQuestMaPTable.class);
		_array = new ArrayList();
		mustMobCountOnly = false;
	}

	public static ServerQuestMaPTable get() {
		if (ServerQuestMaPTable._instance == null) {
			ServerQuestMaPTable._instance = new ServerQuestMaPTable();
		}
		return ServerQuestMaPTable._instance;
	}

	private ServerQuestMaPTable() {
		final PerformanceTimer timer = new PerformanceTimer();
		this.getData();
		ServerQuestMaPTable._log
				.info("載入地圖狩獵數據設置資料數量: " + ServerQuestMaPTable._array.size() + "(" + timer.get() + "ms)");
		if (ServerQuestMaPTable._array.size() <= 0) {
			ServerQuestMaPTable._array.clear();
			ServerQuestMaPTable._array = null;
		}
	}

	public static boolean checkMobMap(final String s, final L1PcInstance pc, final L1NpcInstance npc, final int npcid,
			final int oid) {
		if (ServerQuestMaPTable._array.size() <= 0) {
			return false;
		}
		ArrayList<?> aTempData = null;
		int i = 0;
		while (i < ServerQuestMaPTable._array.size()) {
			aTempData = ServerQuestMaPTable._array.get(i);
			if (aTempData.get(0) != null && ((Integer) aTempData.get(0)).intValue() == npcid
					&& ((String) aTempData.get(1)).equals(s)) {
				if (pc.getQuest().get_step(((Integer) aTempData.get(5)).intValue()) == 1
						&& ((Integer) aTempData.get(5)).intValue() != 255) {
					pc.sendPackets(new S_SystemMessage("該任務正在進行中"));
					return false;
				}
				if (((Integer) aTempData.get(0)).intValue() == npcid && ((String) aTempData.get(1)).equals(s)
						&& pc.getQuest().get_step(((Integer) aTempData.get(5)).intValue()) == 255) {
					pc.sendPackets(new S_SystemMessage("該狩獵任務已完成"));
					return false;
				}
				if (pc.getLevel() < ((Integer) aTempData.get(2)).intValue()) {
					pc.sendPackets(new S_SystemMessage("等級不足:" + ((Integer) aTempData.get(2)).intValue()));
					return false;
				}
				if (pc.getQuest().get_step(9050) == 1 || pc.getQuest().get_step(9051) == 1
						|| pc.getQuest().get_step(9052) == 1 || pc.getQuest().get_step(9053) == 1
						|| pc.getQuest().get_step(9054) == 1 || pc.getQuest().get_step(9055) == 1
						|| pc.getQuest().get_step(9056) == 1 || pc.getQuest().get_step(9057) == 1
						|| pc.getQuest().get_step(9058) == 1 || pc.getQuest().get_step(9059) == 1
						|| pc.getQuest().get_step(9060) == 1 || pc.getQuest().get_step(9061) == 1
						|| pc.getQuest().get_step(9062) == 1 || pc.getQuest().get_step(9063) == 1
						|| pc.getQuest().get_step(9064) == 1 || pc.getQuest().get_step(9065) == 1
						|| pc.getQuest().get_step(9066) == 1 || pc.getQuest().get_step(9067) == 1
						|| pc.getQuest().get_step(9068) == 1 || pc.getQuest().get_step(9069) == 1) {
					pc.sendPackets(new S_SystemMessage("您目前有地圖任務進行中,請先完成在接下個任務。。"));
					return false;
				}
				if (pc.getQuest().get_step(9001) == 1 || pc.getQuest().get_step(9002) == 1
						|| pc.getQuest().get_step(9003) == 1 || pc.getQuest().get_step(9004) == 1
						|| pc.getQuest().get_step(9005) == 1 || pc.getQuest().get_step(9006) == 1
						|| pc.getQuest().get_step(9007) == 1 || pc.getQuest().get_step(9008) == 1
						|| pc.getQuest().get_step(9009) == 1 || pc.getQuest().get_step(9010) == 1
						|| pc.getQuest().get_step(9011) == 1 || pc.getQuest().get_step(9012) == 1
						|| pc.getQuest().get_step(9013) == 1 || pc.getQuest().get_step(9014) == 1
						|| pc.getQuest().get_step(9015) == 1 || pc.getQuest().get_step(9016) == 1
						|| pc.getQuest().get_step(9017) == 1 || pc.getQuest().get_step(9018) == 1
						|| pc.getQuest().get_step(9019) == 1 || pc.getQuest().get_step(9020) == 1
						|| pc.getQuest().get_step(9021) == 1 || pc.getQuest().get_step(9022) == 1
						|| pc.getQuest().get_step(9023) == 1 || pc.getQuest().get_step(9024) == 1
						|| pc.getQuest().get_step(9025) == 1 || pc.getQuest().get_step(9026) == 1
						|| pc.getQuest().get_step(9027) == 1 || pc.getQuest().get_step(9028) == 1
						|| pc.getQuest().get_step(9029) == 1 || pc.getQuest().get_step(9030) == 1) {
					pc.sendPackets(new S_SystemMessage("您目前有每日任務進行中,請先完成在接下個任務。。"));
					return false;
				}
				if (!pc.getInventory().checkItem(((Integer) aTempData.get(3)).intValue(),
						((Integer) aTempData.get(4)).intValue())) {
					final L1Item temp = ItemTable.get().getTemplate(((Integer) aTempData.get(3)).intValue());
					pc.sendPackets(new S_ServerMessage(337, String.valueOf(temp.getName()) + "("
							+ (((Integer) aTempData.get(4)).intValue() - pc.getInventory().countItems(temp.getItemId()))
							+ ")"));
					return false;
				}
				pc.getInventory().consumeItem(((Integer) aTempData.get(3)).intValue(),
						((Integer) aTempData.get(4)).intValue());
				pc.getQuest().set_step(((Integer) aTempData.get(5)).intValue(), 1);
				pc.get_other3().set_type1(0);
				if (((Integer) aTempData.get(6)).intValue() > 0) {
					pc.setSave_Quest_Map1(((Integer) aTempData.get(6)).intValue());
					pc.get_other3().set_type2(((Integer) aTempData.get(6)).intValue());
				}
				if (((Integer) aTempData.get(12)).intValue() > 0) {
					pc.setSave_Quest_Map2(((Integer) aTempData.get(12)).intValue());
					pc.get_other3().set_type3(((Integer) aTempData.get(12)).intValue());
				}
				if (((Integer) aTempData.get(13)).intValue() > 0) {
					pc.setSave_Quest_Map3(((Integer) aTempData.get(13)).intValue());
					pc.get_other3().set_type4(((Integer) aTempData.get(13)).intValue());
				}
				if (((Integer) aTempData.get(14)).intValue() > 0) {
					pc.setSave_Quest_Map4(((Integer) aTempData.get(14)).intValue());
					pc.get_other3().set_type5(((Integer) aTempData.get(14)).intValue());
				}
				if (((Integer) aTempData.get(15)).intValue() > 0) {
					pc.setSave_Quest_Map5(((Integer) aTempData.get(15)).intValue());
					pc.get_other3().set_type6(((Integer) aTempData.get(15)).intValue());
				}
				pc.get_other3().set_type1(((Integer) aTempData.get(7)).intValue());
				pc.sendPackets(new S_SystemMessage((String) aTempData.get(10)));
				pc.sendPackets(new S_CloseList(pc.getId()));
			}
			++i;
		}
		return false;
	}

	public static void check(final L1PcInstance pc) {
		if (ServerQuestMaPTable._array.size() <= 0) {
			return;
		}
		ArrayList<?> aTempData = null;
		int i = 0;
		while (i < ServerQuestMaPTable._array.size()) {
			aTempData = ServerQuestMaPTable._array.get(i);
			if (pc.getQuest().get_step(((Integer) aTempData.get(5)).intValue()) == 1 && !pc.hasSkillEffect(80552)
					&& ((pc.getSave_Quest_Map1() > 0 && pc.getSave_Quest_Map1() == pc.getMapId())
							|| (pc.getSave_Quest_Map2() > 0 && pc.getSave_Quest_Map2() == pc.getMapId())
							|| (pc.getSave_Quest_Map3() > 0 && pc.getSave_Quest_Map3() == pc.getMapId())
							|| (pc.getSave_Quest_Map4() > 0 && pc.getSave_Quest_Map4() == pc.getMapId())
							|| (pc.getSave_Quest_Map5() > 0 && pc.getSave_Quest_Map5() == pc.getMapId()))) {
				pc.get_other3().set_type1(pc.get_other3().get_type1() - 1);
				pc.sendPackets(new S_SystemMessage(String.valueOf(aTempData.get(16)) + pc.get_other3().get_type1()));
				if (pc.get_other3().get_type1() < 1) {
					pc.get_other3().set_type1(0);
					pc.setSkillEffect(80552, 2000);
				}
			}
			++i;
		}
	}

	public static void checkcount(final L1PcInstance pc) {
		if (ServerQuestMaPTable._array.size() <= 0) {
			return;
		}
		ArrayList<?> aTempData = null;
		int i = 0;
		while (i < ServerQuestMaPTable._array.size()) {
			aTempData = ServerQuestMaPTable._array.get(i);
			final int[] materials = (int[]) aTempData.get(8);
			final int[] counts = (int[]) aTempData.get(9);
			if (pc.getQuest().get_step(((Integer) aTempData.get(5)).intValue()) == 1) {
				pc.sendPackets(new S_SystemMessage((String) aTempData.get(11)));
				checkquest(pc);
				pc.setSave_Quest_Map1(0);
				pc.setSave_Quest_Map2(0);
				pc.setSave_Quest_Map3(0);
				pc.setSave_Quest_Map4(0);
				pc.setSave_Quest_Map5(0);
				pc.get_other3().set_type1(0);
				pc.get_other3().set_type2(0);
				pc.get_other3().set_type3(0);
				pc.get_other3().set_type4(0);
				pc.get_other3().set_type5(0);
				pc.get_other3().set_type6(0);
				final int[] newcounts = new int[counts.length];
				int j = 0;
				while (j < counts.length) {
					newcounts[j] = counts[j];
					final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
					item.setIdentified(true);
					if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
						pc.getInventory().storeItem(materials[j], newcounts[j]);
						pc.sendPackets(
								new S_ServerMessage(403, String.valueOf(item.getName()) + "(" + counts[j] + ")"));
					} else {
						World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(materials[j],
								newcounts[j]);
						pc.sendPackets(
								new S_ServerMessage(403, String.valueOf(item.getName()) + "(" + counts[j] + ")"));
					}
					++j;
				}
			}
			++i;
		}
	}

	private static boolean checkquest(final L1PcInstance pc) {
		try {
			if (pc.getQuest().get_step(9050) == 1) {
				pc.getQuest().set_step(9050, 255);
			}
			if (pc.getQuest().get_step(9051) == 1) {
				pc.getQuest().set_step(9051, 255);
			}
			if (pc.getQuest().get_step(9052) == 1) {
				pc.getQuest().set_step(9052, 255);
			}
			if (pc.getQuest().get_step(9053) == 1) {
				pc.getQuest().set_step(9053, 255);
			}
			if (pc.getQuest().get_step(9054) == 1) {
				pc.getQuest().set_step(9054, 255);
			}
			if (pc.getQuest().get_step(9055) == 1) {
				pc.getQuest().set_step(9055, 255);
			}
			if (pc.getQuest().get_step(9056) == 1) {
				pc.getQuest().set_step(9056, 255);
			}
			if (pc.getQuest().get_step(9057) == 1) {
				pc.getQuest().set_step(9057, 255);
			}
			if (pc.getQuest().get_step(9058) == 1) {
				pc.getQuest().set_step(9050, 255);
			}
			if (pc.getQuest().get_step(9059) == 1) {
				pc.getQuest().set_step(9059, 255);
			}
			if (pc.getQuest().get_step(9060) == 1) {
				pc.getQuest().set_step(9060, 255);
			}
			if (pc.getQuest().get_step(9061) == 1) {
				pc.getQuest().set_step(9061, 255);
			}
			if (pc.getQuest().get_step(9062) == 1) {
				pc.getQuest().set_step(9062, 255);
			}
			if (pc.getQuest().get_step(9063) == 1) {
				pc.getQuest().set_step(9063, 255);
			}
			if (pc.getQuest().get_step(9064) == 1) {
				pc.getQuest().set_step(9064, 255);
			}
			if (pc.getQuest().get_step(9065) == 1) {
				pc.getQuest().set_step(9065, 255);
			}
			if (pc.getQuest().get_step(9066) == 1) {
				pc.getQuest().set_step(9066, 255);
			}
			if (pc.getQuest().get_step(9067) == 1) {
				pc.getQuest().set_step(9067, 255);
			}
			if (pc.getQuest().get_step(9068) == 1) {
				pc.getQuest().set_step(9068, 255);
			}
			if (pc.getQuest().get_step(9069) == 1) {
				pc.getQuest().set_step(9069, 255);
			}
		} catch (Exception e) {
			return false;
		}
		return true;
	}

	private void getData() {
		Connection cn = null;
		PreparedStatement ps = null;
		ResultSet rset = null;
		String sTemp = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("SELECT * FROM w_狩獵怪物任務_地圖");
			rset = ps.executeQuery();
			while (rset.next()) {
				final ArrayList<Object> aReturn = new ArrayList();
				aReturn.add(0, new Integer(rset.getInt("npcid")));
				sTemp = rset.getString("action");
				aReturn.add(1, sTemp);
				aReturn.add(2, new Integer(rset.getInt("等級")));
				aReturn.add(3, new Integer(rset.getInt("任務需求道具")));
				aReturn.add(4, new Integer(rset.getInt("任務需求數量")));
				aReturn.add(5, new Integer(rset.getInt("任務記錄編號")));
				aReturn.add(6, new Integer(rset.getInt("指定地圖1")));
				aReturn.add(7, new Integer(rset.getInt("擊殺怪物數量")));
				if (rset.getString("完成後給予道具") != null && !rset.getString("完成後給予道具").equals("")
						&& !rset.getString("完成後給予道具").equals("0")) {
					aReturn.add(8, getArray(rset.getString("完成後給予道具"), ",", 1));
				} else {
					aReturn.add(8, null);
				}
				if (rset.getString("完成後給予數量") != null && !rset.getString("完成後給予數量").equals("")
						&& !rset.getString("完成後給予數量").equals("0")) {
					aReturn.add(9, getArray(rset.getString("完成後給予數量"), ",", 1));
				} else {
					aReturn.add(9, null);
				}
				aReturn.add(10, rset.getString("接取任務訊息"));
				aReturn.add(11, rset.getString("完成任務訊息"));
				aReturn.add(12, new Integer(rset.getInt("指定地圖2")));
				aReturn.add(13, new Integer(rset.getInt("指定地圖3")));
				aReturn.add(14, new Integer(rset.getInt("指定地圖4")));
				aReturn.add(15, new Integer(rset.getInt("指定地圖5")));
				aReturn.add(16, rset.getString("顯示狩獵文字"));
				ServerQuestMaPTable._array.add(aReturn);
			}
		} catch (SQLException e) {
			ServerQuestMaPTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rset);
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
