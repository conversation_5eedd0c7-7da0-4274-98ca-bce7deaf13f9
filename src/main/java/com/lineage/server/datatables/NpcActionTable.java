package com.lineage.server.datatables;

import java.util.Iterator;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.PerformanceTimer;
import com.lineage.server.utils.FileUtil;
import java.io.IOException;
import org.xml.sax.SAXException;
import javax.xml.parsers.ParserConfigurationException;
import org.w3c.dom.Document;
import javax.xml.parsers.DocumentBuilder;
import com.lineage.server.model.npc.action.L1NpcXmlParser;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.util.ArrayList;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.model.npc.action.L1NpcAction;
import java.util.List;

public class NpcActionTable {
	private static final Log _log = LogFactory.getLog(NpcActionTable.class);
	private static NpcActionTable _instance;
	private static final List<L1NpcAction> _actions;
	private static final List<L1NpcAction> _talkActions;

	static {
		_actions = new ArrayList();
		_talkActions = new ArrayList();
	}

	private List<L1NpcAction> loadAction(final File file, final String nodeName)
			throws ParserConfigurationException, SAXException, IOException {
		final DocumentBuilder builder = DocumentBuilderFactory.newInstance().newDocumentBuilder();
		final Document doc = builder.parse(file);
		if (!doc.getDocumentElement().getNodeName().equalsIgnoreCase(nodeName)) {
			return new ArrayList();
		}
		return L1NpcXmlParser.listActions(doc.getDocumentElement());
	}

	private void loadAction(final File file) throws Exception {
		NpcActionTable._actions.addAll(this.loadAction(file, "NpcActionList"));
	}

	private void loadTalkAction(final File file) throws Exception {
		NpcActionTable._talkActions.addAll(this.loadAction(file, "NpcTalkActionList"));
	}

	private void loadDirectoryActions(final File dir) throws Exception {
		if (dir == null || !dir.exists() || !dir.isDirectory()) {
			_log.warn("NPC Actions directory not found: " + (dir != null ? dir.getPath() : "null"));
			return;
		}
		
		final String[] list = dir.list();
		if (list == null) {
			_log.warn("Cannot read directory: " + dir.getPath());
			return;
		}
		
		final int length = list.length;
		int i = 0;
		while (i < length) {
			final String file = list[i];
			final File f = new File(dir, file);
			if (FileUtil.getExtension(f).equalsIgnoreCase("xml")) {
				this.loadAction(f);
				this.loadTalkAction(f);
			}
			++i;
		}
	}

	private NpcActionTable() throws Exception {
		this.loadDirectoryActions(new File("./data/xml/NpcActions/"));
	}

	public static void load() {
		try {
			final PerformanceTimer timer = new PerformanceTimer();
			NpcActionTable._instance = new NpcActionTable();
			NpcActionTable._log.info("載入NPC XML對話結果資料 (" + timer.get() + "ms)");
		} catch (Exception e) {
			NpcActionTable._log.error(e.getLocalizedMessage(), e);
			System.exit(0);
		}
	}

	public static NpcActionTable getInstance() {
		return NpcActionTable._instance;
	}

	public L1NpcAction get(final String actionName, final L1PcInstance pc, final L1Object obj) {
		final Iterator<L1NpcAction> iterator = NpcActionTable._actions.iterator();
		while (iterator.hasNext()) {
			final L1NpcAction action = iterator.next();
			if (action.acceptsRequest(actionName, pc, obj)) {
				return action;
			}
		}
		return null;
	}

	public L1NpcAction get(final L1PcInstance pc, final L1Object obj) {
		final Iterator<L1NpcAction> iterator = NpcActionTable._talkActions.iterator();
		while (iterator.hasNext()) {
			final L1NpcAction action = iterator.next();
			if (action.acceptsRequest("", pc, obj)) {
				return action;
			}
		}
		return null;
	}
}
