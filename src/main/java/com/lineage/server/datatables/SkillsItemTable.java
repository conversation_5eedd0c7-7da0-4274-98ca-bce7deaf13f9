package com.lineage.server.datatables;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.L1SkillItem;
import java.util.Map;
import org.apache.commons.logging.Log;

public class SkillsItemTable {
	private static final Log _log;
	private static SkillsItemTable _instance;
	private static final Map<Integer, L1SkillItem> _skills;

	static {
		_log = LogFactory.getLog(SkillsItemTable.class);
		_skills = new HashMap();
	}

	public static SkillsItemTable get() {
		if (SkillsItemTable._instance == null) {
			SkillsItemTable._instance = new SkillsItemTable();
		}
		return SkillsItemTable._instance;
	}

	public void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `skills_item`");
			rs = pstm.executeQuery();
			this.itemTable(rs);
		} catch (SQLException e) {
			SkillsItemTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		SkillsItemTable._log.info("載入購買技能 材料 設置資料數量: " + SkillsItemTable._skills.size() + "(" + timer.get() + "ms)");
	}

	private void itemTable(final ResultSet rs) throws SQLException {
		while (rs.next()) {
			final L1SkillItem skillItem = new L1SkillItem();
			final int skill_id = rs.getInt("skill_id");
			skillItem.set_skill_id(skill_id);
			final String name = rs.getString("name");
			skillItem.set_name(name);
			final String items = rs.getString("itemids");
			if (items == null) {
				skillItem.set_items(null);
			} else {
				final String[] itemsX = items.split(",");
				final int[] items_x = new int[itemsX.length];
				int i = 0;
				while (i < itemsX.length) {
					items_x[i] = Integer.parseInt(itemsX[i]);
					++i;
				}
				skillItem.set_items(items_x);
			}
			final String counts = rs.getString("counts");
			if (counts == null) {
				skillItem.set_counts(null);
			} else {
				final String[] countsX = counts.split(",");
				final int[] counts_x = new int[countsX.length];
				int j = 0;
				while (j < countsX.length) {
					counts_x[j] = Integer.parseInt(countsX[j]);
					++j;
				}
				skillItem.set_counts(counts_x);
			}
			SkillsItemTable._skills.put(new Integer(skill_id), skillItem);
			if (skillItem.get_items() != null) {
				if (skillItem.get_items().length == skillItem.get_counts().length) {
					continue;
				}
				SkillsItemTable._log.error("購買技能 材料 設置資料異常 技能編號: " + skill_id);
				SkillsItemTable._skills.remove(new Integer(skill_id));
			} else {
				SkillsItemTable._skills.remove(new Integer(skill_id));
			}
		}
	}

	public L1SkillItem getTemplate(final int i) {
		return SkillsItemTable._skills.get(new Integer(i));
	}
}
