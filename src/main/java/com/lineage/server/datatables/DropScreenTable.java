package com.lineage.server.datatables;

import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.Iterator;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import java.util.Random;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.templates.L1Item;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.util.ArrayList;
import com.lineage.server.templates.L1DropScreen;
import java.util.Map;
import org.apache.commons.logging.Log;

public class DropScreenTable {
	private static final Log _log;
	private final Map<Integer, L1DropScreen> _list;
	private static final ArrayList<Integer> _npcList;
	private static DropScreenTable _instance;

	static {
		_log = LogFactory.getLog(DropScreenTable.class);
		_npcList = new ArrayList();
	}

	public static DropScreenTable get() {
		if (DropScreenTable._instance == null) {
			DropScreenTable._instance = new DropScreenTable();
		}
		return DropScreenTable._instance;
	}

	public static void reload() {
		DropScreenTable oldInstance = DropScreenTable._instance;
		DropScreenTable._instance = new DropScreenTable();
		oldInstance._list.clear();
		oldInstance = null;
	}

	private DropScreenTable() {
		this._list = new HashMap();
		this.load();
	}

	private void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `w_參予擊殺獎勵`");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int key = rs.getInt("id");
				final int npc_id = rs.getInt("npc_id");
				final int item_id = rs.getInt("item_id");
				final int min = rs.getInt("min");
				final int max = rs.getInt("max");
				final int random = rs.getInt("random");
				final int enchant_min = rs.getInt("enchant_min");
				final int enchant_max = rs.getInt("enchant_max");
				final boolean is_attack = rs.getBoolean("is_attack");
				if (this.check_item(item_id)) {
					final L1DropScreen drop = new L1DropScreen(npc_id, item_id, min, max, random, enchant_min,
							enchant_max, is_attack);
					this._list.put(Integer.valueOf(key), drop);
					if (DropScreenTable._npcList.contains(Integer.valueOf(npc_id))) {
						continue;
					}
					DropScreenTable._npcList.add(Integer.valueOf(npc_id));
				}
			}
		} catch (SQLException e) {
			DropScreenTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		DropScreenTable._log.info("載入掉落物品資料數量(全畫面掉落): " + this._list.size() + "(" + timer.get() + "ms)");
	}

	private boolean check_item(final int itemId) {
		final L1Item itemTemplate = ItemTable.get().getTemplate(itemId);
		if (itemTemplate == null) {
			DropScreenTable._log.info("掉落物品資料數量(全畫面)錯誤物品->" + itemId);
			return false;
		}
		return true;
	}

	public void runDropScreen(final L1MonsterInstance mob, final ArrayList<L1Character> acquisitorList) {
		if (this._list.isEmpty() || DropScreenTable._npcList.isEmpty()) {
			return;
		}
		final int npc_id = mob.getNpcId();
		if (!DropScreenTable._npcList.contains(Integer.valueOf(npc_id))) {
			return;
		}
		final Iterator<Integer> iterator = this._list.keySet().iterator();
		while (iterator.hasNext()) {
			final Integer key = iterator.next();
			final L1DropScreen drop = this._list.get(key);
			if (drop.getMobid() == npc_id) {
				final Iterator<L1PcInstance> iterator2 = World.get().getVisiblePlayer(mob).iterator();
				while (iterator2.hasNext()) {
					final L1PcInstance pc = iterator2.next();
					if (pc.isDead()) {
						continue;
					}
					if (drop.is_attack() && !acquisitorList.contains(pc)) {
						continue;
					}
					final Random _random = new Random();
					final int random = _random.nextInt(1000) + 1;
					if (drop.getChance() < random) {
						continue;
					}
					int itemCount = drop.getMin();
					final int addCount = drop.getMax() - itemCount + 1;
					if (addCount > 1) {
						itemCount += _random.nextInt(addCount);
					}
					final L1ItemInstance item = pc.getInventory().storeItem(drop.getItemid(), itemCount);
					if (pc.isInParty()) {
						final Object[] pcs = pc.getParty().partyUsers().values().toArray();
						if (pcs.length <= 0) {
							return;
						}
						final Object[] array;
						final int length = (array = pcs).length;
						int i = 0;
						while (i < length) {
							final Object obj = array[i];
							if (obj instanceof L1PcInstance) {
								final L1PcInstance tgpc = (L1PcInstance) obj;
								tgpc.sendPackets(new S_ServerMessage(813, mob.getNameId(),
										String.valueOf(item.getName()) + " (" + itemCount + ")", pc.getName()));
							}
							++i;
						}
					} else {
						pc.sendPackets(new S_ServerMessage(143, mob.getNameId(),
								String.valueOf(item.getName()) + " (" + itemCount + ")"));
					}
				}
			}
		}
	}
}
