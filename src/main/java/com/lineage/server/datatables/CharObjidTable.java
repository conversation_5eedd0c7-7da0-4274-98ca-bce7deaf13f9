package com.lineage.server.datatables;

import java.util.Iterator;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import java.util.HashMap;
import org.apache.commons.logging.LogFactory;
import java.util.Map;
import org.apache.commons.logging.Log;

public class CharObjidTable {
	private static final Log _log;
	private static final Map<Integer, String> _objList;
	private static final Map<Integer, String> _objClanList;
	private static CharObjidTable _instance;

	static {
		_log = LogFactory.getLog(CharObjidTable.class);
		_objList = new HashMap();
		_objClanList = new HashMap();
	}

	public static CharObjidTable get() {
		if (CharObjidTable._instance == null) {
			CharObjidTable._instance = new CharObjidTable();
		}
		return CharObjidTable._instance;
	}

	public void load() {
		this.loadPc();
		this.loadClan();
	}

	private void loadClan() {
		Connection cn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("SELECT clan_id, clan_name FROM `clan_data`");
			rs = ps.executeQuery();
			while (rs.next()) {
				try {
					final int clan_id = rs.getInt("clan_id");
					final String clan_name = rs.getString("clan_name");
					if (clan_name != null && !clan_name.trim().isEmpty()) {
						CharObjidTable._objClanList.put(Integer.valueOf(clan_id), clan_name);
					}
				} catch (Exception e) {
					CharObjidTable._log.warn("載入血盟資料時發生錯誤，跳過此筆資料: " + e.getMessage());
				}
			}
		} catch (SQLException e) {
			CharObjidTable._log.error("載入血盟資料時發生錯誤: " + e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	private void loadPc() {
		Connection cn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("SELECT objid, char_name FROM `characters`");
			rs = ps.executeQuery();
			while (rs.next()) {
				try {
					final int objid = rs.getInt("objid");
					final String char_name = rs.getString("char_name");
					if (char_name != null && !char_name.trim().isEmpty()) {
						CharObjidTable._objList.put(Integer.valueOf(objid), char_name);
					}
				} catch (Exception e) {
					CharObjidTable._log.warn("載入角色資料時發生錯誤，跳過此筆資料: " + e.getMessage());
				}
			}
		} catch (SQLException e) {
			CharObjidTable._log.error("載入角色資料時發生錯誤: " + e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	public void reChar(final int objid, final String char_name) {
		CharObjidTable._objList.put(Integer.valueOf(objid), char_name);
	}

	public void addChar(final int objid, final String char_name) {
		final int tmp = this.charObjid(char_name);
		if (tmp == 0) {
			CharObjidTable._objList.put(Integer.valueOf(objid), char_name);
		}
	}

	public String isChar(final int objid) {
		final String username = CharObjidTable._objList.get(Integer.valueOf(objid));
		if (username != null) {
			return username;
		}
		return null;
	}

	public int charObjid(final String char_name) {
		final Iterator<Integer> iterator = CharObjidTable._objList.keySet().iterator();
		while (iterator.hasNext()) {
			final Integer integer = iterator.next();
			final String tgname = CharObjidTable._objList.get(integer);
			if (char_name.equalsIgnoreCase(tgname)) {
				return integer.intValue();
			}
		}
		return 0;
	}

	public void charRemove(final String char_name) {
		final int objid = this.charObjid(char_name);
		final String username = CharObjidTable._objList.get(Integer.valueOf(objid));
		if (username != null) {
			CharObjidTable._objList.remove(Integer.valueOf(objid));
		}
	}

	public void addClan(final int clan_id, final String clan_name) {
		final int tmp = this.clanObjid(clan_name);
		if (tmp == 0) {
			CharObjidTable._objClanList.put(Integer.valueOf(clan_id), clan_name);
		}
	}

	public boolean isClan(final int clan_id) {
		final String username = CharObjidTable._objClanList.get(Integer.valueOf(clan_id));
		return username != null;
	}

	public int clanObjid(final String clan_name) {
		final Iterator<Integer> iterator = CharObjidTable._objClanList.keySet().iterator();
		while (iterator.hasNext()) {
			final Integer integer = iterator.next();
			final String tgname = CharObjidTable._objClanList.get(integer);
			if (clan_name.equalsIgnoreCase(tgname)) {
				return integer.intValue();
			}
		}
		return 0;
	}
}
