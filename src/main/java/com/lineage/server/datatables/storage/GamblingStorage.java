package com.lineage.server.datatables.storage;

import com.lineage.server.templates.L1Gambling;

public interface GamblingStorage {
	void load();

	L1Gambling getGambling(final String p0);

	L1Gambling getGambling(final int p0);

	void add(final L1Gambling p0);

	void updateGambling(final int p0, final int p1);

	int[] winCount(final int p0);

	int maxId();
	
	/**
	 * 獲取賭場紀錄總數量
	 */
	int getGamblingCount();
}
