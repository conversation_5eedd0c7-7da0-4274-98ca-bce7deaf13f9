package com.lineage.server.datatables.storage;

import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.concurrent.CopyOnWriteArrayList;

public interface DwarfForElfStorage {
	void load();

	CopyOnWriteArrayList<L1ItemInstance> loadItems(final String p0);

	void delUserItems(final String p0);

	boolean getUserItems(final String p0, final int p1, final int p2);

	void insertItem(final String p0, final L1ItemInstance p1);

	void updateItem(final L1ItemInstance p0);

	void deleteItem(final String p0, final L1ItemInstance p1);
}
