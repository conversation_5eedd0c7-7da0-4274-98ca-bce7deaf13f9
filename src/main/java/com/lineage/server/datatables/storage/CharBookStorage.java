package com.lineage.server.datatables.storage;

import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.templates.L1BookMark;

import java.util.ArrayList;

public interface CharBookStorage {
    void load();

    ArrayList<L1BookMark> getBookMarks(L1PcInstance p0);

    L1BookMark getBookMark(L1PcInstance p0, int p1);

    void deleteBookmark(L1PcInstance p0, String p1);

    void addBookmark(L1PcInstance p0, String p1);

    void updateBookmarkName(L1BookMark p0);

    public int getBookId(L1PcInstance pc, int x, int y, int book_id);
}
