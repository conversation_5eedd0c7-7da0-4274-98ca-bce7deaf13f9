package com.lineage.server.datatables.storage;

import com.lineage.server.templates.L1Account;

public interface AccountStorage {
	void load();

	boolean isAccountUT(final String p0);

	L1Account create(final String p0, final String p1, final String p2, final String p3, final String p4);

	boolean isAccount(final String p0);

	L1Account getAccount(final String p0);

	void updateWarehouse(final String p0, final int p1);

	void updateLastActive(final L1Account p0);

	void updateCharacterSlot(final String p0, final int p1);

	void updatePwd(final String p0, final String p1);

	void updateLan(final String p0, final boolean p1);

	void updateLan();

	void updatefp(final String p0, final int p1);

	void update_idcard(final String p0, final int p1);

	String getPalyer(final String p0);
}
