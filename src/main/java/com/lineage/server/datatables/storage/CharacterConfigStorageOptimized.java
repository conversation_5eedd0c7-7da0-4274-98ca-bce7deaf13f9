package com.lineage.server.datatables.storage;

import com.lineage.server.templates.L1ConfigOptimized;

/**
 * 角色配置存儲介面
 * 定義角色配置數據的存儲操作
 */
public interface CharacterConfigStorageOptimized {
    
    /**
     * 載入所有角色配置
     */
    void load();
    
    /**
     * 取得角色配置
     * @param objectId 角色物件ID
     * @return 角色配置，如果不存在則返回null
     */
    L1ConfigOptimized get(int objectId);
    
    /**
     * 儲存角色配置
     * @param objectId 角色物件ID
     * @param length 數據長度
     * @param data 配置數據
     */
    void store(int objectId, int length, byte[] data);
    
    /**
     * 儲存角色配置（舊版方法名）
     * @param objectId 角色物件ID
     * @param length 數據長度
     * @param data 配置數據
     */
    void storeCharacterConfig(int objectId, int length, byte[] data);
    
    /**
     * 更新角色配置
     * @param objectId 角色物件ID
     * @param length 數據長度
     * @param data 配置數據
     */
    void update(int objectId, int length, byte[] data);
    
    /**
     * 更新角色配置（舊版方法名）
     * @param objectId 角色物件ID
     * @param length 數據長度
     * @param data 配置數據
     */
    void updateCharacterConfig(int objectId, int length, byte[] data);
    
    /**
     * 刪除角色配置
     * @param objectId 角色物件ID
     */
    void delete(int objectId);
    
    /**
     * 檢查角色配置是否存在
     * @param objectId 角色物件ID
     * @return 如果存在則返回true
     */
    boolean exists(int objectId);
    
    /**
     * 取得所有角色配置
     * @return 所有角色配置的迭代器
     */
    Iterable<L1ConfigOptimized> getAll();
    
    /**
     * 取得配置數量
     * @return 配置總數
     */
    int getCount();
    
    /**
     * 清空所有配置
     */
    void clear();
    
    /**
     * 重新載入配置
     */
    void reload();
    
    /**
     * 取得統計資訊
     * @return 統計資訊物件
     */
    Object getStatistics();
    
    /**
     * 清理快取
     */
    void cleanupCache();
    
    /**
     * 關閉資源
     */
    void shutdown();
}
