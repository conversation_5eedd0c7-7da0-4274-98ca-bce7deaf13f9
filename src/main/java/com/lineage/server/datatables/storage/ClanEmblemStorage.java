package com.lineage.server.datatables.storage;

import com.lineage.server.templates.L1EmblemIcon;

public interface ClanEmblemStorage {
	void load();

	L1EmblemIcon get(final int p0);

	L1EmblemIcon getEmblem(final int p0);

	void addDeclanEmblem(final int p0, final L1EmblemIcon p1);

	void deleteIcon(final int p0);

	L1EmblemIcon storeClanIcon(final int p0, final byte[] p1, final int p2);

	void updateClanIcon(final L1EmblemIcon p0);
}
