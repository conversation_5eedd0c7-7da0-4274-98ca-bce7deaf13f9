package com.lineage.server.datatables.sql;

import java.sql.Timestamp;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import com.lineage.DatabaseFactoryLogin;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;
import com.lineage.server.datatables.storage.EzpayStorage1;

public class EzpayTable1 implements EzpayStorage1 {
	private static final Log _log;

	static {
		_log = LogFactory.getLog(EzpayTable1.class);
	}

	@Override
	public Map<Integer, int[]> ezpayInfo(final String loginName) {
		Connection co = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		final Map<Integer, int[]> list = new HashMap();
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "SELECT * FROM `shop_user` WHERE `account`=? ORDER BY `id`";
			ps = co.prepareStatement("SELECT * FROM `shop_user` WHERE `account`=? ORDER BY `id`");
			ps.setString(1, loginName.toLowerCase());
			rs = ps.executeQuery();
			while (rs.next()) {
				final int[] value = new int[4];
				final int state = rs.getInt("isget");
				if (state == 0) {
					final int key = rs.getInt("id");
					final int p_id = rs.getInt("p_id");
					final int count = rs.getInt("count");
					final int clan = rs.getInt("clanname");
					value[0] = key;
					value[1] = p_id;
					value[2] = count;
					value[3] = clan;
					list.put(Integer.valueOf(key), value);
				}
			}
		} catch (Exception e) {
			EzpayTable1._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
			SQLUtil.close(rs);
		}
		return list;
	}

	@Override
	public int[] ezpayInfo(final String loginName, final int id) {
		Connection co = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		final int[] info = new int[4];
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "SELECT * FROM `shop_user` WHERE `account`=? AND `id`=?";
			ps = co.prepareStatement("SELECT * FROM `shop_user` WHERE `account`=? AND `id`=?");
			ps.setString(1, loginName.toLowerCase());
			ps.setInt(2, id);
			rs = ps.executeQuery();
			while (rs.next()) {
				final int state = rs.getInt("isget");
				if (state == 0) {
					final int p_id = rs.getInt("p_id");
					final int count = rs.getInt("count");
					info[0] = id;
					info[1] = p_id;
					info[2] = count;
					return info;
				}
			}
		} catch (Exception e) {
			EzpayTable1._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
			SQLUtil.close(rs);
		}
		return null;
	}

	private boolean is(final String loginName, final int id) {
		Connection co = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			co = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "SELECT * FROM `shop_user` WHERE `account`=? AND `id`=?";
			ps = co.prepareStatement("SELECT * FROM `shop_user` WHERE `account`=? AND `id`=?");
			ps.setString(1, loginName.toLowerCase());
			ps.setInt(2, id);
			rs = ps.executeQuery();
			while (rs.next()) {
				final int state = rs.getInt("isget");
				if (state != 0) {
					return false;
				}
			}
		} catch (Exception e) {
			EzpayTable1._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(co);
			SQLUtil.close(rs);
		}
		return true;
	}

	@Override
	public boolean update(final String loginName, final int id, final String pcname, final String ip,
			final String clanName) {
		if (!this.is(loginName, id)) {
			return false;
		}
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			final Timestamp lastactive = new Timestamp(System.currentTimeMillis());
			con = DatabaseFactoryLogin.get().getConnection();
			final String sqlstr = "UPDATE `shop_user` SET `isget`=1,`play`=?,`time`=?,`ip`=?,`clanName`=? WHERE `id`=? AND `account`=?";
			pstm = con.prepareStatement(
					"UPDATE `shop_user` SET `isget`=1,`play`=?,`time`=?,`ip`=?,`clanName`=? WHERE `id`=? AND `account`=?");
			pstm.setString(1, pcname);
			pstm.setTimestamp(2, lastactive);
			pstm.setString(3, ip);
			pstm.setString(4, clanName);
			pstm.setInt(5, id);
			pstm.setString(6, loginName);
			pstm.execute();
			return true;
		} catch (Exception e) {
			EzpayTable1._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		return false;
	}
}
