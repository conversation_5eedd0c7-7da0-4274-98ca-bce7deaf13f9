package com.lineage.server.datatables.sql;

import java.util.Iterator;
import com.lineage.server.model.L1Object;
import com.lineage.server.world.World;
import com.lineage.server.templates.L1Item;
import java.sql.Timestamp;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.server.datatables.InnKeyTable;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.datatables.CharObjidTable;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.model.Instance.L1ItemInstance;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.Map;
import org.apache.commons.logging.Log;
import com.lineage.server.datatables.storage.DwarfForClanStorage;

public class DwarfForClanTable implements DwarfForClanStorage {
	private static final Log _log;
	private static DwarfForClanTable _instance;
	private static final Map<String, CopyOnWriteArrayList<L1ItemInstance>> _itemList;

	static {
		_log = LogFactory.getLog(DwarfForClanTable.class);
		_itemList = new ConcurrentHashMap();
	}

	public static DwarfForClanTable get() {
		if (DwarfForClanTable._instance == null) {
			DwarfForClanTable._instance = new DwarfForClanTable();
		}
		return DwarfForClanTable._instance;
	}

	@Override
	public void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		int i = 0;
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `clan_warehouse` order by item_id");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final int objid = rs.getInt("id");
				final String clan_name = rs.getString("clan_name");
				final int clan_id = CharObjidTable.get().clanObjid(clan_name);
				if (clan_id != 0) {
					final int item_id = rs.getInt("item_id");
					final long count = rs.getLong("count");
					final int enchantlvl = rs.getInt("enchantlvl");
					final int is_id = rs.getInt("is_id");
					final int durability = rs.getInt("durability");
					final int charge_count = rs.getInt("charge_count");
					final int remaining_time = rs.getInt("remaining_time");
					Timestamp last_used = null;
					try {
						last_used = rs.getTimestamp("last_used");
					} catch (Exception e2) {
						last_used = null;
					}
					final int bless = rs.getInt("bless");
					final int attr_enchant_kind = rs.getInt("attr_enchant_kind");
					final int attr_enchant_level = rs.getInt("attr_enchant_level");
					final int itemAttack = rs.getInt("ItemAttack");
					final int itemBowAttack = rs.getInt("ItemBowAttack");
					final int itemHit = rs.getInt("ItemHit");
					final int itemBowHit = rs.getInt("ItemBowHit");
					final int itemReductionDmg = rs.getInt("ItemReductionDmg");
					final int itemSp = rs.getInt("ItemSp");
					final int itemprobability = rs.getInt("Itemprobability");
					final int itemStr = rs.getInt("ItemStr");
					final int itemDex = rs.getInt("ItemDex");
					final int itemInt = rs.getInt("ItemInt");
					final int itemCon = rs.getInt("ItemCon");
					final int itemCha = rs.getInt("ItemCha");
					final int itemWis = rs.getInt("ItemWis");
					final int itemHp = rs.getInt("ItemHp");
					final int itemMp = rs.getInt("ItemMp");
					final int itemMr = rs.getInt("ItemMr");
					final int itemAc = rs.getInt("ItemAc");
					final int itemMag_Red = rs.getInt("ItemMag_Red");
					final int itemMag_Hit = rs.getInt("ItemMag_Hit");
					final int itemDg = rs.getInt("ItemDg");
					final int itemistSustain = rs.getInt("ItemistSustain");
					final int itemistStun = rs.getInt("ItemistStun");
					final int itemistStone = rs.getInt("ItemistStone");
					final int itemistSleep = rs.getInt("ItemistSleep");
					final int itemistFreeze = rs.getInt("ItemistFreeze");
					final int itemistBlind = rs.getInt("ItemistBlind");
					final int itemArmorType = rs.getInt("ItemArmorType");
					final int itemArmorLv = rs.getInt("ItemArmorLv");
					final int skilltype = rs.getInt("skilltype");
					final int skilltypelv = rs.getInt("skilltypelv");
					final int itemType = rs.getInt("ItemType");
					final int itemHpr = rs.getInt("ItemHpr");
					final int itemMpr = rs.getInt("ItemMpr");
					final int itemhppotion = rs.getInt("Itemhppotion");
					final String racegamno = rs.getString("racegamno");
					final L1ItemInstance item = new L1ItemInstance();
					item.setId(objid);
					final L1Item itemTemplate = ItemTable.get().getTemplate(item_id);
					if (itemTemplate == null) {
						errorItem(objid);
					} else {
						item.setItem(itemTemplate);
						item.setCount(count);
						item.setEquipped(false);
						item.setEnchantLevel(enchantlvl);
						item.setIdentified(is_id != 0);
						item.set_durability(durability);
						item.setChargeCount(charge_count);
						item.setRemainingTime(remaining_time);
						item.setLastUsed(last_used);
						item.setBless(bless);
						item.setAttrEnchantKind(attr_enchant_kind);
						item.setAttrEnchantLevel(attr_enchant_level);
						item.setItemAttack(itemAttack);
						item.setItemBowAttack(itemBowAttack);
						item.setItemHit(itemHit);
						item.setItemBowHit(itemBowHit);
						item.setItemReductionDmg(itemReductionDmg);
						item.setItemSp(itemSp);
						item.setItemprobability(itemprobability);
						item.setItemStr(itemStr);
						item.setItemDex(itemDex);
						item.setItemInt(itemInt);
						item.setItemCon(itemCon);
						item.setItemCha(itemCha);
						item.setItemWis(itemWis);
						item.setItemHp(itemHp);
						item.setItemMp(itemMp);
						item.setItemMr(itemMr);
						item.setItemAc(itemAc);
						item.setItemMag_Red(itemMag_Red);
						item.setItemMag_Hit(itemMag_Hit);
						item.setItemDg(itemDg);
						item.setItemistSustain(itemistSustain);
						item.setItemistStun(itemistStun);
						item.setItemistStone(itemistStone);
						item.setItemistSleep(itemistSleep);
						item.setItemistFreeze(itemistFreeze);
						item.setItemistBlind(itemistBlind);
						item.setItemArmorType(itemArmorType);
						item.setItemArmorLv(itemArmorLv);
						item.setskilltype(skilltype);
						item.setskilltypelv(skilltypelv);
						item.setItemType(itemType);
						item.setItemHpr(itemHpr);
						item.setItemMpr(itemMpr);
						item.setItemhppotion(itemhppotion);
						item.setraceGamNo(racegamno);
						item.setGamNo(rs.getInt("gamNo"));
						item.setGamNpcId(rs.getInt("gamNpcId"));
						item.setStarNpcId(rs.getString("starNpcId"));
						if (item.getItem().getItemId() == 40312) {
							InnKeyTable.checkey(item);
						}
						if (item.getItem().getItemId() == 82503) {
							InnKeyTable.checkey(item);
						}
						if (item.getItem().getItemId() == 82504) {
							InnKeyTable.checkey(item);
						}
						addItem(clan_name, item);
						++i;
					}
				} else {
					deleteItem(clan_name);
				}
			}
		} catch (SQLException e) {
			DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
		DwarfForClanTable._log
				.info("載入血盟倉庫物件清單資料數量: " + DwarfForClanTable._itemList.size() + "/" + i + "(" + timer.get() + "ms)");
	}

	private static void errorItem(final int objid) {
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("DELETE FROM `clan_warehouse` WHERE `id`=?");
			pstm.setInt(1, objid);
			pstm.execute();
		} catch (SQLException e) {
			DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
	}

	private static void addItem(final String clan_name, final L1ItemInstance item) {
		CopyOnWriteArrayList<L1ItemInstance> list = DwarfForClanTable._itemList.get(clan_name);
		if (list == null) {
			list = new CopyOnWriteArrayList();
			if (!list.contains(item)) {
				list.add(item);
			}
		} else if (!list.contains(item)) {
			list.add(item);
		}
		if (World.get().findObject(item.getId()) == null) {
			World.get().storeObject(item);
		}
		DwarfForClanTable._itemList.put(clan_name, list);
	}

	private static void deleteItem(final String clan_name) {
		final CopyOnWriteArrayList<L1ItemInstance> list = DwarfForClanTable._itemList.remove(clan_name);
		if (list != null) {
			final Iterator<L1ItemInstance> iterator = list.iterator();
			while (iterator.hasNext()) {
				final L1ItemInstance item = iterator.next();
				World.get().removeObject(item);
			}
		}
		Connection cn = null;
		PreparedStatement ps = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("DELETE FROM `clan_warehouse` WHERE `clan_name`=?");
			ps.setString(1, clan_name);
			ps.execute();
		} catch (SQLException e) {
			DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	@Override
	public CopyOnWriteArrayList<L1ItemInstance> loadItems(final String clan_name) {
		final CopyOnWriteArrayList<L1ItemInstance> list = DwarfForClanTable._itemList.get(clan_name);
		if (list != null) {
			return list;
		}
		return null;
	}

	@Override
	public void delUserItems(final String clan_name) {
		deleteItem(clan_name);
	}

	@Override
	public boolean getUserItems(final String clan_name, final int objid, final int count) {
		final CopyOnWriteArrayList<L1ItemInstance> list = DwarfForClanTable._itemList.get(clan_name);
		if (list != null) {
			if (list.size() <= 0) {
				return false;
			}
			final Iterator<L1ItemInstance> iterator = list.iterator();
			while (iterator.hasNext()) {
				final L1ItemInstance item = iterator.next();
				if (item.getId() == objid && item.getCount() >= count) {
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public void insertItem(final String clan_name, final L1ItemInstance item) {
		DwarfForClanTable._log.warn(
				"血盟:" + clan_name + " 加入血盟倉庫數據:" + item.getNumberedName(item.getCount()) + " OBJID:" + item.getId());
		addItem(clan_name, item);
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement(
					"INSERT INTO `clan_warehouse` SET `id`=?,`clan_name`=?,`item_id`= ?,`item_name`=?,`count`=?,`is_equipped`=0,`enchantlvl`=?,`is_id`=?,`durability`=?,`charge_count`=?,`remaining_time`=?,`last_used`=?,`bless`=?,`attr_enchant_kind`=?,`attr_enchant_level`=?,`ItemAttack`=?,`ItemBowAttack`=?,`ItemHit`=?,`ItemBowHit`=?,`ItemReductionDmg`=?,`ItemSp`=?,`Itemprobability`=?,`ItemStr`=?,`ItemDex`=?,`ItemInt`=?,`ItemCon`=?,`ItemCha`=?,`ItemWis`=?,`ItemHp`=?,`ItemMp`=?,`itemMr`=?,`itemAc`=?,`itemMag_Red`=?,`itemMag_Hit`=?,`itemDg`=?,`itemistSustain`=?,`itemistStun`=?,`itemistStone`=?,`itemistSleep`=?,`itemistFreeze`=?,`itemistBlind`=?,`itemArmorType`=?,`itemArmorLv`=?,`skilltype`=?,`skilltypelv`=?,`itemType`=?,`ItemHpr`=?,`ItemMpr`=?,`Itemhppotion`=?,`gamNo`=?,`gamNpcId` = ?,`starNpcId`=?,`racegamno`=?");
			int i = 0;
			pstm.setInt(++i, item.getId());
			pstm.setString(++i, clan_name);
			pstm.setInt(++i, item.getItemId());
			pstm.setString(++i, item.getItem().getName());
			pstm.setLong(++i, item.getCount());
			pstm.setInt(++i, item.getEnchantLevel());
			pstm.setInt(++i, item.isIdentified() ? 1 : 0);
			pstm.setInt(++i, item.get_durability());
			pstm.setInt(++i, item.getChargeCount());
			pstm.setInt(++i, item.getRemainingTime());
			pstm.setTimestamp(++i, item.getLastUsed());
			pstm.setInt(++i, item.getBless());
			pstm.setInt(++i, item.getAttrEnchantKind());
			pstm.setInt(++i, item.getAttrEnchantLevel());
			pstm.setInt(++i, item.getItemAttack());
			pstm.setInt(++i, item.getItemBowAttack());
			pstm.setInt(++i, item.getItemHit());
			pstm.setInt(++i, item.getItemBowHit());
			pstm.setInt(++i, item.getItemReductionDmg());
			pstm.setInt(++i, item.getItemSp());
			pstm.setInt(++i, item.getItemprobability());
			pstm.setInt(++i, item.getItemStr());
			pstm.setInt(++i, item.getItemDex());
			pstm.setInt(++i, item.getItemInt());
			pstm.setInt(++i, item.getItemCon());
			pstm.setInt(++i, item.getItemCha());
			pstm.setInt(++i, item.getItemWis());
			pstm.setInt(++i, item.getItemHp());
			pstm.setInt(++i, item.getItemMp());
			pstm.setInt(++i, item.getItemMr());
			pstm.setInt(++i, item.getItemAc());
			pstm.setInt(++i, item.getItemMag_Red());
			pstm.setInt(++i, item.getItemMag_Hit());
			pstm.setInt(++i, item.getItemDg());
			pstm.setInt(++i, item.getItemistSustain());
			pstm.setInt(++i, item.getItemistStun());
			pstm.setInt(++i, item.getItemistStone());
			pstm.setInt(++i, item.getItemistSleep());
			pstm.setInt(++i, item.getItemistFreeze());
			pstm.setInt(++i, item.getItemistBlind());
			pstm.setInt(++i, item.getItemArmorType());
			pstm.setInt(++i, item.getItemArmorLv());
			pstm.setInt(++i, item.getskilltype());
			pstm.setInt(++i, item.getItemType());
			pstm.setInt(++i, item.getItemHpr());
			pstm.setInt(++i, item.getItemMpr());
			pstm.setInt(++i, item.getItemhppotion());
			pstm.setInt(++i, item.getskilltypelv());
			pstm.setInt(++i, item.getGamNo());
			pstm.setInt(++i, item.getGamNpcId());
			pstm.setString(++i, item.getStarNpcId());
			pstm.setString(++i, item.getraceGamNo());
			pstm.execute();
		} catch (SQLException e) {
			DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
	}

	@Override
	public void updateItem(final L1ItemInstance item) {
		DwarfForClanTable._log.warn(" 更新血盟倉庫數據:" + item.getNumberedName(item.getCount()) + " OBJID:" + item.getId());
		Connection con = null;
		PreparedStatement pstm = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("UPDATE `clan_warehouse` SET `count`=? WHERE `id`=?");
			pstm.setLong(1, item.getCount());
			pstm.setInt(2, item.getId());
			pstm.execute();
		} catch (SQLException e) {
			DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
	}

	@Override
	public void deleteItem(final String clan_name, final L1ItemInstance item) {
		final CopyOnWriteArrayList<?> list = DwarfForClanTable._itemList.get(clan_name);
		if (list != null) {
			DwarfForClanTable._log.warn("血盟:" + clan_name + " 血盟倉庫物品移出:" + item.getNumberedName(item.getCount())
					+ " OBJID:" + item.getId());
			list.remove(item);
			Connection con = null;
			PreparedStatement pstm = null;
			try {
				con = DatabaseFactory.get().getConnection();
				pstm = con.prepareStatement("DELETE FROM `clan_warehouse` WHERE `id`=?");
				pstm.setInt(1, item.getId());
				pstm.execute();
			} catch (SQLException e) {
				DwarfForClanTable._log.error(e.getLocalizedMessage(), e);
			} finally {
				SQLUtil.close(pstm);
				SQLUtil.close(con);
			}
		}
	}

	@Override
	public boolean getUserItem(final int objid) {
		final Iterator<CopyOnWriteArrayList<L1ItemInstance>> iterator = DwarfForClanTable._itemList.values().iterator();
		while (iterator.hasNext()) {
			final CopyOnWriteArrayList<L1ItemInstance> list = iterator.next();
			final Iterator<L1ItemInstance> iterator2 = list.iterator();
			while (iterator2.hasNext()) {
				final L1ItemInstance item = iterator2.next();
				if (item.getId() == objid) {
					return true;
				}
			}
		}
		return false;
	}
}
