package com.lineage.server.datatables;

import com.lineage.server.templates.L1Item;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.templates.L1Npc;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.utils.PerformanceTimer;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.L1TeleportLoc;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.logging.Log;

public class NpcTeleportTable {
	private static final Log _log;
	private static NpcTeleportTable _instance;
	private static final Map<String, HashMap<Integer, L1TeleportLoc>> _teleportLocList;
	private static final Map<Integer, Integer> _timeMap;
	private static final Map<Integer, Integer> _partyMap;
	private static final Map<Integer, HashMap<String, L1TeleportLoc>> _srcMap;

	static {
		_log = LogFactory.getLog(NpcTeleportTable.class);
		_teleportLocList = new HashMap();
		_timeMap = new HashMap();
		_partyMap = new HashMap();
		_srcMap = new HashMap();
	}

	public static NpcTeleportTable get() {
		if (NpcTeleportTable._instance == null) {
			NpcTeleportTable._instance = new NpcTeleportTable();
		}
		return NpcTeleportTable._instance;
	}

	public void load() {
		final PerformanceTimer timer = new PerformanceTimer();
		Connection cn = null;
		PreparedStatement ps = null;
		ResultSet rs = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("SELECT * FROM `npcaction_teleport` ORDER BY `id`");
			rs = ps.executeQuery();
			while (rs.next()) {
				final int id = rs.getInt("id");
				final String name = rs.getString("name");
				final String orderid = rs.getString("orderid");
				final int locx = rs.getInt("locx");
				final int locy = rs.getInt("locy");
				final int mapid = rs.getInt("mapid");
				final int itemid = rs.getInt("itemid");
				final int price = rs.getInt("price");
				final int time = rs.getInt("time");
				final int user = rs.getInt("user");
				final int min = rs.getInt("min");
				final int max = rs.getInt("max");
				final String note = rs.getString("note");
				final L1TeleportLoc teleportLoc = new L1TeleportLoc();
				teleportLoc.set_id(id);
				teleportLoc.set_name(name);
				teleportLoc.set_orderid(orderid);
				teleportLoc.set_locx(locx);
				teleportLoc.set_locy(locy);
				teleportLoc.set_mapid(mapid);
				teleportLoc.set_itemid(itemid);
				teleportLoc.set_price(price);
				teleportLoc.set_user(user);
				teleportLoc.set_min(min);
				teleportLoc.set_max(max);
				if (name.equalsIgnoreCase(orderid)) {
					final String[] set = note.replace(" ", "").split(",");
					final int[] result = new int[set.length];
					int i = 0;
					while (i < result.length) {
						result[i] = Integer.parseInt(set[i]);
						++i;
					}
					final int[] array;
					final int length = (array = result).length;
					int j = 0;
					while (j < length) {
						final int npcid = array[j];
						final L1Npc npc = NpcTable.get().getTemplate(npcid);
						if (npc == null) {
							this.del(id);
						} else if (!npc.getImpl().equalsIgnoreCase("L1Teleporter")) {
							this.del(id);
						} else {
							HashMap<String, L1TeleportLoc> list = NpcTeleportTable._srcMap.get(Integer.valueOf(npcid));
							if (list != null) {
								list.put(orderid, teleportLoc);
							} else {
								list = new HashMap();
								list.put(orderid, teleportLoc);
							}
							NpcTeleportTable._srcMap.put(Integer.valueOf(npcid), list);
						}
						++j;
					}
				} else {
					if (price <= 0) {
						continue;
					}
					if (time != 0) {
						NpcTeleportTable._timeMap.put(Integer.valueOf(mapid), Integer.valueOf(time));
					}
					teleportLoc.set_time(time);
					if (user != 0) {
						NpcTeleportTable._partyMap.put(Integer.valueOf(mapid), Integer.valueOf(user));
					}
					this.addList(orderid, teleportLoc);
				}
			}
		} catch (SQLException e) {
			NpcTeleportTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
		NpcTeleportTable._log
				.info("載入NPC傳送點設置數量: " + NpcTeleportTable._teleportLocList.size() + "(" + timer.get() + "ms)");
		NpcTeleportTable._log.info("載入時間地圖設置數量: " + NpcTeleportTable._timeMap.size() + "(" + timer.get() + "ms)");
		NpcTeleportTable._log.info("載入團隊地圖設置數量: " + NpcTeleportTable._partyMap.size() + "(" + timer.get() + "ms)");
		NpcTeleportTable._log.info("載入官方傳送點設置數量: " + NpcTeleportTable._srcMap.size() + "(" + timer.get() + "ms)");
	}

	private void del(final int id) {
		Connection cn = null;
		PreparedStatement ps = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement("DELETE FROM `npcaction_teleport` WHERE `id`=?");
			ps.setInt(1, id);
			ps.execute();
		} catch (SQLException e) {
			NpcTeleportTable._log.error(e.getLocalizedMessage(), e);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}

	private void addList(final String orderid, final L1TeleportLoc teleportLoc) {
		HashMap<Integer, L1TeleportLoc> map = NpcTeleportTable._teleportLocList.get(orderid);
		int id = 0;
		if (map != null) {
			id = map.size();
			map.put(new Integer(id), teleportLoc);
		} else {
			map = new HashMap();
			map.put(new Integer(id), teleportLoc);
		}
		NpcTeleportTable._teleportLocList.put(orderid, map);
	}

	public Map<String, HashMap<Integer, L1TeleportLoc>> get_locs() {
		return NpcTeleportTable._teleportLocList;
	}

	public Integer isPartyMap(final int mapid) {
		return NpcTeleportTable._partyMap.get(Integer.valueOf(mapid));
	}

	public Map<Integer, Integer> partyMaps() {
		return NpcTeleportTable._partyMap;
	}

	public boolean isTimeMap(final int mapid) {
		return NpcTeleportTable._timeMap.get(Integer.valueOf(mapid)) != null;
	}

	public Map<Integer, Integer> timeMaps() {
		return NpcTeleportTable._timeMap;
	}

	public HashMap<Integer, L1TeleportLoc> get_teles(final String orderid) {
		if (NpcTeleportTable._teleportLocList.get(orderid) != null) {
			return NpcTeleportTable._teleportLocList.get(orderid);
		}
		return null;
	}

	public L1TeleportLoc get_loc(final String orderid, final int id) {
		final HashMap<Integer, L1TeleportLoc> map = NpcTeleportTable._teleportLocList.get(orderid);
		if (map != null) {
			return map.get(new Integer(id));
		}
		return null;
	}

	public boolean get_teleport(final L1PcInstance pc, final String orderid, final int npcid) {
		final HashMap<String, L1TeleportLoc> map = NpcTeleportTable._srcMap.get(Integer.valueOf(npcid));
		if (map != null) {
			final L1TeleportLoc t = map.get(orderid);
			if (t != null) {
				if (t.get_price() > 0) {
					if (!pc.getInventory().checkItem(t.get_itemid(), t.get_price())) {
						final L1Item itemtmp = ItemTable.get().getTemplate(t.get_itemid());
						pc.sendPackets(new S_ServerMessage(337, itemtmp.getNameId()));
						return true;
					}
					pc.getInventory().consumeItem(t.get_itemid(), t.get_price());
				}
				L1Teleport.teleport(pc, t.get_locx(), t.get_locy(), (short) t.get_mapid(), 5, true);
				return true;
			}
		}
		return false;
	}

	public void set(final String orderid, final int locx, final int locy, final int mapid, final int price,
			String npcids) {
		npcids = (npcids.equals("") ? "---" : npcids);
		Connection cn = null;
		PreparedStatement ps = null;
		try {
			cn = DatabaseFactory.get().getConnection();
			ps = cn.prepareStatement(
					"INSERT INTO `npcaction_teleport` SET `name`=?,`orderid`=?,`locx`=?,`locy`=?,`mapid`=?,`itemid`=?,`price`=?,`time`=?,`user`=?,`min`=?,`max`=?,`note`=?");
			int i = 0;
			ps.setString(++i, orderid);
			ps.setString(++i, orderid);
			ps.setInt(++i, locx);
			ps.setInt(++i, locy);
			ps.setInt(++i, mapid);
			ps.setInt(++i, 40308);
			ps.setInt(++i, price);
			ps.setInt(++i, 0);
			ps.setInt(++i, 0);
			ps.setInt(++i, 0);
			ps.setInt(++i, 200);
			ps.setString(++i, npcids);
			ps.execute();
		} catch (SQLException e) {
			System.out.println("npcids:" + npcids);
		} finally {
			SQLUtil.close(ps);
			SQLUtil.close(cn);
		}
	}
}
