package com.lineage.server.model.drop;

import com.lineage.config.ConfigRate;
import com.lineage.server.datatables.*;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Inventory;
import com.lineage.server.templates.*;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;
import java.util.stream.Collectors;

public class SetDrop implements SetDropExecutor {
    private static final Log _log;
    private static final Random _random;
    private static Map<Integer, ArrayList<L1Drop>> _droplist;
    private static Map<Integer, ArrayList<L1DropMap>> _droplistX;
    private static Map<Integer, L1DropMob> _moblist;

    static {
        _log = LogFactory.getLog(SetDrop.class);
        _random = new Random();
    }

    @Override
    public void addDropMap(Map<Integer, ArrayList<L1Drop>> droplists) {
        if (SetDrop._droplist != null) {
            SetDrop._droplist.clear();
        }
        SetDrop._droplist = droplists;
    }

    @Override
    public void addDropMapX(Map<Integer, ArrayList<L1DropMap>> droplists) {
        if (SetDrop._droplistX != null) {
            SetDrop._droplistX.clear();
        }
        SetDrop._droplistX = droplists;
    }

    @Override
    public void addDropMob(Map<Integer, L1DropMob> droplists) {
        if (SetDrop._moblist != null) {
            SetDrop._moblist.clear();
        }
        SetDrop._moblist = droplists;
    }

    @Override
    public void setDrop(L1NpcInstance npc, L1Inventory inventory) {
        setDrop(npc, inventory, 0.0);
    }

    @Override
    public void setDrop(L1NpcInstance npc, L1Inventory inventory, double random) {
        int mobId = npc.getNpcTemplate().get_npcId();
        int mapid = npc.getMapId();
        ArrayList<L1DropMap> droplistX = SetDrop._droplistX.get(Integer.valueOf(mapid));
        if (droplistX != null) {
            setDrop(npc, inventory, droplistX);
        }
        if (SetDrop._moblist != null) {
            setDrop(npc, inventory, SetDrop._moblist);
        }
        ArrayList<L1Drop> dropList = SetDrop._droplist.get(Integer.valueOf(mobId));
        if (dropList == null) {
            return;
        }
        double droprate = ConfigRate.RATE_DROP_ITEMS;
        if (droprate <= 0.0) {
            droprate = 0.0;
        }
        droprate += random;
        double adenarate = ConfigRate.RATE_DROP_ADENA;
        if (adenarate <= 0.0) {
            adenarate = 0.0;
        }
        if (droprate <= 0.0 && adenarate <= 0.0) {
            return;
        }
        Iterator<L1Drop> iterator = dropList.iterator();
        while (iterator.hasNext()) {
            L1Drop drop = iterator.next();
            int itemId = drop.getItemid();
            if (adenarate == 0.0 && itemId == 40308) {
                continue;
            }
            int randomChance = SetDrop._random.nextInt(1000000) + 1;
            double rateOfMapId = MapsTable.get().getDropRate(npc.getMapId());
            double rateOfItem = DropItemTable.get().getDropRate(itemId);
            if (droprate == 0.0) {
                continue;
            }
            if (drop.getChance() * droprate * rateOfMapId * rateOfItem < randomChance) {
                continue;
            }
            double amount = DropItemTable.get().getDropAmount(itemId);
            long min = (long) (drop.getMin() * amount);
            long max = (long) (drop.getMax() * amount);
            long itemCount = min;
            long addCount = max - min + 1L;
            if (addCount > 1L) {
                itemCount += SetDrop._random.nextInt((int) addCount);
            }
            if (itemId == 40308) {
                itemCount = (long) (itemCount * adenarate);
            }
            if (itemCount < 0L) {
                itemCount = 0L;
            }
            if (itemCount > 2000000000L) {
                itemCount = 2000000000L;
            }
            if (itemCount > 0L) {
                //Kevin 丹丹 檢查這隻怪物是否有重複掉落(防呆)
                if (chexkItemAdd(inventory, itemId)) {
                    if (DropLimitTable.getInstance().giveCheck(itemId, (int) itemCount)) {
                        continue;
                    }
                    additem(npc, inventory, itemId, itemCount);
                }
            } else {
                SetDrop._log.error("NPC加入背包物件數量為0(" + mobId + " itemId: " + itemId + ")");
            }
        }
    }

    private void setDrop(L1NpcInstance npc, L1Inventory inventory, Map<Integer, L1DropMob> moblist) {
        Iterator<Integer> iterator = moblist.keySet().iterator();
        while (iterator.hasNext()) {
            Integer key = iterator.next();
            int random = SetDrop._random.nextInt(1000) + 1;
            int week = moblist.get(key).getweek();
            int starttime = moblist.get(key).getStarttime();
            int unitytime = moblist.get(key).getUnitytime();
            Calendar date = Calendar.getInstance();
            int nowWeek = date.get(7) - 1;
            int nowHour = date.get(11);
            if (nowWeek == 0) {
                nowWeek += 7;
            }
            if (moblist.get(key).getChance() < random) {
                continue;
            }
            if (week > 0 && starttime < 0 && unitytime < 0) {
                if (week != nowWeek) {
                    continue;
                }
            } else if (week > 0 && starttime >= 0 && unitytime >= 0) {
                if (week != nowWeek || nowHour < starttime) {
                    continue;
                }
                if (nowHour >= unitytime) {
                    continue;
                }
            } else if (week < 0 && starttime >= 0 && unitytime >= 0) {
                if (nowHour < starttime) {
                    continue;
                }
                if (nowHour >= unitytime) {
                    continue;
                }
            }
            int min = moblist.get(key).getMin();
            int max = moblist.get(key).getMax();
            int addCount = max - min + 1;
            if (addCount > 1) {
                min += SetDrop._random.nextInt(addCount);
            }
            if (min > 0) {
                //Kevin 丹丹 檢查這隻怪物是否有重複掉落(防呆)
                if (chexkItemAdd(inventory, key.intValue())) {
                    if (DropLimitTable.getInstance().giveCheck(key.intValue(), min)) {
                        continue;
                    }
                    additem(inventory, key.intValue(), min);
                }
            } else {
                SetDrop._log.error("NPC加入背包物件數量為0(" + npc.getNpcId() + " itemId: " + key + ") 掉落");
            }
        }
    }

    private void additem(L1Inventory inventory, int itemId, long itemCount) {
        additem(inventory, itemId, itemCount, 0, 0);
    }

    private void additem(L1Inventory inventory, int itemId, long itemCount, int enchant_min,
                         int enchant_max) {
        try {
            L1Item tmp = ItemTable.get().getTemplate(itemId);
            if (tmp == null) {
                SetDrop._log.error("掉落物品設置錯誤(無這編號物品): " + itemId);
                return;
            }
            if (tmp.isStackable()) {
                L1ItemInstance item = ItemTable.get().createItem(itemId);
                if (item != null) {
                    item.setCount(itemCount);
                    if (enchant_min > 0 && enchant_max > 0) {
                        if (enchant_min == enchant_max) {
                            item.setEnchantLevel(enchant_min);
                        } else {
                            item.setEnchantLevel(SetDrop._random.nextInt(enchant_max) + enchant_min);
                        }
                    }
                    inventory.storeItem(item);
                }
            } else {
                int i = 0;
                while (i < itemCount) {
                    L1ItemInstance item2 = ItemTable.get().createItem(itemId);
                    if (item2 != null) {
                        item2.setCount(1L);
                        if (enchant_min > 0 && enchant_max > 0) {
                            if (enchant_min == enchant_max) {
                                item2.setEnchantLevel(enchant_min);
                            } else {
                                item2.setEnchantLevel(SetDrop._random.nextInt(enchant_max) + enchant_min);
                            }
                        }
                        inventory.storeItem(item2);
                    }
                    ++i;
                }
            }
        } catch (Exception e) {
            SetDrop._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void setDrop(L1NpcInstance npc, L1Inventory inventory, ArrayList<L1DropMap> dropListX) {
        double droprate = ConfigRate.RATE_DROP_ITEMS;
        if (droprate <= 0.0) {
            droprate = 0.0;
        }
        double adenarate = ConfigRate.RATE_DROP_ADENA;
        if (adenarate <= 0.0) {
            adenarate = 0.0;
        }
        if (droprate <= 0.0 && adenarate <= 0.0) {
            return;
        }
        Iterator<L1DropMap> iterator = dropListX.iterator();
        while (iterator.hasNext()) {
            L1DropMap drop = iterator.next();
            int itemId = drop.getItemid();
            if (adenarate == 0.0 && itemId == 40308) {
                continue;
            }
            if (npc.getMapId() != drop.get_mapid()) {
                continue;
            }
            int randomChance = SetDrop._random.nextInt(1000000) + 1;
            double rateOfMapId = MapsTable.get().getDropRate(npc.getMapId());
            double rateOfItem = DropItemTable.get().getDropRate(itemId);
            boolean noadd = drop.getChance() * droprate * rateOfMapId * rateOfItem < randomChance;
            if (droprate == 0.0) {
                continue;
            }
            if (noadd) {
                continue;
            }
            double amount = DropItemTable.get().getDropAmount(itemId);
            long min = (long) (drop.getMin() * amount);
            long max = (long) (drop.getMax() * amount);
            long itemCount = min;
            long addCount = max - min + 1L;
            if (addCount > 1L) {
                itemCount += SetDrop._random.nextInt((int) addCount);
            }
            if (itemId == 40308) {
                itemCount = (long) (itemCount * adenarate);
            }
            if (itemCount < 0L) {
                itemCount = 0L;
            }
            if (itemCount > 2000000000L) {
                itemCount = 2000000000L;
            }
            if (itemCount > 0L) {
                //Kevin 丹丹 檢查這隻怪物是否有重複掉落(防呆)
                if (chexkItemAdd(inventory, itemId)) {
                    if (DropLimitTable.getInstance().giveCheck(itemId, (int) itemCount)) {
                        continue;
                    }
                    additem(npc, inventory, itemId, itemCount);
                }
            } else {
                SetDrop._log.error("NPC加入背包物件數量為0(" + npc.getNpcId() + " itemId: " + itemId + ") 指定地圖");
            }
        }
    }

    private void additem(L1NpcInstance npc, L1Inventory inventory, int itemId, long itemCount) {
        try {
            L1Item tmp = ItemTable.get().getTemplate(itemId);
            if (tmp == null) {
                SetDrop._log.error("掉落物品設置錯誤(無這編號物品): " + itemId);
                return;
            }
            if (tmp.isStackable()) {
                L1ItemInstance item = ItemTable.get().createItem(itemId);
                if (item != null) {
                    item.setCount(itemCount);
                    ArrayList<L1DropEnchant> datalist = DropItemEnchantTable.get().getDatalist(npc.getNpcId());
                    if (datalist != null) {
                        Iterator<L1DropEnchant> iterator = datalist.iterator();
                        while (iterator.hasNext()) {
                            L1DropEnchant data = iterator.next();
                            if (data.getItemid() == itemId) {
                                int level = DropItemEnchantTable.get().getEnchant(data);
                                item.setEnchantLevel(level);
                                break;
                            }
                        }
                    }
                    inventory.storeItem(item);
                }
            } else {
                int i = 0;
                while (i < itemCount) {
                    L1ItemInstance item2 = ItemTable.get().createItem(itemId);
                    if (item2 != null) {
                        item2.setCount(1L);
                        ArrayList<L1DropEnchant> datalist2 = DropItemEnchantTable.get()
                                .getDatalist(npc.getNpcId());
                        if (datalist2 != null) {
                            Iterator<L1DropEnchant> iterator2 = datalist2.iterator();
                            while (iterator2.hasNext()) {
                                L1DropEnchant data2 = iterator2.next();
                                if (data2.getItemid() == itemId) {
                                    int level2 = DropItemEnchantTable.get().getEnchant(data2);
                                    item2.setEnchantLevel(level2);
                                    break;
                                }
                            }
                        }
                        inventory.storeItem(item2);
                    }
                    ++i;
                }
            }
        } catch (Exception e) {
            SetDrop._log.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     * 檢查這隻怪物是否有重複掉落
     * Kevin 丹丹 檢查這隻怪物是否有重複掉落(防呆)
     *
     * @param inventory
     * @param itemId
     * @return
     */
    private boolean chexkItemAdd(L1Inventory inventory, int itemId) {
        List<Integer> npcBackpackItemIdlist = inventory.getItems().stream().map(L1ItemInstance::getItemId).collect(Collectors.toList());
        if (npcBackpackItemIdlist.contains(itemId)) {
            return false;
        }
        return true;
    }
}
