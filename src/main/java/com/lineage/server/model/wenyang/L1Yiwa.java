package com.lineage.server.model.wenyang;


import com.lineage.server.datatables.YiwaTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_GreenMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1Yiwawen;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

/**
 * 伊娃纹样专用
 * by 大陸技術
 *
 * <AUTHOR>
 */
public class L1Yiwa {
    private static final Log _log = LogFactory.getLog(L1Yiwa.class
            .getName());
    static Random _random = new Random();

    public static boolean yiwa(String cmd, L1PcInstance pc) {
        try {
            if (cmd.equalsIgnoreCase("wenyangyw")) {
                WenyangUp(pc);
            } else if (cmd.equalsIgnoreCase("ywjilv")) {
                WenyangRnd(pc);
            } else if (cmd.equalsIgnoreCase("ywstart")) {
                WenyangLevel(pc);
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private static void WenyangLevel(L1PcInstance pc) {
        try {


            if (pc.getyiwacishu() >= 20) {
                pc.sendPackets(new S_SystemMessage("您的伊娃紋樣強化次數已用完"));
                return;
            }

            if (pc.getyiwa() == 33) {
                pc.sendPackets(new S_SystemMessage("已達到max等級無法繼續強化"));
                return;
            }

            if (pc.getyiwajilvdj() == 0 && pc.getzhufudianshu() < 200) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足200"));
                return;
            }

            if (pc.getyiwajilvdj() == 1 && pc.getzhufudianshu() < 400) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足400"));
                return;
            }

            if (pc.getyiwajilvdj() == 2 && pc.getzhufudianshu() < 800) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足800"));
                return;
            }

            if (pc.getyiwajilvdj() == 3 && pc.getzhufudianshu() < 1600) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足1600"));
                return;
            }

            L1Yiwawen yiwa1 = YiwaTable.get().getTemplate(pc.getyiwa());

            if (yiwa1 != null) {
                YiwaTable.get().setYIWA(pc, pc.getyiwa());
            }

            switch (pc.getyiwa()) {
                case 0:
                    WenyangD(pc);
                    break;
                case 1:
                    WenyangD(pc);
                    break;
                case 2:
                    WenyangD(pc);
                    break;
                case 3:
                    WenyangD(pc);
                    break;
                case 4:
                    WenyangD(pc);
                    break;
                case 5:
                    WenyangD(pc);
                    break;
                case 6:
                    WenyangD(pc);
                    break;
                case 7:
                    WenyangD(pc);
                    break;
                case 8:
                    WenyangD(pc);
                    break;
                case 9:
                    WenyangD(pc);
                    break;
                case 10:
                    WenyangD(pc);
                    break;
                case 11:
                    WenyangD(pc);
                    break;
                case 12:
                    WenyangD(pc);
                    break;
                case 13:
                    WenyangD(pc);
                    break;
                case 14:
                    WenyangD(pc);
                    break;
                case 15:
                    WenyangD(pc);
                    break;
                case 16:
                    WenyangD(pc);
                    break;
                case 17:
                    WenyangD(pc);
                    break;
                case 18:
                    WenyangD(pc);
                    break;
                case 19:
                    WenyangD(pc);
                    break;
                case 20:
                    WenyangD(pc);
                    break;
                case 21:
                    WenyangD(pc);
                    break;
                case 22:
                    WenyangD(pc);
                    break;
                case 23:
                    WenyangD(pc);
                    break;
                case 24:
                    WenyangD(pc);
                    break;
                case 25:
                    WenyangD(pc);
                    break;
                case 26:
                    WenyangD(pc);
                    break;
                case 27:
                    WenyangD(pc);
                    break;
                case 28:
                    WenyangD(pc);
                    break;
                case 29:
                    WenyangD(pc);
                    break;
                case 30:
                    WenyangD(pc);
                    break;
                case 31:
                    WenyangD(pc);
                    break;
                case 32:
                    WenyangD(pc);
                    break;
                case 33:
                    WenyangD(pc);
                    break;
            }


            if (pc.getyiwajilvdj() == 0) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 200);
            } else if (pc.getyiwajilvdj() == 1) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 400);
            } else if (pc.getyiwajilvdj() == 2) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 800);
            } else if (pc.getyiwajilvdj() == 3) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 1600);
            }

            int random = _random.nextInt(100);
            if (pc.getyiwa() >= 0) {
                if (random < pc.getyiwajilv3()) {
                    if (pc.getyiwa() >= 32) {
                        pc.setyiwa(pc.getyiwa() + 1);
                        pc.setyiwacishu(pc.getyiwacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }

                    if (pc.getyiwa() >= 31) {
                        pc.setyiwa(pc.getyiwa() + 2);
                        pc.setyiwacishu(pc.getyiwacishu() + 2);
                        pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 2"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setyiwa(pc.getyiwa() + 3);
                    pc.setyiwacishu(pc.getyiwacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 3"));
                    WenyangUp(pc);
                } else if (random < pc.getyiwajilv2()) {
                    if (pc.getyiwa() >= 31) {
                        pc.setyiwa(pc.getyiwa() + 1);
                        pc.setyiwacishu(pc.getyiwacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setyiwa(pc.getyiwa() + 2);
                    pc.setyiwacishu(pc.getyiwacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 2"));
                    WenyangUp(pc);
                } else if (random < pc.getyiwajilv1()) {
                    pc.setyiwa(pc.getyiwa() + 1);
                    pc.setyiwacishu(pc.getyiwacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("伊娃紋樣 + 1"));
                    WenyangUp(pc);
                } else {
                    pc.setyiwacishu(pc.getyiwacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("強化失敗扣除點數"));
                    WenyangUp(pc);
                }
            }

            pc.save();

            L1Yiwawen yiwa = YiwaTable.get().getTemplate(pc.getyiwa());

            if (yiwa != null) {
                YiwaTable.get().addYIWA(pc, pc.getyiwa());
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangD(L1PcInstance pc) {
        try {
            String[] data = new String[33];

            if (pc.getyiwa() == 0) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }

                data[2] = String.valueOf("5000");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5001");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5000");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5001");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 1) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5001");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5001");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 2) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5002");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 3) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5003");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 4) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5004");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 5) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5005");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 6) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 7) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 8) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 9) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 10) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 11) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 12) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 13) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 14) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 15) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 16) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 17) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 18) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 19) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 20) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 21) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 22) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 23) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 24) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 25) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 26) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 27) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5034");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 28) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5035");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 29) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5036");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 30) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5030");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 31) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5033");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5031");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5033");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            } else if (pc.getyiwa() == 32) {
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5033");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5033");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5045");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
                Thread.sleep(300);
                if (pc.getyiwacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
                }

                if (pc.getyiwajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyiwajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyiwajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyiwajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5033");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangRnd(L1PcInstance pc) {
        if (pc.getyiwajilvdj() == 0) {
            pc.setyiwajilv1(pc.getyiwajilv1() - 5.3);
            pc.setyiwajilv2(pc.getyiwajilv2() + 5.3);
            pc.setyiwajilv3(pc.getyiwajilv3() + 3.9);
            pc.setyiwajilvdj(pc.getyiwajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyiwajilvdj() == 1) {
            pc.setyiwajilv1(pc.getyiwajilv1() - 8.1);
            pc.setyiwajilv2(pc.getyiwajilv2() + 7.3);
            pc.setyiwajilv3(pc.getyiwajilv3() + 5.3);
            pc.setyiwajilvdj(pc.getyiwajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyiwajilvdj() == 2) {
            pc.setyiwajilv1(pc.getyiwajilv1() - 12.9);
            pc.setyiwajilv2(pc.getyiwajilv2() + 9.5);
            pc.setyiwajilv3(pc.getyiwajilv3() + 7.9);
            pc.setyiwajilvdj(pc.getyiwajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyiwajilvdj() == 3) {
            pc.setyiwajilv1(61.5);
            pc.setyiwajilv2(23.2);
            pc.setyiwajilv3(15.9);
            pc.setyiwajilvdj(0);
            WenyangUp(pc);
        }

    }

    public static void WenyangUp(L1PcInstance pc) {
        String[] data = new String[5];
        if (pc.getyiwacishu() >= 0) {//判定以强化次数
            data[0] = String.valueOf("(" + pc.getyiwacishu() + "/20)");//赋予对话当显示次数
        }

        if (pc.getzhufudianshu() >= 0) {
            data[1] = String.valueOf(pc.getzhufudianshu());
        }

        switch (pc.getyiwa()) {//判定纹样等级调用IMG
            case 0:
                data[2] = String.valueOf("5000");
                break;
            case 1:
                data[2] = String.valueOf("5001");
                break;
            case 2:
                data[2] = String.valueOf("5002");
                break;
            case 3:
                data[2] = String.valueOf("5003");
                break;
            case 4:
                data[2] = String.valueOf("5004");
                break;
            case 5:
                data[2] = String.valueOf("5005");
                break;
            case 6:
                data[2] = String.valueOf("5006");
                break;
            case 7:
                data[2] = String.valueOf("5007");
                break;
            case 8:
                data[2] = String.valueOf("5008");
                break;
            case 9:
                data[2] = String.valueOf("5009");
                break;
            case 10:
                data[2] = String.valueOf("5010");
                break;
            case 11:
                data[2] = String.valueOf("5011");
                break;
            case 12:
                data[2] = String.valueOf("5012");
                break;
            case 13:
                data[2] = String.valueOf("5013");
                break;
            case 14:
                data[2] = String.valueOf("5014");
                break;
            case 15:
                data[2] = String.valueOf("5015");
                break;
            case 16:
                data[2] = String.valueOf("5016");
                break;
            case 17:
                data[2] = String.valueOf("5017");
                break;
            case 18:
                data[2] = String.valueOf("5018");
                break;
            case 19:
                data[2] = String.valueOf("5019");
                break;
            case 20:
                data[2] = String.valueOf("5020");
                break;
            case 21:
                data[2] = String.valueOf("5021");
                break;
            case 22:
                data[2] = String.valueOf("5022");
                break;
            case 23:
                data[2] = String.valueOf("5023");
                break;
            case 24:
                data[2] = String.valueOf("5024");
                break;
            case 25:
                data[2] = String.valueOf("5025");
                break;
            case 26:
                data[2] = String.valueOf("5026");
                break;
            case 27:
                data[2] = String.valueOf("5034");
                break;
            case 28:
                data[2] = String.valueOf("5035");
                break;
            case 29:
                data[2] = String.valueOf("5036");
                break;
            case 30:
                data[2] = String.valueOf("5030");
                break;
            case 31:
                data[2] = String.valueOf("5031");
                break;
            case 32:
                data[2] = String.valueOf("5045");
                break;
            case 33:
                data[2] = String.valueOf("5033");
                break;

            default:
                data[2] = null;
                break;
        }

        if (pc.getyiwajilv1() >= 0.0 && pc.getyiwajilv2() >= 0.0 && pc.getyiwajilv3() >= 0.0) {
            data[3] = String.valueOf("1(" + pc.getyiwajilv1() + "%)  2(" + pc.getyiwajilv2() + "%)  3(" + pc.getyiwajilv3() + "%)");
        }

        if (pc.getyiwajilvdj() == 0) {
            data[4] = String.valueOf("5990");
        } else if (pc.getyiwajilvdj() == 1) {
            data[4] = String.valueOf("5991");
        } else if (pc.getyiwajilvdj() == 2) {
            data[4] = String.valueOf("5992");
        } else if (pc.getyiwajilvdj() == 3) {
            data[4] = String.valueOf("5993");
        }

        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyw", data));
    }


}
