package com.lineage.server.model.wenyang;


import com.lineage.server.datatables.shahaTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_GreenMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1shahawen;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

/**
 * 沙哈纹样专用
 * by 大陸技術
 *
 * <AUTHOR>
 */
public class L1Shaha {
    private static final Log _log = LogFactory.getLog(L1Shaha.class
            .getName());
    static Random _random = new Random();

    public static boolean shaha(String cmd, L1PcInstance pc) {
        try {
            if (cmd.equalsIgnoreCase("wenyangsh")) {
                WenyangUp(pc);
            } else if (cmd.equalsIgnoreCase("shjilv")) {
                WenyangRnd(pc);
            } else if (cmd.equalsIgnoreCase("shstart")) {
                WenyangLevel(pc);
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private static void WenyangLevel(L1PcInstance pc) {
        try {

            if (pc.getshahacishu() >= 20) {
                pc.sendPackets(new S_SystemMessage("您的沙哈紋樣強化次數已用完"));
                return;
            }

            if (pc.getshaha() == 33) {
                pc.sendPackets(new S_SystemMessage("已達到max等級無法繼續強化"));
                return;
            }

            if (pc.getshahajilvdj() == 0 && pc.getzhufudianshu() < 200) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足200"));
                return;
            }

            if (pc.getshahajilvdj() == 1 && pc.getzhufudianshu() < 400) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足400"));
                return;
            }

            if (pc.getshahajilvdj() == 2 && pc.getzhufudianshu() < 800) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足800"));
                return;
            }

            if (pc.getshahajilvdj() == 3 && pc.getzhufudianshu() < 1600) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足1600"));
                return;
            }

            L1shahawen shaha1 = shahaTable.get().getTemplate(pc.getshaha());

            if (shaha1 != null) {
                shahaTable.get().setshaha(pc, pc.getshaha());
            }

            switch (pc.getshaha()) {
                case 0:
                    WenyangD(pc);
                    break;
                case 1:
                    WenyangD(pc);
                    break;
                case 2:
                    WenyangD(pc);
                    break;
                case 3:
                    WenyangD(pc);
                    break;
                case 4:
                    WenyangD(pc);
                    break;
                case 5:
                    WenyangD(pc);
                    break;
                case 6:
                    WenyangD(pc);
                    break;
                case 7:
                    WenyangD(pc);
                    break;
                case 8:
                    WenyangD(pc);
                    break;
                case 9:
                    WenyangD(pc);
                    break;
                case 10:
                    WenyangD(pc);
                    break;
                case 11:
                    WenyangD(pc);
                    break;
                case 12:
                    WenyangD(pc);
                    break;
                case 13:
                    WenyangD(pc);
                    break;
                case 14:
                    WenyangD(pc);
                    break;
                case 15:
                    WenyangD(pc);
                    break;
                case 16:
                    WenyangD(pc);
                    break;
                case 17:
                    WenyangD(pc);
                    break;
                case 18:
                    WenyangD(pc);
                    break;
                case 19:
                    WenyangD(pc);
                    break;
                case 20:
                    WenyangD(pc);
                    break;
                case 21:
                    WenyangD(pc);
                    break;
                case 22:
                    WenyangD(pc);
                    break;
                case 23:
                    WenyangD(pc);
                    break;
                case 24:
                    WenyangD(pc);
                    break;
                case 25:
                    WenyangD(pc);
                    break;
                case 26:
                    WenyangD(pc);
                    break;
                case 27:
                    WenyangD(pc);
                    break;
                case 28:
                    WenyangD(pc);
                    break;
                case 29:
                    WenyangD(pc);
                    break;
                case 30:
                    WenyangD(pc);
                    break;
                case 31:
                    WenyangD(pc);
                    break;
                case 32:
                    WenyangD(pc);
                    break;
                case 33:
                    WenyangD(pc);
                    break;
            }


            if (pc.getshahajilvdj() == 0) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 200);
            } else if (pc.getshahajilvdj() == 1) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 400);
            } else if (pc.getshahajilvdj() == 2) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 800);
            } else if (pc.getshahajilvdj() == 3) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 1600);
            }

            int random = _random.nextInt(100);
            if (pc.getshaha() >= 0) {
                if (random < pc.getshahajilv3()) {
                    if (pc.getshaha() >= 32) {
                        pc.setshaha(pc.getshaha() + 1);
                        pc.setshahacishu(pc.getshahacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }

                    if (pc.getshaha() >= 31) {
                        pc.setshaha(pc.getshaha() + 2);
                        pc.setshahacishu(pc.getshahacishu() + 2);
                        pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 2"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setshaha(pc.getshaha() + 3);
                    pc.setshahacishu(pc.getshahacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 3"));
                    WenyangUp(pc);
                } else if (random < pc.getshahajilv2()) {
                    if (pc.getshaha() >= 31) {
                        pc.setshaha(pc.getshaha() + 1);
                        pc.setshahacishu(pc.getshahacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setshaha(pc.getshaha() + 2);
                    pc.setshahacishu(pc.getshahacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 2"));
                    WenyangUp(pc);
                } else if (random < pc.getshahajilv1()) {
                    pc.setshaha(pc.getshaha() + 1);
                    pc.setshahacishu(pc.getshahacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("沙哈紋樣 + 1"));
                    WenyangUp(pc);
                } else {
                    pc.setshahacishu(pc.getshahacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("強化失敗扣除點數"));
                    WenyangUp(pc);
                }
            }

            pc.save();

            L1shahawen shaha = shahaTable.get().getTemplate(pc.getshaha());

            if (shaha != null) {
                shahaTable.get().addshaha(pc, pc.getshaha());
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangD(L1PcInstance pc) {
        try {
            String[] data = new String[33];

            if (pc.getshaha() == 0) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }

                data[2] = String.valueOf("5510");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5511");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5510");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5511");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 1) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5511");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5511");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 2) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5512");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 3) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5513");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 4) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5514");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 5) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5515");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 6) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5516");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 7) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5517");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 8) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5518");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 9) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5519");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 10) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5520");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 11) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5521");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 12) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5522");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 13) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5523");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 14) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5524");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 15) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5525");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 16) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5526");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 17) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5527");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 18) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5528");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 19) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5529");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 20) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5530");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 21) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5531");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 22) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5532");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 23) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5533");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 24) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5534");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 25) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5535");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 26) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5536");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 27) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5537");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 28) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5538");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 29) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5539");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 30) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5540");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 31) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5543");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5541");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5543");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            } else if (pc.getshaha() == 32) {
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5543");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5543");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5542");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
                Thread.sleep(300);
                if (pc.getshahacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
                }

                if (pc.getshahajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getshahajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getshahajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getshahajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5543");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangRnd(L1PcInstance pc) {
        if (pc.getshahajilvdj() == 0) {
            pc.setshahajilv1(pc.getshahajilv1() - 5.3);
            pc.setshahajilv2(pc.getshahajilv2() + 5.3);
            pc.setshahajilv3(pc.getshahajilv3() + 3.9);
            pc.setshahajilvdj(pc.getshahajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getshahajilvdj() == 1) {
            pc.setshahajilv1(pc.getshahajilv1() - 8.1);
            pc.setshahajilv2(pc.getshahajilv2() + 7.3);
            pc.setshahajilv3(pc.getshahajilv3() + 5.3);
            pc.setshahajilvdj(pc.getshahajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getshahajilvdj() == 2) {
            pc.setshahajilv1(pc.getshahajilv1() - 12.9);
            pc.setshahajilv2(pc.getshahajilv2() + 9.5);
            pc.setshahajilv3(pc.getshahajilv3() + 7.9);
            pc.setshahajilvdj(pc.getshahajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getshahajilvdj() == 3) {
            pc.setshahajilv1(61.5);
            pc.setshahajilv2(23.2);
            pc.setshahajilv3(15.9);
            pc.setshahajilvdj(0);
            WenyangUp(pc);
        }

    }

    public static void WenyangUp(L1PcInstance pc) {
        String[] data = new String[5];
        if (pc.getshahacishu() >= 0) {//判定以强化次数
            data[0] = String.valueOf("(" + pc.getshahacishu() + "/20)");//赋予对话当显示次数
        }

        if (pc.getzhufudianshu() >= 0) {
            data[1] = String.valueOf(pc.getzhufudianshu());
        }

        switch (pc.getshaha()) {//判定纹样等级调用IMG
            case 0:
                data[2] = String.valueOf("5510");
                break;
            case 1:
                data[2] = String.valueOf("5511");
                break;
            case 2:
                data[2] = String.valueOf("5512");
                break;
            case 3:
                data[2] = String.valueOf("5513");
                break;
            case 4:
                data[2] = String.valueOf("5514");
                break;
            case 5:
                data[2] = String.valueOf("5515");
                break;
            case 6:
                data[2] = String.valueOf("5516");
                break;
            case 7:
                data[2] = String.valueOf("5517");
                break;
            case 8:
                data[2] = String.valueOf("5518");
                break;
            case 9:
                data[2] = String.valueOf("5519");
                break;
            case 10:
                data[2] = String.valueOf("5520");
                break;
            case 11:
                data[2] = String.valueOf("5521");
                break;
            case 12:
                data[2] = String.valueOf("5522");
                break;
            case 13:
                data[2] = String.valueOf("5523");
                break;
            case 14:
                data[2] = String.valueOf("5524");
                break;
            case 15:
                data[2] = String.valueOf("5525");
                break;
            case 16:
                data[2] = String.valueOf("5526");
                break;
            case 17:
                data[2] = String.valueOf("5527");
                break;
            case 18:
                data[2] = String.valueOf("5528");
                break;
            case 19:
                data[2] = String.valueOf("5529");
                break;
            case 20:
                data[2] = String.valueOf("5530");
                break;
            case 21:
                data[2] = String.valueOf("5531");
                break;
            case 22:
                data[2] = String.valueOf("5532");
                break;
            case 23:
                data[2] = String.valueOf("5533");
                break;
            case 24:
                data[2] = String.valueOf("5534");
                break;
            case 25:
                data[2] = String.valueOf("5535");
                break;
            case 26:
                data[2] = String.valueOf("5536");
                break;
            case 27:
                data[2] = String.valueOf("5537");
                break;
            case 28:
                data[2] = String.valueOf("5538");
                break;
            case 29:
                data[2] = String.valueOf("5539");
                break;
            case 30:
                data[2] = String.valueOf("5540");
                break;
            case 31:
                data[2] = String.valueOf("5541");
                break;
            case 32:
                data[2] = String.valueOf("5542");
                break;
            case 33:
                data[2] = String.valueOf("5543");
                break;

            default:
                data[2] = null;
                break;
        }

        if (pc.getshahajilv1() >= 0.0 && pc.getshahajilv2() >= 0.0 && pc.getshahajilv3() >= 0.0) {
            data[3] = String.valueOf("1(" + pc.getshahajilv1() + "%)  2(" + pc.getshahajilv2() + "%)  3(" + pc.getshahajilv3() + "%)");
        }

        if (pc.getshahajilvdj() == 0) {
            data[4] = String.valueOf("5990");
        } else if (pc.getshahajilvdj() == 1) {
            data[4] = String.valueOf("5991");
        } else if (pc.getshahajilvdj() == 2) {
            data[4] = String.valueOf("5992");
        } else if (pc.getshahajilvdj() == 3) {
            data[4] = String.valueOf("5993");
        }

        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangsh", data));
    }


}
