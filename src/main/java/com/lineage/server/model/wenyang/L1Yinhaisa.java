package com.lineage.server.model.wenyang;


import com.lineage.server.datatables.yinhaisaTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_GreenMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1yinhaisawen;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

/**
 * 殷海薩纹样专用
 * by 大陸技術
 *
 * <AUTHOR>
 */
public class L1Yinhaisa {
    private static final Log _log = LogFactory.getLog(L1Yinhaisa.class
            .getName());
    static Random _random = new Random();

    public static boolean yinhaisa(String cmd, L1PcInstance pc) {
        try {
            if (cmd.equalsIgnoreCase("wenyangyhs")) {
                WenyangUp(pc);
            } else if (cmd.equalsIgnoreCase("yhsjilv")) {
                WenyangRnd(pc);
            } else if (cmd.equalsIgnoreCase("yhsstart")) {
                WenyangLevel(pc);
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private static void WenyangLevel(L1PcInstance pc) {
        try {


            if (pc.getyinhaisacishu() >= 20) {
                pc.sendPackets(new S_SystemMessage("您的殷海薩紋樣強化次數已用完"));
                return;
            }

            if (pc.getyinhaisa() == 33) {
                pc.sendPackets(new S_SystemMessage("已達到max等級無法繼續強化"));
                return;
            }

            if (pc.getyinhaisajilvdj() == 0 && pc.getzhufudianshu() < 200) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足200"));
                return;
            }

            if (pc.getyinhaisajilvdj() == 1 && pc.getzhufudianshu() < 400) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足400"));
                return;
            }

            if (pc.getyinhaisajilvdj() == 2 && pc.getzhufudianshu() < 800) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足800"));
                return;
            }

            if (pc.getyinhaisajilvdj() == 3 && pc.getzhufudianshu() < 1600) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足1600"));
                return;
            }

            L1yinhaisawen yinhaisa1 = yinhaisaTable.get().getTemplate(pc.getyinhaisa());

            if (yinhaisa1 != null) {
                yinhaisaTable.get().setyinhaisa(pc, pc.getyinhaisa());
            }

            switch (pc.getyinhaisa()) {
                case 0:
                    WenyangD(pc);
                    break;
                case 1:
                    WenyangD(pc);
                    break;
                case 2:
                    WenyangD(pc);
                    break;
                case 3:
                    WenyangD(pc);
                    break;
                case 4:
                    WenyangD(pc);
                    break;
                case 5:
                    WenyangD(pc);
                    break;
                case 6:
                    WenyangD(pc);
                    break;
                case 7:
                    WenyangD(pc);
                    break;
                case 8:
                    WenyangD(pc);
                    break;
                case 9:
                    WenyangD(pc);
                    break;
                case 10:
                    WenyangD(pc);
                    break;
                case 11:
                    WenyangD(pc);
                    break;
                case 12:
                    WenyangD(pc);
                    break;
                case 13:
                    WenyangD(pc);
                    break;
                case 14:
                    WenyangD(pc);
                    break;
                case 15:
                    WenyangD(pc);
                    break;
                case 16:
                    WenyangD(pc);
                    break;
                case 17:
                    WenyangD(pc);
                    break;
                case 18:
                    WenyangD(pc);
                    break;
                case 19:
                    WenyangD(pc);
                    break;
                case 20:
                    WenyangD(pc);
                    break;
                case 21:
                    WenyangD(pc);
                    break;
                case 22:
                    WenyangD(pc);
                    break;
                case 23:
                    WenyangD(pc);
                    break;
                case 24:
                    WenyangD(pc);
                    break;
                case 25:
                    WenyangD(pc);
                    break;
                case 26:
                    WenyangD(pc);
                    break;
                case 27:
                    WenyangD(pc);
                    break;
                case 28:
                    WenyangD(pc);
                    break;
                case 29:
                    WenyangD(pc);
                    break;
                case 30:
                    WenyangD(pc);
                    break;
                case 31:
                    WenyangD(pc);
                    break;
                case 32:
                    WenyangD(pc);
                    break;
                case 33:
                    WenyangD(pc);
                    break;
            }


            if (pc.getyinhaisajilvdj() == 0) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 200);
            } else if (pc.getyinhaisajilvdj() == 1) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 400);
            } else if (pc.getyinhaisajilvdj() == 2) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 800);
            } else if (pc.getyinhaisajilvdj() == 3) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 1600);
            }

            int random = _random.nextInt(100);
            if (pc.getyinhaisa() >= 0) {
                if (random < pc.getyinhaisajilv3()) {
                    if (pc.getyinhaisa() >= 32) {
                        pc.setyinhaisa(pc.getyinhaisa() + 1);
                        pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }

                    if (pc.getyinhaisa() >= 31) {
                        pc.setyinhaisa(pc.getyinhaisa() + 2);
                        pc.setyinhaisacishu(pc.getyinhaisacishu() + 2);
                        pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 2"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setyinhaisa(pc.getyinhaisa() + 3);
                    pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 3"));
                    WenyangUp(pc);
                } else if (random < pc.getyinhaisajilv2()) {
                    if (pc.getyinhaisa() >= 31) {
                        pc.setyinhaisa(pc.getyinhaisa() + 1);
                        pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                        pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setyinhaisa(pc.getyinhaisa() + 2);
                    pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 2"));
                    WenyangUp(pc);
                } else if (random < pc.getyinhaisajilv1()) {
                    pc.setyinhaisa(pc.getyinhaisa() + 1);
                    pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("殷海薩紋樣 + 1"));
                    WenyangUp(pc);
                } else {
                    pc.setyinhaisacishu(pc.getyinhaisacishu() + 1);
                    pc.sendPackets(new S_GreenMessage("強化失敗扣除點數"));
                    WenyangUp(pc);
                }
            }

            pc.save();

            L1yinhaisawen yinhaisa = yinhaisaTable.get().getTemplate(pc.getyinhaisa());

            if (yinhaisa != null) {
                yinhaisaTable.get().addyinhaisa(pc, pc.getyinhaisa());
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangD(L1PcInstance pc) {
        try {
            String[] data = new String[33];

            if (pc.getyinhaisa() == 0) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }

                data[2] = String.valueOf("5810");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5811");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5810");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5811");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 1) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5811");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5811");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 2) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5812");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 3) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5813");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 4) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5814");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 5) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5815");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 6) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5816");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 7) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5817");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 8) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5818");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 9) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5819");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 10) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5820");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 11) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5821");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 12) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5822");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 13) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5823");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 14) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5824");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 15) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5825");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 16) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5826");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 17) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5827");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 18) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5828");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 19) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5829");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 20) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5830");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 21) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5831");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 22) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5832");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 23) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5833");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 24) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5834");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 25) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5835");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 26) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5836");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 27) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5837");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 28) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5838");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 29) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5839");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 30) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5840");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 31) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5843");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5841");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5843");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            } else if (pc.getyinhaisa() == 32) {
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5843");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5843");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5842");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
                Thread.sleep(300);
                if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
                }

                if (pc.getyinhaisajilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getyinhaisajilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getyinhaisajilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getyinhaisajilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5843");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangRnd(L1PcInstance pc) {
        if (pc.getyinhaisajilvdj() == 0) {
            pc.setyinhaisajilv1(pc.getyinhaisajilv1() - 5.3);
            pc.setyinhaisajilv2(pc.getyinhaisajilv2() + 5.3);
            pc.setyinhaisajilv3(pc.getyinhaisajilv3() + 3.9);
            pc.setyinhaisajilvdj(pc.getyinhaisajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyinhaisajilvdj() == 1) {
            pc.setyinhaisajilv1(pc.getyinhaisajilv1() - 8.1);
            pc.setyinhaisajilv2(pc.getyinhaisajilv2() + 7.3);
            pc.setyinhaisajilv3(pc.getyinhaisajilv3() + 5.3);
            pc.setyinhaisajilvdj(pc.getyinhaisajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyinhaisajilvdj() == 2) {
            pc.setyinhaisajilv1(pc.getyinhaisajilv1() - 12.9);
            pc.setyinhaisajilv2(pc.getyinhaisajilv2() + 9.5);
            pc.setyinhaisajilv3(pc.getyinhaisajilv3() + 7.9);
            pc.setyinhaisajilvdj(pc.getyinhaisajilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getyinhaisajilvdj() == 3) {
            pc.setyinhaisajilv1(61.5);
            pc.setyinhaisajilv2(23.2);
            pc.setyinhaisajilv3(15.9);
            pc.setyinhaisajilvdj(0);
            WenyangUp(pc);
        }

    }

    public static void WenyangUp(L1PcInstance pc) {
        String[] data = new String[5];
        if (pc.getyinhaisacishu() >= 0) {//判定以强化次数
            data[0] = String.valueOf("(" + pc.getyinhaisacishu() + "/20)");//赋予对话当显示次数
        }

        if (pc.getzhufudianshu() >= 0) {
            data[1] = String.valueOf(pc.getzhufudianshu());
        }

        switch (pc.getyinhaisa()) {//判定纹样等级调用IMG
            case 0:
                data[2] = String.valueOf("5810");
                break;
            case 1:
                data[2] = String.valueOf("5811");
                break;
            case 2:
                data[2] = String.valueOf("5812");
                break;
            case 3:
                data[2] = String.valueOf("5813");
                break;
            case 4:
                data[2] = String.valueOf("5814");
                break;
            case 5:
                data[2] = String.valueOf("5815");
                break;
            case 6:
                data[2] = String.valueOf("5816");
                break;
            case 7:
                data[2] = String.valueOf("5817");
                break;
            case 8:
                data[2] = String.valueOf("5818");
                break;
            case 9:
                data[2] = String.valueOf("5819");
                break;
            case 10:
                data[2] = String.valueOf("5820");
                break;
            case 11:
                data[2] = String.valueOf("5821");
                break;
            case 12:
                data[2] = String.valueOf("5822");
                break;
            case 13:
                data[2] = String.valueOf("5823");
                break;
            case 14:
                data[2] = String.valueOf("5824");
                break;
            case 15:
                data[2] = String.valueOf("5825");
                break;
            case 16:
                data[2] = String.valueOf("5826");
                break;
            case 17:
                data[2] = String.valueOf("5827");
                break;
            case 18:
                data[2] = String.valueOf("5828");
                break;
            case 19:
                data[2] = String.valueOf("5829");
                break;
            case 20:
                data[2] = String.valueOf("5830");
                break;
            case 21:
                data[2] = String.valueOf("5831");
                break;
            case 22:
                data[2] = String.valueOf("5832");
                break;
            case 23:
                data[2] = String.valueOf("5833");
                break;
            case 24:
                data[2] = String.valueOf("5834");
                break;
            case 25:
                data[2] = String.valueOf("5835");
                break;
            case 26:
                data[2] = String.valueOf("5836");
                break;
            case 27:
                data[2] = String.valueOf("5837");
                break;
            case 28:
                data[2] = String.valueOf("5838");
                break;
            case 29:
                data[2] = String.valueOf("5839");
                break;
            case 30:
                data[2] = String.valueOf("5840");
                break;
            case 31:
                data[2] = String.valueOf("5841");
                break;
            case 32:
                data[2] = String.valueOf("5842");
                break;
            case 33:
                data[2] = String.valueOf("5843");
                break;

            default:
                data[2] = null;
                break;
        }

        if (pc.getyinhaisajilv1() >= 0.0 && pc.getyinhaisajilv2() >= 0.0 && pc.getyinhaisajilv3() >= 0.0) {
            data[3] = String.valueOf("1(" + pc.getyinhaisajilv1() + "%)  2(" + pc.getyinhaisajilv2() + "%)  3(" + pc.getyinhaisajilv3() + "%)");
        }

        if (pc.getyinhaisajilvdj() == 0) {
            data[4] = String.valueOf("5990");
        } else if (pc.getyinhaisajilvdj() == 1) {
            data[4] = String.valueOf("5991");
        } else if (pc.getyinhaisajilvdj() == 2) {
            data[4] = String.valueOf("5992");
        } else if (pc.getyinhaisajilvdj() == 3) {
            data[4] = String.valueOf("5993");
        }

        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangyhs", data));
    }


}
