package com.lineage.server.model.wenyang;


import com.lineage.server.datatables.mapuleTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_GreenMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1mapulewen;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

/**
 * 馬普勒纹样专用
 * by 大陸技術
 *
 * <AUTHOR>
 */
public class L1Mapule {
    private static final Log _log = LogFactory.getLog(L1Mapule.class
            .getName());
    static Random _random = new Random();

    public static boolean mapule(String cmd, L1PcInstance pc) {
        try {
            if (cmd.equalsIgnoreCase("wenyangmpl")) {
                WenyangUp(pc);
            } else if (cmd.equalsIgnoreCase("mpljilv")) {
                WenyangRnd(pc);
            } else if (cmd.equalsIgnoreCase("mplstart")) {
                WenyangLevel(pc);
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private static void WenyangLevel(L1PcInstance pc) {
        try {


            if (pc.getmapulecishu() >= 20) {
                pc.sendPackets(new S_SystemMessage("您的馬普勒紋樣強化次數已用完"));
                return;
            }

            if (pc.getmapule() == 33) {
                pc.sendPackets(new S_SystemMessage("已達到max等級無法繼續強化"));
                return;
            }

            if (pc.getmapulejilvdj() == 0 && pc.getzhufudianshu() < 200) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足200"));
                return;
            }

            if (pc.getmapulejilvdj() == 1 && pc.getzhufudianshu() < 400) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足400"));
                return;
            }

            if (pc.getmapulejilvdj() == 2 && pc.getzhufudianshu() < 800) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足800"));
                return;
            }

            if (pc.getmapulejilvdj() == 3 && pc.getzhufudianshu() < 1600) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足1600"));
                return;
            }

            L1mapulewen mapule1 = mapuleTable.get().getTemplate(pc.getmapule());

            if (mapule1 != null) {
                mapuleTable.get().setmapule(pc, pc.getmapule());
            }

            switch (pc.getmapule()) {
                case 0:
                    WenyangD(pc);
                    break;
                case 1:
                    WenyangD(pc);
                    break;
                case 2:
                    WenyangD(pc);
                    break;
                case 3:
                    WenyangD(pc);
                    break;
                case 4:
                    WenyangD(pc);
                    break;
                case 5:
                    WenyangD(pc);
                    break;
                case 6:
                    WenyangD(pc);
                    break;
                case 7:
                    WenyangD(pc);
                    break;
                case 8:
                    WenyangD(pc);
                    break;
                case 9:
                    WenyangD(pc);
                    break;
                case 10:
                    WenyangD(pc);
                    break;
                case 11:
                    WenyangD(pc);
                    break;
                case 12:
                    WenyangD(pc);
                    break;
                case 13:
                    WenyangD(pc);
                    break;
                case 14:
                    WenyangD(pc);
                    break;
                case 15:
                    WenyangD(pc);
                    break;
                case 16:
                    WenyangD(pc);
                    break;
                case 17:
                    WenyangD(pc);
                    break;
                case 18:
                    WenyangD(pc);
                    break;
                case 19:
                    WenyangD(pc);
                    break;
                case 20:
                    WenyangD(pc);
                    break;
                case 21:
                    WenyangD(pc);
                    break;
                case 22:
                    WenyangD(pc);
                    break;
                case 23:
                    WenyangD(pc);
                    break;
                case 24:
                    WenyangD(pc);
                    break;
                case 25:
                    WenyangD(pc);
                    break;
                case 26:
                    WenyangD(pc);
                    break;
                case 27:
                    WenyangD(pc);
                    break;
                case 28:
                    WenyangD(pc);
                    break;
                case 29:
                    WenyangD(pc);
                    break;
                case 30:
                    WenyangD(pc);
                    break;
                case 31:
                    WenyangD(pc);
                    break;
                case 32:
                    WenyangD(pc);
                    break;
                case 33:
                    WenyangD(pc);
                    break;
            }


            if (pc.getmapulejilvdj() == 0) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 200);
            } else if (pc.getmapulejilvdj() == 1) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 400);
            } else if (pc.getmapulejilvdj() == 2) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 800);
            } else if (pc.getmapulejilvdj() == 3) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 1600);
            }

            int random = _random.nextInt(100);
            if (pc.getmapule() >= 0) {
                if (random < pc.getmapulejilv3()) {
                    if (pc.getmapule() >= 32) {
                        pc.setmapule(pc.getmapule() + 1);
                        pc.setmapulecishu(pc.getmapulecishu() + 1);
                        pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }

                    if (pc.getmapule() >= 31) {
                        pc.setmapule(pc.getmapule() + 2);
                        pc.setmapulecishu(pc.getmapulecishu() + 2);
                        pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 2"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setmapule(pc.getmapule() + 3);
                    pc.setmapulecishu(pc.getmapulecishu() + 1);
                    pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 3"));
                    WenyangUp(pc);
                } else if (random < pc.getmapulejilv2()) {
                    if (pc.getmapule() >= 31) {
                        pc.setmapule(pc.getmapule() + 1);
                        pc.setmapulecishu(pc.getmapulecishu() + 1);
                        pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setmapule(pc.getmapule() + 2);
                    pc.setmapulecishu(pc.getmapulecishu() + 1);
                    pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 2"));
                    WenyangUp(pc);
                } else if (random < pc.getmapulejilv1()) {
                    pc.setmapule(pc.getmapule() + 1);
                    pc.setmapulecishu(pc.getmapulecishu() + 1);
                    pc.sendPackets(new S_GreenMessage("馬普勒紋樣 + 1"));
                    WenyangUp(pc);
                } else {
                    pc.setmapulecishu(pc.getmapulecishu() + 1);
                    pc.sendPackets(new S_GreenMessage("強化失敗扣除點數"));
                    WenyangUp(pc);
                }
            }

            pc.save();

            L1mapulewen mapule = mapuleTable.get().getTemplate(pc.getmapule());

            if (mapule != null) {
                mapuleTable.get().addmapule(pc, pc.getmapule());
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangD(L1PcInstance pc) {
        try {
            String[] data = new String[33];

            if (pc.getmapule() == 0) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }

                data[2] = String.valueOf("5610");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5611");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5610");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5611");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 1) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5611");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5611");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 2) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5612");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 3) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5613");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 4) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5614");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 5) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5615");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 6) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5616");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 7) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5617");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 8) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5618");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 9) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5619");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 10) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5620");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 11) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5621");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 12) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5622");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 13) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5623");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 14) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5624");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 15) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5625");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 16) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5626");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 17) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5627");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 18) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5628");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 19) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5629");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 20) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5630");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 21) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5631");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 22) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5632");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 23) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5633");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 24) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5634");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 25) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5635");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 26) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6020");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 27) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6023");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 28) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6026");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 29) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6021");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 30) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6024");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 31) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6027");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            } else if (pc.getmapule() == 32) {
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6022");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
                Thread.sleep(300);
                if (pc.getmapulecishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
                }

                if (pc.getmapulejilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getmapulejilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getmapulejilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getmapulejilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6025");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangRnd(L1PcInstance pc) {
        if (pc.getmapulejilvdj() == 0) {
            pc.setmapulejilv1(pc.getmapulejilv1() - 5.3);
            pc.setmapulejilv2(pc.getmapulejilv2() + 5.3);
            pc.setmapulejilv3(pc.getmapulejilv3() + 3.9);
            pc.setmapulejilvdj(pc.getmapulejilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getmapulejilvdj() == 1) {
            pc.setmapulejilv1(pc.getmapulejilv1() - 8.1);
            pc.setmapulejilv2(pc.getmapulejilv2() + 7.3);
            pc.setmapulejilv3(pc.getmapulejilv3() + 5.3);
            pc.setmapulejilvdj(pc.getmapulejilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getmapulejilvdj() == 2) {
            pc.setmapulejilv1(pc.getmapulejilv1() - 12.9);
            pc.setmapulejilv2(pc.getmapulejilv2() + 9.5);
            pc.setmapulejilv3(pc.getmapulejilv3() + 7.9);
            pc.setmapulejilvdj(pc.getmapulejilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getmapulejilvdj() == 3) {
            pc.setmapulejilv1(61.5);
            pc.setmapulejilv2(23.2);
            pc.setmapulejilv3(15.9);
            pc.setmapulejilvdj(0);
            WenyangUp(pc);
        }

    }

    public static void WenyangUp(L1PcInstance pc) {
        String[] data = new String[5];
        if (pc.getmapulecishu() >= 0) {//判定以强化次数
            data[0] = String.valueOf("(" + pc.getmapulecishu() + "/20)");//赋予对话当显示次数
        }

        if (pc.getzhufudianshu() >= 0) {
            data[1] = String.valueOf(pc.getzhufudianshu());
        }

        switch (pc.getmapule()) {//判定纹样等级调用IMG
            case 0:
                data[2] = String.valueOf("5610");
                break;
            case 1:
                data[2] = String.valueOf("5611");
                break;
            case 2:
                data[2] = String.valueOf("5612");
                break;
            case 3:
                data[2] = String.valueOf("5613");
                break;
            case 4:
                data[2] = String.valueOf("5614");
                break;
            case 5:
                data[2] = String.valueOf("5615");
                break;
            case 6:
                data[2] = String.valueOf("5616");
                break;
            case 7:
                data[2] = String.valueOf("5617");
                break;
            case 8:
                data[2] = String.valueOf("5618");
                break;
            case 9:
                data[2] = String.valueOf("5619");
                break;
            case 10:
                data[2] = String.valueOf("5620");
                break;
            case 11:
                data[2] = String.valueOf("5621");
                break;
            case 12:
                data[2] = String.valueOf("5622");
                break;
            case 13:
                data[2] = String.valueOf("5623");
                break;
            case 14:
                data[2] = String.valueOf("5624");
                break;
            case 15:
                data[2] = String.valueOf("5625");
                break;
            case 16:
                data[2] = String.valueOf("5626");
                break;
            case 17:
                data[2] = String.valueOf("5627");
                break;
            case 18:
                data[2] = String.valueOf("5628");
                break;
            case 19:
                data[2] = String.valueOf("5629");
                break;
            case 20:
                data[2] = String.valueOf("5630");
                break;
            case 21:
                data[2] = String.valueOf("5631");
                break;
            case 22:
                data[2] = String.valueOf("5632");
                break;
            case 23:
                data[2] = String.valueOf("5633");
                break;
            case 24:
                data[2] = String.valueOf("5634");
                break;
            case 25:
                data[2] = String.valueOf("5635");
                break;
            case 26:
                data[2] = String.valueOf("6020");
                break;
            case 27:
                data[2] = String.valueOf("6023");
                break;
            case 28:
                data[2] = String.valueOf("6026");
                break;
            case 29:
                data[2] = String.valueOf("6021");
                break;
            case 30:
                data[2] = String.valueOf("6024");
                break;
            case 31:
                data[2] = String.valueOf("6027");
                break;
            case 32:
                data[2] = String.valueOf("6022");
                break;
            case 33:
                data[2] = String.valueOf("6025");
                break;

            default:
                data[2] = null;
                break;
        }

        if (pc.getmapulejilv1() >= 0.0 && pc.getmapulejilv2() >= 0.0 && pc.getmapulejilv3() >= 0.0) {
            data[3] = String.valueOf("1(" + pc.getmapulejilv1() + "%)  2(" + pc.getmapulejilv2() + "%)  3(" + pc.getmapulejilv3() + "%)");
        }

        if (pc.getmapulejilvdj() == 0) {
            data[4] = String.valueOf("5990");
        } else if (pc.getmapulejilvdj() == 1) {
            data[4] = String.valueOf("5991");
        } else if (pc.getmapulejilvdj() == 2) {
            data[4] = String.valueOf("5992");
        } else if (pc.getmapulejilvdj() == 3) {
            data[4] = String.valueOf("5993");
        }

        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangmpl", data));
    }


}
