package com.lineage.server.model.wenyang;


import com.lineage.server.datatables.pageliaoTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_GreenMessage;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.templates.L1pageliaowen;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

/**
 * 帕格里奧纹样专用
 * by 大陸技術
 *
 * <AUTHOR>
 */
public class L1Pageliao {
    private static final Log _log = LogFactory.getLog(L1Pageliao.class
            .getName());
    static Random _random = new Random();

    public static boolean pageliao(String cmd, L1PcInstance pc) {
        try {
            if (cmd.equalsIgnoreCase("wenyangpgla")) {
                WenyangUp(pc);
            } else if (cmd.equalsIgnoreCase("pglajilv")) {
                WenyangRnd(pc);
            } else if (cmd.equalsIgnoreCase("pglastart")) {
                WenyangLevel(pc);
            }
        } catch (Exception e) {
            _log.error(e.getLocalizedMessage(), e);
        }
        return false;
    }

    private static void WenyangLevel(L1PcInstance pc) {
        try {


            if (pc.getpageliaocishu() >= 20) {
                pc.sendPackets(new S_SystemMessage("您的帕格里奧紋樣強化次數已用完"));
                return;
            }

            if (pc.getpageliao() == 33) {
                pc.sendPackets(new S_SystemMessage("已達到max等級無法繼續強化"));
                return;
            }

            if (pc.getpageliaojilvdj() == 0 && pc.getzhufudianshu() < 200) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足200"));
                return;
            }

            if (pc.getpageliaojilvdj() == 1 && pc.getzhufudianshu() < 400) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足400"));
                return;
            }

            if (pc.getpageliaojilvdj() == 2 && pc.getzhufudianshu() < 800) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足800"));
                return;
            }

            if (pc.getpageliaojilvdj() == 3 && pc.getzhufudianshu() < 1600) {
                pc.sendPackets(new S_SystemMessage("您的祝福點數不足1600"));
                return;
            }

            L1pageliaowen pageliao1 = pageliaoTable.get().getTemplate(pc.getpageliao());

            if (pageliao1 != null) {
                pageliaoTable.get().setpageliao(pc, pc.getpageliao());
            }

            switch (pc.getpageliao()) {
                case 0:
                    WenyangD(pc);
                    break;
                case 1:
                    WenyangD(pc);
                    break;
                case 2:
                    WenyangD(pc);
                    break;
                case 3:
                    WenyangD(pc);
                    break;
                case 4:
                    WenyangD(pc);
                    break;
                case 5:
                    WenyangD(pc);
                    break;
                case 6:
                    WenyangD(pc);
                    break;
                case 7:
                    WenyangD(pc);
                    break;
                case 8:
                    WenyangD(pc);
                    break;
                case 9:
                    WenyangD(pc);
                    break;
                case 10:
                    WenyangD(pc);
                    break;
                case 11:
                    WenyangD(pc);
                    break;
                case 12:
                    WenyangD(pc);
                    break;
                case 13:
                    WenyangD(pc);
                    break;
                case 14:
                    WenyangD(pc);
                    break;
                case 15:
                    WenyangD(pc);
                    break;
                case 16:
                    WenyangD(pc);
                    break;
                case 17:
                    WenyangD(pc);
                    break;
                case 18:
                    WenyangD(pc);
                    break;
                case 19:
                    WenyangD(pc);
                    break;
                case 20:
                    WenyangD(pc);
                    break;
                case 21:
                    WenyangD(pc);
                    break;
                case 22:
                    WenyangD(pc);
                    break;
                case 23:
                    WenyangD(pc);
                    break;
                case 24:
                    WenyangD(pc);
                    break;
                case 25:
                    WenyangD(pc);
                    break;
                case 26:
                    WenyangD(pc);
                    break;
                case 27:
                    WenyangD(pc);
                    break;
                case 28:
                    WenyangD(pc);
                    break;
                case 29:
                    WenyangD(pc);
                    break;
                case 30:
                    WenyangD(pc);
                    break;
                case 31:
                    WenyangD(pc);
                    break;
                case 32:
                    WenyangD(pc);
                    break;
                case 33:
                    WenyangD(pc);
                    break;
            }


            if (pc.getpageliaojilvdj() == 0) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 200);
            } else if (pc.getpageliaojilvdj() == 1) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 400);
            } else if (pc.getpageliaojilvdj() == 2) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 800);
            } else if (pc.getpageliaojilvdj() == 3) {
                pc.setzhufudianshu(pc.getzhufudianshu() - 1600);
            }

            int random = _random.nextInt(100);
            if (pc.getpageliao() >= 0) {
                if (random < pc.getpageliaojilv3()) {
                    if (pc.getpageliao() >= 32) {
                        pc.setpageliao(pc.getpageliao() + 1);
                        pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                        pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }

                    if (pc.getpageliao() >= 31) {
                        pc.setpageliao(pc.getpageliao() + 2);
                        pc.setpageliaocishu(pc.getpageliaocishu() + 2);
                        pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 2"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setpageliao(pc.getpageliao() + 3);
                    pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                    pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 3"));
                    WenyangUp(pc);
                } else if (random < pc.getpageliaojilv2()) {
                    if (pc.getpageliao() >= 31) {
                        pc.setpageliao(pc.getpageliao() + 1);
                        pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                        pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 1"));
                        WenyangUp(pc);
                        return;
                    }
                    pc.setpageliao(pc.getpageliao() + 2);
                    pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                    pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 2"));
                    WenyangUp(pc);
                } else if (random < pc.getpageliaojilv1()) {
                    pc.setpageliao(pc.getpageliao() + 1);
                    pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                    pc.sendPackets(new S_GreenMessage("帕格里奧紋樣 + 1"));
                    WenyangUp(pc);
                } else {
                    pc.setpageliaocishu(pc.getpageliaocishu() + 1);
                    pc.sendPackets(new S_GreenMessage("強化失敗扣除點數"));
                    WenyangUp(pc);
                }
            }

            pc.save();

            L1pageliaowen pageliao = pageliaoTable.get().getTemplate(pc.getpageliao());

            if (pageliao != null) {
                pageliaoTable.get().addpageliao(pc, pc.getpageliao());
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangD(L1PcInstance pc) {
        try {
            String[] data = new String[33];

            if (pc.getpageliao() == 0) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }

                data[2] = String.valueOf("5710");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5711");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5710");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5711");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 1) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5711");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5711");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 2) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5712");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 3) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5713");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 4) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5714");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 5) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5715");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 6) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5716");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 7) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5717");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 8) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5718");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 9) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5719");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 10) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5720");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 11) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5721");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 12) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5722");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 13) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6028");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 14) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6018");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 15) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6019");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 16) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6016");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 17) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6017");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 18) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5728");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 19) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5729");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 20) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5730");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 21) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5731");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 22) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5732");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 23) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("5733");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 24) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6006");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 25) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6007");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 26) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6008");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 27) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6009");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 28) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6010");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 29) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6011");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 30) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6012");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 31) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6013");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            } else if (pc.getpageliao() == 32) {
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6014");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
                Thread.sleep(300);
                if (pc.getpageliaocishu() >= 0) {//判定以强化次数
                    data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
                }

                if (pc.getzhufudianshu() >= 0) {
                    data[1] = String.valueOf(pc.getzhufudianshu());
                }

                if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
                    data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
                }

                if (pc.getpageliaojilvdj() == 0) {
                    data[4] = String.valueOf("5990");
                } else if (pc.getpageliaojilvdj() == 1) {
                    data[4] = String.valueOf("5991");
                } else if (pc.getpageliaojilvdj() == 2) {
                    data[4] = String.valueOf("5992");
                } else if (pc.getpageliaojilvdj() == 3) {
                    data[4] = String.valueOf("5993");
                }
                data[2] = String.valueOf("6015");
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
            }

        } catch (Exception e) {
            // TODO: handle exception
        }
    }

    private static void WenyangRnd(L1PcInstance pc) {
        if (pc.getpageliaojilvdj() == 0) {
            pc.setpageliaojilv1(pc.getpageliaojilv1() - 5.3);
            pc.setpageliaojilv2(pc.getpageliaojilv2() + 5.3);
            pc.setpageliaojilv3(pc.getpageliaojilv3() + 3.9);
            pc.setpageliaojilvdj(pc.getpageliaojilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getpageliaojilvdj() == 1) {
            pc.setpageliaojilv1(pc.getpageliaojilv1() - 8.1);
            pc.setpageliaojilv2(pc.getpageliaojilv2() + 7.3);
            pc.setpageliaojilv3(pc.getpageliaojilv3() + 5.3);
            pc.setpageliaojilvdj(pc.getpageliaojilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getpageliaojilvdj() == 2) {
            pc.setpageliaojilv1(pc.getpageliaojilv1() - 12.9);
            pc.setpageliaojilv2(pc.getpageliaojilv2() + 9.5);
            pc.setpageliaojilv3(pc.getpageliaojilv3() + 7.9);
            pc.setpageliaojilvdj(pc.getpageliaojilvdj() + 1);
            WenyangUp(pc);
        } else if (pc.getpageliaojilvdj() == 3) {
            pc.setpageliaojilv1(61.5);
            pc.setpageliaojilv2(23.2);
            pc.setpageliaojilv3(15.9);
            pc.setpageliaojilvdj(0);
            WenyangUp(pc);
        }

    }

    public static void WenyangUp(L1PcInstance pc) {
        String[] data = new String[5];
        if (pc.getpageliaocishu() >= 0) {//判定以强化次数
            data[0] = String.valueOf("(" + pc.getpageliaocishu() + "/20)");//赋予对话当显示次数
        }

        if (pc.getzhufudianshu() >= 0) {
            data[1] = String.valueOf(pc.getzhufudianshu());
        }

        switch (pc.getpageliao()) {//判定纹样等级调用IMG
            case 0:
                data[2] = String.valueOf("5710");
                break;
            case 1:
                data[2] = String.valueOf("5711");
                break;
            case 2:
                data[2] = String.valueOf("5712");
                break;
            case 3:
                data[2] = String.valueOf("5713");
                break;
            case 4:
                data[2] = String.valueOf("5714");
                break;
            case 5:
                data[2] = String.valueOf("5715");
                break;
            case 6:
                data[2] = String.valueOf("5716");
                break;
            case 7:
                data[2] = String.valueOf("5717");
                break;
            case 8:
                data[2] = String.valueOf("5718");
                break;
            case 9:
                data[2] = String.valueOf("5719");
                break;
            case 10:
                data[2] = String.valueOf("5720");
                break;
            case 11:
                data[2] = String.valueOf("5721");
                break;
            case 12:
                data[2] = String.valueOf("5722");
                break;
            case 13:
                data[2] = String.valueOf("6028");
                break;
            case 14:
                data[2] = String.valueOf("6018");
                break;
            case 15:
                data[2] = String.valueOf("6019");
                break;
            case 16:
                data[2] = String.valueOf("6016");
                break;
            case 17:
                data[2] = String.valueOf("6017");
                break;
            case 18:
                data[2] = String.valueOf("5728");
                break;
            case 19:
                data[2] = String.valueOf("5729");
                break;
            case 20:
                data[2] = String.valueOf("5730");
                break;
            case 21:
                data[2] = String.valueOf("5731");
                break;
            case 22:
                data[2] = String.valueOf("5732");
                break;
            case 23:
                data[2] = String.valueOf("5733");
                break;
            case 24:
                data[2] = String.valueOf("6006");
                break;
            case 25:
                data[2] = String.valueOf("6007");
                break;
            case 26:
                data[2] = String.valueOf("6008");
                break;
            case 27:
                data[2] = String.valueOf("6009");
                break;
            case 28:
                data[2] = String.valueOf("6010");
                break;
            case 29:
                data[2] = String.valueOf("6011");
                break;
            case 30:
                data[2] = String.valueOf("6012");
                break;
            case 31:
                data[2] = String.valueOf("6013");
                break;
            case 32:
                data[2] = String.valueOf("6014");
                break;
            case 33:
                data[2] = String.valueOf("6015");
                break;

            default:
                data[2] = null;
                break;
        }

        if (pc.getpageliaojilv1() >= 0.0 && pc.getpageliaojilv2() >= 0.0 && pc.getpageliaojilv3() >= 0.0) {
            data[3] = String.valueOf("1(" + pc.getpageliaojilv1() + "%)  2(" + pc.getpageliaojilv2() + "%)  3(" + pc.getpageliaojilv3() + "%)");
        }

        if (pc.getpageliaojilvdj() == 0) {
            data[4] = String.valueOf("5990");
        } else if (pc.getpageliaojilvdj() == 1) {
            data[4] = String.valueOf("5991");
        } else if (pc.getpageliaojilvdj() == 2) {
            data[4] = String.valueOf("5992");
        } else if (pc.getpageliaojilvdj() == 3) {
            data[4] = String.valueOf("5993");
        }

        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "wenyangpgla", data));
    }


}
