package com.lineage.server.model;

import com.add.AutoAttackUpdate;
import com.add.TFUpdate;
import com.add.system.CardBookCmd;
import com.lineage.DatabaseFactory;
import com.lineage.FollowAutoAttackUpdate;
import com.lineage.config.ConfigAlt;
import com.lineage.config.ConfigClan;
import com.lineage.config.ConfigOther;
import com.lineage.data.QuestClass;
import com.lineage.data.event.OnlineGiftSet;
import com.lineage.data.event.QuestSet;
import com.lineage.data.npc.event.GamblingNpc;
import com.lineage.server.beans.clan.ClanLevelUpCondition;
import com.lineage.server.clientpackets.C_CreateChar;
import com.lineage.server.clientpackets.C_NPCAction;
import com.lineage.server.command.executor.L1ToPC2;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.CharacterQuestReading;
import com.lineage.server.datatables.sql.CharacterTable;
import com.lineage.server.datatables.sql.ClanTable;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.skill.L1BuffUtil;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.model.wenyang.*;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1Item;
import com.lineage.server.templates.L1Quest;
import com.lineage.server.templates.L1Skills;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.timecontroller.event.GamblingTime;
import com.lineage.server.utils.CalcStat;
import com.lineage.server.utils.SQLUtil;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.william.ItemAction;
import com.lineage.william.ItemActionPoly;
import com.lineage.william.ItemShop;
import com.lineage.william.PayBonus;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class L1ActionPc {
    public static final Random _random;
    private static final Log _log;
    private static BufferedWriter out;

    static {
        _log = LogFactory.getLog(L1ActionPc.class);
        _random = new Random();
    }

    private final L1PcInstance _pc;

    public L1ActionPc(L1PcInstance pc) {
        _pc = pc;
    }

    public static void showStartQuest(L1PcInstance pc, int objid) {
        try {
            pc.get_otherList().QUESTMAP.clear();
            int key = 0;
            int i = QuestTable.MINQID;
            while (i <= QuestTable.MAXQID) {
                L1Quest value = QuestTable.get().getTemplate(i);
                if (value != null) {
                    if (!pc.getQuest().isEnd(value.get_id())) {
                        if (pc.getQuest().isStart(value.get_id())) {
                            pc.get_otherList().QUESTMAP.put(Integer.valueOf(key++), value);
                        }
                    }
                }
                ++i;
            }
            if (pc.get_otherList().QUESTMAP.size() <= 0) {
                pc.sendPackets(new S_NPCTalkReturn(objid, "y_q_not7"));
            } else {
                L1ActionShowHtml show = new L1ActionShowHtml(pc);
                show.showQuestMap(0);
            }
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    public static void showQuest(L1PcInstance pc, int objid) {
        try {
            pc.get_otherList().QUESTMAP.clear();
            int key = 0;
            int i = QuestTable.MINQID;
            while (i <= QuestTable.MAXQID) {
                L1Quest value = QuestTable.get().getTemplate(i);
                if (value != null && pc.getLevel() >= value.get_questlevel()) {
                    if (!pc.getQuest().isEnd(value.get_id())) {
                        if (!pc.getQuest().isStart(value.get_id())) {
                            if (value.check(pc)) {
                                pc.get_otherList().QUESTMAP.put(key++, value);
                            }
                        }
                    }
                }
                ++i;
            }
            if (pc.get_otherList().QUESTMAP.size() <= 0) {
                pc.sendPackets(new S_NPCTalkReturn(objid, "y_q_not4"));
            } else {
                L1ActionShowHtml show = new L1ActionShowHtml(pc);
                show.showQuestMap(0);
            }
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    public static void showQuestAll(L1PcInstance pc, int objid) {
        try {
            pc.get_otherList().QUESTMAP.clear();
            int key = 0;
            int i = QuestTable.MINQID;
            while (i <= QuestTable.MAXQID) {
                L1Quest value = QuestTable.get().getTemplate(i);
                if (value != null && value.check(pc)) {
                    pc.get_otherList().QUESTMAP.put(Integer.valueOf(key++), value);
                }
                ++i;
            }
            L1ActionShowHtml show = new L1ActionShowHtml(pc);
            show.showQuestMap(0);
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private static synchronized void checkSponsor(L1PcInstance pc) {
        Connection con = null;
        PreparedStatement pstm = null;
        ResultSet rs = null;
        PreparedStatement pstm2 = null;
        try {
            String AccountName = pc.getAccountName();
            con = DatabaseFactory.get().getConnection();
            pstm = con.prepareStatement(
                    "select ordernumber,amount,payname,state from ezpay where state = 1 and payname ='" + AccountName
                            + "'");
            rs = pstm.executeQuery();
            boolean isfind = false;
            while (rs.next() && rs != null) {
                int serial = rs.getInt("ordernumber");
                if (pc.getAccountName().equalsIgnoreCase(rs.getString("payname"))) {
                    isfind = true;
                    pstm2 = con.prepareStatement("update ezpay set state = 2 where ordernumber = ?");
                    pstm2.setInt(1, serial);
                    pstm2.execute();
                    int count = rs.getInt("amount");
                    GiveItem(pc, 44070, count);
                    PayBonus.getItem(pc, count);
                    pc.setpaycount(pc.getpaycount() + count);
                    writeSponsorlog(pc, count);
                }
            }
            if (!isfind) {
                pc.sendPackets(new S_ServerMessage("尚未偵測到關於您的贊助。"));
            }
        } catch (SQLException e) {
            L1ActionPc._log.error(e.getLocalizedMessage());
        } finally {
            SQLUtil.close(rs);
            SQLUtil.close(pstm);
            SQLUtil.close(pstm2);
            SQLUtil.close(con);
        }
    }

    public static void GiveItem(L1PcInstance pc, int itemId, int count) {
        L1ItemInstance item = ItemTable.get().createItem(itemId);
        item.setCount(count);
        if (pc.getInventory().checkAddItem(item, count) == 0) {
            pc.getInventory().storeItem(item);
            pc.sendPackets(new S_ServerMessage("本次贊助共獲得： " + item.getLogName() + "。"));
        }
    }

    public static void writeSponsorlog(L1PcInstance player, int count) {
        try {
            File DeleteLog = new File("自動贊助\\贊助領取資料.log");
            if (DeleteLog.createNewFile()) {
                (L1ActionPc.out = new BufferedWriter(new FileWriter("自動贊助\\贊助領取資料.log", false)))
                        .write("※以下是玩家[領取贊助]的所有紀錄※\r\n");
                L1ActionPc.out.close();
            }
            (L1ActionPc.out = new BufferedWriter(new FileWriter("自動贊助\\贊助領取資料.log", true))).write("\r\n");
            L1ActionPc.out.write("來自帳號: " + player.getAccountName() + "來自ip: " + player.getNetConnection().getIp()
                    + ",來自玩家: " + player.getName() + ",領取了: " + count + " 個 " + ",<領取時間:"
                    + new Timestamp(System.currentTimeMillis()) + ">" + "\r\n");
            L1ActionPc.out.close();
        } catch (IOException e) {
            System.out.println("以下是錯誤訊息: " + e.getMessage());
        }
    }

    public static void initCharStatus(L1PcInstance pc, int inithp, int initmp, int str,
                                      int intel, int wis, int dex, int con, int cha) {
        pc.addBaseMaxHp((short) (inithp - pc.getBaseMaxHp()));
        pc.addBaseMaxMp((short) (initmp - pc.getBaseMaxMp()));
        pc.addBaseStr((byte) (str - pc.getBaseStr()));
        pc.addBaseInt((byte) (intel - pc.getBaseInt()));
        pc.addBaseWis((byte) (wis - pc.getBaseWis()));
        pc.addBaseDex((byte) (dex - pc.getBaseDex()));
        pc.addBaseCon((byte) (con - pc.getBaseCon()));
        pc.addBaseCha((byte) (cha - pc.getBaseCha()));
        pc.getQuest().set_step(85, 0);
        pc.getQuest().set_step(86, 0);
        pc.getQuest().set_step(87, 0);
        pc.getQuest().set_step(88, 0);
        pc.getQuest().set_step(89, 0);
        pc.getQuest().set_step(90, 0);
    }

    public static void checkInitPower(L1PcInstance pc) {
        int initpoint = C_CreateChar.ORIGINAL_AMOUNT[pc.getType()];
        final String msg0 = "初始能力點數";
        String msg2 = String.valueOf(initpoint - pc.getTempInitPoint());
        final String msg3 = "請選擇想要提升的屬性：";
        if (initpoint - pc.getTempInitPoint() > 0) {
            String[] msgs = {msg0, msg2, msg3};
            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "charreset1", msgs));
        } else {
            if (pc.getLevel() == 1) {
                CharacterTable.saveCharStatus(pc);
            }
            checkLevelUp(pc);
        }
    }

    private static void checkLevelUp(L1PcInstance pc) {
        final String msg0 = "提升等級";
        final String msg2 = "0";
        final String msg3 = "要提升多少等級呢?";
        String msg4 = "";
        String msg5 = "";
        if (pc.getTempMaxLevel() - pc.getTempLevel() > 10 && pc.getTempLevel() + 10 < 51) {
            msg4 = "提升 1 級";
            msg5 = "提升 10 級";
        } else {
            msg4 = "提升 1 級";
            msg5 = "";
        }
        if (pc.getTempMaxLevel() - pc.getTempLevel() > 0 && pc.getTempLevel() < 50) {
            String[] msgs = {msg0, msg2, msg3, msg4, msg5};
            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "charreset2", msgs));
        } else {
            checkBonusPower(pc);
        }
    }

    private static void checkBonusPower(L1PcInstance pc) {
        final String msg0 = "等級獎勵點數";
        String msg2 = String.valueOf(pc.getTempMaxLevel() - pc.getTempLevel());
        final String msg3 = "請選擇想要提升的屬性：";
        if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
            String[] msgs = {msg0, msg2, msg3};
            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "charreset3", msgs));
        } else {
            checkElixirPower(pc);
        }
    }

    private static void checkElixirPower(L1PcInstance pc) {
        String msg0 = "萬能藥點數";
        String msg2 = String.valueOf(pc.getElixirStats() - pc.getTempElixirstats());
        String msg3 = "請選擇想要提升的屬性：";
        final String msg4 = "力量";
        final String msg5 = "智力";
        final String msg6 = "精神";
        final String msg7 = "敏捷";
        final String msg8 = "體質";
        final String msg9 = "魅力";
        if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
            String[] msgs = {msg0, msg2, msg3, msg4, msg5, msg6, msg7, msg8, msg9};
            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "charreset4", msgs));
        } else {
            msg0 = "重置完成。";
            msg2 = String.valueOf(pc.getElixirStats() - pc.getTempElixirstats());
            msg3 = "已完成所有點數的分配！";
            String[] msgs = {msg0, msg2, msg3};
            pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "charreset5", msgs));
        }
    }

    private static void saveNewCharStatus(L1PcInstance pc) {
        pc.getInventory().consumeItem(49142, 1L);
        pc.setInCharReset(false);
        pc.refresh();
        pc.setExp(pc.getoldexp());
        pc.setCurrentHp(pc.getMaxHp());
        pc.setCurrentMp(pc.getMaxMp());
        if (pc.getTempMaxLevel() != pc.getLevel()) {
            pc.setLevel(pc.getTempMaxLevel());
            pc.setExp(ExpTable.getExpByLevel(pc.getTempMaxLevel()));
        }
        pc.setTempMaxLevel(0);
        if (pc.getLevel() > 50) {
            pc.setBonusStats(pc.getLevel() - 50);
        } else {
            pc.setBonusStats(0);
        }
        pc.sendPackets(new S_Paralysis(4, false));
        try {
            pc.save();
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
        L1Teleport.teleport(pc, 32628, 32772, (short) 4, 4, false);
        RecordTable.get().reshp1(pc.getName());
        pc.sendPackets(new S_SPMR(pc));
        pc.sendPackets(new S_OwnCharStatus(pc));
        pc.sendPackets(new S_PacketBox(132, pc.getEr()));
        L1PolyMorph.undoPoly(pc);
        RecordTable.get().reshp1(pc.getName());
        if (ConfigOther.restsavepclog) {
            pc.setsavepclog((int) pc.getExp());
            pc.setExp(1L);
            try {
                Thread.sleep(1000L);
            } catch (Exception ex) {
            }
            pc.setExp(pc.gesavepclog());
        }
        pc.onChangeExp();
    }

    public static void clanLevel(L1PcInstance pc, int yy) {
        if (yy == 1) {
            leverUpClan(pc);
        } else if (yy == 3) {
            if (pc.getQuest().get_step(8541) == 10) {
                pc.sendPackets(new S_SystemMessage("今日貢獻金幣已達上限"));
                return;
            }
            if (!pc.getClanname().equals(pc.getClanNameContribution())) {
                pc.setClanNameContribution(pc.getClanname());
            }
            if (pc.getQuest().get_step(8541) == 0) {
                pc.getQuest().set_step(8541, 1);
            } else if (pc.getQuest().get_step(8541) == 1) {
                pc.getQuest().set_step(8541, 2);
            } else if (pc.getQuest().get_step(8541) == 2) {
                pc.getQuest().set_step(8541, 3);
            } else if (pc.getQuest().get_step(8541) == 3) {
                pc.getQuest().set_step(8541, 4);
            } else if (pc.getQuest().get_step(8541) == 4) {
                pc.getQuest().set_step(8541, 5);
            } else if (pc.getQuest().get_step(8541) == 5) {
                pc.getQuest().set_step(8541, 6);
            } else if (pc.getQuest().get_step(8541) == 6) {
                pc.getQuest().set_step(8541, 7);
            } else if (pc.getQuest().get_step(8541) == 7) {
                pc.getQuest().set_step(8541, 8);
            } else if (pc.getQuest().get_step(8541) == 8) {
                pc.getQuest().set_step(8541, 9);
            } else if (pc.getQuest().get_step(8541) == 9) {
                pc.getQuest().set_step(8541, 10);
            }
            L1Clan clan3 = WorldClan.get().getClan(pc.getClanname());
            pc.setclanadena(pc.getclanadena() + ConfigClan.PcClanAdena);
            clan3.setclanadena(clan3.getclanadena() + ConfigClan.PcClanAdena);
            pc.getInventory().consumeItem(40308, ConfigClan.PcClanAdena);
            ClanTable.getInstance().updateClan(clan3);
            pc.sendPackets(new S_SystemMessage("你給予目前的血盟資金有【" + pc.getclanadena() + "】。"));
            pc.sendPackets(new S_SystemMessage("你的血盟目前所獲得的資金總合:【" + clan3.getclanadena() + "】。"));
        } else if (yy == 5) {
            if (pc.getQuest().get_step(8545) == 1) {
                pc.sendPackets(new S_SystemMessage("元寶貢獻能量今日已達上限"));
                return;
            }
            if (!pc.getClanname().equals(pc.getClanNameContribution())) {
                pc.setClanNameContribution(pc.getClanname());
            }
            L1Clan clan3 = WorldClan.get().getClan(pc.getClanname());
            clan3.setClanContribution(clan3.getClanContribution() + ConfigClan.ClanContribution);
            ClanTable.getInstance().updateClan(clan3);
            pc.setClanContribution(pc.getClanContribution() + ConfigClan.ClanContribution);
            pc.getInventory().storeItem(92164, ConfigClan.ClanContribution);
            pc.sendPackets(new S_SystemMessage("獲得血盟貢獻幣:【" + ConfigClan.ClanContribution + "】個。"));
            pc.sendPackets(new S_SystemMessage("你給予目前的血盟貢獻度有【" + pc.getClanContribution() + "】點。"));
            pc.sendPackets(new S_SystemMessage("你的血盟目前所獲得的貢獻度總合有【" + clan3.getClanContribution() + "】點。"));
            pc.getQuest().set_step(8545, 1);
            pc.getInventory().consumeItem(44070, ConfigClan.ClanItem44070);
        } else if (yy == 6) {
            if (pc.getQuest().get_step(8546) == 1) {
                pc.sendPackets(new S_SystemMessage("元寶貢獻金幣今日已達上限"));
                return;
            }
            if (!pc.getClanname().equals(pc.getClanNameContribution())) {
                pc.setClanNameContribution(pc.getClanname());
            }
            L1Clan clan3 = WorldClan.get().getClan(pc.getClanname());
            clan3.setclanadena(clan3.getclanadena() + ConfigClan.ClanContribution_1);
            ClanTable.getInstance().updateClan(clan3);
            pc.setclanadena(pc.getclanadena() + ConfigClan.ClanContribution_1);
            pc.sendPackets(new S_SystemMessage("你給予目前的血盟資金有:【" + pc.getclanadena() + "】。"));
            pc.sendPackets(new S_SystemMessage("你的血盟目前所獲得的資金總合有【" + clan3.getclanadena() + "】。"));
            pc.getQuest().set_step(8546, 1);
            pc.getInventory().consumeItem(44070, ConfigClan.ClanItem44070_1);
        }
    }

    private static void leverUpClan(L1PcInstance pc) {
        if (pc.getClanid() == 0) {
            pc.sendPackets(new S_SystemMessage("血盟等級升級必須先擁有血盟喔。"));
            return;
        }

        L1Clan clan = WorldClan.get().getClan(pc.getClanname());
        if (clan == null) {
            pc.sendPackets(new S_SystemMessage("查無血盟。"));
            return;
        }

        if (clan.getClanLevel() >= ConfigClan.CLAN_MAX_LEVEL) {
            pc.sendPackets(new S_SystemMessage("你所屬的血盟已強化至最高等級。"));
            return;
        }

        if (checkClanLevelUpCondition(pc, clan)) {
            consumeInventoryItems(pc, clan);
            leverUpClan(pc, clan);
        }
    }

    private static Boolean checkClanLevelUpCondition(L1PcInstance pc, L1Clan clanBean) {
        ClanLevelUpCondition condition = ConfigClan.clansLevelUpCondition.get(clanBean.getClanLevel());

        if (condition.getClanEnergy() > clanBean.getClanContribution()) {
            pc.sendPackets(new S_ServerMessage(String.format("血盟能量不足%s個", condition.getClanAdena())));
            return Boolean.FALSE;
        }

        if (condition.getClanAdena() > clanBean.getclanadena()) {
            pc.sendPackets(new S_ServerMessage(String.format("血盟金幣不足%s個", condition.getClanAdena())));
            return Boolean.FALSE;
        }

        for (Map.Entry<Integer, Integer> entry : condition.getMaterials().entrySet()) {
            L1ItemInstance inventoryItem = pc.getInventory().findItemId(entry.getKey());
            L1Item consumeItem = ItemTable.get().getTemplate(entry.getKey());

            if (inventoryItem == null) {
                pc.sendPackets(new S_ServerMessage(String.format("缺少%s(%s)", consumeItem.getName(), entry.getValue())));
                return Boolean.FALSE;
            }

            if (inventoryItem.getCount() < entry.getValue()) {
                pc.sendPackets(new S_ServerMessage(String.format("%s數量不足(%s)", consumeItem.getName(), entry.getValue())));
                return Boolean.FALSE;
            }
        }
        return Boolean.TRUE;
    }

    private static void consumeInventoryItems(L1PcInstance pc, L1Clan clanBean) {
        ClanLevelUpCondition condition = ConfigClan.clansLevelUpCondition.get(clanBean.getClanLevel());
        condition.getMaterials().forEach((itemId, itemCount) -> pc.getInventory().consumeItem(itemId, itemCount));
    }

    private static void leverUpClan(L1PcInstance pc, L1Clan clanBean) {
        ClanLevelUpCondition condition = ConfigClan.clansLevelUpCondition.get(clanBean.getClanLevel());

        int newClanLevel = clanBean.getClanLevel() + 1;

        clanBean.setClanLevel(newClanLevel);
        clanBean.setClanContribution(clanBean.getClanContribution() - condition.getClanEnergy());
        clanBean.setclanadena(clanBean.getclanadena() - condition.getClanAdena());

        ClanTable.getInstance().updateClan(clanBean);
        pc.sendPackets(new S_SystemMessage("你的血盟等級已強化為 " + newClanLevel + " 級。"));
        L1PcInstance[] clanMember = clanBean.getOnlineClanMember();
        if (clanMember.length > 0) {
            pc.sendPackets(
                    new S_SystemMessage("你所屬的血盟(" + pc.getClanname() + ")等級已強化為 " + newClanLevel + " 級"));
        }
    }

    private static void newai(L1PcInstance pc) {
        String[] info = new String[34];
        info[1] = String.valueOf(pc.getnewai1());
        info[2] = String.valueOf(pc.getnewai2());
        info[3] = String.valueOf(pc.getnewai3());
        info[4] = String.valueOf(pc.getnewai4());
        info[5] = String.valueOf(pc.getnewai5());
        info[6] = String.valueOf(pc.getnewai6());
        switch (L1ActionPc._random.nextInt(10) + 1) {
            case 1: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai", info));
                break;
            }
            case 2: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai1", info));
                break;
            }
            case 3: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai2", info));
                break;
            }
            case 4: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai3", info));
                break;
            }
            case 5: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai4", info));
                break;
            }
            case 6: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai5", info));
                break;
            }
            case 7: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai6", info));
                break;
            }
            case 8: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai7", info));
                break;
            }
            case 9: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai8", info));
                break;
            }
            case 10: {
                pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "newai9", info));
                break;
            }
        }
    }

    public L1PcInstance get_pc() {
        return _pc;
    }

    public void action(String cmd, long amount) {
        try {
            if (_pc.isInCharReset()) {
                switch (cmd) {
                    case "1": {
                        if (checkvalid(_pc, cmd)) {
                            setLevelUp(_pc, 1);
                        }
                        checkLevelUp(_pc);
                        break;
                    }
                    case "10": {
                        if (checkvalid(_pc, cmd)) {
                            setLevelUp(_pc, 10);
                        }
                        checkLevelUp(_pc);
                        break;
                    }
                    case "OK": {
                        saveNewCharStatus(_pc);
                        break;
                    }
                    case "Cha": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseCha(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "Con": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseCon(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "Dex": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseDex(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "Int": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseInt(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "Str": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseStr(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "Wis": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.addBaseWis(1);
                            setLevelUp(_pc, 1);
                        }
                        checkBonusPower(_pc);
                        break;
                    }
                    case "exCha": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseCha(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "exCon": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseCon(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "exDex": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseDex(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "exInt": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseInt(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "exStr": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseStr(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "exWis": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempElixirstats(_pc.getTempElixirstats() + 1);
                            _pc.addBaseWis(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkElixirPower(_pc);
                        break;
                    }
                    case "initCha": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseCha(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    case "initCon": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseCon(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    case "initDex": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseDex(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    case "initInt": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseInt(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    case "initStr": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseStr(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    case "initWis": {
                        if (checkvalid(_pc, cmd)) {
                            _pc.setTempInitPoint(_pc.getTempInitPoint() + 1);
                            _pc.addBaseWis(1);
                            _pc.sendPackets(new S_OwnCharStatus2(_pc));
                        }
                        checkInitPower(_pc);
                        break;
                    }
                    default:
                        break;
                }
                return;
            }
            if (_pc.isShapeChange()) {
                _pc.get_other().set_gmHtml(null);
                int awakeSkillId = _pc.getAwakeSkillId();
                if (awakeSkillId == 185 || awakeSkillId == 190 || awakeSkillId == 195) {
                    _pc.sendPackets(new S_ServerMessage(1384));
                    return;
                }
                L1PolyMorph.handleCommands(_pc, cmd);
                _pc.setShapeChange(false);
                _pc.setSummonMonster(false);
                return;
            }
            if (_pc.isItemPoly()) {
                L1ItemInstance item = _pc.getPolyScroll();
                L1PolyMorph poly = PolyTable.get().getTemplate(cmd);
                if (poly != null || cmd.equals("none")) {
                    if (item.getItemId() == 44212 || (item.getItemId() >= 80057 && item.getItemId() <= 80066)) {
                        usePolyBook(_pc, item, cmd);
                        return;
                    }
                    if (item.getItemId() >= 80067 && item.getItemId() <= 80070) {
                        usePolyBookcouitem(_pc, item, cmd);
                        return;
                    }
                    usePolyScroll(_pc, item, cmd);
                    return;
                }
            }
            if (_pc.isPhantomTeleport()) {
                usePhantomTeleport(_pc, cmd);
                return;
            }
            if (_pc.get_other().get_gmHtml() != null) {
                _pc.get_other().get_gmHtml().action(cmd);
                return;
            }
            if (AutoAttackUpdate.getInstance().PcCommand(_pc, cmd)) {
                return;
            }
            if (TFUpdate.getInstance().PcCommand(_pc, cmd)) {
                return;
            }
            if (CardBookCmd.get().Cmd(_pc, cmd)) {
                return;
            }
            if (CardBookCmd.get().PolyCmd(_pc, cmd)) {
                return;
            }
            if (FollowAutoAttackUpdate.getInstance().PcCommand(_pc, cmd)) {
                return;
            }
            if (ItemActionPoly.forNpcQuest(cmd, _pc)) {
                return;
            }
            L1ItemInstance item2 = _pc.getPolyScrol2();
            if (ItemAction.forNpcQuest(cmd, _pc, item2)) {
                return;
            }
            if (ItemShop.forNpcQuest(cmd, _pc)) {
                return;
            }
            // 紋樣系統
            if (L1Yiwa.yiwa(cmd, _pc)) {
                return;
            }
            if (L1Shaha.shaha(cmd, _pc)) {
                return;
            }
            if (L1Pageliao.pageliao(cmd, _pc)) {
                return;
            }
            if (L1Mapule.mapule(cmd, _pc)) {
                return;
            }
            if (L1Yinhaisa.yinhaisa(cmd, _pc)) {
                return;
            }

            _pc.get_other().set_gmHtml(null);
            if (_pc.isGm()) {
                if (cmd.equals("tp_refresh")) {
                    L1ToPC2.checkTPhtmlPredicate(_pc, 0, true);
                } else if (cmd.equals("tp_refresh_map")) {
                    L1ToPC2.checkTPhtmlPredicate(_pc, 0, false);
                } else if (cmd.equals("tp_page_up")) {
                    L1ToPC2.checkTPhtml(_pc, _pc.get_other().get_page() - 1);
                } else if (cmd.equals("tp_page_down")) {
                    L1ToPC2.checkTPhtml(_pc, _pc.get_other().get_page() + 1);
                } else if (cmd.matches("tp_[0-9]+")) {
                    int index = Integer.parseInt(cmd.substring(3));
                    L1ToPC2.teleport2Player(_pc, index);
                }
            }
            if (cmd.equalsIgnoreCase("power")) {
                if (_pc.power()) {
                    _pc.sendPackets(new S_Bonusstats(_pc.getId()));
                }
                _pc.sendPackets(new S_ServerMessage("\\fU請輸入玩家名稱。"));
            } else if (cmd.equalsIgnoreCase("bad_3")) {
                _pc.setKeyInEnemy(true);
                _pc.sendPackets(new S_ServerMessage("\\fU請輸入玩家名稱。"));

            } else if (cmd.equalsIgnoreCase("bad_4")) {
                _pc.setKeyOutEnemy(true);
                _pc.sendPackets(new S_ServerMessage("\\fU請輸入玩家名稱。"));
            } else if (cmd.equalsIgnoreCase("bad_5")) {
                BadEnemyList(_pc);
            } else if (cmd.equalsIgnoreCase("clanlv1")) {
                if (_pc.isCrown()) {
                    if (_pc.getClan() == null) {
                        _pc.sendPackets(new S_SystemMessage("血盟等級升級必須先擁有血盟喔。"));
                    } else {
                        clanLevel(_pc, 1);
                        L1Clan clan = WorldClan.get().getClan(_pc.getClanname());
                        String[] info = new String[7];
                        if (_pc.getClanid() > 0) {
                            info[0] = String.valueOf(_pc.getClanname());
                        } else {
                            info[0] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[1] = String.valueOf(clan.getClanLevel());
                        } else {
                            info[1] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[2] = String.valueOf(clan.getClanContribution());
                        } else {
                            info[2] = "無";
                        }
                        info[3] = String.valueOf(_pc.getPcContribution());
                        info[4] = String.valueOf(_pc.getClanContribution());
                        info[5] = String.valueOf(_pc.getclanadena());
                        info[6] = String.valueOf(clan.getclanadena());
                        _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "clanlvadd", info));
                    }
                } else {
                    _pc.sendPackets(new S_SystemMessage("你的職業無法強化血盟等級。"));
                }
            } else if (cmd.equals("deletebook")) {
                _pc.sendPackets(new S_SystemMessage("請輸入刪除座標名稱(60秒內)"));
                _pc.setSkillEffect(95133, 60000);
            } else if (cmd.equals("clanlv3")) {
                if (_pc.getClan() == null) {
                    _pc.sendPackets(new S_SystemMessage("想要貢獻必須先擁有血盟喔。"));
                } else if (_pc.getInventory().checkItem(40308, ConfigClan.PcClanAdena)) {
                    if (_pc.getLevel() >= ConfigClan.clanlevel) {
                        clanLevel(_pc, 3);
                        L1Clan clan = WorldClan.get().getClan(_pc.getClanname());
                        String[] info = new String[7];
                        if (_pc.getClanid() > 0) {
                            info[0] = String.valueOf(_pc.getClanname());
                        } else {
                            info[0] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[1] = String.valueOf(clan.getClanLevel());
                        } else {
                            info[1] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[2] = String.valueOf(clan.getClanContribution());
                        } else {
                            info[2] = "無";
                        }
                        info[3] = String.valueOf(_pc.getPcContribution());
                        info[4] = String.valueOf(_pc.getClanContribution());
                        info[5] = String.valueOf(_pc.getclanadena());
                        info[6] = String.valueOf(clan.getclanadena());
                        _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "clanlvadd", info));
                    } else {
                        _pc.sendPackets(new S_SystemMessage(ConfigClan.clanmsg1));
                    }
                } else {
                    _pc.sendPackets(new S_SystemMessage("金幣不足無法幫助你的血盟喔。"));
                }
            } else if (cmd.equals("clanlv5")) {
                if (_pc.getClan() == null) {
                    _pc.sendPackets(new S_SystemMessage("想要貢獻必須先擁有血盟喔。"));
                } else if (_pc.getInventory().checkItem(44070, ConfigClan.ClanItem44070)) {
                    if (_pc.getLevel() >= ConfigClan.clanlevel) {
                        clanLevel(_pc, 5);
                        L1Clan clan = WorldClan.get().getClan(_pc.getClanname());
                        String[] info = new String[7];
                        if (_pc.getClanid() > 0) {
                            info[0] = String.valueOf(_pc.getClanname());
                        } else {
                            info[0] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[1] = String.valueOf(clan.getClanLevel());
                        } else {
                            info[1] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[2] = String.valueOf(clan.getClanContribution());
                        } else {
                            info[2] = "無";
                        }
                        info[3] = String.valueOf(_pc.getPcContribution());
                        info[4] = String.valueOf(_pc.getClanContribution());
                        info[5] = String.valueOf(_pc.getclanadena());
                        info[6] = String.valueOf(clan.getclanadena());
                        _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "clanlvadd", info));
                    } else {
                        _pc.sendPackets(new S_SystemMessage(ConfigClan.clanmsg1));
                    }
                } else {
                    _pc.sendPackets(new S_SystemMessage("元寶不足無法幫助你的血盟喔。"));
                }
            } else if (cmd.equals("clanlv6")) {
                if (_pc.getClan() == null) {
                    _pc.sendPackets(new S_SystemMessage("想要貢獻必須先擁有血盟喔。"));
                } else if (_pc.getInventory().checkItem(44070, ConfigClan.ClanItem44070_1)) {
                    if (_pc.getLevel() >= ConfigClan.clanlevel) {
                        clanLevel(_pc, 6);
                        L1Clan clan = WorldClan.get().getClan(_pc.getClanname());
                        String[] info = new String[7];
                        if (_pc.getClanid() > 0) {
                            info[0] = String.valueOf(_pc.getClanname());
                        } else {
                            info[0] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[1] = String.valueOf(clan.getClanLevel());
                        } else {
                            info[1] = "無";
                        }
                        if (_pc.getClanid() > 0) {
                            info[2] = String.valueOf(clan.getClanContribution());
                        } else {
                            info[2] = "無";
                        }
                        info[3] = String.valueOf(_pc.getPcContribution());
                        info[4] = String.valueOf(_pc.getClanContribution());
                        info[5] = String.valueOf(_pc.getclanadena());
                        info[6] = String.valueOf(clan.getclanadena());
                        _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "clanlvadd", info));
                    } else {
                        _pc.sendPackets(new S_SystemMessage(ConfigClan.clanmsg1));
                    }
                } else {
                    _pc.sendPackets(new S_SystemMessage("元寶不足無法幫助你的血盟喔。"));
                }
            } else if (cmd.equalsIgnoreCase("cleanaiq")) {
                _pc.setnewai4(0);
                _pc.setnewai5(0);
                _pc.setnewai6(0);
                newai(_pc);
            } else if (cmd.equalsIgnoreCase("1q")) {
                _pc.setnewaiq1(1);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq1());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq1());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq1());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("2q")) {
                _pc.setnewaiq2(2);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq2());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq2());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq2());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("3q")) {
                _pc.setnewaiq3(3);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq3());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq3());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq3());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("4q")) {
                _pc.setnewaiq4(4);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq4());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq4());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq4());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.setSkillEffect(6930, 1200000);
                    _pc.killSkillEffectTimer(6933);
                }
            } else if (cmd.equalsIgnoreCase("5q")) {
                _pc.setnewaiq5(5);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq5());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq5());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq5());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("6q")) {
                _pc.setnewaiq6(6);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq6());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq6());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq6());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("7q")) {
                _pc.setnewaiq7(7);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq7());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq7());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq7());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("8q")) {
                _pc.setnewaiq8(8);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq8());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq8());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq8());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("9q")) {
                _pc.setnewaiq9(9);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq9());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq9());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq9());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("0q")) {
                _pc.setnewaiq0(0);
                if (_pc.getnewai4() == 0) {
                    _pc.setnewai4(_pc.getnewaiq1());
                } else if (_pc.getnewai5() == 0) {
                    _pc.setnewai5(_pc.getnewaiq1());
                } else if (_pc.getnewai6() == 0) {
                    _pc.setnewai6(_pc.getnewaiq1());
                }
                newai(_pc);
                if (_pc.getnewai4() == _pc.getnewai1() && _pc.getnewai5() == _pc.getnewai2()
                        && _pc.getnewai6() == _pc.getnewai3()) {
                    _pc.sendPackets(new S_SystemMessage("恭喜您驗證成功"));
                    _pc.setnewai4(0);
                    _pc.setnewai5(0);
                    _pc.setnewai6(0);
                    _pc.sendPackets(new S_CloseList(_pc.getId()));
                    _pc.killSkillEffectTimer(6933);
                    _pc.setSkillEffect(6930, 1200000);
                }
            } else if (cmd.equalsIgnoreCase("locerr1")) {
                if (_pc.isParalyzed() || _pc.isSleeped() || _pc.isParalyzedX()) {
                    return;
                }
                if (_pc.isDead()) {
                    return;
                }
                if (_pc.isInvisble()) {
                    return;
                }
                if (_pc.hasSkillEffect(4000)) {
                    return;
                }
                if (_pc.hasSkillEffect(33) || _pc.hasSkillEffect(50) || _pc.hasSkillEffect(66)
                        || _pc.hasSkillEffect(87)) {
                    return;
                }
                if (_pc.hasSkillEffect(1011)) {
                    return;
                }
                if (_pc.hasSkillEffect(157)) {
                    return;
                }
                if (_pc.hasSkillEffect(1009)) {
                    return;
                }
                if (_pc.hasSkillEffect(4017)) {
                    return;
                }
                if (_pc.hasSkillEffect(192)) {
                    return;
                }
                if (_pc.hasSkillEffect(87)) {
                    return;
                }
                if (_pc.hasSkillEffect(208)) {
                    return;
                }
                if (_pc.hasSkillEffect(1011)) {
                    return;
                }
                if (_pc.hasSkillEffect(1009)) {
                    return;
                }
                if (_pc.hasSkillEffect(55889)) {
                    _pc.sendPackets(new S_ServerMessage("無法連續使用該功能"));
                    return;
                }
                _pc.setSkillEffect(55889, 30000);
                _pc.set_unfreezingTime(5);
            } else if (cmd.equalsIgnoreCase("locerr2")) {
                if (_pc.isParalyzed() || _pc.isSleeped() || _pc.isParalyzedX()) {
                    return;
                }
                if (_pc.isDead()) {
                    return;
                }
                if (_pc.isInvisble()) {
                    return;
                }
                if (_pc.hasSkillEffect(4000)) {
                    return;
                }
                if (_pc.hasSkillEffect(33) || _pc.hasSkillEffect(50) || _pc.hasSkillEffect(66)
                        || _pc.hasSkillEffect(87)) {
                    return;
                }
                if (_pc.hasSkillEffect(1011)) {
                    return;
                }
                if (_pc.hasSkillEffect(157)) {
                    return;
                }
                if (_pc.hasSkillEffect(1009)) {
                    return;
                }
                if (_pc.hasSkillEffect(4017)) {
                    return;
                }
                if (_pc.hasSkillEffect(192)) {
                    return;
                }
                if (_pc.hasSkillEffect(87)) {
                    return;
                }
                if (_pc.hasSkillEffect(208)) {
                    return;
                }
                if (_pc.hasSkillEffect(1011)) {
                    return;
                }
                if (_pc.hasSkillEffect(1009)) {
                    return;
                }
                if (_pc.hasSkillEffect(55889)) {
                    _pc.sendPackets(new S_ServerMessage("無法連續使用該功能"));
                    return;
                }
                _pc.setSkillEffect(55889, 30000);
                _pc.set_misslocTime(3);
            } else if (cmd.equalsIgnoreCase("index")) {
                _pc.isWindows();
            } else if (cmd.equalsIgnoreCase("gfxid")) {
                if (_pc.getopengfxid()) {
                    _pc.setopengfxid(false);
                    _pc.sendPackets(new S_ServerMessage("關閉-畫面中玩家特效"));
                } else {
                    _pc.setopengfxid(true);
                    _pc.sendPackets(new S_ServerMessage("開啟-畫面中玩家特效"));
                }
            } else if (cmd.equalsIgnoreCase("npcdropmenu")) {
                _pc.setItemName(true);
                _pc.sendPackets(new S_SystemMessage("\\fY請輸入要查詢道具名稱後，按(Enter)。"));
            } else if (cmd.equalsIgnoreCase("pc_score")) {
                if (_pc.get_other().get_score() >= 0) {
                    _pc.sendPackets(new S_ServerMessage("\\aD目前陣營積分:" + _pc.get_other().get_score()));
                }
            } else if (cmd.equalsIgnoreCase("npcclanbuff")) {
                if (_pc.hasSkillEffect(95413)) {
                    return;
                }
                if (_pc.getClanid() <= 0) { //Kevin 新增防呆
                    _pc.sendPackets(new S_ServerMessage("沒有加入血盟，有錢你也無法使用此功能"));
                    return;
                }
                if (_pc.getInventory().checkItem(ConfigOther.NeedItem, ConfigOther.NeedItemCount)) {
                    _pc.getInventory().consumeItem(ConfigOther.NeedItem, ConfigOther.NeedItemCount);
                    _pc.setSkillEffect(95413, 10000);
                    int[] skills = ConfigOther.Give_skill;
                    L1Clan clan2 = WorldClan.get().getClan(_pc.getClanname());
                    L1PcInstance[] clanMembers = clan2.getOnlineClanMember();
                    int i = 0;
                    while (i < skills.length) {
                        L1PcInstance[] array;
                        int length = (array = clanMembers).length;
                        int k = 0;
                        while (k < length) {
                            L1PcInstance clanMember1 = array[k];
                            L1Skills skill = SkillsTable.get().getTemplate(skills[i]);
                            L1SkillUse skillUse = new L1SkillUse();
                            skillUse.handleCommands(clanMember1, skills[i], clanMember1.getId(), clanMember1.getX(),
                                    clanMember1.getY(), skill.getBuffDuration(), 4);
                            ++k;
                        }
                        ++i;
                    }
                    L1PcInstance[] onlineClanMember;
                    int k = (onlineClanMember = clan2.getOnlineClanMember()).length;
                    int l = 0;
                    while (l < k) {
                        L1PcInstance clanMembers2 = onlineClanMember[l];
                        clanMembers2
                                .sendPackets(new S_SystemMessage(String.format(ConfigOther.Msg, _pc.getName())));
                        ++l;
                    }
                } else {
                    _pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg));
                }
            } else if (cmd.equalsIgnoreCase("npcallbuff")) {
                if (_pc.hasSkillEffect(95413)) {
                    return;
                }
                _pc.setSkillEffect(95413, 10000);
                if (_pc.getInventory().checkItem(ConfigOther.NeedItem1, ConfigOther.NeedItemCount1)) {
                    World.get().broadcastPacketToAll(
                            new S_SystemMessage(String.format(ConfigOther.Msg1, _pc.getName())));
                    //World.get().broadcastPacketToAll( Kevin  太擋視線 拿掉
                    //new S_BlueMessage(166, "\\f2" + String.format(ConfigOther.Msg1, _pc.getName())));
                    _pc.getInventory().consumeItem(ConfigOther.NeedItem1, ConfigOther.NeedItemCount1);
                    AllBuffRunnable allBuffRunnable = new AllBuffRunnable();
                    GeneralThreadPool.get().execute(allBuffRunnable);
                    try {
                        Thread.sleep(1000L);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } else {
                    _pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg1));
                }
            } else if (cmd.equalsIgnoreCase("pcbuff1")) {
                if (_pc.hasSkillEffect(95413)) {
                    return;
                }
                _pc.setSkillEffect(95413, 10000);
                if (_pc.getInventory().checkItem(ConfigOther.NeedItem2, ConfigOther.NeedItemCount2)) {
                    _pc.getInventory().consumeItem(ConfigOther.NeedItem2, ConfigOther.NeedItemCount2);
                    _pc.sendPackets(new S_ServerMessage(ConfigOther.Msg2));
                    int[] skills = ConfigOther.Give_skill2;
                    int j = 0;
                    while (j < skills.length) {
                        L1Skills skill2 = SkillsTable.get().getTemplate(skills[j]);
                        L1SkillUse skillUse2 = new L1SkillUse();
                        skillUse2.handleCommands(_pc, skills[j], _pc.getId(), _pc.getX(),
                                _pc.getY(), skill2.getBuffDuration(), 4);
                        ++j;
                    }
                } else {
                    _pc.sendPackets(new S_ServerMessage(ConfigOther.ItemMsg2));
                }
            } else if (cmd.equalsIgnoreCase("kor_meau")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("aa")) {
                boolean isTalent = false;
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    isTalent = true;
                }
                if (isTalent) {
                    if (_pc.getlogpcpower_SkillFor1() >= 10) {
                        _pc.sendPackets(new S_SystemMessage("該天賦技能已滿"));
                        isTalent = false;
                    } else {
                        _pc.setlogpcpower_SkillFor1(_pc.getlogpcpower_SkillFor1() + 1);
                        _pc.setlogpcpower_SkillCount(_pc.getlogpcpower_SkillCount() - 1);
                    }
                }
                String[] info = new String[6];
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    info[0] = String.valueOf(_pc.getlogpcpower_SkillCount());
                } else {
                    info[0] = "0";
                }
                if (_pc.getlogpcpower_SkillFor1() > 0) {
                    info[1] = String.valueOf(_pc.getlogpcpower_SkillFor1());
                } else {
                    info[1] = "0";
                }
                if (_pc.getlogpcpower_SkillFor2() > 0) {
                    info[2] = String.valueOf(_pc.getlogpcpower_SkillFor2());
                } else {
                    info[2] = "0";
                }
                if (_pc.getlogpcpower_SkillFor3() > 0) {
                    info[3] = String.valueOf(_pc.getlogpcpower_SkillFor3());
                } else {
                    info[3] = "0";
                }
                if (_pc.getlogpcpower_SkillFor4() > 0) {
                    info[4] = String.valueOf(_pc.getlogpcpower_SkillFor4());
                } else {
                    info[4] = "0";
                }
                if (_pc.getlogpcpower_SkillFor5() > 0) {
                    info[5] = String.valueOf(_pc.getlogpcpower_SkillFor5());
                } else {
                    info[5] = "0";
                }
                if (_pc.isCrown()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talCrown", info));
                } else if (_pc.isKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talKnight", info));
                } else if (_pc.isWizard()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talWizard", info));
                } else if (_pc.isElf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talElf", info));
                } else if (_pc.isDarkelf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDarkelf", info));
                } else if (_pc.isDragonKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDragonK", info));
                } else if (_pc.isIllusionist()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talIllusi", info));
                }
            } else if (cmd.equalsIgnoreCase("bb")) {
                boolean isTalent = false;
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    isTalent = true;
                }
                if (isTalent) {
                    if (_pc.getlogpcpower_SkillFor2() >= 10) {
                        _pc.sendPackets(new S_SystemMessage("該天賦技能已滿"));
                        isTalent = false;
                    } else {
                        _pc.setlogpcpower_SkillFor2(_pc.getlogpcpower_SkillFor2() + 1);
                        _pc.setlogpcpower_SkillCount(_pc.getlogpcpower_SkillCount() - 1);
                    }
                }
                String[] info = new String[6];
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    info[0] = String.valueOf(_pc.getlogpcpower_SkillCount());
                } else {
                    info[0] = "0";
                }
                if (_pc.getlogpcpower_SkillFor1() > 0) {
                    info[1] = String.valueOf(_pc.getlogpcpower_SkillFor1());
                } else {
                    info[1] = "0";
                }
                if (_pc.getlogpcpower_SkillFor2() > 0) {
                    info[2] = String.valueOf(_pc.getlogpcpower_SkillFor2());
                } else {
                    info[2] = "0";
                }
                if (_pc.getlogpcpower_SkillFor3() > 0) {
                    info[3] = String.valueOf(_pc.getlogpcpower_SkillFor3());
                } else {
                    info[3] = "0";
                }
                if (_pc.getlogpcpower_SkillFor4() > 0) {
                    info[4] = String.valueOf(_pc.getlogpcpower_SkillFor4());
                } else {
                    info[4] = "0";
                }
                if (_pc.getlogpcpower_SkillFor5() > 0) {
                    info[5] = String.valueOf(_pc.getlogpcpower_SkillFor5());
                } else {
                    info[5] = "0";
                }
                if (_pc.isCrown()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talCrown", info));
                } else if (_pc.isKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talKnight", info));
                } else if (_pc.isWizard()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talWizard", info));
                } else if (_pc.isElf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talElf", info));
                } else if (_pc.isDarkelf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDarkelf", info));
                } else if (_pc.isDragonKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDragonK", info));
                } else if (_pc.isIllusionist()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talIllusi", info));
                }
            } else if (cmd.equalsIgnoreCase("cc")) {
                boolean isTalent = false;
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    isTalent = true;
                }
                if (isTalent) {
                    if (_pc.getlogpcpower_SkillFor3() >= 10) {
                        _pc.sendPackets(new S_SystemMessage("該天賦技能已滿"));
                        isTalent = false;
                    } else {
                        _pc.setlogpcpower_SkillFor3(_pc.getlogpcpower_SkillFor3() + 1);
                        _pc.setlogpcpower_SkillCount(_pc.getlogpcpower_SkillCount() - 1);
                    }
                }
                String[] info = new String[6];
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    info[0] = String.valueOf(_pc.getlogpcpower_SkillCount());
                } else {
                    info[0] = "0";
                }
                if (_pc.getlogpcpower_SkillFor1() > 0) {
                    info[1] = String.valueOf(_pc.getlogpcpower_SkillFor1());
                } else {
                    info[1] = "0";
                }
                if (_pc.getlogpcpower_SkillFor2() > 0) {
                    info[2] = String.valueOf(_pc.getlogpcpower_SkillFor2());
                } else {
                    info[2] = "0";
                }
                if (_pc.getlogpcpower_SkillFor3() > 0) {
                    info[3] = String.valueOf(_pc.getlogpcpower_SkillFor3());
                } else {
                    info[3] = "0";
                }
                if (_pc.getlogpcpower_SkillFor4() > 0) {
                    info[4] = String.valueOf(_pc.getlogpcpower_SkillFor4());
                } else {
                    info[4] = "0";
                }
                if (_pc.getlogpcpower_SkillFor5() > 0) {
                    info[5] = String.valueOf(_pc.getlogpcpower_SkillFor5());
                } else {
                    info[5] = "0";
                }
                if (_pc.isCrown()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talCrown", info));
                } else if (_pc.isKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talKnight", info));
                } else if (_pc.isWizard()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talWizard", info));
                } else if (_pc.isElf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talElf", info));
                } else if (_pc.isDarkelf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDarkelf", info));
                } else if (_pc.isDragonKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDragonK", info));
                } else if (_pc.isIllusionist()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talIllusi", info));
                }
            } else if (cmd.equalsIgnoreCase("dd")) {
                boolean isTalent = false;
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    isTalent = true;
                }
                if (isTalent) {
                    if (_pc.getlogpcpower_SkillFor4() >= 10) {
                        _pc.sendPackets(new S_SystemMessage("該天賦技能已滿"));
                        isTalent = false;
                    } else {
                        _pc.setlogpcpower_SkillFor4(_pc.getlogpcpower_SkillFor4() + 1);
                        _pc.setlogpcpower_SkillCount(_pc.getlogpcpower_SkillCount() - 1);
                    }
                }
                String[] info = new String[6];
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    info[0] = String.valueOf(_pc.getlogpcpower_SkillCount());
                } else {
                    info[0] = "0";
                }
                if (_pc.getlogpcpower_SkillFor1() > 0) {
                    info[1] = String.valueOf(_pc.getlogpcpower_SkillFor1());
                } else {
                    info[1] = "0";
                }
                if (_pc.getlogpcpower_SkillFor2() > 0) {
                    info[2] = String.valueOf(_pc.getlogpcpower_SkillFor2());
                } else {
                    info[2] = "0";
                }
                if (_pc.getlogpcpower_SkillFor3() > 0) {
                    info[3] = String.valueOf(_pc.getlogpcpower_SkillFor3());
                } else {
                    info[3] = "0";
                }
                if (_pc.getlogpcpower_SkillFor4() > 0) {
                    info[4] = String.valueOf(_pc.getlogpcpower_SkillFor4());
                } else {
                    info[4] = "0";
                }
                if (_pc.getlogpcpower_SkillFor5() > 0) {
                    info[5] = String.valueOf(_pc.getlogpcpower_SkillFor5());
                } else {
                    info[5] = "0";
                }
                if (_pc.isCrown()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talCrown", info));
                } else if (_pc.isKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talKnight", info));
                } else if (_pc.isWizard()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talWizard", info));
                } else if (_pc.isElf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talElf", info));
                } else if (_pc.isDarkelf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDarkelf", info));
                } else if (_pc.isDragonKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDragonK", info));
                } else if (_pc.isIllusionist()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talIllusi", info));
                }
            } else if (cmd.equalsIgnoreCase("ee")) {
                boolean isTalent = false;
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    isTalent = true;
                }
                if (isTalent) {
                    if (_pc.getlogpcpower_SkillFor5() >= 10) {
                        _pc.sendPackets(new S_SystemMessage("該天賦技能已滿"));
                        isTalent = false;
                    } else {
                        _pc.setlogpcpower_SkillFor5(_pc.getlogpcpower_SkillFor5() + 1);
                        _pc.setlogpcpower_SkillCount(_pc.getlogpcpower_SkillCount() - 1);
                        if (_pc.isDragonKnight()) {
                            if (_pc.getlogpcpower_SkillFor5() == 1) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 2) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 3) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 4) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 5) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 6) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 7) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 8) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 9) {
                                _pc.addMaxHp(400);
                            } else if (_pc.getlogpcpower_SkillFor5() == 10) {
                                _pc.addMaxHp(400);
                                _pc.sendPackets(new S_OwnCharStatus(_pc));
                            }
                        }
                    }
                }
                String[] info = new String[6];
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    info[0] = String.valueOf(_pc.getlogpcpower_SkillCount());
                } else {
                    info[0] = "0";
                }
                if (_pc.getlogpcpower_SkillFor1() > 0) {
                    info[1] = String.valueOf(_pc.getlogpcpower_SkillFor1());
                } else {
                    info[1] = "0";
                }
                if (_pc.getlogpcpower_SkillFor2() > 0) {
                    info[2] = String.valueOf(_pc.getlogpcpower_SkillFor2());
                } else {
                    info[2] = "0";
                }
                if (_pc.getlogpcpower_SkillFor3() > 0) {
                    info[3] = String.valueOf(_pc.getlogpcpower_SkillFor3());
                } else {
                    info[3] = "0";
                }
                if (_pc.getlogpcpower_SkillFor4() > 0) {
                    info[4] = String.valueOf(_pc.getlogpcpower_SkillFor4());
                } else {
                    info[4] = "0";
                }
                if (_pc.getlogpcpower_SkillFor5() > 0) {
                    info[5] = String.valueOf(_pc.getlogpcpower_SkillFor5());
                } else {
                    info[5] = "0";
                }
                if (_pc.isCrown()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talCrown", info));
                } else if (_pc.isKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talKnight", info));
                } else if (_pc.isWizard()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talWizard", info));
                } else if (_pc.isElf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talElf", info));
                } else if (_pc.isDarkelf()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDarkelf", info));
                } else if (_pc.isDragonKnight()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talDragonK", info));
                } else if (_pc.isIllusionist()) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "talIllusi", info));
                }

            } else if (cmd.equalsIgnoreCase("ff")) {
                L1Item temp = ItemTable.get().getTemplate(ConfigOther.recaseff);
                if (!_pc.getInventory().checkItem(ConfigOther.recaseff, ConfigOther.recaseffcount)) {
                    _pc.sendPackets(new S_SystemMessage(
                            String.valueOf(temp.getName()) + "不足" + ConfigOther.recaseffcount + "個"));
                    return;
                }
                if (_pc.getMeteLevel() == 0) {
                    _pc.sendPackets(new S_SystemMessage("您尚未轉身無法重置"));
                    return;
                }
                if (_pc.getlogpcpower_SkillCount() > 0) {
                    _pc.sendPackets(new S_SystemMessage("請先將天賦點數點完"));
                    return;
                }
                _pc.getInventory().consumeItem(44070, 499L);
                int SkillCount = _pc.getlogpcpower_SkillFor1() + _pc.getlogpcpower_SkillFor2()
                        + _pc.getlogpcpower_SkillFor3() + _pc.getlogpcpower_SkillFor4()
                        + _pc.getlogpcpower_SkillFor5();
                _pc.setlogpcpower_SkillCount(SkillCount);
                if (_pc.isDragonKnight()) {
                    _pc.addMaxHp(-400 * _pc.getlogpcpower_SkillFor5());
                    _pc.sendPackets(new S_OwnCharStatus(_pc));
                }
                _pc.setlogpcpower_SkillFor1(0);
                _pc.setlogpcpower_SkillFor2(0);
                _pc.setlogpcpower_SkillFor3(0);
                _pc.setlogpcpower_SkillFor4(0);
                _pc.setlogpcpower_SkillFor5(0);
                _pc.sendPackets(new S_SystemMessage("天賦能力已還原"));
                _pc.sendPackets(new S_CloseList(_pc.getId()));
            } else if (cmd.equalsIgnoreCase("atkmessage")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                if (_pc.hasSkillEffect(1688)) {
                    _pc.killSkillEffectTimer(1688);
                } else if (!_pc.hasSkillEffect(1688)) {
                    _pc.setSkillEffect(1688, 0);
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("partymessage")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                if (_pc.hasSkillEffect(1689)) {
                    _pc.killSkillEffectTimer(1689);
                } else if (!_pc.hasSkillEffect(1689)) {
                    _pc.setSkillEffect(1689, 0);
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("pcdrop")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                if (_pc.hasSkillEffect(1690)) {
                    _pc.killSkillEffectTimer(1690);
                } else if (!_pc.hasSkillEffect(1690)) {
                    _pc.setSkillEffect(1690, 0);
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("alldrop")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                if (_pc.hasSkillEffect(1691)) {
                    _pc.killSkillEffectTimer(1691);
                } else if (!_pc.hasSkillEffect(1691)) {
                    _pc.setSkillEffect(1691, 0);
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("yncheck")) {
                if (_pc.isActived()) {
                    _pc.sendPackets(new S_ServerMessage("掛機中無法使用開關"));
                    return;
                }
                if (_pc.hasSkillEffect(1692)) {
                    _pc.killSkillEffectTimer(1692);
                } else if (!_pc.hasSkillEffect(1692)) {
                    _pc.setSkillEffect(1692, 0);
                }
                String type1 = "";
                String type2 = "";
                String type3 = "";
                String type4 = "";
                String type5 = "";
                if (_pc.hasSkillEffect(1688)) {
                    type1 = "開啟";
                } else {
                    type1 = "關閉";
                }
                if (_pc.hasSkillEffect(1689)) {
                    type2 = "開啟";
                } else {
                    type2 = "關閉";
                }
                if (_pc.hasSkillEffect(1690)) {
                    type3 = "開啟";
                } else {
                    type3 = "關閉";
                }
                if (_pc.hasSkillEffect(1691)) {
                    type4 = "開啟";
                } else {
                    type4 = "關閉";
                }
                if (_pc.hasSkillEffect(1692)) {
                    type5 = "開啟";
                } else {
                    type5 = "關閉";
                }
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "pconly3",
                        new String[]{type1, type2, type3, type4, type5}));
            } else if (cmd.equalsIgnoreCase("newcharpra")) {
                if (_pc.hasSkillEffect(55899)) {
                    _pc.sendPackets(new S_ServerMessage("此功能無法連續使用"));
                    return;
                }
                if (!L1CastleLocation.checkInAllWarArea(_pc.getX(), _pc.getY(), _pc.getMapId())) {
                    if (_pc.getLevel() <= ConfigOther.newcharpralv) {
                        if (_pc.getnewcharpra()) {
                            _pc.setnewcharpra(false);
                            _pc.sendPackets(new S_ServerMessage("關閉-新手保護"));
                            _pc.setSkillEffect(55899, 60000);
                        } else if (!_pc.getnewcharpra()) {
                            _pc.setnewcharpra(true);
                            _pc.setSkillEffect(55899, 60000);
                            _pc.sendPackets(new S_ServerMessage("開啟-新手保護"));
                        }
                    } else {
                        _pc.sendPackets(new S_ServerMessage("等級大於" + ConfigOther.newcharpralv + "無法對您進行保護了"));
                    }
                } else {
                    _pc.sendPackets(new S_ServerMessage("旗子內禁止使用此功能"));
                }
            } else if (cmd.equalsIgnoreCase("qt")) {
                showStartQuest(_pc, _pc.getId());
            } else if (cmd.equalsIgnoreCase("quest")) {
                showQuest(_pc, _pc.getId());
            } else if (cmd.equalsIgnoreCase("questa")) {
                showQuestAll(_pc, _pc.getId());
            } else if (cmd.equalsIgnoreCase("i")) {
                L1Quest quest = QuestTable.get().getTemplate(_pc.getTempID());
                _pc.setTempID(0);
                if (quest == null) {
                    return;
                }
                QuestClass.get().showQuest(_pc, quest.get_id());
            } else if (cmd.equalsIgnoreCase("d")) {
                L1Quest quest = QuestTable.get().getTemplate(_pc.getTempID());
                _pc.setTempID(0);
                if (quest == null) {
                    return;
                }
                if (_pc.getQuest().isEnd(quest.get_id())) {
                    questDel(quest);
                    return;
                }
                if (!_pc.getQuest().isStart(quest.get_id())) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_q_not6"));
                    return;
                }
                questDel(quest);
            } else if (cmd.equalsIgnoreCase("dy")) {
                L1Quest quest = QuestTable.get().getTemplate(_pc.getTempID());
                _pc.setTempID(0);
                if (quest == null) {
                    return;
                }
                if (_pc.getQuest().isEnd(quest.get_id())) {
                    isDel(quest);
                    return;
                }
                if (!_pc.getQuest().isStart(quest.get_id())) {
                    _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_q_not6"));
                    return;
                }
                isDel(quest);
            } else if (cmd.equalsIgnoreCase("up")) {
                int page = _pc.get_other().get_page() - 1;
                L1ActionShowHtml show = new L1ActionShowHtml(_pc);
                show.showQuestMap(page);
            } else if (cmd.equalsIgnoreCase("dn")) {
                int page = _pc.get_other().get_page() + 1;
                L1ActionShowHtml show = new L1ActionShowHtml(_pc);
                show.showQuestMap(page);
            } else if (cmd.equalsIgnoreCase("q0")) {
                int key = _pc.get_other().get_page() * 10 + 0;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q1")) {
                int key = _pc.get_other().get_page() * 10 + 1;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q2")) {
                int key = _pc.get_other().get_page() * 10 + 2;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q3")) {
                int key = _pc.get_other().get_page() * 10 + 3;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q4")) {
                int key = _pc.get_other().get_page() * 10 + 4;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q5")) {
                int key = _pc.get_other().get_page() * 10 + 5;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q6")) {
                int key = _pc.get_other().get_page() * 10 + 6;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q7")) {
                int key = _pc.get_other().get_page() * 10 + 7;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q8")) {
                int key = _pc.get_other().get_page() * 10 + 8;
                showPage(key);
            } else if (cmd.equalsIgnoreCase("q9")) {
                int key = _pc.get_other().get_page() * 10 + 9;
                showPage(key);
            }
            /**
             * Kevin 小天 新增穿雲箭接收訊息開關
             */
            if (cmd.equalsIgnoreCase("Open_AllcallClan")) {
                _pc.setAllCall_clan(1);
                _pc.sendPackets(new S_ServerMessage("\\aE穿雲箭召換訊息：開啟"));
            } else if (cmd.equalsIgnoreCase("Close_AllcallClan")) {
                _pc.setAllCall_clan(0);

                _pc.sendPackets(new S_ServerMessage("\\aE穿雲箭召換訊息：關閉"));
            }

            if (cmd.equalsIgnoreCase("Allpcclan")) {// 邀請屏幕內所有盟友組隊
                if (_pc.isInParty()) {
                    if (!_pc.getParty().isLeader(_pc)) {
                        // 只有領導者才能邀請其他的成員。
                        _pc.sendPackets(new S_ServerMessage(416));
                        return;
                    }
                }

                if (_pc.getClanid() == 0) {
                    _pc.sendPackets(new S_SystemMessage("您還沒有血盟哦,快去加入一個血盟吧"));
                    return;
                }

                _pc.sendPackets(new S_SystemMessage("屏幕血盟組隊邀請已發送!等待入隊..."));
                for (L1Object obj : World.get().getVisibleObjects(_pc)) {
                    if (obj instanceof L1PcInstance) {
                        L1PcInstance tgpc = (L1PcInstance) obj;
                        if (tgpc.isGhost()) { // 鬼魂模式
                            continue;
                        }
                        if (tgpc.isDead()) { // 死亡
                            continue;
                        }
                        if (tgpc.isTeleport()) { // 傳送中
                            continue;
                        }
                        if (tgpc.getClanid() == 0) { // 沒加入血盟
                            continue;
                        }
                        if (tgpc.getClanid() != _pc.getClanid()) { // 不屬於一個血盟
                            continue;
                        }
                        if (tgpc.isPrivateShop()) {
                            continue;
                        }
                        if (tgpc.isInParty()) {
                            if (_pc.getParty() != null && !_pc.getParty().isMember(tgpc)) {// 是自己的隊員
                                _pc.sendPackets(new S_SystemMessage(tgpc.getName() + " 已在其他隊伍中"));
                            }
                            continue;
                        }
                        tgpc.setPartyID(_pc.getId());
                        // 玩家 %0%s 邀請您加入隊伍？(Y/N)
                        tgpc.sendPackets(new S_Message_YN(953, _pc.getName()));
                    }
                }
                return;
            }
            if (cmd.equalsIgnoreCase("Allpcparty")) {// 邀請屏幕內所有玩家組隊
                if (_pc.isInParty()) {
                    if (!_pc.getParty().isLeader(_pc)) {
                        // 只有領導者才能邀請其他的成員。
                        _pc.sendPackets(new S_ServerMessage(416));
                        return;
                    }
                }

                _pc.sendPackets(new S_SystemMessage("同畫面全體組隊邀請已發送!等待入隊..."));
                for (L1Object obj : World.get().getVisibleObjects(_pc)) {
                    if (obj instanceof L1PcInstance) {
                        L1PcInstance tgpc = (L1PcInstance) obj;
                        if (tgpc.isGhost()) { // 鬼魂模式
                            continue;
                        }
                        if (tgpc.isDead()) { // 死亡
                            continue;
                        }
                        if (tgpc.isTeleport()) { // 傳送中
                            continue;
                        }
                        if (tgpc.isPrivateShop()) {
                            continue;
                        }
                        if (tgpc.isInParty()) {
                            if (_pc.getParty().isMember(tgpc)) {// 是自己的隊員
                                continue;
                            } else {
                                _pc.sendPackets(new S_SystemMessage(tgpc.getName() + " 已在其他隊伍中"));
                                continue;
                            }
                        }
                        tgpc.setPartyID(_pc.getId());
                        // 玩家 %0%s 邀請您加入隊伍？(Y/N)
                        tgpc.sendPackets(new S_Message_YN(953, _pc.getName()));
                    }
                }
                return;
            }

            // 在適當位置添加物理攻擊修復工具命令
            if (cmd.equalsIgnoreCase("checkattack")) {
                // 檢測物理攻擊問題
                try {
                    // 獲取目標
                    L1Object target = _pc.getTarget();
                    if (target instanceof L1Character) {
                        String result = com.lineage.server.utils.PhysicalAttackRepair.detectPhysicalAttackIssues(_pc, (L1Character) target);
                        _pc.sendPackets(new S_SystemMessage("=== 物理攻擊問題檢測 ==="));
                        _pc.sendPackets(new S_SystemMessage(result));
                    } else {
                        _pc.sendPackets(new S_SystemMessage("請選擇有效的攻擊目標"));
                    }
                } catch (Exception e) {
                    _pc.sendPackets(new S_SystemMessage("檢測失敗: " + e.getMessage()));
                }
            } else if (cmd.equalsIgnoreCase("fixattack")) {
                // 修復物理攻擊問題
                try {
                    // 獲取目標
                    L1Object target = _pc.getTarget();
                    if (target instanceof L1Character) {
                        String result = com.lineage.server.utils.PhysicalAttackRepair.repairPhysicalAttack(_pc, (L1Character) target);
                        _pc.sendPackets(new S_SystemMessage("=== 物理攻擊修復 ==="));
                        _pc.sendPackets(new S_SystemMessage(result));
                    } else {
                        _pc.sendPackets(new S_SystemMessage("請選擇有效的攻擊目標"));
                    }
                } catch (Exception e) {
                    _pc.sendPackets(new S_SystemMessage("修復失敗: " + e.getMessage()));
                }
            } else if (cmd.equalsIgnoreCase("testattack")) {
                // 測試物理攻擊
                try {
                    // 獲取目標
                    L1Object target = _pc.getTarget();
                    if (target instanceof L1Character) {
                        String result = com.lineage.server.utils.PhysicalAttackRepair.testPhysicalAttack(_pc, (L1Character) target);
                        _pc.sendPackets(new S_SystemMessage("=== 物理攻擊測試 ==="));
                        _pc.sendPackets(new S_SystemMessage(result));
                    } else {
                        _pc.sendPackets(new S_SystemMessage("請選擇有效的攻擊目標"));
                    }
                } catch (Exception e) {
                    _pc.sendPackets(new S_SystemMessage("測試失敗: " + e.getMessage()));
                }
            } else if (cmd.equalsIgnoreCase("quickfixattack")) {
                // 快速檢測和修復物理攻擊
                try {
                    // 獲取目標
                    L1Object target = _pc.getTarget();
                    if (target instanceof L1Character) {
                        _pc.sendPackets(new S_SystemMessage("=== 快速物理攻擊修復 ==="));
                        
                        // 1. 檢測
                        String detect = com.lineage.server.utils.PhysicalAttackRepair.detectPhysicalAttackIssues(_pc, (L1Character) target);
                        _pc.sendPackets(new S_SystemMessage("檢測結果: " + detect));
                        
                        // 2. 如果有問題就修復
                        if (detect.contains("❌")) {
                            String repair = com.lineage.server.utils.PhysicalAttackRepair.repairPhysicalAttack(_pc, (L1Character) target);
                            _pc.sendPackets(new S_SystemMessage("修復完成: " + repair));
                        } else {
                            _pc.sendPackets(new S_SystemMessage("物理攻擊狀態正常，無需修復"));
                        }
                    } else {
                        _pc.sendPackets(new S_SystemMessage("請選擇有效的攻擊目標"));
                    }
                } catch (Exception e) {
                    _pc.sendPackets(new S_SystemMessage("快速修復失敗: " + e.getMessage()));
                }
            }

        } catch (Exception e2) {
            L1ActionPc._log.error(e2.getLocalizedMessage(), e2);
        }
    }

    private void usePolyScroll(L1PcInstance pc, L1ItemInstance item, String s) {
        try {
            L1PolyMorph poly = PolyTable.get().getTemplate(s);
            int time = 1800;
            if (item.getBless() == 0) {
                time = 2100;
            }
            if (item.getBless() == 128) {
                time = 2100;
            }
            boolean isUseItem = false;
            if (s.equals("none")) {
                if (pc.getTempCharGfx() == 6034 || pc.getTempCharGfx() == 6035) {
                    isUseItem = true;
                } else {
                    L1PolyMorph.undoPoly(pc);
                    isUseItem = true;
                }
            } else if (poly.getMinLevel() <= pc.getLevel() || pc.isGm()) {
                if (poly.getPolyId() == 13715 && (pc.get_sex() != 0 || !pc.isCrown())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13717 && (pc.get_sex() != 1 || !pc.isCrown())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13719 && (pc.get_sex() != 0 || !pc.isKnight())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13721 && (pc.get_sex() != 1 || !pc.isKnight())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13723 && (pc.get_sex() != 0 || !pc.isElf())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13725 && (pc.get_sex() != 1 || !pc.isElf())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13727 && (pc.get_sex() != 0 || !pc.isWizard())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13729 && (pc.get_sex() != 1 || !pc.isWizard())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13731 && (pc.get_sex() != 0 || !pc.isDarkelf())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13733 && (pc.get_sex() != 1 || !pc.isDarkelf())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13735 && (pc.get_sex() != 0 || !pc.isDragonKnight())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13737 && (pc.get_sex() != 1 || !pc.isDragonKnight())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13739 && (pc.get_sex() != 0 || !pc.isIllusionist())) {
                    isUseItem = false;
                } else if (poly.getPolyId() == 13741 && (pc.get_sex() != 1 || !pc.isIllusionist())) {
                    isUseItem = false;
                } else {
                    L1PolyMorph.doPoly(pc, poly.getPolyId(), time, 1);
                    isUseItem = true;
                }
            }
            if (isUseItem) {
                pc.getInventory().removeItem(item, 1L);
                pc.sendPackets(new S_CloseList(pc.getId()));
            } else {
                pc.sendPackets(new S_ServerMessage(181));
                pc.sendPackets(new S_CloseList(pc.getId()));
            }
            pc.setItemPoly(false);
            pc.setPolyScroll(null);
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void usePolyBook(L1PcInstance pc, L1ItemInstance item, String s) {
        try {
            L1PolyMorph poly = PolyTable.get().getTemplate(s);
            int time = 1800;
            if (item.getBless() == 0) {
                time = 2100;
            }
            if (item.getBless() == 128) {
                time = 2100;
            }
            boolean isUseItem = false;
            if (s.equals("none")) {
                if (pc.getTempCharGfx() == 6034 || pc.getTempCharGfx() == 6035) {
                    isUseItem = true;
                } else {
                    L1PolyMorph.undoPoly(pc);
                    isUseItem = true;
                }
            } else {
                L1PolyMorph.doPoly(pc, poly.getPolyId(), time, 1);
                isUseItem = true;
            }
            if (isUseItem) {
                pc.sendPackets(new S_CloseList(pc.getId()));
            } else {
                pc.sendPackets(new S_ServerMessage(181));
                pc.sendPackets(new S_CloseList(pc.getId()));
            }
            pc.setItemPoly(false);
            pc.setPolyScroll(null);
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void usePolyBookcouitem(L1PcInstance pc, L1ItemInstance item, String s) {
        try {
            L1PolyMorph poly = PolyTable.get().getTemplate(s);
            int time = 1800;
            if (item.getBless() == 0) {
                time = 2100;
            }
            if (item.getBless() == 128) {
                time = 2100;
            }
            boolean isUseItem = false;
            if (s.equals("none")) {
                if (pc.getTempCharGfx() == 6034 || pc.getTempCharGfx() == 6035) {
                    isUseItem = true;
                } else {
                    L1PolyMorph.undoPoly(pc);
                    isUseItem = true;
                }
            } else {
                L1PolyMorph.doPoly(pc, poly.getPolyId(), time, 1);
                isUseItem = true;
            }
            if (isUseItem) {
                pc.sendPackets(new S_CloseList(pc.getId()));
            } else {
                pc.sendPackets(new S_ServerMessage(181));
                pc.sendPackets(new S_CloseList(pc.getId()));
            }
            pc.setItemPoly(false);
            pc.setPolyScroll(null);
            pc.getInventory().removeItem(item, 1L);
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void usePhantomTeleport(L1PcInstance pc, String cmd) {
        try {
            int x = 0;
            int y = 0;
            short mapid = 0;
            switch (cmd) {
                case "a": {
                    x = 32797;
                    y = 32799;
                    mapid = 3301;
                    break;
                }
                case "b": {
                    x = 32797;
                    y = 32799;
                    mapid = 3302;
                    break;
                }
                case "c": {
                    x = 32797;
                    y = 32799;
                    mapid = 3303;
                    break;
                }
                case "d": {
                    x = 32668;
                    y = 32864;
                    mapid = 3304;
                    break;
                }
                case "e": {
                    x = 32668;
                    y = 32864;
                    mapid = 3305;
                    break;
                }
                case "f": {
                    x = 32717;
                    y = 32871;
                    mapid = 3306;
                    break;
                }
                case "g": {
                    x = 32668;
                    y = 32864;
                    mapid = 3307;
                    break;
                }
                case "h": {
                    x = 32668;
                    y = 32864;
                    mapid = 3308;
                    break;
                }
                case "i": {
                    x = 32668;
                    y = 32864;
                    mapid = 3309;
                    break;
                }
                case "j": {
                    x = 32797;
                    y = 32799;
                    mapid = 3310;
                    break;
                }
                case "k": {
                    x = 32760;
                    y = 32894;
                    mapid = 7100;
                    break;
                }
                case "l": {
                    x = 32692;
                    y = 32903;
                    mapid = 7100;
                    break;
                }
                default:
                    break;
            }
            L1Teleport.teleport(pc, x, y, mapid, pc.getHeading(), true);
            pc.setPhantomTeleport(false);
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void questDel(L1Quest quest) {
        try {
            if (quest.is_del()) {
                _pc.setTempID(quest.get_id());
                String over = null;
                if (_pc.getQuest().isEnd(quest.get_id())) {
                    over = "完成任務";
                } else {
                    over = String.valueOf(_pc.getQuest().get_step(quest.get_id())) + " / "
                            + quest.get_difficulty();
                }
                String[] info = {quest.get_questname(), Integer.toString(quest.get_questlevel()), over};
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_qi2", info));
            } else {
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_q_not5"));
            }
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void isDel(L1Quest quest) {
        try {
            if (quest.is_del()) {
                QuestClass.get().stopQuest(_pc, quest.get_id());
                CharacterQuestReading.get().delQuest(_pc.getId(), quest.get_id());
                String[] info = {quest.get_questname(), Integer.toString(quest.get_questlevel())};
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_qi3", info));
            } else {
                _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_q_not5"));
            }
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private boolean checkvalid(L1PcInstance pc, String cmd) {
        int initpoint = C_CreateChar.ORIGINAL_AMOUNT[pc.getType()];
        if (pc.isInCharReset()) {
            if (cmd.equalsIgnoreCase("initStr")) {
                if (pc.getBaseStr() >= C_CreateChar.MAX_STR[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseStr() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("initInt")) {
                if (pc.getBaseInt() >= C_CreateChar.MAX_INT[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseInt() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("initWis")) {
                if (pc.getBaseWis() >= C_CreateChar.MAX_WIS[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseWis() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("initDex")) {
                if (pc.getBaseDex() >= C_CreateChar.MAX_DEX[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseDex() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("initCon")) {
                if (pc.getBaseCon() >= C_CreateChar.MAX_CON[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseCon() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("initCha")) {
                if (pc.getBaseCha() >= C_CreateChar.MAX_CHA[pc.getType()]) {
                    pc.sendPackets(new S_ServerMessage("該能力初始已達上限"));
                    return false;
                }
                if (pc.getBaseCha() < 20) {
                    if (initpoint - pc.getTempInitPoint() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("初始點數上限只能到20。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Str")) {
                if (pc.getBaseStr() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Int")) {
                if (pc.getBaseInt() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Wis")) {
                if (pc.getBaseWis() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Dex")) {
                if (pc.getBaseDex() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Con")) {
                if (pc.getBaseCon() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("Cha")) {
                if (pc.getBaseCha() < ConfigAlt.BaseResetPOWER) {
                    if (pc.getTempMaxLevel() - pc.getTempLevel() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.BaseResetPOWER + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exStr")) {
                if (pc.getBaseStr() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exInt")) {
                if (pc.getBaseInt() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exWis")) {
                if (pc.getBaseWis() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exDex")) {
                if (pc.getBaseDex() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exCon")) {
                if (pc.getBaseCon() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("exCha")) {
                if (pc.getBaseCha() < ConfigAlt.POWERMEDICINE) {
                    if (pc.getElixirStats() - pc.getTempElixirstats() > 0) {
                        return true;
                    }
                    pc.sendPackets(new S_ServerMessage("已經沒有多餘的點數了。"));
                } else {
                    pc.sendPackets(new S_ServerMessage("屬性最大值只能到" + ConfigAlt.POWERMEDICINE + "。 請重試一次。"));
                }
            } else if (cmd.equalsIgnoreCase("1")) {
                if (pc.getTempMaxLevel() - pc.getTempLevel() >= 1) {
                    return true;
                }
                pc.sendPackets(new S_ServerMessage("無法再繼續升級了。"));
            } else if (cmd.equalsIgnoreCase("10")) {
                if (pc.getTempMaxLevel() - pc.getTempLevel() >= 10) {
                    return true;
                }
                pc.sendPackets(new S_ServerMessage("無法再繼續升級了。"));
            }
        } else {
            pc.sendPackets(new S_ServerMessage("必須在人物重置模式下才能使用！"));
        }
        return false;
    }

    private void setLevelUp(L1PcInstance pc, int addLv) {
        pc.setTempLevel(pc.getTempLevel() + addLv);
        int i = 0;
        while (i < addLv) {
            short randomHp = CalcStat.calcStatHp(pc.getType(), pc.getBaseMaxHp(), pc.getBaseCon(),
                    pc.getOriginalHpup());
            short randomMp = CalcStat.calcStatMp(pc.getType(), pc.getBaseMaxMp(), pc.getBaseWis(),
                    pc.getOriginalMpup());
            pc.addBaseMaxHp(randomHp);
            pc.addBaseMaxMp(randomMp);
            ++i;
        }
        pc.setExp(ExpTable.getExpByLevel(pc.getTempLevel()));
        pc.resetLevel();
        pc.sendPackets(new S_SPMR(pc));
        pc.sendPackets(new S_OwnCharStatus(pc));
        pc.sendPackets(new S_PacketBox(132, pc.getEr()));
    }

    private void showPage(int key) {
        try {
            L1Quest quest = _pc.get_otherList().QUESTMAP.get(Integer.valueOf(key));
            _pc.setTempID(quest.get_id());
            String over = null;
            if (_pc.getQuest().isEnd(quest.get_id())) {
                over = "完成任務";
            } else {
                over = String.valueOf(_pc.getQuest().get_step(quest.get_id())) + " / " + quest.get_difficulty();
            }
            String[] info = {quest.get_questname(), Integer.toString(quest.get_questlevel()), over, ""};
            _pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "y_qi1", info));
        } catch (Exception e) {
            L1ActionPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    public void BadEnemyList(L1PcInstance pc) {
        StringBuilder msg = new StringBuilder();
        Iterator<String> iterator = pc.InBadEnemyList().iterator();
        while (iterator.hasNext()) {
            String name = iterator.next();
            msg.append(String.valueOf(name) + ",");
        }
        String[] clientStrAry = msg.toString().split(",");
        pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "x_autolist4", clientStrAry));
    }

    private class AllBuffRunnable implements Runnable {
        @Override
        public void run() {
            try {
                Iterator<L1PcInstance> iterator = World.get().getAllPlayers().iterator();
                while (iterator.hasNext()) {
                    L1PcInstance tgpc = iterator.next();
                    startPc(tgpc);
                    Thread.sleep(1L);
                }
            } catch (Exception ex) {
            }
        }

        public void startPc(L1PcInstance target) {
            int[] allBuffSkill = ConfigOther.Give_skill1;
            int i = 0;
            while (i < allBuffSkill.length) {
                L1Skills skill = SkillsTable.get().getTemplate(allBuffSkill[i]);
                L1SkillUse skillUse = new L1SkillUse();
                skillUse.handleCommands(target, allBuffSkill[i], target.getId(), target.getX(), target.getY(),
                        skill.getBuffDuration(), 4);
                ++i;
            }
        }
    }
}
