package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public abstract class SkillMode {
	public abstract int start(final L1PcInstance p0, final L1Character p1, final L1Magic p2, final int p3)
			throws Exception;

	public abstract int start(final L1NpcInstance p0, final L1Character p1, final L1Magic p2, final int p3)
			throws Exception;

	public abstract void stop(final L1Character p0) throws Exception;

	public abstract void start(final L1PcInstance p0, final Object p1) throws Exception;
}
