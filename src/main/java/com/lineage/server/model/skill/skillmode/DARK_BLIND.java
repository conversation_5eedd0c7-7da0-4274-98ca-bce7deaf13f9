package com.lineage.server.model.skill.skillmode;

import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class DARK_BLIND extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.setSkillEffect(66, integer * 1000);
			pc.sendPackets(new S_Paralysis(3, true));
		} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
				|| cha instanceof L1PetInstance) {
			final L1NpcInstance tgnpc = (L1NpcInstance) cha;
			tgnpc.setSkillEffect(66, integer * 1000);
			tgnpc.setSleeped(true);
		}
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.sendPackets(new S_Paralysis(3, false));
			pc.sendPackets(new S_OwnCharStatus(pc));
			pc.removeSkillEffect(66);
		} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
				|| cha instanceof L1PetInstance) {
			final L1NpcInstance tgnpc = (L1NpcInstance) cha;
			tgnpc.removeSkillEffect(66);
			tgnpc.setSleeped(false);
		}
	}
}
