package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.Random;

public class BONE_BREAK extends SkillMode {
	private static Random _random;

	static {
		_random = new Random();
	}

	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = magic.calcMagicDamage(208);
		if (cha.hasSkillEffect(208)) {
			return dmg;
		}
		final int time = BONE_BREAK._random.nextInt(2) + 1;
		cha.setSkillEffect(208, time * 1000);
		L1SpawnUtil.spawnEffect(86123, time, cha.getX(), cha.getY(), cha.getMapId(), cha, 0);
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.sendPackets(new S_Paralysis(5, true));
		} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
				|| cha instanceof L1PetInstance) {
			final L1NpcInstance npc = (L1NpcInstance) cha;
			npc.setParalyzed(true);
		}
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return 0;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.sendPackets(new S_Paralysis(5, false));
		} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
				|| cha instanceof L1PetInstance) {
			final L1NpcInstance npc = (L1NpcInstance) cha;
			npc.setParalyzed(false);
		}
	}
}
