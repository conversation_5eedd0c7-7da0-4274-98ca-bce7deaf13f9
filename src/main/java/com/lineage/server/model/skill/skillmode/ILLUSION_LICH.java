package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SPMR;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class ILLUSION_LICH extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		if (!cha.hasSkillEffect(209)) {
			if (cha instanceof L1PcInstance) {
				final L1PcInstance pc = (L1PcInstance) cha;
				pc.addSp(2);
				pc.sendPackets(new S_SPMR(pc));
				pc.setSkillEffect(209, integer * 1000);
			} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
					|| cha instanceof L1PetInstance) {
				final L1NpcInstance tgnpc = (L1NpcInstance) cha;
				tgnpc.addSp(2);
				tgnpc.setSkillEffect(209, integer * 1000);
			}
		}
		return 0;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return 0;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
		if (cha instanceof L1PcInstance) {
			final L1PcInstance pc = (L1PcInstance) cha;
			pc.addSp(-2);
			pc.sendPackets(new S_SPMR(pc));
		} else if (cha instanceof L1MonsterInstance || cha instanceof L1SummonInstance
				|| cha instanceof L1PetInstance) {
			final L1NpcInstance tgnpc = (L1NpcInstance) cha;
			tgnpc.addSp(-2);
		}
	}
}
