package com.lineage.server.model.skill.skillmode;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_NPCPack;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class KIRTAS_BARRIER1 extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return 0;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		npc.startKIRTAS_Timer();
		final int dmg = magic.calcMagicDamage(11060);
		npc.setHiddenStatus(4);
		npc.setStatus(20);
		npc.setSkillEffect(11060, integer * 1000);
		npc.broadcastPacketAll(new S_NPCPack(npc));
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
