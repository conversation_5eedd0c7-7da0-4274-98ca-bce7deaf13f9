package com.lineage.server.model.skill.skillmode;

import com.lineage.server.templates.L1Npc;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.datatables.NpcTable;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class GREATER_ELEMENTAL extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		final L1PcInstance pc = (L1PcInstance) cha;
		final int attr = pc.getElfAttr();
		if (attr != 0) {
			if (!pc.getMap().isRecallPets()) {
				pc.sendPackets(new S_ServerMessage(353));
				return 0;
			}
			int petcost = 0;
			final Object[] petlist = pc.getPetList().values().toArray();
			final Object[] array;
			final int length = (array = petlist).length;
			int i = 0;
			while (i < length) {
				final Object pet = array[i];
				petcost += ((L1NpcInstance) pet).getPetcost();
				++i;
			}
			if (petcost == 0) {
				int summonid = 0;
				switch (attr) {
				case 1: {
					summonid = 81053;
					break;
				}
				case 2: {
					summonid = 81050;
					break;
				}
				case 4: {
					summonid = 81051;
					break;
				}
				case 8: {
					summonid = 81052;
					break;
				}
				}
				if (summonid != 0) {
					final L1Npc npcTemp = NpcTable.get().getTemplate(summonid);
					final L1SummonInstance summon = new L1SummonInstance(npcTemp, pc);
					summon.setPetcost(pc.getCha() + 7);
				}
			}
		}
		return 0;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		return 0;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
