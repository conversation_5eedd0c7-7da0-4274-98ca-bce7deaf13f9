package com.lineage.server.model.skill.skillmode;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_PacketBoxDk;
import com.lineage.server.model.L1Magic;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public class FOE_SLAYER extends SkillMode {
	@Override
	public int start(final L1PcInstance srcpc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		srcpc.setFoeSlayer(true);
		int i = 0;
		while (i < 3) {
			cha.onAction(srcpc);
			if (i == 2) {
				srcpc.setFoeSlayer(false);
				srcpc.set_weaknss(0, 0L);
				srcpc.sendPackets(new S_PacketBoxDk(0));
			}
			++i;
		}
		srcpc.sendPacketsAll(new S_SkillSound(srcpc.getId(), 7020));
		srcpc.sendPacketsAll(new S_SkillSound(cha.getId(), 12119));
		return dmg;
	}

	@Override
	public int start(final L1NpcInstance npc, final L1Character cha, final L1Magic magic, final int integer)
			throws Exception {
		final int dmg = 0;
		int i = 0;
		while (i < 3) {
			npc.attackTarget(cha);
			++i;
		}
		npc.broadcastPacketAll(new S_SkillSound(cha.getId(), 7020));
		npc.broadcastPacketAll(new S_SkillSound(cha.getId(), 12119));
		return dmg;
	}

	@Override
	public void start(final L1PcInstance srcpc, final Object obj) throws Exception {
	}

	@Override
	public void stop(final L1Character cha) throws Exception {
	}
}
