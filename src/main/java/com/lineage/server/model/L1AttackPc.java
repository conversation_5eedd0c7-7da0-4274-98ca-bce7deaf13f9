package com.lineage.server.model;

import com.add.PeepCard;
import com.lineage.config.*;
import com.lineage.data.event.FeatureItemSet;
import com.lineage.server.datatables.ItemSpecialAttributeTable;
import com.lineage.server.datatables.NpcTable;
import com.lineage.server.datatables.PowerItemTable;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.model.Instance.*;
import com.lineage.server.model.poison.L1DamagePoison;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.model.weaponskill.WeaponSkillStart;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.*;
import com.lineage.server.timecontroller.server.ServerWarExecutor;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.utils.RandomArrayList;
import com.lineage.william.L1WilliamGfxIdOrginal;
import com.lineage.william.L1WilliamGfxIdOrginalpoly;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.util.*;

import static com.lineage.server.model.skill.L1SkillId.STATUS_BRAVE3;

public class L1AttackPc extends L1AttackMode {
    private static final Log _log;

    static {
        _log = LogFactory.getLog(L1AttackPc.class);
    }

    L1ItemSpecialAttributeChar item_attr_char;
    Random random;
    private byte _attackType;
    private int _weaponDamage;
    private int _weaponTotalDamage;
    private int hit_rnd;

    public L1AttackPc(L1PcInstance attacker, L1Character target) {
        _attackType = 0;
        item_attr_char = null;
        random = new Random();
        if (target == null) {
            return;
        }
        if (target.isDead()) {
            return;
        }
        _pc = attacker;
        if (target instanceof L1PcInstance) {
            _targetPc = (L1PcInstance) target;
            _calcType = 1;
        } else if (target instanceof L1NpcInstance) {
            _targetNpc = (L1NpcInstance) target;
            _calcType = 2;
        }
        _weapon = _pc.getWeapon();
        if (_weapon != null) {
            item_attr_char = _weapon.get_ItemAttrName();
            int attr_DmgSmall = 0;
            int attr_DmgLarge = 0;
            int attr_HitModifier = 0;
            int attr_DmgModifier = 0;
            if (item_attr_char != null) {
                L1ItemSpecialAttribute attr = ItemSpecialAttributeTable.get()
                        .getAttrId(item_attr_char.get_attr_id());
                attr_DmgSmall = attr.get_dmg_small();
                attr_DmgLarge = attr.get_dmg_large();
                attr_HitModifier = attr.get_hitmodifier();
                attr_DmgModifier = attr.get_dmgmodifier();
            }
            _weaponId = _weapon.getItem().getItemId();
            _weaponType = _weapon.getItem().getType1();
            _weaponType2 = _weapon.getItem().getType();
            _weaponAddHit = _weapon.getItem().getHitModifier() + _weapon.getHitByMagic()
                    + _weapon.getItemHit() + _weapon.getItemBowHit() + attr_HitModifier;
            _weaponAddDmg = _weapon.getItem().getDmgModifier() + _weapon.getDmgByMagic()
                    + _weapon.getItemAttack() + _weapon.getItemBowAttack() + attr_DmgModifier;
            _weaponSmall = _weapon.getItem().getDmgSmall() + attr_DmgSmall;
            _weaponLarge = _weapon.getItem().getDmgLarge() + attr_DmgLarge;
            _weaponRange = _weapon.getItem().getRange();
            _weaponBless = _weapon.getItem().getBless();
            L1ItemPower_name power = _weapon.get_power_name();
            if (power != null) {
                skillEffice(power.get_hole_1(), target);
                skillEffice(power.get_hole_2(), target);
                skillEffice(power.get_hole_3(), target);
                skillEffice(power.get_hole_4(), target);
                skillEffice(power.get_hole_5(), target);
                boolean polyid = polyidEictff(power.get_hole_1(), target);
                if (!polyid) {
                    polyid = polyidEictff(power.get_hole_2(), target);
                    if (!polyid) {
                        polyid = polyidEictff(power.get_hole_3(), target);
                        if (!polyid) {
                            polyid = polyidEictff(power.get_hole_4(), target);
                            if (!polyid) {
                                polyid = polyidEictff(power.get_hole_5(), target);
                            }
                        }
                    }
                }
            }
            if (_weaponType != 20 && _weaponType != 62) {
                _weaponEnchant = _weapon.getEnchantLevel() - _weapon.get_durability();
            } else {
                _weaponEnchant = _weapon.getEnchantLevel();
            }
            _weaponMaterial = _weapon.getItem().getMaterial();
            if (_weaponType == 20) {
                _arrow = _pc.getInventory().getArrow();
                if (_arrow != null) {
                    _weaponBless = _arrow.getItem().getBless();
                    _weaponMaterial = _arrow.getItem().getMaterial();
                }
            }
            if (_weaponType == 62) {
                _sting = _pc.getInventory().getSting();
                if (_sting != null) {
                    _weaponBless = _sting.getItem().getBless();
                    _weaponMaterial = _sting.getItem().getMaterial();
                }
            }
            _weaponDoubleDmgChance = _weapon.getDoubleDmgChance();
            _weaponAttrEnchantKind = _weapon.getAttrEnchantKind();
            _weaponAttrEnchantLevel = _weapon.getAttrEnchantLevel();
        } else {
            // 空手攻擊初始化
            _weaponId = 0;
            _weaponType = 0;
            _weaponType2 = 0;
            _weaponAddHit = 0;
            _weaponAddDmg = 0;
            _weaponSmall = 1;
            _weaponLarge = 1;
            _weaponRange = -1; // 空手攻擊使用 -1 表示遠距離攻擊封包
            _weaponBless = 1;
            _weaponEnchant = 0;
            _weaponMaterial = 0;
            _weaponDoubleDmgChance = 0;
            _weaponAttrEnchantKind = 0;
            _weaponAttrEnchantLevel = 0;
        }
        if (_weaponType == 20 || _weaponType == 62) {
            Integer dmg = L1AttackList.DEXD.get(Integer.valueOf(_pc.getDex()));
            if (dmg != null) {
                _statusDamage = dmg.intValue();
            }
        } else {
            Integer dmg = L1AttackList.STRD.get(Integer.valueOf(_pc.getStr()));
            if (dmg != null) {
                _statusDamage = dmg.intValue();
            }
        }
        _target = target;
        _targetId = target.getId();
        _targetX = target.getX();
        _targetY = target.getY();
    }

    private static boolean isInWarAreaAndWarTime(L1PcInstance pc) {
        int castleId = L1CastleLocation.getCastleIdByArea(pc);
        return castleId != 0 && ServerWarExecutor.get().isNowWar(castleId);
    }

    private void skillEffice(int hole, L1Character target) {
        if (hole > 0) {
            Random random = new Random();
            int skill_id = PowerItemTable.get().getItem(hole).skill_id;
            int probability = PowerItemTable.get().getItem(hole).probability;
            int target_to = PowerItemTable.get().getItem(hole).target_to;
            int rnd = random.nextInt(100) + 1;
            if (!_pc.hasSkillEffect(90000000 + skill_id) && skill_id > 0 && rnd <= probability) {
                if (L1WilliamGfxIdOrginal.Cancellation(target.getTempCharGfx())) {
                    return;
                }
                if (L1WilliamGfxIdOrginalpoly.Cancellation(target.getTempCharGfx())) {
                    return;
                }
                L1Skills skill = SkillsTable.get().getTemplate(skill_id);
                _pc.setSkillEffect(90000000 + skill_id, 2000);
                if (skill.getTarget().equals("buff")) {
                    if (target_to == 0) {
                        new L1SkillUse().handleCommands(_pc, skill_id, _pc.getId(), _pc.getX(),
                                _pc.getY(), skill.getBuffDuration(), 5);
                    } else if (target_to == 1) {
                        new L1SkillUse().handleCommands(_pc, skill_id, target.getId(), target.getX(),
                                target.getY(), skill.getBuffDuration(), 5);
                    }
                } else if (skill.getTarget().equals("none")) {
                    if (target_to == 0) {
                        new L1SkillUse().handleCommands(_pc, skill_id, _pc.getId(), _pc.getX(),
                                _pc.getY(), skill.getBuffDuration(), 5);
                    } else if (target_to == 1) {
                        new L1SkillUse().handleCommands(_pc, skill_id, target.getId(), target.getX(),
                                target.getY(), skill.getBuffDuration(), 5);
                    }
                } else if (skill.getTarget().equals("attack") && target_to == 1) {
                    new L1SkillUse().handleCommands(_pc, skill_id, target.getId(), target.getX(), target.getY(),
                            skill.getBuffDuration(), 5);
                }
            }
            boolean unequipment = PowerItemTable.get().getItem(hole).unequipment;
            probability = PowerItemTable.get().getItem(hole).probability_unequi;
            rnd = random.nextInt(100) + 1;
            if (unequipment && rnd <= probability && !_targetPc.isSafetyZone() && target instanceof L1PcInstance) {
                L1PcInstance targetPC = (L1PcInstance) target;
                targetPC.getInventory().setEquipped(targetPC.getWeapon(), false, false, false);
                _pc.sendPackets(new S_SystemMessage("成功解除對方裝備!"));
                targetPC.sendPackets(new S_SystemMessage("你已被對方解除武器!"));
            }
        }
    }

    private boolean polyidEictff(int hole, L1Character target) {
        if (hole > 0 && target instanceof L1PcInstance) {
            Random random = new Random();
            int polyid = PowerItemTable.get().getItem(hole).polyid;
            int polyid_time = PowerItemTable.get().getItem(hole).polyid_time;
            int probability = PowerItemTable.get().getItem(hole).probability_polyid;
            int rnd = random.nextInt(100) + 1;
            if (polyid > 0 && !_targetPc.isSafetyZone() && rnd <= probability) {
                if (L1WilliamGfxIdOrginal.Cancellation(target.getTempCharGfx())) {
                    return false;
                }
                if (L1WilliamGfxIdOrginalpoly.Cancellation(target.getTempCharGfx())) {
                    return false;
                }
                L1PolyMorph.doPoly(target, polyid, polyid_time, 2);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean calcHit() {
        if (this._target == null) {
            return this._isHit = false;
        }
        switch (this._calcType) {
        case 1: {
            this._isHit = this.calcPcHit();
            break;
        }
        case 2: {
            this._isHit = this.calcNpcHit();
            break;
        }
        }
        return this._isHit;
    }

    private int str_dex_Hit() {
        int hitRate = 0;
        Integer hitStr = L1AttackList.STRH.get(Integer.valueOf(_pc.getStr() - 1));
        if (hitStr != null) {
            hitRate += hitStr.intValue();
        } else {
            hitRate += 15;
        }
        Integer hitDex = L1AttackList.DEXH.get(Integer.valueOf(_pc.getDex() - 1));
        if (hitDex != null) {
            hitRate += hitDex.intValue();
        } else {
            hitRate += 25;
        }
        return hitRate;
    }

    private boolean calcPcHit() {
        if (ConfigGuaji.Guaji_save && _targetPc.isActived()
                && _targetPc.getLevel() <= ConfigGuaji.Guaji_level) {
            return false;
        }
        if (_targetPc == _pc) {
            return false;
        }
        if (_targetPc == null) {
            return false;
        }
        if (L1AttackMode.dmg0(_targetPc)) {
            return false;
        }
        if (ConfigOther.newcharpra && _targetPc.getnewcharpra()) {
            return false;
        }
        if (ConfigOther.newcharpra && _pc.getnewcharpra()) {
            return false;
        }
        if (calcEvasion()) {
            return false;
        }
        if (_weaponType2 == 17) {
            return true;
        }
        _hitRate = _pc.getLevel();
        _hitRate += str_dex_Hit();
        if (_weaponType != 20 && _weaponType != 62) {
            _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getHitup()
                    + _pc.getOriginalHitup() + _weaponEnchant * 0.6));
        } else {
            _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getBowHitup()
                    + _pc.getOriginalBowHitup() + _weaponEnchant * 0.6));
        }
        if (_weaponType != 20 && _weaponType != 62) {
            _hitRate += _pc.getHitModifierByArmor();
        } else {
            _hitRate += _pc.getBowHitModifierByArmor();
        }
        int weight240 = _pc.getInventory().getWeight240();
        if (weight240 > 80) {
            if (80 < weight240 && 120 >= weight240) {
                --_hitRate;
            } else if (121 <= weight240 && 160 >= weight240) {
                _hitRate -= 3;
            } else if (161 <= weight240 && 200 >= weight240) {
                _hitRate -= 5;
            }
        }
        if (_targetPc.isDarkelf() && _targetPc.getlogpcpower_SkillFor3() != 0
                && _targetPc.getlogpcpower_SkillFor3() >= 1) {
            _hitRate -= _targetPc.getlogpcpower_SkillFor3();
        }
        _hitRate += hitUp();
        if (_pc.is_mazu()) {
            _hitRate += 5;
        }
        if (_pc.get_other().get_PvP_hit() > 0) {
            _hitRate += _pc.get_other().get_PvP_hit();
        }
        if ((_pc.get_other().get_PvP_bow_hit() > 0 && _weaponType == 20) || _weaponType == 62) {
            _hitRate += _pc.get_other().get_PvP_bow_hit();
        }
        int attackerDice = L1AttackMode._random.nextInt(20) + 1 + _hitRate - 10;
        attackerDice += L1AttackMode.attackerDice(_targetPc);
        int defenderDice = 0;
        int defenderValue = (int) (_targetPc.getAc() * 1.5) * -1;
        if (_targetPc.getAc() >= 0) {
            defenderDice = 10 - _targetPc.getAc();
        } else if (_targetPc.getAc() < 0) {
            defenderDice = 10 + L1AttackMode._random.nextInt(defenderValue) + 1;
        }
        int fumble = _hitRate - 9;
        int critical = _hitRate + 10;
        if (_pc.isElf() && _pc.getElfAttr() == 2) {
            ++attackerDice;
        }
        if (attackerDice <= fumble) {
            _hitRate = 15;
        } else if (attackerDice >= critical) {
            _hitRate = 100;
        } else if (attackerDice > defenderDice) {
            _hitRate = 100;
        } else if (attackerDice <= defenderDice) {
            _hitRate = 15;
        }
        int rnd = L1AttackMode._random.nextInt(100) + 1;
        if (_weaponType == 20) {
            if (_hitRate > rnd) {
                return calcErEvasion();
            }
            if (_hitRate > 95) {
                _hitRate = 95;
            }
        }
        return _hitRate >= rnd;
    }

    private boolean calcNpcHit() {
        int gfxid = _targetNpc.getNpcTemplate().get_gfxid();
        switch (gfxid) {
            case 2412: {
                if (!_pc.getInventory().checkEquipped(20046)) {
                    return false;
                }
                break;
            }
        }
        if (L1AttackMode.dmg0(_targetNpc)) {
            return false;
        }
        if (_targetNpc.isShop()) {
            return false;
        }
        if (_weaponType2 == 17) {
            return true;
        }
        if (_targetNpc.hasSkillEffect(91)) {
            L1Magic magic = new L1Magic(_targetNpc, _pc);
            boolean isCounterBarrier = false;
            boolean isProbability = magic.calcProbabilityMagic(91);
            boolean isShortDistance = isShortDistance();
            L1ItemInstance weapon = _pc.getWeapon();
            if (isProbability && isShortDistance && weapon.getItem().getType() != 17) {
                isCounterBarrier = true;
            }
            if (isCounterBarrier) {
                commitCounterBarrier();
                return false;
            }
        }
        if (_targetNpc.hasSkillEffect(11060)) {
            commitCounterBarrier();
            return false;
        }
        _hitRate = _pc.getLevel();
        _hitRate += str_dex_Hit();
        if (_weaponType != 20 && _weaponType != 62) {
            _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getHitup()
                    + _pc.getOriginalHitup() + _weaponEnchant * 0.6));
        } else {
            _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getBowHitup()
                    + _pc.getOriginalBowHitup() + _weaponEnchant * 0.6));
        }
        if (_weaponType != 20 && _weaponType != 62) {
            _hitRate += _pc.getHitModifierByArmor();
        } else {
            _hitRate += _pc.getBowHitModifierByArmor();
        }
        int weight240 = _pc.getInventory().getWeight240();
        if (weight240 > 80) {
            if (80 < weight240 && 120 >= weight240) {
                --_hitRate;
            } else if (121 <= weight240 && 160 >= weight240) {
                _hitRate -= 3;
            } else if (161 <= weight240 && 200 >= weight240) {
                _hitRate -= 5;
            }
        }
        _hitRate += hitUp();
        if (_pc.is_mazu()) {
            _hitRate += 5;
        }
        int attackerDice = L1AttackMode._random.nextInt(20) + 2 + _hitRate - 10;
        attackerDice += L1AttackMode.attackerDice(_targetNpc);
        int defenderDice = 10 - _targetNpc.getAc();
        int fumble = _hitRate - 9;
        int critical = _hitRate + 10;
        if (attackerDice <= fumble) {
            _hitRate = 0;
        } else if (attackerDice >= critical) {
            _hitRate = 100;
        } else if (attackerDice > defenderDice) {
            _hitRate = 100;
        } else if (attackerDice <= defenderDice) {
            _hitRate = 0;
        }
        int npcId = _targetNpc.getNpcTemplate().get_npcId();
        Integer tgskill = L1AttackList.SKNPC.get(Integer.valueOf(npcId));
        if (tgskill != null && !_pc.hasSkillEffect(tgskill.intValue())) {
            _hitRate = 0;
        }
        Integer tgpoly = L1AttackList.PLNPC.get(Integer.valueOf(npcId));
        if (tgpoly != null && tgpoly.equals(Integer.valueOf(_pc.getTempCharGfx()))) {
            _hitRate = 0;
        }
        int rnd = L1AttackMode._random.nextInt(100) + 1;
        return _hitRate >= rnd;
    }

    private int hitUp() {
        int hitUp = 0;
        if (_pc.getSkillEffect().size() <= 0) {
            return hitUp;
        }
        if (!_pc.getSkillisEmpty()) {
            try {
                if (_weaponType != 20 && _weaponType != 62) {
                    Iterator<Integer> iterator = _pc.getSkillEffect().iterator();
                    while (iterator.hasNext()) {
                        Integer key = iterator.next();
                        Integer integer = L1AttackList.SKU1.get(key);
                        if (integer != null) {
                            hitUp += integer.intValue();
                        }
                    }
                } else {
                    Iterator<Integer> iterator2 = _pc.getSkillEffect().iterator();
                    while (iterator2.hasNext()) {
                        Integer key = iterator2.next();
                        Integer integer = L1AttackList.SKU2.get(key);
                        if (integer != null) {
                            hitUp += integer.intValue();
                        }
                    }
                }
            } catch (ConcurrentModificationException ex) {
            } catch (Exception e) {
                L1AttackPc._log.error(e.getLocalizedMessage(), e);
            }
        }
        return hitUp;
    }

    @Override
    public int calcDamage() {
        if (this._target == null) {
            return 0;
        }
        switch (this._calcType) {
        case 1: {
            this._damage = this.calcPcDamage();
            break;
        }
        case 2: {
            this._damage = this.calcNpcDamage();
            break;
        }
        }
        return this._damage;
    }

    private int weaponDamage1(int weaponMaxDamage) {
        int weaponDamage = 0;
        switch (_weaponType2) {
            case 3: {
                weaponDamage = L1AttackMode._random.nextInt(weaponMaxDamage) + 1;
                if (_weaponId == 217 && _pc.getLawful() < 500) {
                    int a = _pc.getLawful() / 1000;
                    int b = Math.abs(a);
                    weaponDamage += b;
                    break;
                }
                if (_weaponId == 410165 && _pc.getLawful() < 500) {
                    int a = _pc.getLawful() / 3000;
                    int b = Math.abs(a);
                    weaponDamage += b;
                    break;
                }
                break;
            }
            case 11: {
                weaponDamage = L1AttackMode._random.nextInt(weaponMaxDamage) + 1;
                if (L1AttackMode._random.nextInt(100) + 1 <= _weaponDoubleDmgChance) {
                    weaponDamage = weaponMaxDamage;
                    _attackType = (byte) (int) ConfigDarkElfSkill.DOUBLE_BREAK_DMG;
                    break;
                }
                break;
            }
            case 1:
            case 2:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 12:
            case 13:
            case 14:
            case 15:
            case 16:
            case 17:
            case 18: {
                weaponDamage = L1AttackMode._random.nextInt(weaponMaxDamage) + 1;
                break;
            }
        }
        if (_pc.getClanid() != 0) {
            weaponDamage = (int) (weaponDamage + L1AttackMode.getDamageUpByClan(_pc));
        }
        switch (_pc.guardianEncounter()) {
            case 3: {
                ++weaponDamage;
                break;
            }
            case 4: {
                weaponDamage += 3;
                break;
            }
            case 5: {
                weaponDamage += 5;
                break;
            }
        }
        return weaponDamage;
    }

    private double weaponDamage2(double weaponTotalDamage) {
        double dmg = 0.0;
        weaponTotalDamage += calcAttrEnchantDmg();
        boolean doubleBrake = false;
        switch (_weaponType2) {
            case 1:
            case 2:
            case 3:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 14:
            case 15:
            case 16:
            case 18: {
                dmg = weaponTotalDamage + _statusDamage + _pc.getDmgup() + _pc.getOriginalDmgup();
                break;
            }
            case 11: {
                if (_pc.hasSkillEffect(105)) {
                    doubleBrake = true;
                }
                dmg = weaponTotalDamage + _statusDamage + _pc.getDmgup() + _pc.getOriginalDmgup();
                break;
            }
            case 12: {
                if (L1AttackMode._random.nextInt(100) + 1 <= _weaponDoubleDmgChance) {
                    weaponTotalDamage *= 2.0;
                    _attackType = 4;
                }
                if (_pc.hasSkillEffect(105)) {
                    doubleBrake = true;
                }
                dmg = weaponTotalDamage + _statusDamage + _pc.getDmgup() + _pc.getOriginalDmgup();
                break;
            }
            case 0: {
                dmg = L1AttackMode._random.nextInt(5) + 4 >> 2;
                break;
            }
            case 4:
            case 13: {
                int add = _statusDamage;
                dmg = weaponTotalDamage + add + _pc.getBowDmgup() + _pc.getOriginalBowDmgup();
                if (_arrow != null) {
                    int add_dmg = Math.max(_arrow.getItem().getDmgSmall(), 1);
                    dmg = dmg + L1AttackMode._random.nextInt(add_dmg) + 1.0;
                    break;
                }
                if (_weaponId == 190) {
                    dmg += 10.0;
                    break;
                }
                break;
            }
            case 10: {
                dmg = weaponTotalDamage + _statusDamage + _pc.getBowDmgup() + _pc.getOriginalBowDmgup();
                if (_sting != null) {
                    int add_dmg = Math.max(_sting.getItem().getDmgSmall(), 1);
                    dmg += L1AttackMode._random.nextInt(add_dmg) + 1;
                    break;
                }
                break;
            }
            case 17: {
                dmg = L1WeaponSkill.getKiringkuDamage(_pc, _target);
                dmg += weaponTotalDamage;
                Integer dmgint = L1AttackList.INTD.get(Integer.valueOf(_pc.getInt()));
                if (dmgint != null) {
                    dmg += dmgint.intValue();
                    break;
                }
                break;
            }
        }
        int addchance = 0;
        if (_pc.getLevel() > 45) {
            addchance = (_pc.getLevel() - 45) / 5;
        }
        if (doubleBrake && L1AttackMode._random.nextInt(100) + 1 <= 25 + addchance) {
            dmg *= 2.0;
        }
        if (_weaponType2 != 0) {
            int add_dmg = _weapon.getItem().get_add_dmg();
            if (add_dmg != 0) {
                dmg += add_dmg;
            }
        }
        return dmg;
    }

    private double pcDmgMode(double dmg) {
        dmg = calcBuffDamage(dmg);
        if (_weaponType != 20 && _weaponType != 62) {
            if (_weaponType != 58) {
                dmg += _pc.getDmgModifierByArmor();
            }
        } else {
            dmg += _pc.getBowDmgModifierByArmor();
        }
        dmg += dmgUp();
        dmg += _pc.dmgAdd();
        if (_pc.getweapondmg() != 0) {
            dmg += _pc.getweapondmg();
        }
        int Dmgdouble = _pc.getDmgdouble();
        if (Dmgdouble != 0) {
            double i = dmg * (Dmgdouble / 100);
            dmg += i;
        }
        int[] data = _pc.getReward_Weapon();
        if (data[4] != 0 && L1AttackMode._random.nextInt(1000) + 1 <= data[4] - _pc.getelfweapon()) {
            if (data[5] != 0) {
                dmg += data[5];
            }
            if (data[6] != 0) {
                dmg += L1AttackMode._random.nextInt(data[6]) + 1;
            }
            if (data[2] != 0 && _target.getCurrentHp() > data[2]) {
                _pc.setCurrentHp((short) (_pc.getCurrentHp() + data[2]));
            }
            if (data[3] != 0 && _target.getCurrentMp() > data[3]) {
                _pc.setCurrentMp((short) (_pc.getCurrentMp() + data[3]));
            }
            if (data[7] != 0) {
                if (_targetPc != null) {
                    _targetPc.sendPacketsX8(new S_SkillSound(_targetPc.getId(), data[7]));
                } else {
                    _target.broadcastPacketX8(new S_SkillSound(_target.getId(), data[7]));
                }
            }
        }
        if (item_attr_char != null) {
            L1ItemSpecialAttribute attr = ItemSpecialAttributeTable.get()
                    .getAttrId(item_attr_char.get_attr_id());
            int drain_min_hp = attr.get_add_drain_min_hp();
            int drain_max_hp = attr.get_add_drain_max_hp();
            if (drain_min_hp > 0 && drain_max_hp > 0) {
                int random = L1AttackMode._random.nextInt(100) + 1;
                if (random <= attr.get_drain_hp_rand() && _target != null) {
                    _drainHp = Math.min(L1AttackMode._random.nextInt(drain_max_hp) + drain_min_hp, drain_max_hp);
                    _pc.setCurrentHp((short) (_pc.getCurrentHp() + _drainHp));
                }
            }
            int drain_min_mp = attr.get_add_drain_min_mp();
            int drain_max_mp = attr.get_add_drain_max_mp();
            if (drain_min_mp > 0 && drain_max_mp > 0) {
                int random2 = L1AttackMode._random.nextInt(100) + 1;
                if (random2 <= attr.get_drain_mp_rand() && _target != null) {
                    _drainMana = Math.min(L1AttackMode._random.nextInt(drain_max_mp) + drain_min_mp, drain_max_mp);
                    _pc.setCurrentMp((short) (_pc.getCurrentMp() + _drainMana));
                }
            }
            if (attr.get_add_skill_gfxid() > 0 && attr.get_add_skill_rand() >= L1AttackMode._random.nextInt(100) + 1) {
                _pc.sendPacketsX10(new S_SkillSound(_target.getId(), attr.get_add_skill_gfxid()));
                dmg += attr.get_add_skill_dmg();
            }
        }
        if (_pc.get_double_dmg() != 0 && L1AttackMode._random.nextInt(100) + 1 <= _pc.get_double_dmg()) {
            dmg *= 2.0;
        }
        return dmg;
    }

    public int calcPcDamage() {
        if (_targetPc == null) {
            return 0;
        }
        if (_targetPc == _pc) {
            return 0;
        }
        if (L1AttackMode.dmg0(_targetPc)) {
            _isHit = false;
            return _drainHp = 0;
        }
        if (!_isHit) {
            return 0;
        }
        if (_targetPc.isActived() && _targetPc.IsAttackTeleport() && !_targetPc.hasSkillEffect(6955)) {
            _targetPc.setSkillEffect(6599, 3000);
            _targetPc.sendPackets(new S_SystemMessage("您受到攻擊3秒後自動瞬移"));
        }
        int weaponMaxDamage = _weaponSmall;
        _weaponDamage = weaponDamage1(weaponMaxDamage);
        if (_pc.hasSkillEffect(175) && _weaponType != 20 && _weaponType != 62) {
            _weaponDamage = (int) (weaponMaxDamage * ConfigElfSkill.SOUL_OF_FLAME_DAMAGE);
        }
        if (_pc.is_mazu()) {
            _weaponDamage += 5;
        }
        _weaponTotalDamage = _weaponDamage + _weaponAddDmg + _weaponEnchant;
        double dmg = weaponDamage2(_weaponTotalDamage);
        dmg = pcDmgMode(dmg);
        if (_weaponEnchant >= 10) {
            dmg += _weaponEnchant - 9;
        }

        if (_targetPc.hasSkillEffect(113)) {
            dmg *= ConfigPrinceSkill.STRIKER_DMG;
        }
        if (_pc.getIsTRIPLE_ARROW()) {
            dmg *= ConfigElfSkill.TRIPLE_ARROW_DMG;
        }
        if (_weaponRange != -1 && _targetPc.hasSkillEffect(112)) {
            dmg *= ConfigDarkElfSkill.ARMOR_BREAK_DMG;
        }
        dmg -= _targetPc.getDamageReductionByArmor() + _targetPc.getother_ReductionDmg()
                + _targetPc.getClan_ReductionDmg() + _targetPc.get_reduction_dmg();
        dmg -= _targetPc.dmgDowe();
        if (_targetPc.getClanid() != 0) {
            dmg -= L1AttackMode.getDamageReductionByClan(_targetPc);
        }
        dmg += weaponSkill(_pc, _targetPc, _weaponTotalDamage);
        if (!_pc.getDolls().isEmpty()) {
            Iterator<L1DollInstance> iter = _pc.getDolls().values().iterator();
            while (iter.hasNext()) {
                L1DollInstance doll = iter.next();
                doll.startDollSkill(_targetPc, dmg);
            }
        }
        if (_targetPc.hasSkillEffect(88)) {
            int targetPcLvl = Math.max(_targetPc.getLevel(), 50);
            dmg -= (targetPcLvl - 50) / 5 + 10;
        }
        if (_pc.get_other().get_PvP_dmg() > 0) {
            dmg += _pc.get_other().get_PvP_dmg();
        }
        if (_pc.get_other().get_PvP_bow_dmg() > 0 && _weaponType == 20) {
            dmg += _pc.get_other().get_PvP_bow_dmg();
        }
        if (_targetPc.getNoweaponRedmg() > 0) {
            dmg -= _targetPc.getNoweaponRedmg();
        }
        if (_targetPc.hasSkillEffect(219)) {
            dmg -= dmg * (_targetPc.getAvatar() / 100);
        }
        if (_pc.get_other().get_Ran_PvP_dmg_b() > 0
                && L1AttackMode._random.nextInt(100) + 1 <= _pc.get_other().get_Ran_PvP_dmg_b()) {
            if (_pc.get_other().getPvP_dmg_b() >= 1.0) {
                dmg *= _pc.get_other().getPvP_dmg_b();
            } else {
                dmg *= 1.0 + _pc.get_other().getPvP_dmg_b();
            }
        }
        if (_targetPc.get_other().get_PvP_dmg_r() > 0) {
            dmg -= _targetPc.get_other().get_PvP_dmg_r();
        }
        if (_pc.get_PVPdmgg() > 0) {
            dmg += _pc.get_PVPdmgg();
        }
        if (_weaponEnchant >= 10) {
            dmg += _weaponEnchant - 9;
        }
        if (_pc.isCrown()) {
            dmg *= Config_Pc_Damage.Other_To_isCrownpc;
        } else if (_pc.isKnight()) {
            dmg *= Config_Pc_Damage.Other_To_isKnightpc;
        } else if (_pc.isWizard()) {
            dmg *= Config_Pc_Damage.Other_To_isWizardpc;
        } else if (_pc.isElf()) {
            dmg *= Config_Pc_Damage.Other_To_isElfpc;
        } else if (_pc.isDarkelf()) {
            dmg *= Config_Pc_Damage.Other_To_isDarkelfpc;
        } else if (_pc.isDragonKnight()) {
            dmg *= Config_Pc_Damage.Other_To_isDragonKnightpc;
        } else if (_pc.isIllusionist()) {
            dmg *= Config_Pc_Damage.Other_To_isIllusionistpc;
        }
        if (_targetPc.hasSkillEffect(174)) {
            dmg *= ConfigElfSkill.STRIKER_DMG2;
        }
        if (_targetPc.getPVPdmgReduction() > 0) {
            dmg -= _targetPc.getPVPdmgReduction();
        }
        if (_pc.getPVPdmg() != 0) {
            dmg += _pc.getPVPdmg();
        }
        if (_pc.get_weaknss() == 1) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_1;
        } else if (_pc.get_weaknss() == 2) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_2;
        } else if (_pc.get_weaknss() == 3) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_3;
        }
        if (_pc.hasSkillEffect(8895) && L1AttackMode._random.nextInt(100) + 1 <= 3) {
            PeepCard.BackMagic(_pc, _targetPc);
        }
        if (_pc.hasSkillEffect(8863)) {
            _damage += 5;
        }
        if (_targetPc.hasSkillEffect(8867)) {
            dmg = 0.0;
        }
        if (_pc.hasSkillEffect(8871)) {
            dmg += (int) dmg + _pc.getStr();
        }
        if (_pc.hasSkillEffect(8873)) {
            _targetPc.receiveDamage(_pc, 200 + L1AttackMode._random.nextInt(150) + 1, false, false);
            L1PcInstance pc = (L1PcInstance) _target;
            L1SpawnUtil.spawnEffect(81162, 2, pc.getX(), pc.getY(), pc.getMapId(), pc, 0);
            pc.setSkillEffect(87, 3000);
            pc.sendPackets(new S_Paralysis(5, true));
            pc.killSkillEffectTimer(8873);
        }
        if (_targetPc.isIllusionist() && _targetPc.getlogpcpower_SkillFor4() != 0) {
            int hp = _targetPc.getMaxHp() / _targetPc.getCurrentHp();
            if (hp > 1) {
                if (hp > 10) {
                    hp = 10;
                }
                if (RandomArrayList.getInc(100, 1) <= _targetPc.getlogpcpower_SkillFor4()) {
                    dmg -= hp * 40;
                    _targetPc.sendPackets(new S_SystemMessage("觸發 痛苦化身 減免了" + hp * 40 + "滴傷害。"));
                    _targetPc.sendPackets(new S_SkillSound(_targetPc.getId(), 5377));
                }
            }
        }
        if (_pc.isIllusionist() && _pc.getlogpcpower_SkillFor5() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor5()) {
            // 修改：減少冰封心智的持續時間，從2000ms改為500ms
            _targetPc.setSkillEffect(8007, 500);
            _targetPc.sendPackets(new S_SystemMessage("對方發動 冰封心智 使你無法攻擊0.5秒 。"));
            _pc.sendPackets(new S_SystemMessage("觸發 冰封心智"));
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
        }
        if (_targetPc.hasSkillEffect(9055)) {
            dmg = 0.0;
        }
        if (_targetPc.isCrown() && _targetPc.getlogpcpower_SkillFor1() != 0
                && !_targetPc.hasSkillEffect(9055)
                && RandomArrayList.getInc(100, 1) <= _targetPc.getlogpcpower_SkillFor1()) {
            _targetPc.setSkillEffect(9055, 1000);
            _targetPc.sendPackets(new S_SkillSound(_targetPc.getId(), 2234));
            _targetPc.broadcastPacketAll(new S_SkillSound(_targetPc.getId(), 2234));
            _targetPc.sendPackets(new S_SystemMessage("觸發 王者無敵1秒"));
        }
        if (_targetPc.isCrown() && _targetPc.isEsoteric()) {
            dmg = (int) (dmg * (1.0 + _targetPc.getlogpcpower_SkillFor2() * 0.01));
        }
        if (_pc.isCrown() && _pc.isEsoteric()) {
            dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor2() * 0.02));
        }
        if (_pc.isCrown() && _pc.getlogpcpower_SkillFor4() != 0 && _pc.getlogpcpower_SkillFor4() >= 1
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor4()) {
            _targetPc.setSkillEffect(4017, 1000);
            _target.broadcastPacketX8(new S_SkillSound(_target.getId(), 4184));
            _targetPc.sendPackets(new S_SkillSound(_target.getId(), 4184));
            _targetPc.sendPackets(new S_Paralysis(6, true));
        }
        if (_weaponType == 20 && _pc.isTripleArrow() && _pc.getlogpcpower_SkillFor1() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
            dmg += _targetPc.getCurrentHp() * 2 / 170;
            _pc.setTripleArrow(false);
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 射擊之箭"));
        }
        if (_pc.hasSkillEffect(175) && _weaponType != 20 && _weaponType != 62 && _pc.isElf()
                && _pc.getlogpcpower_SkillFor3() != 0 && _pc.getElfAttr() == 2
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor3()) {
            dmg *= 3.0;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發精通能量 造成4倍傷害！"));
        }
        if (_weaponType == 20 && _pc.hasSkillEffect(166) && _pc.getElfAttr() == 8 && _pc.isElf()
                && _pc.getlogpcpower_SkillFor3() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor3()) {
            dmg *= 1.4;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發精通能量 造成2倍傷害！"));
        }
        if (_targetPc.hasSkillEffect(168) && _targetPc.isElf()
                && _targetPc.getlogpcpower_SkillFor3() != 0 && _targetPc.getElfAttr() == 1
                && RandomArrayList.getInc(100, 1) <= _targetPc.getlogpcpower_SkillFor3()) {
            dmg *= 0.3;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _targetPc.sendPackets(new S_SystemMessage("觸發 精通能量 減免傷害"));
        }
        if (_pc.isElf() && _pc.getlogpcpower_SkillFor4() != 0) {
            int rr = _targetPc.getCurrentMp() / 100;
            if (RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor4()) {
                _targetPc.setCurrentMp(Math.max(_targetPc.getCurrentMp() - rr, 0));
            }
            _pc.sendPackets(new S_SystemMessage("觸發 吞噬魔力"));
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
        }
        if (_pc.isElf() && _pc.getlogpcpower_SkillFor5() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor5()) {
            if (_weaponType != 20 && _weaponType != 62) {
                dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor5() * 0.03));
            } else {
                dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor5() * 0.02));
            }
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 傷害擊殺"));
        }
        if (_pc.isWizard() && _pc.getlogpcpower_SkillFor4() != 0 && _pc.isEsoteric()
                && _pc.getlogpcpower_SkillFor4() >= 1) {
            dmg = (int) (dmg * (_pc.getlogpcpower_SkillFor4() * 0.03 + 1.0));
        }
        if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor5() != 0 && _pc.isEsoteric()) {
            if (_pc.getCurrentMp() > 2 * _pc.getlogpcpower_SkillFor5()) {
                dmg += 5 * _pc.getlogpcpower_SkillFor5();
                _pc.setCurrentMp(_pc.getCurrentMp() - 2 * _pc.getlogpcpower_SkillFor5());
                _pc.sendPackets(new S_SkillSound(_pc.getId(), 4592));
                _pc.broadcastPacketAll(new S_SkillSound(_pc.getId(), 4592));
            } else {
                _pc.setEsoteric(false);
                _pc.sendPackets(new S_SystemMessage("\\fU關閉轉生技能(刀劍之影)"));
            }
        }
        if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor1() != 0) {
            boolean isSameAttr = false;
            if (_targetPc.getHeading() == 0
                    && (_pc.getHeading() == 7 || _pc.getHeading() == 0 || _pc.getHeading() == 1)) {
                isSameAttr = true;
            } else if (_targetPc.getHeading() == 7
                    && (_pc.getHeading() == 6 || _pc.getHeading() == 7 || _pc.getHeading() == 0)) {
                isSameAttr = true;
            } else if (_targetPc.getHeading() == _pc.getHeading()
                    || _targetPc.getHeading() == _pc.getHeading() + 1
                    || _targetPc.getHeading() == _pc.getHeading() - 1) {
                isSameAttr = true;
            }
            if (isSameAttr && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
                dmg = (int) (dmg * 1.5);
                _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
                _pc.sendPackets(new S_SystemMessage("觸發 背部襲擊 造成 1.5倍傷害"));
            }
        }
        if (_pc.isDragonKnight() && _pc.isFoeSlayer() && _pc.getlogpcpower_SkillFor1() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
            dmg += _target.getCurrentHp() * 5 / 100;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 屠宰擊殺"));
        }
        if (_pc.isDragonKnight() && _pc.getlogpcpower_SkillFor2() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor2()) {
            double chp = dmg;
            _pc.setCurrentHp((int) (_pc.getCurrentHp() + chp));
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 1609));
            _pc.sendPackets(new S_SystemMessage("觸發 乾坤挪移 吸取 " + chp + "滴生命值"));
        }
        if (_targetPc.isDragonKnight() && _targetPc.getlogpcpower_SkillFor3() != 0
                && RandomArrayList.getInc(100, 1) <= _targetPc.getlogpcpower_SkillFor3()) {
            dmg -= (int) dmg * (_targetPc.getlogpcpower_SkillFor3() * 0.01);
            _pc.sendPackets(new S_SystemMessage("觸發 強之護鎧"));
        }
        if (_pc.isDragonKnight() && _pc.getlogpcpower_SkillFor4() != 0
                && _targetPc.getMaxHp() / 20 * 9 >= _targetPc.getCurrentHp()) {
            double adddmg = 1.0;
            if (_pc.getlogpcpower_SkillFor4() >= 1) {
                adddmg += _pc.getlogpcpower_SkillFor4() * 0.3;
            }
            dmg *= adddmg;
        }
        if (_pc.isKnight() && _pc.getlogpcpower_SkillFor4() != 0
                && _pc.getMaxHp() / 3 >= _pc.getCurrentHp() && !_pc.isEsoteric()
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor4()) {
            dmg *= 1.9;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 狂暴致命給予3倍高傷害"));
        }
        int ran = L1AttackMode._random.nextInt(100) + 1;
        if (_targetPc.getInventory().checkSkillType(113) && ran <= ConfigOther.armor_type1) {
            dmg *= 0.98;
        }
        if (_targetPc.getInventory().checkSkillType(114) && ran <= ConfigOther.armor_type2) {
            dmg *= 0.95;
        }
        if (_targetPc.getInventory().checkSkillType(115) && ran <= ConfigOther.armor_type3) {
            dmg *= 0.9;
        }
        if (_targetPc.getInventory().checkSkillType(116) && ran <= ConfigOther.armor_type4) {
            dmg *= 0.85;
        }
        if (_targetPc.getInventory().checkSkillType(117) && ran <= ConfigOther.armor_type5) {
            dmg *= 0.8;
        }
        if (_pc.getInventory().checkSkillType(118) && ran <= ConfigOther.armor_type6) {
            dmg += 5.0;
        }
        if (_pc.getInventory().checkSkillType(119) && ran <= ConfigOther.armor_type7) {
            dmg += 10.0;
        }
        if (_pc.getInventory().checkSkillType(120) && ran <= ConfigOther.armor_type8) {
            dmg += 15.0;
        }
        if (_pc.getInventory().checkSkillType(121) && ran <= ConfigOther.armor_type9) {
            dmg += 20.0;
        }
        if (_pc.getInventory().checkSkillType(122) && ran <= ConfigOther.armor_type10) {
            dmg += 30.0;
        }
        if (_pc.getInventory().checkSkillType(123) && ran <= ConfigOther.armor_type11) {
            dmg *= 1.2;
        }
        if (_pc.getInventory().checkSkillType(124) && ran <= ConfigOther.armor_type12) {
            dmg *= 1.4;
        }
        if (_pc.getInventory().checkSkillType(125) && ran <= ConfigOther.armor_type13) {
            dmg *= 1.6;
        }
        if (_pc.getInventory().checkSkillType(126) && ran <= ConfigOther.armor_type14) {
            dmg *= 1.8;
        }
        if (_pc.getInventory().checkSkillType(127) && ran <= ConfigOther.armor_type15) {
            dmg *= 2.0;
        }
        if (_targetPc.getInventory().checkSkillType(128) && ran <= ConfigOther.armor_type16) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(129) && ran <= ConfigOther.armor_type17) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(130) && ran <= ConfigOther.armor_type18) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(131) && ran <= ConfigOther.armor_type19) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(132) && ran <= ConfigOther.armor_type20) {
            dmg = 0.0;
        }
        Iterator<L1ItemInstance> iterator = _targetPc.getInventory().getItems().iterator();
        while (iterator.hasNext()) {
            L1ItemInstance item = iterator.next();
            if (item.getItemId() == 400041 && item.isEquipped()) {
                Random random = new Random();
                int r = random.nextInt(100) + 1;
                if (item.getEnchantLevel() * 2 < r) {
                    continue;
                }
                dmg -= 50.0;
                _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), 6320));
            }
        }
        boolean dmgX2 = false;
        if (!_targetPc.getSkillisEmpty() && _targetPc.getSkillEffect().size() > 0) {
            try {
                Iterator<Integer> iterator2 = _targetPc.getSkillEffect().iterator();
                while (iterator2.hasNext()) {
                    Integer key = iterator2.next();
                    Integer integer = L1AttackList.SKD3.get(key);
                    if (integer != null) {
                        if (integer.equals(key)) {
                            dmgX2 = true;
                        } else {
                            dmg += integer.intValue();
                        }
                    }
                }
            } catch (ConcurrentModificationException ex) {
            } catch (Exception e) {
                L1AttackPc._log.error(e.getLocalizedMessage(), e);
            }
        }
        if (dmgX2) {
            dmg /= ConfigOther.IMMUNE_TO_HARM;
        }
        if (dmg <= 0.0) {
            _isHit = false;
            _drainHp = 0;
        }
        return (int) dmg;
    }

    private int c3_power() {
        if (_pc.hasSkillEffect(7005)) {
            return 1;
        }
        if (_pc.hasSkillEffect(7006)) {
            return 2;
        }
        return 0;
    }

    private int c3_power_to_pc(int type) {
        int damage = c3_power_dmg(type);
        int resist = 0;
        switch (type) {
            case 1: {
                resist = _targetPc.getFire();
                if (resist > 0) {
                    damage = c3_power_dmg_down(damage, Math.min(100, resist));
                    break;
                }
                if (resist < 0) {
                    damage = c3_power_dmg_up(damage, Math.min(0, resist));
                    break;
                }
                break;
            }
            case 2: {
                resist = _targetPc.getWater();
                if (resist > 0) {
                    damage = c3_power_dmg_down(damage, Math.min(100, resist));
                    break;
                }
                if (resist < 0) {
                    damage = c3_power_dmg_up(damage, Math.min(0, resist));
                    break;
                }
                break;
            }
        }
        return damage;
    }

    private int c3_power_to_npc(int type) {
        int damage = c3_power_dmg(type);
        switch (type) {
            case 1: {
                if (!(_targetNpc instanceof L1MonsterInstance)) {
                    break;
                }
                L1MonsterInstance tgmob = (L1MonsterInstance) _targetNpc;
                if (!tgmob.isDead()) {
                    tgmob.receiveDamage(_pc, damage, 2);
                    tgmob.broadcastPacketX8(new S_DoActionGFX(tgmob.getId(), 2));
                    break;
                }
                break;
            }
            case 2: {
                if (!(_targetNpc instanceof L1MonsterInstance)) {
                    break;
                }
                L1MonsterInstance tgmob = (L1MonsterInstance) _targetNpc;
                if (!tgmob.isDead()) {
                    tgmob.receiveDamage(_pc, damage, 4);
                    tgmob.broadcastPacketX8(new S_DoActionGFX(tgmob.getId(), 2));
                    break;
                }
                break;
            }
        }
        return 0;
    }

    private int c3_power_dmg_down(int damage, int resist) {
        int r = 100 - resist;
        int dmg = damage * r / 100;
        return Math.max(10, dmg);
    }

    private int c3_power_dmg_up(int damage, int resist) {
        int dmg = damage - damage * resist / 100;
        return Math.abs(dmg);
    }

    private int c3_power_dmg(int type) {
        int damage = 0;
        int level = _pc.getLevel();
        switch (type) {
            case 1: {
                if (level >= 50 && level < 70) {
                    damage = random_dmg(40, 100);
                    break;
                }
                if (level >= 70 && level < 90) {
                    damage = random_dmg(50, 120);
                    break;
                }
                if (level >= 90 && level < 110) {
                    damage = random_dmg(60, 140);
                    break;
                }
                if (level >= 110 && level < 130) {
                    damage = random_dmg(70, 160);
                    break;
                }
                if (level >= 130 && level < 150) {
                    damage = random_dmg(80, 180);
                    break;
                }
                if (level >= 150 && level < 175) {
                    damage = random_dmg(90, 200);
                    break;
                }
                if (level >= 175 && level < 190) {
                    damage = random_dmg(100, 250);
                    break;
                }
                if (level >= 190 && level < 200) {
                    damage = random_dmg(110, 300);
                    break;
                }
                damage = random_dmg(200, 300);
                break;
            }
            case 2: {
                if (level >= 50 && level < 70) {
                    damage = random_dmg(40, 100);
                    break;
                }
                if (level >= 70 && level < 90) {
                    damage = random_dmg(50, 120);
                    break;
                }
                if (level >= 90 && level < 110) {
                    damage = random_dmg(60, 140);
                    break;
                }
                if (level >= 110 && level < 130) {
                    damage = random_dmg(70, 160);
                    break;
                }
                if (level >= 130 && level < 150) {
                    damage = random_dmg(80, 180);
                    break;
                }
                if (level >= 150 && level < 175) {
                    damage = random_dmg(90, 200);
                    break;
                }
                if (level >= 175 && level < 190) {
                    damage = random_dmg(100, 250);
                    break;
                }
                if (level >= 190 && level < 200) {
                    damage = random_dmg(110, 300);
                    break;
                }
                damage = random_dmg(200, 300);
                break;
            }
        }
        return damage;
    }

    private int random_dmg(int i, int j) {
        return L1AttackMode._random.nextInt(j - i) + i;
    }

    private int calcNpcDamage() {
        if (_targetNpc == null) {
            return 0;
        }
        if (L1AttackMode.dmg0(_targetNpc)) {
            _isHit = false;
            _drainHp = 0;
            return 0;
        }
        if (!_isHit) {
            return 0;
        }
        int weaponMaxDamage = 0;
        if (_targetNpc.getNpcTemplate().isSmall() && _weaponSmall > 0) {
            weaponMaxDamage = _weaponSmall;
        } else if (_targetNpc.getNpcTemplate().isLarge() && _weaponLarge > 0) {
            weaponMaxDamage = _weaponLarge;
        } else if (_weaponSmall > 0) {
            weaponMaxDamage = _weaponSmall;
        }
        _weaponDamage = weaponDamage1(weaponMaxDamage);
        if (_pc.hasSkillEffect(175) && _weaponType != 20 && _weaponType != 62) {
            _weaponDamage = (int) (weaponMaxDamage * ConfigElfSkill.SOUL_OF_FLAME_DAMAGE);
        }
        if (_pc.is_mazu()) {
            _weaponDamage += 5;
        }
        _weaponTotalDamage = _weaponDamage + _weaponAddDmg + _weaponEnchant;
        _weaponTotalDamage += calcMaterialBlessDmg();
        double dmg = weaponDamage2(_weaponTotalDamage);
        dmg = pcDmgMode(dmg);
        dmg -= calcNpcDamageReduction();
        dmg -= calcPcDefense();
        dmg += weaponSkill(_pc, _targetNpc, _weaponTotalDamage);
        boolean isNowWar = false;
        int castleId = L1CastleLocation.getCastleIdByArea(_targetNpc);
        if (castleId > 0) {
            isNowWar = ServerWarExecutor.get().isNowWar(castleId);
        }
        if (!isNowWar) {
            if (_targetNpc instanceof L1PetInstance) {
                dmg *= ConfigOther.pcdmgpet;
            }
            if (_targetNpc instanceof L1SummonInstance) {
                L1SummonInstance summon = (L1SummonInstance) _targetNpc;
                if (summon.isExsistMaster()) {
                    dmg *= ConfigOther.pcdmgsumm;
                }
            }
        } else if (isNowWar) {
            if (_targetNpc instanceof L1PetInstance) {
                dmg *= ConfigOther.pcdmgpet_war;
            }
            if (_targetNpc instanceof L1SummonInstance) {
                L1SummonInstance summon = (L1SummonInstance) _targetNpc;
                if (summon.isExsistMaster()) {
                    dmg *= ConfigOther.pcdmgsumm_war;
                }
            }
        }
        if (_targetNpc.getNpcTemplate().is_hard() && (_weaponType == 20 || _weaponType == 62)) {
            dmg -= 3.0;
        }
        if (_pc.isCrown()) {
            dmg *= Config_Pc_Damage.Other_To_isCrownnpc;
        } else if (_pc.isKnight()) {
            dmg *= Config_Pc_Damage.Other_To_isKnightnpc;
        } else if (_pc.isWizard()) {
            dmg *= Config_Pc_Damage.Other_To_isWizardnpc;
        } else if (_pc.isElf()) {
            dmg *= Config_Pc_Damage.Other_To_isElfnpc;
        } else if (_pc.isDarkelf()) {
            dmg *= Config_Pc_Damage.Other_To_isDarkelfnpc;
        } else if (_pc.isDragonKnight()) {
            dmg *= Config_Pc_Damage.Other_To_isDragonKnightnpc;
        } else if (_pc.isIllusionist()) {
            dmg *= Config_Pc_Damage.Other_To_isIllusionistnpc;
        }
        if (_weaponEnchant >= 10) {
            dmg += _weaponEnchant - 9;
        }
        if (_pc.get_weaknss() == 1) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_1;
        } else if (_pc.get_weaknss() == 2) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_2;
        } else if (_pc.get_weaknss() == 3) {
            dmg += ConfigDragonKnightSkill.VULNERABILITY_3;
        }
        if (_pc.getIsTRIPLE_ARROW()) {
            dmg *= ConfigElfSkill.TRIPLE_ARROW_DMG;
        }
        if (!_targetNpc.getNpcTemplate().is_boss()) {
            L1Npc template = NpcTable.get().getTemplate(_targetNpc.getNpcId());
            if (template.getImpl().equals("L1Monster") && !_pc.isActived() && _pc.get_followmaster() == null
                    && _pc.hasSkillEffect(6931)) {
                _pc.killSkillEffectTimer(6931);
                _pc.setSkillEffect(6932, 1000);
            }
        }
        if (_pc.hasSkillEffect(7951) && !_pc.isGm() && !_targetNpc.getNpcTemplate().is_boss()
                && !isInWarAreaAndWarTime(_pc)) {
            _pc.killSkillEffectTimer(7951);
            _pc.setSkillEffect(7952, 2000);
        }
        if (_weaponRange != -1 && _targetNpc.hasSkillEffect(112)
                && !_targetNpc.getNpcTemplate().is_boss()) {
            dmg *= 1.58;
        }
        if (_pc.hasSkillEffect(8863)) {
            _damage += 5;
        }
        if (_pc.hasSkillEffect(8871)) {
            dmg += (int) dmg + _pc.getStr();
        }
        if (!_pc.getDolls().isEmpty()) {
            Iterator<L1DollInstance> iter = _pc.getDolls().values().iterator();
            while (iter.hasNext()) {
                L1DollInstance doll = iter.next();
                doll.startDollSkill(_targetNpc, dmg);
            }
        }
        if (_pc.isCrown() && _pc.isEsoteric()) {
            dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor2() * 0.02));
        }
        if (_weaponType == 20 && _pc.isTripleArrow() && _pc.getlogpcpower_SkillFor1() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
            dmg += 110.0;
            _pc.setTripleArrow(false);
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 射擊之箭"));
        }
        if (_pc.hasSkillEffect(175) && _weaponType != 20 && _weaponType != 62 && _pc.isElf()
                && _pc.getlogpcpower_SkillFor3() != 0 && _pc.getElfAttr() == 2
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor3()) {
            dmg *= 3.0;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發精通元素 造成4倍傷害！"));
        }
        if (_weaponType == 20 && _pc.hasSkillEffect(166) && _pc.getElfAttr() == 8 && _pc.isElf()
                && _pc.getlogpcpower_SkillFor3() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor3()) {
            dmg *= 1.6;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發精通元素 造成2倍傷害！"));
        }
        if (_pc.isElf() && _pc.getlogpcpower_SkillFor5() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor5()) {
            if (_weaponType != 20 && _weaponType != 62) {
                dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor5() * 0.03));
            } else {
                dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor5() * 0.02));
            }
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 傷害擊殺"));
        }
        if (_pc.isWizard() && _pc.getlogpcpower_SkillFor4() != 0 && _pc.isEsoteric()
                && _pc.getlogpcpower_SkillFor4() >= 1) {
            dmg = (int) (dmg * (_pc.getlogpcpower_SkillFor4() * 0.03 + 1.0));
        }
        if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor5() != 0 && _pc.isEsoteric()) {
            if (_pc.getCurrentMp() > 2 * _pc.getlogpcpower_SkillFor5()) {
                dmg += 5 * _pc.getlogpcpower_SkillFor5();
                _pc.setCurrentMp(_pc.getCurrentMp() - 2 * _pc.getlogpcpower_SkillFor5());
                _pc.sendPackets(new S_SkillSound(_pc.getId(), 4592));
                _pc.broadcastPacketAll(new S_SkillSound(_pc.getId(), 4592));
            } else {
                _pc.setEsoteric(false);
                _pc.sendPackets(new S_SystemMessage("\fU關閉轉生技能(刀劍之影)"));
            }
        }
        if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor1() != 0) {
            boolean isSameAttr = false;
            if (_targetPc.getHeading() == 0
                    && (_pc.getHeading() == 7 || _pc.getHeading() == 0 || _pc.getHeading() == 1)) {
                isSameAttr = true;
            } else if (_targetPc.getHeading() == 7
                    && (_pc.getHeading() == 6 || _pc.getHeading() == 7 || _pc.getHeading() == 0)) {
                isSameAttr = true;
            } else if (_targetPc.getHeading() == _pc.getHeading()
                    || _targetPc.getHeading() == _pc.getHeading() + 1
                    || _targetPc.getHeading() == _pc.getHeading() - 1) {
                isSameAttr = true;
            }
            if (isSameAttr && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
                dmg = (int) (dmg * 1.5);
                _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
                _pc.sendPackets(new S_SystemMessage("觸發 背部襲擊 造成 1.5倍傷害"));
            }
        }
        if (_pc.isDragonKnight() && _pc.isFoeSlayer() && _pc.getlogpcpower_SkillFor1() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor1()) {
            dmg += _target.getCurrentHp() * 5 / 100;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 屠宰擊殺"));
        }
        if (_pc.isDragonKnight() && _pc.getlogpcpower_SkillFor2() != 0
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor2()) {
            double chp = dmg;
            _pc.setCurrentHp((int) (_pc.getCurrentHp() + chp));
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 1609));
            _pc.sendPackets(new S_SystemMessage("觸發 乾坤挪移 吸取 " + chp + "滴生命值"));
        }
        if (_targetPc.isDragonKnight() && _targetPc.getlogpcpower_SkillFor3() != 0
                && RandomArrayList.getInc(100, 1) <= _targetPc.getlogpcpower_SkillFor3()) {
            dmg -= (int) dmg * (_targetPc.getlogpcpower_SkillFor3() * 0.01);
            _pc.sendPackets(new S_SystemMessage("觸發 強之護鎧"));
        }
        if (_pc.isDragonKnight() && _pc.getlogpcpower_SkillFor4() != 0
                && _targetPc.getMaxHp() / 20 * 9 >= _targetPc.getCurrentHp()) {
            double adddmg = 1.0;
            if (_pc.getlogpcpower_SkillFor4() >= 1) {
                adddmg += _pc.getlogpcpower_SkillFor4() * 0.3;
            }
            dmg *= adddmg;
        }
        if (_pc.isKnight() && _pc.getlogpcpower_SkillFor4() != 0
                && _pc.getMaxHp() / 3 >= _pc.getCurrentHp() && !_pc.isEsoteric()
                && RandomArrayList.getInc(100, 1) <= _pc.getlogpcpower_SkillFor4()) {
            dmg *= 1.9;
            _pc.sendPackets(new S_SkillSound(_pc.getId(), 5377));
            _pc.sendPackets(new S_SystemMessage("觸發 狂暴致命給予3倍高傷害"));
        }
        int ran = L1AttackMode._random.nextInt(100) + 1;
        if (_targetPc.getInventory().checkSkillType(113) && ran <= ConfigOther.armor_type1) {
            dmg *= 0.98;
        }
        if (_targetPc.getInventory().checkSkillType(114) && ran <= ConfigOther.armor_type2) {
            dmg *= 0.95;
        }
        if (_targetPc.getInventory().checkSkillType(115) && ran <= ConfigOther.armor_type3) {
            dmg *= 0.9;
        }
        if (_targetPc.getInventory().checkSkillType(116) && ran <= ConfigOther.armor_type4) {
            dmg *= 0.85;
        }
        if (_targetPc.getInventory().checkSkillType(117) && ran <= ConfigOther.armor_type5) {
            dmg *= 0.8;
        }
        if (_pc.getInventory().checkSkillType(118) && ran <= ConfigOther.armor_type6) {
            dmg += 5.0;
        }
        if (_pc.getInventory().checkSkillType(119) && ran <= ConfigOther.armor_type7) {
            dmg += 10.0;
        }
        if (_pc.getInventory().checkSkillType(120) && ran <= ConfigOther.armor_type8) {
            dmg += 15.0;
        }
        if (_pc.getInventory().checkSkillType(121) && ran <= ConfigOther.armor_type9) {
            dmg += 20.0;
        }
        if (_pc.getInventory().checkSkillType(122) && ran <= ConfigOther.armor_type10) {
            dmg += 30.0;
        }
        if (_pc.getInventory().checkSkillType(123) && ran <= ConfigOther.armor_type11) {
            dmg *= 1.2;
        }
        if (_pc.getInventory().checkSkillType(124) && ran <= ConfigOther.armor_type12) {
            dmg *= 1.4;
        }
        if (_pc.getInventory().checkSkillType(125) && ran <= ConfigOther.armor_type13) {
            dmg *= 1.6;
        }
        if (_pc.getInventory().checkSkillType(126) && ran <= ConfigOther.armor_type14) {
            dmg *= 1.8;
        }
        if (_pc.getInventory().checkSkillType(127) && ran <= ConfigOther.armor_type15) {
            dmg *= 2.0;
        }
        if (_targetPc.getInventory().checkSkillType(128) && ran <= ConfigOther.armor_type16) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(129) && ran <= ConfigOther.armor_type17) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(130) && ran <= ConfigOther.armor_type18) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(131) && ran <= ConfigOther.armor_type19) {
            dmg = 0.0;
        }
        if (_targetPc.getInventory().checkSkillType(132) && ran <= ConfigOther.armor_type20) {
            dmg = 0.0;
        }
        Iterator<L1ItemInstance> iterator = _targetPc.getInventory().getItems().iterator();
        while (iterator.hasNext()) {
            L1ItemInstance item = iterator.next();
            if (item.getItemId() == 400041 && item.isEquipped()) {
                Random random = new Random();
                int r = random.nextInt(100) + 1;
                if (item.getEnchantLevel() * 2 < r) {
                    continue;
                }
                dmg -= 50.0;
                _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), 6320));
            }
        }
        boolean dmgX2 = false;
        if (!_targetPc.getSkillisEmpty() && _targetPc.getSkillEffect().size() > 0) {
            try {
                Iterator<Integer> iterator2 = _targetPc.getSkillEffect().iterator();
                while (iterator2.hasNext()) {
                    Integer key = iterator2.next();
                    Integer integer = L1AttackList.SKD3.get(key);
                    if (integer != null) {
                        if (integer.equals(key)) {
                            dmgX2 = true;
                        } else {
                            dmg += integer.intValue();
                        }
                    }
                }
            } catch (ConcurrentModificationException ex) {
            } catch (Exception e) {
                L1AttackPc._log.error(e.getLocalizedMessage(), e);
            }
        }
        if (dmgX2) {
            dmg /= ConfigOther.IMMUNE_TO_HARM;
        }
        if (dmg <= 0.0) {
            _isHit = false;
            _drainHp = 0;
        }
        return (int) dmg;
    }

    private void dk_dmgUp() {
        if (_pc.isDragonKnight() && _weaponType2 == 18) {
            long h_time = Calendar.getInstance().getTimeInMillis() / 1000L;
            int random = L1AttackMode._random.nextInt(100);
            int weaponchance = ConfigDragonKnightSkill.VULNERABILITY_ROM;
            if (_weapon.getItemId() == 410189) {
                weaponchance = ConfigDragonKnightSkill.VULNERABILITY_OTHER;
            }
            if (_pc.get_tmp_targetid() != _targetId) {
                _pc.set_weaknss(0, 0L);
                _pc.sendPackets(new S_PacketBoxDk(0));
            }
            _pc.set_tmp_targetid(_targetId);
            switch (_pc.get_weaknss()) {
                case 0: {
                    if (random < weaponchance) {
                        _pc.set_weaknss(1, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(1));
                        break;
                    }
                    break;
                }
                case 1: {
                    if (random < weaponchance) {
                        _pc.set_weaknss(1, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(1));
                        break;
                    }
                    if (random >= weaponchance && random < weaponchance * 2) {
                        _pc.set_weaknss(2, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(2));
                        break;
                    }
                    break;
                }
                case 2: {
                    if (random < weaponchance) {
                        _pc.set_weaknss(2, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(2));
                        break;
                    }
                    if (random >= weaponchance && random < weaponchance * 2) {
                        _pc.set_weaknss(3, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(3));
                        break;
                    }
                    break;
                }
                case 3: {
                    if (random < weaponchance) {
                        _pc.set_weaknss(3, h_time);
                        _pc.sendPackets(new S_PacketBoxDk(3));
                        break;
                    }
                    break;
                }
            }
        }
    }

    private double dmgUp() {
        double dmg = 0.0;
        if (_pc.getSkillEffect().size() <= 0) {
            return dmg;
        }
        if (!_pc.getSkillisEmpty()) {
            try {
                if (_weaponType != 20 && _weaponType != 62) {
                    Iterator<Integer> iterator = _pc.getSkillEffect().iterator();
                    while (iterator.hasNext()) {
                        Integer key = iterator.next();
                        Integer integer = L1AttackList.SKD1.get(key);
                        if (integer != null) {
                            dmg += integer.intValue();
                        }
                    }
                } else {
                    Iterator<Integer> iterator2 = _pc.getSkillEffect().iterator();
                    while (iterator2.hasNext()) {
                        Integer key = iterator2.next();
                        Integer integer = L1AttackList.SKD2.get(key);
                        if (integer != null) {
                            dmg += integer.intValue();
                        }
                    }
                }
            } catch (ConcurrentModificationException ex) {
            } catch (Exception e) {
                L1AttackPc._log.error(e.getLocalizedMessage(), e);
            }
        }
        return dmg;
    }

    private double weaponSkill(L1PcInstance pcInstance, L1Character character,
                               double weaponTotalDamage) {
        double dmg = 0.0;
        dmg = WeaponSkillStart.start_weapon_skill(pcInstance, character, _weapon, weaponTotalDamage);
        if (dmg != 0.0) {
            return dmg;
        }
        switch (_weaponId) {
            case 124: {
                dmg = L1WeaponSkill.getBaphometStaffDamage(_pc, _target);
                break;
            }
            case 204:
            case 100204: {
                L1WeaponSkill.giveFettersEffect(_pc, _target);
                break;
            }
            case 261: {
                L1WeaponSkill.giveArkMageDiseaseEffect(_pc, _target);
                break;
            }
            case 410131: {
                L1WeaponSkill.giveTurn_Undead(_pc, _target);
                break;
            }
            case 260:
            case 263: {
                dmg = L1WeaponSkill.getAreaSkillWeaponDamage(_pc, _target, _weaponId);
                break;
            }
            case 264: {
                dmg = L1WeaponSkill.getLightningEdgeDamage(_pc, _target);
                break;
            }
            default: {
                dmg = L1WeaponSkill.getWeaponSkillDamage(_pc, _target, _weaponId);
                break;
            }
        }
        return dmg;
    }

    private double calcBuffDamage(double dmg) {
        int random = L1AttackMode._random.nextInt(100) + 1;
        if (_weaponType == 20) {
            return dmg;
        }
        if (_weaponType == 62) {
            return dmg;
        }
        if (_weaponType2 == 17) {
            return dmg;
        }
        if (_pc.hasSkillEffect(171) && random <= ConfigElfSkill.ELEMENTAL_FIRE_RND && _weaponType != 20
                && _weaponType != 62) {
            dmg *= ConfigElfSkill.ELEMENTAL_FIRE;
        }
        if (_pc.hasSkillEffect(102) && random <= ConfigDarkElfSkill.BURNING_CHANCE) {
            dmg *= ConfigDarkElfSkill.BURNING_DMG;
        }
        if (_pc.hasSkillEffect(148)) {
            dmg += 4.0;
        }
        if (_pc.hasSkillEffect(163)) {
            dmg += 6.0;
        }
        if (_pc.hasSkillEffect(55)) {
            dmg += 5.0;
        }
        if (_pc.hasSkillEffect(182)) {
            dmg += 10.0;
            _pc.sendPacketsAll(new S_EffectLocation(_targetX, _targetY, 6591));
            _pc.killSkillEffectTimer(182);
        }
        return dmg;
    }

    private int calcMaterialBlessDmg() {
        int damage = 0;

        if (_pc.getWeapon() != null) {
            int undead = _targetNpc.getNpcTemplate().get_undead();
            switch (undead) {
                case 1:
                case 3:
                case 4: {
                    if (_weaponMaterial == 14 || _weaponMaterial == 17 || _weaponMaterial == 22) {
                        damage += L1AttackMode._random.nextInt(20) + 1;
                    }
                    if (_weaponBless == 0) {
                        damage += L1AttackMode._random.nextInt(4) + 1;
                    }
                    switch (_weaponType) {
                        case 20:
                        case 62: {
                            break;
                        }
                        default: {
                            if (_weapon.getHolyDmgByMagic() != 0) {
                                damage += _weapon.getHolyDmgByMagic();
                                break;
                            }
                            break;
                        }
                    }
                    break;
                }
                case 2: {
                    if (_weaponMaterial == 17 || _weaponMaterial == 22) {
                        damage += L1AttackMode._random.nextInt(3) + 1;
                    }
                    if (_weaponBless == 0) {
                        damage += L1AttackMode._random.nextInt(4) + 1;
                        break;
                    }
                    break;
                }
                case 5: {
                    if (_weaponMaterial == 14 || _weaponMaterial == 17 || _weaponMaterial == 22) {
                        damage += L1AttackMode._random.nextInt(20) + 1;
                        break;
                    }
                    break;
                }
            }
        }

        return damage;
    }

    private void soulHp() {
        switch (_calcType) {
            case 1: {
                if (_pc.isSoulHp() <= 0) {
                    break;
                }
                ArrayList<Integer> soulHp = new ArrayList();
                soulHp = _pc.get_soulHp();
                int r = soulHp.get(0).intValue();
                int min = soulHp.get(1).intValue();
                int max = soulHp.get(2).intValue();
                if (L1AttackMode._random.nextInt(100) < r) {
                    if (_pc.isSoulHp() == 1) {
                        _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), 11673));
                    } else {
                        _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), 11677));
                    }
                    int hpadd = L1AttackMode._random.nextInt(max - min) + min;
                    int newHp = _pc.getCurrentHp() + hpadd;
                    if (newHp >= _pc.getMaxHp()) {
                        _pc.setCurrentHp(_pc.getMaxHp());
                    } else {
                        _pc.setCurrentHp(newHp);
                    }
                    _targetPc.receiveDamage(_pc, hpadd, false, true);
                    break;
                }
                break;
            }
            case 2: {
                if (_pc.isSoulHp() <= 0 || !(_targetNpc instanceof L1MonsterInstance)) {
                    break;
                }
                ArrayList<Integer> soulHp = new ArrayList();
                soulHp = _pc.get_soulHp();
                int r = soulHp.get(0).intValue();
                int min = soulHp.get(1).intValue();
                int max = soulHp.get(2).intValue();
                if (L1AttackMode._random.nextInt(100) < r) {
                    if (_pc.isSoulHp() == 1) {
                        _targetNpc.broadcastPacketAll(new S_SkillSound(_targetNpc.getId(), 11673));
                    } else {
                        _targetNpc.broadcastPacketAll(new S_SkillSound(_targetNpc.getId(), 11677));
                    }
                    int hpadd = L1AttackMode._random.nextInt(max - min) + min;
                    int newHp = _pc.getCurrentHp() + hpadd;
                    if (newHp >= _pc.getMaxHp()) {
                        _pc.setCurrentHp(_pc.getMaxHp());
                    } else {
                        _pc.setCurrentHp(newHp);
                    }
                    _targetNpc.receiveDamage(_pc, hpadd);
                    break;
                }
                break;
            }
        }
    }

    private void AttrAmuletEffect() {
        int rnd = _pc.get_AttrAmulet_rnd();
        int dmg = _pc.get_AttrAmulet_dmg();
        int gfxid = _pc.get_AttrAmulet_gfxid();
        if (rnd <= 0) {
            return;
        }
        switch (_calcType) {
            case 1: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    if (_targetPc.hasSkillEffect(68)) {
                        dmg = (int) (dmg / ConfigOther.IMMUNE_TO_HARM);
                    }
                    _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), gfxid));
                    _targetPc.receiveDamage(_pc, dmg, false, true);
                    break;
                }
                break;
            }
            case 2: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    if (_targetNpc.hasSkillEffect(68)) {
                        dmg = (int) (dmg / ConfigOther.IMMUNE_TO_HARM);
                    }
                    _targetNpc.broadcastPacketAll(new S_SkillSound(_targetNpc.getId(), gfxid));
                    _targetNpc.receiveDamage(_pc, dmg);
                    break;
                }
                break;
            }
        }
    }

    private int calcAttrEnchantDmg() {
        int damage = 0;
        switch (_weaponAttrEnchantLevel) {
            case 1: {
                damage = 1;
                break;
            }
            case 2: {
                damage = 3;
                break;
            }
            case 3: {
                damage = 5;
                break;
            }
            case 4: {
                damage = 7;
                break;
            }
            case 5: {
                damage = 9;
                break;
            }
        }
        if (_weaponType == 20 && _weaponId != 190 && _arrow == null && _arrow.getItemId() >= 84077
                && _arrow.getItemId() >= 84080) {
            damage += 3;
        }
        if (_weaponType == 20 && _weaponId != 190) {
            if (_arrow.getItemId() == 84080) {
                _arrowGfxid = 4579;
            }
            if (_arrow.getItemId() == 84077) {
                _arrowGfxid = 7191;
            }
            if (_arrow.getItemId() == 84079) {
                _arrowGfxid = 2546;
            }
            if (_arrow.getItemId() == 84078) {
                _arrowGfxid = 4432;
            }
        }
        int resist = 0;

        switch (_calcType) {
            case 1: {
                switch (_weaponAttrEnchantKind) {
                    case 1: {
                        resist = _targetPc.getEarth();
                        break;
                    }
                    case 2: {
                        resist = _targetPc.getFire();
                        break;
                    }
                    case 4: {
                        resist = _targetPc.getWater();
                        break;
                    }
                    case 8: {
                        resist = _targetPc.getWind();
                        break;
                    }
                }
                break;
            }
            case 2: {
                switch (_weaponAttrEnchantKind) {
                    case 1: {
                        resist = _targetNpc.getEarth();
                        break;
                    }
                    case 2: {
                        resist = _targetNpc.getFire();
                        break;
                    }
                    case 4: {
                        resist = _targetNpc.getWater();
                        break;
                    }
                    case 8: {
                        resist = _targetNpc.getWind();
                        break;
                    }
                }
            }

        }
        int resistFloor = (int) (0.16 * Math.abs(resist));
        if (resist < 0) {
            resistFloor *= -1;
        }
        double attrDeffence = resistFloor / 32.0;
        double attrCoefficient = 1.0 - attrDeffence;
        damage = (int) (damage * attrCoefficient);
        return damage;
    }

    private void addPcPoisonAttack(L1Character target) {
        boolean isCheck = false;
        boolean isCheck2 = false;
        switch (_weaponId) {
            case 0: {
                break;
            }
            case 13:
            case 14: {
                isCheck = true;
                break;
            }
            default: {
                if (_pc.hasSkillEffect(98)) {
                    isCheck = true;
                }
                if (_pc.hasSkillEffect(8864)) {
                    isCheck2 = true;
                    break;
                }
                break;
            }
        }
        int hp = 0;
        if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor2() != 0
                && _pc.getlogpcpower_SkillFor2() >= 1) {
            hp += _pc.getlogpcpower_SkillFor2() * 15;
        }
        if (isCheck2) {
            L1DamagePoison.doInfection(_pc, target, 1000, 50);
        }
        if (isCheck) {
            int chance = L1AttackMode._random.nextInt(100) + 1;
            if (chance <= 10) {
                L1DamagePoison.doInfection(_pc, target, 3000, 5 + hp);
            }
        }
    }

    @Override
    public void action() {
        try {
            if (_pc == null) {
                return;
            }
            if (_target == null) {
                return;
            }
            _pc.setHeading(_pc.targetDirection(_targetX, _targetY));
            if (_weaponRange == -1) {
                actionX1();
            } else {
                actionX2();
            }
        } catch (Exception e) {
            L1AttackPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void actionX2() {
        try {
            if (_isHit) {
                int attackgfx = 0;
                switch (_weaponType2) {
                    case 1: {
                        attackgfx = 13411;
                        break;
                    }
                    case 2: {
                        attackgfx = 13412;
                        break;
                    }
                    case 3: {
                        attackgfx = 13410;
                        break;
                    }
                    case 5:
                    case 14: {
                        attackgfx = 13402;
                        break;
                    }
                    case 6:
                    case 15: {
                        attackgfx = 13415;
                        break;
                    }
                    case 7:
                    case 16: {
                        attackgfx = 13414;
                        break;
                    }
                    case 18: {
                        attackgfx = 13413;
                        break;
                    }
                    case 11: {
                        attackgfx = 13416;
                        break;
                    }
                    case 12: {
                        attackgfx = 13417;
                        break;
                    }
                    case 17: {
                        attackgfx = 13396;
                        break;
                    }
                }
                if (_pc.getTempCharGfx() >= 13715 && _pc.getTempCharGfx() <= 13745) {
                    if (attackgfx > 0 && attackgfx != 13396) {
                        if (_pc.getTempCharGfx() != 13731 && _pc.getTempCharGfx() != 13733) {
                            if (L1AttackMode._random.nextInt(100) < 20) {
                                L1Location loc = _target.getLocation();
                                L1NpcInstance dummy = L1SpawnUtil.spawnS(loc, 86132, _pc.get_showId(), 1,
                                        _pc.getHeading());
                                dummy.broadcastPacketAll(new S_NPCPack(dummy));
                                dummy.broadcastPacketAll(new S_SkillSound(dummy.getId(), attackgfx));
                            }
                        } else if (_attackType == 2 || _attackType == 4) {
                            L1Location loc = _target.getLocation();
                            L1NpcInstance dummy = L1SpawnUtil.spawnS(loc, 86132, _pc.get_showId(), 1,
                                    _pc.getHeading());
                            dummy.broadcastPacketAll(new S_NPCPack(dummy));
                            dummy.broadcastPacketAll(new S_SkillSound(dummy.getId(), attackgfx));
                        }
                    }
                    _pc.sendPacketsAll(new S_AttackPacketPc(_pc, _target, 0, _damage));
                } else {
                    _pc.sendPacketsAll(
                            new S_AttackPacketPc(_pc, _target, _attackType, _damage));
                }
            } else if (_targetId > 0) {
                _pc.sendPacketsAll(new S_AttackPacketPc(_pc, _target));
            } else {
                _pc.sendPacketsAll(new S_AttackPacketPc(_pc));
            }
        } catch (Exception e) {
            L1AttackPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void Imperius_Tshirt_Effect() {
        int rnd = _pc.get_Imperius_Tshirt_rnd();
        int min = _pc.get_Tshirt_drainingHP_min();
        int max = _pc.get_Tshirt_drainingHP_max();
        if (rnd <= 0) {
            return;
        }
        int value = 0;
        switch (_calcType) {
            case 1: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    value = L1AttackMode._random.nextInt(max - min + 1) + min;
                    _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), 11769));
                    _targetPc.receiveDamage(_pc, value, false, true);
                    short newHp = (short) (_pc.getCurrentHp() + value);
                    _pc.setCurrentHp(newHp);
                    break;
                }
                break;
            }
            case 2: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    value = L1AttackMode._random.nextInt(max - min + 1) + min;
                    _targetNpc.broadcastPacketAll(new S_SkillSound(_targetNpc.getId(), 11769));
                    _targetNpc.receiveDamage(_pc, value);
                    short newHp = (short) (_pc.getCurrentHp() + value);
                    _pc.setCurrentHp(newHp);
                    break;
                }
                break;
            }
        }
    }

    private void actionX1() {
        try {
            if (_pc.getpolyarrow() > 0) {
                _arrowGfxid = _pc.getpolyarrow();
            }
            if (_pc.getTempCharGfx() >= 13715 && _pc.getTempCharGfx() <= 13745) {
                _arrowGfxid = 11762;
                _stingGfxid = 11762;
            }
            if (_isHit) {
                switch (_weaponType) {
                    case 20: {
                        if (_arrow != null) {
                            _pc.sendPacketsAll(new S_UseArrowSkill(_pc, _targetId, _arrowGfxid,
                                    _targetX, _targetY, _damage, 1));
                            _pc.getInventory().removeItem(_arrow, 1L);
                            break;
                        }
                        if (_weaponId == 190) {
                            _pc.sendPacketsAll(new S_UseArrowSkill(_pc, _targetId, 2349, _targetX,
                                    _targetY, _damage, 1));
                            break;
                        }
                        break;
                    }
                    case 62: {
                        if (_sting != null) {
                            _pc.sendPacketsAll(new S_UseArrowSkill(_pc, _targetId, _stingGfxid,
                                    _targetX, _targetY, _damage, 1));
                            _pc.getInventory().removeItem(_sting, 1L);
                            break;
                        }
                        break;
                    }
                }
            } else if (_targetId > 0) {
                switch (_weaponType) {
                    case 20: {
                        if (_arrow != null) {
                            _pc.sendPacketsAll(
                                    new S_UseArrowSkill(_pc, _arrowGfxid, _targetX, _targetY, 1));
                            _pc.getInventory().removeItem(_arrow, 1L);
                            break;
                        }
                        if (_weaponId == 190) {
                            _pc.sendPacketsAll(new S_UseArrowSkill(_pc, _targetId, 2349, _targetX,
                                    _targetY, _damage, 1));
                            break;
                        }
                        _pc.sendPacketsAll(new S_UseArrowSkill(_pc));
                        break;
                    }
                    case 62: {
                        if (_sting != null) {
                            _pc.sendPacketsAll(
                                    new S_UseArrowSkill(_pc, _stingGfxid, _targetX, _targetY, 1));
                            _pc.getInventory().removeItem(_sting, 1L);
                            break;
                        }
                        _pc.sendPacketsAll(new S_UseArrowSkill(_pc));
                        break;
                    }
                }
            } else {
                _pc.sendPacketsAll(new S_UseArrowSkill(_pc));
            }
        } catch (Exception e) {
            L1AttackPc._log.error(e.getLocalizedMessage(), e);
        }
    }

    @Override
    public void commit() {
        if (_isHit) {
            if (_pc.dice_hp() != 0 && L1AttackMode._random.nextInt(100) + 1 <= _pc.dice_hp()) {
                _drainHp = _pc.sucking_hp();
            }
            if (_pc.dice_mp() != 0 && L1AttackMode._random.nextInt(100) + 1 <= _pc.dice_mp()) {
                _drainMana = _pc.sucking_mp();
            }
            if (_pc.has_powerid(6610)) {
                final int rad = 3;
                final int time = 5;
                if (L1AttackMode._random.nextInt(100) < rad && !_pc.hasSkillEffect(STATUS_BRAVE3)) {
                    _pc.setSkillEffect(STATUS_BRAVE3, time * 1000);
                    _pc.sendPacketsAll(new S_Liquor(_pc.getId(), 8));
                    _pc.sendPacketsAll(new S_SkillSound(_pc.getId(), 11817));
                }
            }
            int i = (int) (_damage / Math.pow(10.0, 0.0) % 10.0) + ConfigOther.Attack_1;
            int k = (int) (_damage / Math.pow(10.0, 1.0) % 10.0) + ConfigOther.Attack_2;
            int h = (int) (_damage / Math.pow(10.0, 2.0) % 10.0) + ConfigOther.Attack_3;
            int s = (int) (_damage / Math.pow(10.0, 3.0) % 10.0) + ConfigOther.Attack_4;
            int m = (int) (_damage / Math.pow(10.0, 4.0) % 10.0) + ConfigOther.Attack_5;
            switch (_calcType) {
                case 1: {
                    if (_pc.lift() != 0) {
                        int counter = L1AttackMode._random.nextInt(_pc.lift()) + 1;
                        StringBuffer sbr = new StringBuffer();
                        Iterator<L1ItemInstance> iterator2 = _targetPc.getInventory().getItems().iterator();
                        while (iterator2.hasNext()) {
                            L1ItemInstance item = iterator2.next();
                            if (item.getItem().getType2() == 2) {
                                if (!item.isEquipped()) {
                                    continue;
                                }
                                _targetPc.getInventory().setEquipped(item, false, false, false);
                                sbr.append("[").append(item.getNumberedName(1L)).append("]");
                                if (--counter <= 0) {
                                    break;
                                }
                                continue;
                            }
                        }
                        if (sbr.length() > 0) {
                            _targetPc.sendPackets(new S_SystemMessage("以下裝備被對方卸除:" + sbr.toString()));
                            _pc.sendPackets(new S_SystemMessage("成功卸除對方以下裝備:" + sbr.toString()));
                        }
                    }
                    commitPc();
                    if (!_pc.hasSkillEffect(1688)) {
                        break;
                    }
                    if (_damage > 0 && _damage < 10) {
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), i));
                        break;
                    }
                    if (_damage >= 10 && _damage < 100) {
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), i));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), k));
                        break;
                    }
                    if (_damage >= 100 && _damage < 1000) {
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), i));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), k));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), h));
                        break;
                    }
                    if (_damage >= 1000 && _damage < 10000) {
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), i));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), k));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), h));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), s));
                        break;
                    }
                    if (_damage >= 10000) {
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), i));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), k));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), h));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), s));
                        _pc.sendPackets(new S_SkillSound(_targetPc.getId(), m));
                        break;
                    }
                    break;
                }
                case 2: {
                    if (_pc.hasSkillEffect(1688)) {
                        if (_damage > 0 && _damage < 10) {
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), i));
                        } else if (_damage >= 10 && _damage < 100) {
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), i));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), k));
                        } else if (_damage >= 100 && _damage < 1000) {
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), i));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), k));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), h));
                        } else if (_damage >= 1000 && _damage < 10000) {
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), i));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), k));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), h));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), s));
                        } else if (_damage >= 10000) {
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), i));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), k));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), h));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), s));
                            _pc.sendPackets(new S_SkillSound(_targetNpc.getId(), m));
                        }
                    }
                    commitNpc();
                    break;
                }
            }
        }
        if (!ConfigAlt.ALT_ATKMSG) {
            return;
        }
        switch (_calcType) {
            case 1: {
                if (!_pc.isGm() && !_targetPc.isGm()) {
                    return;
                }
                break;
            }
            case 2: {
                if (!_pc.isGm()) {
                    return;
                }
                break;
            }
        }
        String srcatk = _pc.getName();
        String tgatk = "";
        String hitinfo = "";
        String dmginfo = "";
        String x = "";
        switch (_calcType) {
            case 1: {
                tgatk = _targetPc.getName();
                hitinfo = "命中機率:" + _hitRate + "% 剩餘hp:" + _targetPc.getCurrentHp();
                dmginfo = (_isHit ? ("傷害:" + _damage + " ") : "未命中 ");
                x = String.valueOf(srcatk) + ">" + tgatk + " " + dmginfo + hitinfo;
                if (_pc.isGm()) {
                    _pc.sendPackets(new S_ServerMessage(166, "對PC送出攻擊: " + x));
                }
                if (_targetPc.isGm()) {
                    _targetPc.sendPackets(new S_ServerMessage(166, "受到PC攻擊: " + x));
                    break;
                }
                break;
            }
            case 2: {
                tgatk = _targetNpc.getName();
                hitinfo = "命中機率:" + hit_rnd + "% 剩餘hp:" + _targetNpc.getCurrentHp();
                dmginfo = (_isHit ? ("傷害:" + _damage + " ") : "未命中 ");
                x = String.valueOf(srcatk) + ">" + tgatk + " " + dmginfo + hitinfo;
                if (_pc.isGm()) {
                    _pc.sendPackets(new S_ServerMessage(166, "對NPC送出攻擊: " + x));
                    break;
                }
                break;
            }
        }
    }

    private void commitPc() {
        if (_drainMana > 0 && _targetPc.getCurrentMp() > 0) {
            if (_drainMana > _targetPc.getCurrentMp()) {
                _drainMana = _targetPc.getCurrentMp();
            }
            _targetPc.receiveManaDamage(_pc, _drainMana);
            int newMp = _pc.getCurrentMp() + _drainMana;
            _pc.setCurrentMp(newMp);
        }
        if (_drainHp > 0) {
            short newHp = (short) (_pc.getCurrentHp() + _drainHp);
            _pc.setCurrentHp(newHp);
        }
        _targetPc.receiveDamage(_pc, _damage, false, false);
    }

    private void commitNpc() {
        if (_drainMana > 0) {
            int drainValue = _targetNpc.drainMana(_drainMana);
            if (drainValue > 0) {
                _targetNpc.ReceiveManaDamage(_pc, drainValue);
                int newMp = _pc.getCurrentMp() + drainValue;
                _pc.setCurrentMp(newMp);
            }
        }
        if (_drainHp > 0) {
            short newHp = (short) (_pc.getCurrentHp() + _drainHp);
            _pc.setCurrentHp(newHp);
        }
        damageNpcWeaponDurability();
        // 先執行正常的傷害處理
        _targetNpc.receiveDamage(_pc, _damage);
        // 移除傷害追蹤工具累積傷害
    }

    @Override
    public boolean isShortDistance() {
        boolean isShortDistance = true;
        if (_weaponType == 20 || _weaponType == 62) {
            isShortDistance = false;
        }
        return isShortDistance;
    }

    private void MoonAmuletEffect() {
        int rnd = _pc.get_MoonAmulet_rnd();
        int dmgmin = _pc.get_MoonAmulet_dmg_min();
        int dmgmax = _pc.get_MoonAmulet_dmg_max();
        int gfxid = _pc.get_MoonAmulet_gfxid();
        if (rnd <= 0) {
            return;
        }
        int damage = 0;
        switch (_calcType) {
            case 1: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    damage = L1AttackMode._random.nextInt(dmgmax - dmgmin + 1) + dmgmin;
                    if (_targetPc.hasSkillEffect(68)) {
                        damage = (int) (damage / ConfigOther.IMMUNE_TO_HARM);
                    }
                    _targetPc.sendPacketsAll(new S_SkillSound(_targetPc.getId(), gfxid));
                    _targetPc.receiveDamage(_pc, damage, false, true);
                    break;
                }
                break;
            }
            case 2: {
                if (L1AttackMode._random.nextInt(1000) < rnd) {
                    damage = L1AttackMode._random.nextInt(dmgmax - dmgmin + 1) + dmgmin;
                    if (_targetNpc.hasSkillEffect(68)) {
                        damage = (int) (damage / ConfigOther.IMMUNE_TO_HARM);
                    }
                    _targetNpc.broadcastPacketAll(new S_SkillSound(_targetNpc.getId(), gfxid));
                    _targetNpc.receiveDamage(_pc, damage);
                    break;
                }
                break;
            }
        }
    }

    @Override
    public void commitCounterBarrier() {
        int damage = calcCounterBarrierDamage();
        if (damage == 0) {
            return;
        }
        if (_pc.getId() == _target.getId()) {
            return;
        }
        if (_pc.hasSkillEffect(68)) {
            damage = (int) (damage / ConfigOther.IMMUNE_TO_HARM);
        }
        _pc.sendPacketsAll(new S_DoActionGFX(_pc.getId(), 2));
        _pc.sendPacketsAll(new S_SkillSound(_target.getId(), 10710));
        _pc.receiveDamage(_target, damage, false, true);
    }

    private void damageNpcWeaponDurability() {
        if (_calcType != 2) {
            return;
        }
        if (!_targetNpc.getNpcTemplate().is_hard()) {
            return;
        }
        if (_weaponType == 0) {
            return;
        }
        if (_weapon.getItem().get_canbedmg() == 0) {
            return;
        }
        if (_pc.hasSkillEffect(175)) {
            return;
        }
        if (_pc.getInventory().checkSkillTypebless(0) && _weaponBless == 1) {
            return;
        }
        int random = L1AttackMode._random.nextInt(100) + 1;
        switch (_weaponBless) {
            case 0: {
                if (random < 3) {
                    _pc.sendPackets(new S_ServerMessage(268, _weapon.getLogName()));
                    _pc.getInventory().receiveDamage(_weapon);
                    _pc.sendPacketsX8(new S_SkillSound(_pc.getId(), 10712));
                    break;
                }
                break;
            }
            case 1:
            case 2: {
                if (random < 10) {
                    _pc.sendPackets(new S_ServerMessage(268, _weapon.getLogName()));
                    _pc.getInventory().receiveDamage(_weapon);
                    _pc.sendPacketsX8(new S_SkillSound(_pc.getId(), 10712));
                    break;
                }
                break;
            }
        }
    }

    private void damagePcWeaponDurability() {
        if (_calcType != 1) {
            return;
        }
        if (_weaponType == 0) {
            return;
        }
        if (_weaponType == 20) {
            return;
        }
        if (_weaponType == 62) {
            return;
        }
        if (!_targetPc.hasSkillEffect(89)) {
            return;
        }
        if (_pc.hasSkillEffect(175)) {
            return;
        }
        if (L1AttackMode._random.nextInt(100) + 1 <= 10) {
            _pc.sendPackets(new S_ServerMessage(268, _weapon.getLogName()));
            _pc.getInventory().receiveDamage(_weapon);
            _pc.sendPacketsX8(new S_SkillSound(_pc.getId(), 10712));
        }
    }

    @Override
    public void calcStaffOfMana() {
        if (this._weapon != null && this._weapon.getItem().getItemId() == 40006) {
            if (this._pc.getCurrentMp() >= 10) {
                this._pc.setCurrentMp(this._pc.getCurrentMp() - 10);
            } else {
                this._pc.setCurrentMp(0);
            }
        }
    }
}
