package com.lineage.server.model.Instance;

import com.lineage.server.model.L1AttackMode;
import com.lineage.server.model.L1Character;
import com.lineage.server.serverpackets.S_DoActionGFX;
import com.lineage.server.utils.CalcExp;
import java.util.ArrayList;
import com.lineage.config.ConfigOther;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.model.L1AttackPc;
import com.lineage.server.templates.L1Npc;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

/**
 * 稻草人 NPC 實例
 *
 * 主要功能：
 * - 作為測試攻擊傷害與經驗獲得的假人目標
 * - 玩家攻擊時可獲知傷害結果
 * - 可依照等級設定給予經驗值
 *
 * <p>
 * 注意：本類型 NPC 不會主動互動或回擊。
 * </p>
 */
public class L1ScarecrowInstance extends L1NpcInstance {

	private static final long serialVersionUID = 1L;

	// 日誌工具
	private static final Log _log = LogFactory.getLog(L1ScarecrowInstance.class);

	/**
	 * 建構子
	 *
	 * @param template 稻草人 NPC 樣板資料
	 */
	public L1ScarecrowInstance(final L1Npc template) {
		super(template);
	}

	/**
	 * 玩家攻擊稻草人時的觸發方法。
	 * 判斷是否命中、計算傷害，顯示訊息，並依據設定發放經驗值。
	 *
	 * @param player 發起攻擊的玩家物件
	 */
	@Override
	public void onAction(final L1PcInstance player) {
		try {
			// 建立玩家對稻草人的攻擊物件
			final L1AttackMode attack = new L1AttackPc(player, this);

			// 判斷是否命中
			if (attack.calcHit()) {
				final int damage = attack.calcDamage();

				// 若是指定ID範圍的稻草人，回報傷害訊息給玩家
				if (this.getNpcId() >= 45001 && this.getNpcId() <= 45003) {
					player.sendPackets(new S_ServerMessage(166, "傷害輸出: " + damage));
				}

				// 若玩家等級低於設定值，則給予經驗值（只給新手用於練習）
				if (player.getLevel() < ConfigOther.Scarecrowlevel) {
					final ArrayList<L1Character> targetList = new ArrayList<>();
					targetList.add(player); // player 型別是 L1PcInstance，沒問題
					final ArrayList<Integer> hateList = new ArrayList<>();
					hateList.add(1);
					int exp = (int) this.getExp();
					CalcExp.calcExp(player, this.getId(), targetList, hateList, exp);
				}

				// 播放受擊動畫（動作GFX: 2）
				this.broadcastPacketAll(new S_DoActionGFX(this.getId(), 2));

			} else if (this.getNpcId() >= 45001 && this.getNpcId() <= 45003) {
				// 若未命中，通知玩家
				player.sendPackets(new S_ServerMessage(166, "未命中"));
			}

			// 播放攻擊動畫/音效等
			attack.action();

		} catch (Exception e) {
			_log.error("L1ScarecrowInstance 執行 onAction 發生例外: " + e.getLocalizedMessage(), e);
		}
	}

	/**
	 * 稻草人不回應任何對話
	 *
	 * @param l1pcinstance 發起對話的玩家
	 */
	@Override
	public void onTalkAction(final L1PcInstance l1pcinstance) {
		// 稻草人為靜態目標，不回應對話
	}

	/**
	 * 稻草人無須執行結束時的特殊行為
	 */
	public void onFinalAction() {
		// 無需實作
	}

	/**
	 * 稻草人無須執行額外結束動作
	 */
	public void doFinalAction() {
		// 無需實作
	}
}