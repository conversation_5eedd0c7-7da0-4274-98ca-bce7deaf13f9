package com.lineage.server.model.Instance;

import com.lineage.config.ConfigAlt;
import com.lineage.config.ConfigClan;
import com.lineage.config.ConfigOther;
import com.lineage.config.ConfigRate;
import com.lineage.data.event.*;
import com.lineage.server.Instance.character.CharacterPunishInstance;
import com.lineage.server.datatables.*;
import com.lineage.server.datatables.lock.CharItemsReading;
import com.lineage.server.datatables.sql.ClanTable;
import com.lineage.server.datatables.sql.L1MonTable;
import com.lineage.server.model.*;
import com.lineage.server.model.drop.DropShare;
import com.lineage.server.model.drop.DropShareExecutor;
import com.lineage.server.model.drop.SetDrop;
import com.lineage.server.model.drop.SetDropExecutor;
import com.lineage.server.model.skill.L1SkillMode;
import com.lineage.server.model.skill.skillmode.SkillMode;
import com.lineage.server.serverpackets.*;
import com.lineage.server.templates.L1Mon;
import com.lineage.server.templates.L1Npc;
import com.lineage.server.templates.L1QuestUser;
import com.lineage.server.thread.GeneralThreadPool;
import com.lineage.server.types.Point;
import com.lineage.server.utils.CalcExp;
import com.lineage.server.utils.CheckUtil;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.world.World;
import com.lineage.server.world.WorldClan;
import com.lineage.server.world.WorldMob;
import com.lineage.server.world.WorldQuest;
import com.lineage.william.Npc_Dead_span;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.utils.DamageTracker;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ScheduledFuture;

/**
 * 怪物實體主類別
 * 主要流程註解：
 * 1. 建構子初始化怪物狀態
 * 2. onItemUse：怪物自動補血/變身
 * 3. onPerceive：玩家看到怪物時觸發AI與廣播
 * 4. searchTarget：怪物自動尋找攻擊目標
 * 5. onAction/receiveDamage：攻擊觸發與受傷處理
 * 6. distributeExpDropKarma/distributeDrop：經驗與掉寶分配
 * 7. BOSS特殊行為（如死亡開門、廣播、特殊掉寶）
 */
public class L1MonsterInstance extends L1NpcInstance {
    private static final long serialVersionUID = 1L;
    private static final Log _log;
    private static final Random _random;

    static {
        _log = LogFactory.getLog(L1MonsterInstance.class);
        _random = new Random();
    }

    private boolean _storeDroped;
    private int _ubSealCount;
    private int _ubId;
    private long _lasthprtime;
    private long _lastmprtime;

    /**
     * 建構子：初始化怪物狀態
     */
    public L1MonsterInstance(L1Npc template) {
        super(template);
        _ubSealCount = 0;
        _ubId = 0;
        _lasthprtime = 0L;
        _lastmprtime = 0L;
        _storeDroped = false;
    }

    /**
     * 怪物死亡時開啟特定地圖的門（BOSS特殊行為）
     */
    private static void openDoorWhenNpcDied(L1NpcInstance npc) {
        int[] npcId = {46143, 46144, 46145, 46146, 46147, 46148, 46149, 46150, 46151, 46152};
        int[] doorId = {5001, 5002, 5003, 5004, 5005, 5006, 5007, 5008, 5009, 5010};
        int i = 0;
        while (i < npcId.length) {
            if (npc.getNpcTemplate().get_npcId() == npcId[i]) {
                openDoorInCrystalCave(doorId[i]);
            }
            ++i;
        }
    }

    private static void openDoorInCrystalCave(int doorId) {
        for (L1Object object : World.get().getObject()) {
            if (object instanceof L1DoorInstance) {
                L1DoorInstance door = (L1DoorInstance) object;
                if (door.getDoorId() != doorId) {
                    continue;
                }
                door.open();
            }
        }
    }

    public static void bossbox(String info) {
        try {
            BufferedWriter out = new BufferedWriter(new FileWriter("玩家紀錄/尾刀王紀錄.txt", true));
            out.write(String.valueOf(info) + "\r\n");
            out.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 怪物自動補血/變身（如分身怪）
     */
    @Override
    public void onItemUse() {
        if (!isActived() && _target != null && getNpcTemplate().is_doppel()
                && _target instanceof L1PcInstance) {
            L1PcInstance targetPc = (L1PcInstance) _target;
            setName(_target.getName());
            setNameId(_target.getName());
            setTitle(_target.getTitle());
            setTempLawful(_target.getLawful());
            setTempCharGfx(targetPc.getClassId());
            setGfxId(targetPc.getClassId());
            setPassispeed(640);
            setAtkspeed(900);
            for (L1PcInstance pc : World.get().getRecognizePlayer(this)) {
                pc.sendPackets(new S_RemoveObject(this));
                pc.removeKnownObject(this);
                pc.updateObject();
            }
        }
        if (getCurrentHp() * 100 / getMaxHp() < 40) {
            useItem(1, 50);
        }
    }

    /**
     * 玩家看到怪物時觸發AI與廣播
     */
    @Override
    public void onPerceive(L1PcInstance perceivedFrom) {
        try {
            if (perceivedFrom.get_showId() != get_showId()) {
                return;
            }
            perceivedFrom.addKnownObject(this);
            if (getCurrentHp() > 0) {
                perceivedFrom.sendPackets(new S_NPCPack(this, perceivedFrom));
                onNpcAI();
                if (getBraveSpeed() == 1) {
                    perceivedFrom.sendPackets(new S_SkillBrave(getId(), 1, 600000));
                }
            } else {
                perceivedFrom.sendPackets(new S_NPCPack(this));
            }
        } catch (Exception e) {
            L1MonsterInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    /**
     * 怪物自動尋找攻擊目標
     */
    @Override
    public void searchTarget() {
        L1NpcInstance targetNpc = searchTargetnpc(this);
        L1PcInstance targetPlayer = searchTarget(this);
        if (targetNpc != null) {
            _hateList.add(targetNpc, 0);
            _target = targetNpc;
        } else if (targetPlayer != null) {
            _hateList.add(targetPlayer, 0);
            _target = targetPlayer;
        } else {
            ISASCAPE = false;
        }
    }

    private L1MonsterInstance searchTargetnpc(L1MonsterInstance npc) {
        L1MonsterInstance targetNpc = null;
        if (getMapId() == 93) {
            Point npcpt = getLocation();
            for (L1Object object : World.get().getVisibleObjects(this, -1)) {
                if (object instanceof L1MonsterInstance) {
                    L1MonsterInstance tgnpc = (L1MonsterInstance) object;
                    if (getMapId() != tgnpc.getMapId()) {
                        continue;
                    }
                    if (!npcpt.isInScreen(tgnpc.getLocation())) {
                        continue;
                    }
                    if (tgnpc == null || tgnpc.getCurrentHp() <= 0 || tgnpc.isDead() || getId() == tgnpc.getId()
                            || get_showId() != tgnpc.get_showId()) {
                        continue;
                    }
                    targetNpc = tgnpc;
                }
            }
        }
        return targetNpc;
    }

    private L1PcInstance searchTarget(L1MonsterInstance npc) {
        L1PcInstance targetPlayer = null;
        for (L1PcInstance pc : World.get().getVisiblePlayer(npc)) {
            try {
                Thread.sleep(10L);
            } catch (InterruptedException e) {
                L1MonsterInstance._log.error(e.getLocalizedMessage(), e);
            }
            if (pc.getCurrentHp() <= 0) {
                continue;
            }
            if (pc.isDead()) {
                continue;
            }
            if (pc.isGhost()) {
                continue;
            }
            if (pc.isGm()) {
                continue;
            }
            if (npc.getNpcTemplate().is_Atkpc()) {
                continue;
            }
            if (npc.get_showId() != pc.get_showId()) {
                continue;
            }
            if (npc.getMapId() == 410 && pc.getTempCharGfx() == 4261) {
                continue;
            }
            if (npc.getNpcTemplate().get_family() == NpcTable.ORC && pc.getClan() != null
                    && pc.getClan().getCastleId() == 2) {
                continue;
            }
            L1PcInstance tgpc1 = npc.attackPc1(pc);
            if (tgpc1 != null) {
                targetPlayer = tgpc1;
                return targetPlayer;
            }
            L1PcInstance tgpc2 = npc.attackPc2(pc);
            if (tgpc2 != null) {
                targetPlayer = tgpc2;
                return targetPlayer;
            }
            if (npc.getNpcTemplate().getKarma() < 0 && pc.getKarmaLevel() >= 1) {
                continue;
            }
            if (npc.getNpcTemplate().getKarma() > 0 && pc.getKarmaLevel() <= -1) {
                continue;
            }
            if (pc.getTempCharGfx() == 6034 && npc.getNpcTemplate().getKarma() < 0) {
                continue;
            }
            if (pc.getTempCharGfx() == 6035) {
                if (npc.getNpcTemplate().getKarma() > 0) {
                    continue;
                }
                if (npc.getNpcTemplate().get_npcId() == 46070) {
                    continue;
                }
                if (npc.getNpcTemplate().get_npcId() == 46072) {
                    continue;
                }
            }
            L1PcInstance tgpc3 = npc.targetPlayer1000(pc);
            if (tgpc3 != null) {
                targetPlayer = tgpc3;
                return targetPlayer;
            }
            if (pc.isInvisble() && !getNpcTemplate().is_agrocoi()) {
                continue;
            }
            if (pc.hasSkillEffect(67)) {
                if (getNpcTemplate().is_agrososc()) {
                    targetPlayer = pc;
                    break;
                }
            } else if (getNpcTemplate().is_agro()) {
                targetPlayer = pc;
                break;
            }
            if (npc.getNpcTemplate().is_agrogfxid1() >= 0 && pc.getGfxId() == npc.getNpcTemplate().is_agrogfxid1()) {
                targetPlayer = pc;
                return targetPlayer;
            }
            if (npc.getNpcTemplate().is_agrogfxid2() >= 0 && pc.getGfxId() == npc.getNpcTemplate().is_agrogfxid2()) {
                targetPlayer = pc;
                return targetPlayer;
            }
        }
        return targetPlayer;
    }

    private L1PcInstance attackPc2(L1PcInstance pc) {
        if (getNpcId() == 45600) {
            if (pc.isCrown() && pc.getTempCharGfx() == pc.getClassId()) {
                return pc;
            }
            if (pc.isDarkelf()) {
                return pc;
            }
        }
        return null;
    }

    private L1PcInstance attackPc1(L1PcInstance pc) {
        int mapId = getMapId();
        boolean isCheck = false;
        if (mapId == 88) {
            isCheck = true;
        }
        if (mapId == 98) {
            isCheck = true;
        }
        if (mapId == 92) {
            isCheck = true;
        }
        if (mapId == 91) {
            isCheck = true;
        }
        if (mapId == 95) {
            isCheck = true;
        }
        if (isCheck && (!pc.isInvisble() || getNpcTemplate().is_agrocoi())) {
            return pc;
        }
        return null;
    }

    private L1PcInstance targetPlayer1000(L1PcInstance pc) {
        if (ConfigOther.KILLRED && !getNpcTemplate().is_agro() && !getNpcTemplate().is_agrososc()
                && getNpcTemplate().is_agrogfxid1() < 0 && getNpcTemplate().is_agrogfxid2() < 0
                && pc.getLawful() < -1000) {
            return pc;
        }
        return null;
    }

    @Override
    public void setLink(L1Character cha) {
        if (get_showId() != cha.get_showId()) {
            return;
        }
        if (_hateList.isEmpty()) {
            _hateList.add(cha, 0);
            checkTarget();
        }
    }

    @Override
    public void onNpcAI() {
        if (isAiRunning()) {
            return;
        }
        if (!_storeDroped) {
            SetDropExecutor setdrop = new SetDrop();
            setdrop.setDrop(this, getInventory());
            getInventory().shuffle();
            _storeDroped = true;
        }
        setActived(false);
        startAI();
    }

    @Override
    public void onTalkAction(L1PcInstance pc) {
        setHeading(targetDirection(pc.getX(), pc.getY()));
        broadcastPacketAll(new S_ChangeHeading(this));
        set_stop_time(10);
        setRest(true);
    }

    /**
     * 玩家攻擊怪物時的觸發（攻擊主流程）
     */
    @Override
    public void onAction(L1PcInstance pc) {
        if (ATTACK != null) {
            ATTACK.attack(pc, this);
        }
        if (getCurrentHp() > 0 && !isDead()) {
            L1AttackMode attack = new L1AttackPc(pc, this);
            if (attack.calcHit()) {
                attack.calcDamage();
                attack.calcStaffOfMana();
            }
            attack.action();
            attack.commit();
        }
    }

    @Override
    public void ReceiveManaDamage(L1Character attacker, int mpDamage) {
        if (mpDamage > 0 && !isDead()) {
            setHate(attacker, mpDamage);
            onNpcAI();
            if (attacker instanceof L1PcInstance) {
                serchLink((L1PcInstance) attacker, getNpcTemplate().get_family());
            }
            int newMp = getCurrentMp() - mpDamage;
            if (newMp < 0) {
                newMp = 0;
            }
            setCurrentMp(newMp);
        }
    }

    public void receiveDamage(L1Character attacker, double damage, int attr) {
        int mrdef = getMr();
        int rnd = L1MonsterInstance._random.nextInt(100) + 1;
        if (mrdef >= rnd) {
            damage /= 2.0;
        }
        int resist = 0;
        switch (attr) {
            case 1: {
                resist = getEarth();
                break;
            }
            case 2: {
                resist = getFire();
                break;
            }
            case 4: {
                resist = getWater();
                break;
            }
            case 8: {
                resist = getWind();
                break;
            }
        }
        int resistFloor = (int) (0.16 * Math.abs(resist));
        if (resist >= 0) {
            resistFloor *= 1;
        } else {
            resistFloor *= -1;
        }
        double attrDeffence = resistFloor / 32.0;
        double coefficient = 1.0 - attrDeffence;
        damage *= coefficient;
        receiveDamage(attacker, (int) damage);
    }

    /**
     * 處理怪物受傷與死亡（主流程）
     */
    @Override
    public void receiveDamage(L1Character attacker, int damage) {
        ISASCAPE = false;
        if (getCurrentHp() > 0 && !isDead()) {
            if (getHiddenStatus() == 1 || getHiddenStatus() == 2) {
                return;
            }
            if (damage >= 0) {
                if (attacker instanceof L1EffectInstance) {
                    L1EffectInstance effect = (L1EffectInstance) attacker;
                    attacker = effect.getMaster();
                    if (attacker != null) {
                        setHate(attacker, damage);
                    }
                } else if (attacker instanceof L1IllusoryInstance) {
                    L1IllusoryInstance ill = (L1IllusoryInstance) attacker;
                    attacker = ill.getMaster();
                    if (attacker != null) {
                        setHate(attacker, damage);
                    }
                } else if (attacker instanceof L1MonsterInstance) {
                    switch (getNpcTemplate().get_npcId()) {
                        case 91290:
                        case 91294:
                        case 91295:
                        case 91296: {
                            setHate(attacker, damage);
                            damage = 0;
                            break;
                        }
                    }
                } else {
                    setHate(attacker, damage);
                }
            }
            if (damage > 0) {
                removeSkillEffect(66);
                removeSkillEffect(212);
            }
            onNpcAI();
            L1PcInstance atkpc = null;
            if (attacker instanceof L1PcInstance) {
                atkpc = (L1PcInstance) attacker;
                if (damage > 0) {
                    atkpc.setPetTarget(this);
                    switch (getNpcTemplate().get_npcId()) {
                        case 45681: // 林德拜爾
                        case 45682: // 安塔瑞斯
                        case 45683: // 法利昂
                        case 45684: // 巴拉卡斯
                            recall(atkpc);
                            break;
                        default:
                            //如果是boss敵人超過距離4就拉到身邊揍
                            if (getNpcTemplate().is_boss()) {
                                recall(atkpc);
                            }
                            break;
                    }
                }
                serchLink(atkpc, getNpcTemplate().get_family());
            }
            if (hasSkillEffect(219)) {
                damage = (int) (damage * 1.05);
            }
            int newHp = getCurrentHp() - damage;
            if (newHp <= 0 && !isDead()) {
                int transformId = getNpcTemplate().getTransformId();
                if (transformId == -1) {
                    setCurrentHpDirect(0);
                    setDead(true);
                    setStatus(8);
                    openDoorWhenNpcDied(this);
                    Death death = new Death(attacker);
                    GeneralThreadPool.get().execute(death);
                    if (Npe_DeadSpan.START) {
                        Npc_Dead_span.forresolvent(atkpc, this);
                    }
                    if (atkpc != null && !isResurrect() && ProtectorSet.CHANCE > 0
                            && L1MonsterInstance._random.nextInt(1000) < ProtectorSet.CHANCE
                            && CharItemsReading.get().checkItemId(ProtectorSet.ITEM_ID) < ProtectorSet.DROP_LIMIT) {
                        L1ItemInstance item = ItemTable.get().createItem(ProtectorSet.ITEM_ID);
                        atkpc.getInventory().storeItem(item);
                        World.get().broadcastPacketToAll(new S_BlueMessage(166, "\\f=有人獲得了守護者的靈魂"));
                    }
                    if (getNpcId() >= 71014 && getNpcId() <= 71016) {
                        GeneralThreadPool.get().execute(new deathDragonTimer1(this, getMapId()));
                    } else if (getNpcId() >= 71026 && getNpcId() <= 71028) {
                        GeneralThreadPool.get().execute(new deathDragonTimer2(this, getMapId()));
                    } else if (getNpcId() >= 97204 && getNpcId() <= 97209) {
                        GeneralThreadPool.get().execute(new deathDragonTimer3(this));
                    }
                } else {
                    transform(transformId);
                }
            }
            if (newHp > 0) {
                setCurrentHp(newHp);
                hide();
            }
            if (ConfigOther.HPBAR) {
                if (atkpc == null) {
                    if (attacker instanceof L1PetInstance) {
                        atkpc = (L1PcInstance) ((L1PetInstance) attacker).getMaster();
                    } else if (attacker instanceof L1SummonInstance) {
                        atkpc = (L1PcInstance) ((L1SummonInstance) attacker).getMaster();
                    }
                    if (atkpc != null) {
                        broadcastPacketHP(atkpc);
                    }
                } else {
                    broadcastPacketHP(atkpc);
                }
            }
        } else if (!isDead()) {
            setDead(true);
            setStatus(8);
            Death death2 = new Death(attacker);
            GeneralThreadPool.get().execute(death2);
        }
    }

    private void recall(L1PcInstance pc) {
        if (getMapId() != pc.getMapId()) {
            return;
        }
        if (getLocation().getTileLineDistance(pc.getLocation()) > 7) {
            int count = 0;
            while (count < 10) {
                L1Location newLoc = getLocation().randomLocation(3, 4, false);
                if (glanceCheck(newLoc.getX(), newLoc.getY())) {
                    L1Teleport.teleport(pc, newLoc.getX(), newLoc.getY(), getMapId(), 5, true);
                    break;
                }
                ++count;
            }
        }
    }

    @Override
    public void setCurrentHp(int i) {
        int currentHp = Math.min(i, getMaxHp());
        if (getCurrentHp() == currentHp) {
            return;
        }
        setCurrentHpDirect(currentHp);
    }

    @Override
    public void setCurrentMp(int i) {
        int currentMp = Math.min(i, getMaxMp());
        if (getCurrentMp() == currentMp) {
            return;
        }
        setCurrentMpDirect(currentMp);
    }

    /**
     * 經驗與掉寶分配（主流程）
     */
    private void distributeExpDropKarma(L1Character lastAttacker) {
        if (lastAttacker == null) {
            return;
        }
        L1PcInstance pc = null;
        if (DEATH != null) {
            pc = DEATH.death(lastAttacker, this);
        } else {
            pc = CheckUtil.checkAtkPc(lastAttacker);
        }
        if (pc != null) {
            ArrayList<L1Character> targetList = _hateList.toTargetArrayList();
            ArrayList<Integer> hateList = _hateList.toHateArrayList();
            long exp = getExp();
            CalcExp.calcExp(pc, getId(), targetList, hateList, exp);
            if (pc.get_c_power() != null && pc.get_c_power().get_c1_type() != 0) {
                int score = NpcScoreTable.get().get_score(getNpcId());
                if (score > 0 && !isResurrect()) {
                    if (pc.getdouble_score() != 0) {
                        score *= pc.getdouble_score();
                    }
                    pc.get_other().add_score(score);
                    pc.sendPackets(new S_ServerMessage("\\fR獲得陣營積分:" + score));
                    if (CampSet.CAMPSTART && pc.get_c_power() != null && pc.get_c_power().get_c1_type() != 0) {
                        int lv = C1_Name_Type_Table.get().getLv(pc.get_c_power().get_c1_type(),
                                pc.get_other().get_score());
                        if (lv != pc.get_c_power().get_power().get_c1_id()) {
                            pc.get_c_power().set_power(pc, false);
                            pc.sendPackets(
                                    new S_ServerMessage("\\fR階級變更:" + pc.get_c_power().get_power().get_c1_name_type()));
                            pc.sendPacketsAll(new S_ChangeName(pc, true));
                        }
                    }
                }
            }
            if (prestigtable.START) {
                int prestige = NpcPrestigeTable.get().get_score(getNpcId());
                if (prestige != 0) {
                    pc.addPrestige(prestige);
                    pc.sendPackets(new S_ServerMessage("狩獵獲得" + pr_type_name._1 + "增加:" + prestige, 11));
                }
            }
            if (ClanContribution.START && pc.getClanid() != 0) {
                int ClanContribution = NpcClanContribution.get().get_score(getNpcId());
                L1Clan clan = WorldClan.get().getClan(pc.getClanname());
                if (ClanContribution != 0 && pc.getQuest().get_step(8544) != 1) {
                    clan.setClanContribution(clan.getClanContribution() + ClanContribution);
                    ClanTable.getInstance().updateClan(clan);
                    pc.setPcContribution(pc.getPcContribution() + ClanContribution);
                    pc.setClanContribution(pc.getClanContribution() + ClanContribution);
                    pc.getInventory().storeItem(92164, ClanContribution);
                    pc.sendPackets(new S_ServerMessage("狩獵獲得血盟能量+:" + ClanContribution, 11));
                    pc.sendPackets(new S_SystemMessage("你給予目前的血盟貢獻度有【" + pc.getClanContribution() + "】點。"));
                    pc.sendPackets(new S_SystemMessage("你的血盟目前所獲得的貢獻度總合有【" + clan.getClanContribution() + "】點。"));
                    pc.sendPackets(new S_SystemMessage("獲得血盟貢獻幣:【" + ClanContribution + "】個。"));
                    if (pc.getPcContribution() >= ConfigClan.PcContribution) {
                        pc.sendPackets(new S_ServerMessage("\\fY每日血盟能量上限:" + ConfigClan.PcContribution));
                        pc.setPcContribution(ConfigClan.PcContribution);
                        pc.getQuest().set_step(8544, 1);
                        pc.setPcContribution(0);
                    }
                }
            }
            if (pc.getATK_ai()) {
                pc.setATK_ai(false);
                pc.setSkillEffect(7902, 2000);
            }
            if (QuestMobSet.START && !isResurrect() && !pc.isInParty()) {
                ServerQuestMobTable.get().checkQuestMob(pc, getNpcId());
            }
            int Npc_Mid = getNpcTemplate().get_npcId();
            if (Npc_Mid == L1Mon.CheckNpcMid(Npc_Mid)) {
                L1Mon mon = L1MonTable.get().getMon(Npc_Mid);
                if (mon.getX1() > 0) {
                    L1Teleport.teleport(pc, mon.getX1(), mon.getY1(), (short) mon.getMap1(), 5, true);
                }
                if (mon.getDorplostid() > 0) {
                    if (pc.getnpcdmg() > mon.getpcdmg()) {
                        int itemid = mon.getDorplostid();
                        L1ItemInstance item = ItemTable.get().createItem(itemid);
                        pc.getInventory().storeItem(item);
                        String a = getNpcTemplate().get_name();
                        String b = item.getName();
                        String c = pc.getName();
                        World.get().broadcastPacketToAll(new S_SystemMessage("\\fU恭喜：[" + c + "],神尾刀【" + a + "】"));
                        World.get().broadcastPacketToAll(new S_SystemMessage("\\fU獲得獎勵【" + b + "】"));
                        bossbox("IP(" + pc.getNetConnection().getIp() + ")" + "玩家" + ":【 " + pc.getName() + " 】 "
                                + "(尾刀)擊敗" + "【 + " + getName() + "】時間:"
                                + new Timestamp(System.currentTimeMillis()) + ")。");
                    } else {
                        pc.sendPackets(new S_ServerMessage("\\fU由於您打擊傷害,未達標準。"));
                        pc.sendPackets(new S_ServerMessage("\\fU將不發放擊殺[尾刀]寶相"));
                    }
                }
            }
            if (Npc_Mid == L1Mon.CheckNpcMid(Npc_Mid)) {
                L1Mon mon = L1MonTable.get().getMon(Npc_Mid);
                if (mon.getDorpid() > 0 || mon.getX() > 0) {
                    for (L1Object tgobj : World.get().getVisibleObjects(this, 15)) {
                        if (tgobj instanceof L1PcInstance) {
                            L1PcInstance tgpc = (L1PcInstance) tgobj;
                            if (mon.getX() > 0) {
                                L1Teleport.teleport(tgpc, mon.getX(), mon.getY(), (short) mon.getMap(), 5, true);
                            }
                            if (mon.getDorpid() <= 0) {
                                continue;
                            }
                            if (tgpc.getnpcdmg() > mon.getpcdmg()) {
                                int itemid2 = mon.getDorpid();
                                L1ItemInstance item2 = ItemTable.get().createItem(itemid2);
                                tgpc.getInventory().storeItem(item2);
                                String b2 = item2.getName();
                                tgpc.sendPackets(new S_ServerMessage("\\fT本次傷害達標準,獲得獎勵【" + b2 + "】"));
                                if (tgpc.getnpcdmg() <= 0.0) {
                                    continue;
                                }
                                tgpc.setnpcdmg(0.0);
                                tgpc.setnpciddmg(0);
                            } else {
                                tgpc.sendPackets(new S_ServerMessage("\\fU本次打擊傷害,未達標準。"));
                                tgpc.sendPackets(new S_ServerMessage("\\fU將不發放擊殺寶相"));
                                if (tgpc.getnpcdmg() <= 0.0) {
                                    continue;
                                }
                                tgpc.setnpcdmg(0.0);
                                tgpc.setnpciddmg(0);
                            }
                        }
                    }
                }
            }
            if (pc.getnpcdmg() > 0.0) {
                pc.setnpcdmg(0.0);
                pc.setnpciddmg(0);
            }
            if (getNpcTemplate().is_boss()) {
                World.get().broadcastPacketToAll(
                        new S_SystemMessage("\\fW恭喜玩家：" + pc.getName() + "\\fU☆擊敗了\\fY( " + getName() + " )"));
                World.get().broadcastPacketToAll(
                        new S_PacketBoxGree("\\fW恭喜玩家：" + pc.getName() + "\\fU☆擊敗了\\fY( " + getName() + " )"));
            }
            if (pc.get_other3().get_type1() > 0 && !isResurrect()) {
                ServerQuestMaPTable.check(pc);
            }
            if (isDead()) {
                if (pc.isActived() && pc.getguaji_count() > 0) {
                    pc.setguaji_count(pc.getguaji_count() - 1);
                    if (pc.getguaji_count() < 0) {
                        pc.setguaji_count(0);
                    }
                }
                distributeDrop();
                giveKarma(pc);
                if (pc.isActived() && pc.get_fwgj() == 0) {
                    pc.setSkillEffect(8853, 60000);
                }
            }
        }
    }

    /**
     * 掉寶分配（主流程）
     */
    private void distributeDrop() {
        ArrayList<L1Character> dropTargetList = _dropHateList.toTargetArrayList();
        ArrayList<Integer> dropHateList = _dropHateList.toHateArrayList();
        checkPunishList(dropTargetList);

        try {
            if (dropTargetList.isEmpty()) return;

            DropShareExecutor dropShareExecutor = new DropShare();
            dropShareExecutor.dropShare(this, dropTargetList, dropHateList);
            if (DroplistScreenSet.START) {
                DropScreenTable.get().runDropScreen(this, dropTargetList);
            }
        } catch (Exception e) {
            L1MonsterInstance._log.error(e.getLocalizedMessage(), e);
        }
    }

    private void checkPunishList(List<L1Character> characters) {
        for (int i = 0; i < characters.size(); i++) {
            if (CharacterPunishInstance.get().checkCharacter(characters.get(i).getId())) {
                characters.remove(i);
            }
        }
    }

    private void giveKarma(L1PcInstance pc) {
        int karma = getKarma();
        if (karma != 0) {
            int karmaSign = Integer.signum(karma);
            int pcKarmaLevel = pc.getKarmaLevel();
            int pcKarmaLevelSign = Integer.signum(pcKarmaLevel);
            if (pcKarmaLevelSign != 0 && karmaSign != pcKarmaLevelSign) {
                karma *= 5;
            }
            pc.addKarma((int) (karma * ConfigRate.RATE_KARMA));
        }
    }

    private void giveUbSeal() {
        if (getUbSealCount() != 0) {
            L1UltimateBattle ub = UBTable.getInstance().getUb(getUbId());
            if (ub != null) {
                L1PcInstance[] membersArray;
                int length = (membersArray = ub.getMembersArray()).length;
                int i = 0;
                while (i < length) {
                    L1PcInstance pc = membersArray[i];
                    if (pc != null && !pc.isDead() && !pc.isGhost()) {
                        L1ItemInstance item = pc.getInventory().storeItem(41402, getUbSealCount());
                        pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
                    }
                    ++i;
                }
            }
        }
    }

    public boolean is_storeDroped() {
        return _storeDroped;
    }

    public void set_storeDroped(boolean flag) {
        _storeDroped = flag;
    }

    public int getUbSealCount() {
        return _ubSealCount;
    }

    public void setUbSealCount(int i) {
        _ubSealCount = i;
    }

    public int getUbId() {
        return _ubId;
    }

    public void setUbId(int i) {
        _ubId = i;
    }

    private void hide() {
        int npcid = getNpcTemplate().get_npcId();
        switch (npcid) {
            case 45061:
            case 45161:
            case 45181:
            case 45455: {
                if (getMaxHp() / 3 <= getCurrentHp()) {
                    break;
                }
                int rnd = L1MonsterInstance._random.nextInt(10);
                if (rnd < 1) {
                    allTargetClear();
                    setHiddenStatus(1);
                    broadcastPacketAll(new S_DoActionGFX(getId(), 11));
                    setStatus(13);
                    broadcastPacketAll(new S_NPCPack(this));
                    break;
                }
                break;
            }
            case 45682: {
                if (getMaxHp() / 3 <= getCurrentHp()) {
                    break;
                }
                int rnd = L1MonsterInstance._random.nextInt(100);
                if (rnd < 1) {
                    allTargetClear();
                    setHiddenStatus(1);
                    broadcastPacketAll(new S_DoActionGFX(getId(), 20));
                    setStatus(20);
                    broadcastPacketAll(new S_NPCPack(this));
                    break;
                }
                break;
            }
            case 97259: {
                if (getMaxHp() / 3 <= getCurrentHp()) {
                    break;
                }
                int rnd = L1MonsterInstance._random.nextInt(100);
                if (rnd < 1) {
                    allTargetClear();
                    setHiddenStatus(1);
                    broadcastPacketAll(new S_DoActionGFX(getId(), 11));
                    setStatus(11);
                    broadcastPacketAll(new S_NPCPack(this));
                    break;
                }
                break;
            }
            case 45067:
            case 45090:
            case 45264:
            case 45321:
            case 45445:
            case 45452: {
                if (getMaxHp() / 3 <= getCurrentHp()) {
                    break;
                }
                int rnd = L1MonsterInstance._random.nextInt(10);
                if (rnd < 1) {
                    allTargetClear();
                    setHiddenStatus(2);
                    broadcastPacketAll(new S_DoActionGFX(getId(), 44));
                    setStatus(4);
                    broadcastPacketAll(new S_NPCPack(this));
                    break;
                }
                break;
            }
            case 46107:
            case 46108: {
                if (getMaxHp() / 4 > getCurrentHp()) {
                    int rnd = L1MonsterInstance._random.nextInt(10);
                    if (rnd < 1) {
                        allTargetClear();
                        setHiddenStatus(1);
                        broadcastPacketAll(new S_DoActionGFX(getId(), 11));
                        setStatus(13);
                        broadcastPacketAll(new S_NPCPack(this));
                    }
                }
            }
            case 105078: {
                if (getMaxHp() / 3 <= getCurrentHp()) {
                    break;
                }
                int rnd = L1MonsterInstance._random.nextInt(100);
                if (rnd < 2) {
                    allTargetClear();
                    setHiddenStatus(1);
                    broadcastPacketAll(new S_DoActionGFX(getId(), 4));
                    setStatus(4);
                    broadcastPacketAll(new S_NPCPack(this));
                    break;
                }
                break;
            }
        }
    }

    public void initHide() {
        int npcid = getNpcTemplate().get_npcId();
        int rnd = L1MonsterInstance._random.nextInt(3);
        switch (npcid) {
            case 45061:
            case 45161:
            case 45181:
            case 45455: {
                if (1 > rnd) {
                    setHiddenStatus(1);
                    setStatus(13);
                    break;
                }
                break;
            }
            case 45045:
            case 45126:
            case 45134:
            case 45281: {
                if (1 > rnd) {
                    setHiddenStatus(1);
                    setStatus(4);
                    break;
                }
                break;
            }
            case 45067:
            case 45090:
            case 45264:
            case 45321:
            case 45445:
            case 45452: {
                setHiddenStatus(2);
                setStatus(4);
                break;
            }
            case 45681: {
                setHiddenStatus(2);
                setStatus(11);
                break;
            }
            case 46107:
            case 46108: {
                if (1 > rnd) {
                    setHiddenStatus(1);
                    setStatus(13);
                    break;
                }
                break;
            }
            case 46125:
            case 46126:
            case 46127:
            case 46128: {
                setHiddenStatus(3);
                setStatus(4);
                break;
            }
            case 97259: {
                setHiddenStatus(1);
                setStatus(11);
                break;
            }
        }
    }

    public void initHideForMinion(L1NpcInstance leader) {
        int npcid = getNpcTemplate().get_npcId();
        if (leader.getHiddenStatus() == 1) {
            switch (npcid) {
                case 45061:
                case 45161:
                case 45181:
                case 45455:
                case 46107:
                case 46108: {
                    setHiddenStatus(1);
                    setStatus(13);
                    break;
                }
                case 45045:
                case 45126:
                case 45134:
                case 45281: {
                    setHiddenStatus(1);
                    setStatus(4);
                    break;
                }
            }
        } else if (leader.getHiddenStatus() == 2) {
            switch (npcid) {
                case 45067:
                case 45090:
                case 45264:
                case 45321:
                case 45445:
                case 45452: {
                    setHiddenStatus(2);
                    setStatus(4);
                    break;
                }
                case 45681: {
                    setHiddenStatus(2);
                    setStatus(11);
                    break;
                }
                case 46125:
                case 46126:
                case 46127:
                case 46128: {
                    setHiddenStatus(3);
                    setStatus(4);
                    break;
                }
            }
        }
    }

    @Override
    public void transform(int transformId) {
        super.transform(transformId);
        getInventory().clearItems();
        SetDropExecutor setDropExecutor = new SetDrop();
        setDropExecutor.setDrop(this, getInventory());
        getInventory().shuffle();
    }

    private void sendServerMessage(int msgid) {
        L1QuestUser quest = WorldQuest.get().get(get_showId());
        if (quest != null && !quest.pcList().isEmpty()) {
            for (L1PcInstance pc : quest.pcList()) {
                pc.sendPackets(new S_ServerMessage(msgid));
            }
        }
    }

    public long getLastHprTime() {
        if (_lasthprtime == 0L) {
            return _lasthprtime = System.currentTimeMillis() / 1000L - 5L;
        }
        return _lasthprtime;
    }

    public void setLastHprTime(long time) {
        _lasthprtime = time;
    }

    public long getLastMprTime() {
        if (_lastmprtime == 0L) {
            return _lastmprtime = System.currentTimeMillis() / 1000L - 5L;
        }
        return _lastmprtime;
    }

    public void setLastMprTime(long time) {
        _lastmprtime = time;
    }

    private final class CountDownTimer extends TimerTask {
        private final int _loc_x;
        private final int _loc_y;
        private final short _loc_mapId;
        private final L1QuestUser _quest;
        private final int _firstMsgId;

        public CountDownTimer(int loc_x, int loc_y, short loc_mapId, L1QuestUser quest,
                              int firstMsgId) {
            _loc_x = loc_x;
            _loc_y = loc_y;
            _loc_mapId = loc_mapId;
            _quest = quest;
            _firstMsgId = firstMsgId;
        }

        @Override
        public void run() {
            try {
                if (_firstMsgId != 0) {
                    sendServerMessage(_firstMsgId);
                }
                Thread.sleep(10000L);
                sendServerMessage(1476);
                Thread.sleep(10000L);
                sendServerMessage(1477);
                Thread.sleep(10000L);
                sendServerMessage(1478);
                Thread.sleep(5000L);
                sendServerMessage(1480);
                Thread.sleep(1000L);
                sendServerMessage(1481);
                Thread.sleep(1000L);
                sendServerMessage(1482);
                Thread.sleep(1000L);
                sendServerMessage(1483);
                Thread.sleep(1000L);
                sendServerMessage(1484);
                Thread.sleep(1000L);
                int i = 10;
                while (i > 0) {
                    if (_quest != null && !_quest.pcList().isEmpty()) {
                        for (L1PcInstance pc : _quest.pcList()) {
                            L1Teleport.teleport(pc, _loc_x, _loc_y, _loc_mapId, pc.getHeading(), true);
                        }
                    }
                    Thread.sleep(500L);
                    --i;
                }
            } catch (Exception ex) {
            }
        }
    }

    class Death implements Runnable {
        L1Character _lastAttacker;

        public Death(L1Character lastAttacker) {
            _lastAttacker = lastAttacker;
        }

        @Override
        public void run() {
            L1MonsterInstance mob = L1MonsterInstance.this;
            
            // 修復：重置所有攻擊者的傷害累積
            try {
                ArrayList<L1Character> hateList = mob.getHateList().toTargetArrayList();
                for (L1Character attacker : hateList) {
                    if (attacker instanceof L1PcInstance) {
                        L1PcInstance pc = (L1PcInstance) attacker;
                        // 重置該玩家對這個怪物的傷害累積
                        pc.setnpcdmg(0.0);
                        
                        // 記錄重置（僅限 GM）
                        if (pc.isGm()) {
                            pc.sendPackets(new S_SystemMessage("怪物死亡，傷害累積已重置"));
                        }
                    }
                }
            } catch (Exception e) {
                L1MonsterInstance._log.error("重置傷害累積失敗: " + e.getMessage(), e);
            }
            
            if (_lastAttacker instanceof L1PcInstance) {
                L1PcInstance pc = (L1PcInstance) _lastAttacker;
                pc.setKillCount(pc.getKillCount() + 1);
                pc.sendPackets(new S_OwnCharStatus(pc));
                if (mob.getUbId() != 0) {
                    int ubexp = (int) (getExp() / 10L);
                    pc.setUbScore(pc.getUbScore() + ubexp);
                }
            }
            mob.setDeathProcessing(true);
            mob.setCurrentHpDirect(0);
            mob.setDead(true);
            mob.setStatus(8);
            mob.broadcastPacketAll(new S_DoActionGFX(mob.getId(), 8));
            mob.getMap().setPassable(mob.getLocation(), true);
            mob.startChat(1);
            mob.distributeExpDropKarma(_lastAttacker);
            mob.giveUbSeal();
            mob.setDeathProcessing(false);
            mob.setExp(0L);
            mob.setKarma(0);
            mob.allTargetClear();
            switch (mob.getNpcId()) {
                case 46123:
                case 46124: {
                    if (getMapId() == 782
                            && WorldMob.get().getCount(getMapId(), 46124) == 0
                            && WorldMob.get().getCount(getMapId(), 46123) == 0) {
                        World.get().broadcastPacketToAll(new S_ServerMessage(1470));
                        ServerCrockTable.get().updateKillTime();
                        break;
                    }
                    break;
                }
                case 92000:
                case 92001: {
                    if (getMapId() == 784
                            && WorldMob.get().getCount(getMapId(), 92000) == 0
                            && WorldMob.get().getCount(getMapId(), 92001) == 0) {
                        World.get().broadcastPacketToAll(new S_ServerMessage(1470));
                        ServerCrockTable.get().updateKillTime();
                        break;
                    }
                    break;
                }
            }
            int deltime = 0;
            switch (mob.getNpcId()) {
                case 71016:
                case 71028:
                case 97206: {
                    deltime = 60;
                    break;
                }
                default: {
                    deltime = ConfigAlt.NPC_DELETION_TIME;
                    break;
                }
            }
            mob.startDeleteTimer(deltime);
        }
    }

    private class deathDragonTimer1 extends TimerTask {
        private final L1MonsterInstance npc;
        private final short mapId;

        public deathDragonTimer1(L1MonsterInstance paramShort, short arg3) {
            npc = paramShort;
            mapId = arg3;
        }

        @Override
        public void run() {
            try {
                if (npc.getNpcId() == 71014) {
                    Thread.sleep(5000L);
                    sendServerMessage(1573);
                    Thread.sleep(5000L);
                    sendServerMessage(1574);
                    Thread.sleep(10000L);
                    sendServerMessage(1575);
                    Thread.sleep(10000L);
                    sendServerMessage(1576);
                    Thread.sleep(10000L);
                    int i = 32776 + L1MonsterInstance._random.nextInt(20);
                    int k = 32679 + L1MonsterInstance._random.nextInt(20);
                    Thread.sleep(5000L);
                    L1Location loc = new L1Location(i, k, mapId);
                    L1SpawnUtil.spawn(71015, loc, new Random().nextInt(8), get_showId());
                } else if (npc.getNpcId() == 71015) {
                    Thread.sleep(5000L);
                    sendServerMessage(1577);
                    Thread.sleep(5000L);
                    sendServerMessage(1578);
                    Thread.sleep(10000L);
                    sendServerMessage(1579);
                    Thread.sleep(10000L);
                    int j = 32776 + L1MonsterInstance._random.nextInt(20);
                    int m = 32679 + L1MonsterInstance._random.nextInt(20);
                    Thread.sleep(5000L);
                    L1Location loc = new L1Location(j, m, mapId);
                    L1SpawnUtil.spawn(71016, loc, new Random().nextInt(8), get_showId());
                } else if (npc.getNpcId() == 71016) {
                    for (L1PcInstance pc : World.get().getVisiblePlayer(npc)) {
                        pc.sendPacketsX8(new S_SkillSound(pc.getId(), 7854));
                        SkillMode mode = L1SkillMode.get().getSkill(6797);
                        if (mode != null) {
                            mode.start(pc, null, null, 86400);
                        }
                    }
                    Thread.sleep(5000L);
                    sendServerMessage(1580);
                    Thread.sleep(5000L);
                    sendServerMessage(1581);
                    Thread.sleep(10000L);
                    GeneralThreadPool.get().execute(new CountDownTimer(33718, 32506, (short) 4,
                            WorldQuest.get().get(get_showId()), 0));
                }
            } catch (Exception ignored) {
            }
        }
    }

    private class deathDragonTimer2 extends TimerTask {
        private final L1MonsterInstance npc;
        private final short mapId;

        public deathDragonTimer2(L1MonsterInstance paramShort, short arg3) {
            npc = paramShort;
            mapId = arg3;
        }

        @Override
        public void run() {
            try {
                if (npc.getNpcId() == 71026) {
                    Thread.sleep(5000L);
                    sendServerMessage(1661);
                    Thread.sleep(5000L);
                    sendServerMessage(1662);
                    Thread.sleep(10000L);
                    sendServerMessage(1663);
                    Thread.sleep(10000L);
                    sendServerMessage(1664);
                    Thread.sleep(10000L);
                    int j = 32948 + L1MonsterInstance._random.nextInt(20);
                    int m = 32825 + L1MonsterInstance._random.nextInt(20);
                    L1Location loc = new L1Location(j, m, mapId);
                    L1SpawnUtil.spawn(71027, loc, new Random().nextInt(8), get_showId());
                } else if (npc.getNpcId() == 71027) {
                    Thread.sleep(5000L);
                    sendServerMessage(1665);
                    Thread.sleep(5000L);
                    sendServerMessage(1666);
                    Thread.sleep(10000L);
                    sendServerMessage(1667);
                    Thread.sleep(10000L);
                    int k = 32948 + L1MonsterInstance._random.nextInt(20);
                    int n = 32825 + L1MonsterInstance._random.nextInt(20);
                    L1Location loc = new L1Location(k, n, mapId);
                    L1SpawnUtil.spawn(71028, loc, new Random().nextInt(8), get_showId());
                } else if (npc.getNpcId() == 71028) {
                    ArrayList<L1Character> targetList = npc.getHateList().toTargetArrayList();
                    if (!targetList.isEmpty()) {
                        for (L1Character cha : targetList) {
                            if (cha instanceof L1PcInstance) {
                                L1PcInstance pc = (L1PcInstance) cha;
                                pc.sendPacketsX8(new S_SkillSound(pc.getId(), 7854));
                                SkillMode mode = L1SkillMode.get().getSkill(6798);
                                if (mode == null) {
                                    continue;
                                }
                                mode.start(pc, null, null, 86400);
                            }
                        }
                    }
                    Thread.sleep(5000L);
                    sendServerMessage(1668);
                    Thread.sleep(5000L);
                    sendServerMessage(1669);
                    Thread.sleep(10000L);
                    GeneralThreadPool.get().execute(new CountDownTimer(33718, 32506, (short) 4,
                            WorldQuest.get().get(get_showId()), 0));
                }
            } catch (Exception ignored) {
            }
        }
    }

    private class deathDragonTimer3 extends TimerTask {
        private final L1MonsterInstance npc;

        public deathDragonTimer3(L1MonsterInstance npc) {
            this.npc = npc;
        }

        @Override
        public void run() {
            try {
                if (npc.getNpcId() == 97204) {
                    Thread.sleep(5000L);
                    sendServerMessage(1759);
                    Thread.sleep(5000L);
                    sendServerMessage(1760);
                    Thread.sleep(10000L);
                    sendServerMessage(1761);
                    Thread.sleep(10000L);
                    sendServerMessage(1762);
                    Thread.sleep(10000L);
                    L1Location loc = new L1Location(32846, 32878, npc.getMapId()).randomLocation(10, true);
                    L1SpawnUtil.spawn(97205, loc, 0, npc.get_showId());
                    Thread.sleep(5000L);
                    sendServerMessage(1763);
                    Thread.sleep(5000L);
                    sendServerMessage(1764);
                    Thread.sleep(10000L);
                    sendServerMessage(1765);
                    Thread.sleep(10000L);
                    sendServerMessage(1766);
                    Thread.sleep(10000L);
                    Thread.sleep(5000L);
                    L1Location loc2 = new L1Location(32846, 32877, npc.getMapId()).randomLocation(10, true);
                    L1SpawnUtil.spawn(97206, loc2, 0, npc.get_showId());
                } else if (npc.getNpcId() == 97206) {
                    for (L1PcInstance pc : World.get().getVisiblePlayer(npc)) {
                        pc.broadcastPacketX10(new S_SkillSound(pc.getId(), 7854));
                        SkillMode mode = L1SkillMode.get().getSkill(6799);
                        if (mode != null) {
                            mode.start(pc, null, null, 86400);
                        }
                    }
                    Thread.sleep(5000L);
                    sendServerMessage(1772);
                    Thread.sleep(5000L);
                    sendServerMessage(1773);
                    Thread.sleep(10000L);
                    GeneralThreadPool.get().execute(new CountDownTimer(33718, 32506, (short) 4,
                            WorldQuest.get().get(get_showId()), 0));
                }
            } catch (Exception ignored) {
            }
        }
    }
}
