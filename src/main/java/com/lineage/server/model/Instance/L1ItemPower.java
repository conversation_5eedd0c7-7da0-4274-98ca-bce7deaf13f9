package com.lineage.server.model.Instance;

import java.util.HashMap;
import java.util.Map;

public class L1ItemPower {
    public static final Map<Integer, Integer> MR;
    public static final Map<Integer, Integer> MPR;
    public static final Map<Integer, Integer> SP;
    public static final Map<Integer, Integer> X2;
    public static final Map<Integer, Integer> HIT;
    public static final Map<Integer, Integer> HP;
    public static final Map<Integer, Integer> DMG_REDUCE;
    public static final Map<Integer, Integer> Attack;
    public static final Map<Integer, Integer> BowAttack;
    public static final Map<Integer, Integer> weightReductionByEnchant;
    public static final Map<Integer, Integer> MP;

    static {
        MP = new HashMap<>();
        MR = new HashMap<>();
        MPR = new HashMap<>();
        SP = new HashMap<>();
        X2 = new HashMap<>();
        HIT = new HashMap<>();
        HP = new HashMap<>();
        DMG_REDUCE = new HashMap<>();
        Attack = new HashMap<>();
        BowAttack = new HashMap<>();
        weightReductionByEnchant = new HashMap<>();
    }

    private final L1ItemInstance _itemInstance;

    protected L1ItemPower(L1ItemInstance itemInstance) {
        _itemInstance = itemInstance;
    }

    public static void load() {
        L1ItemPower.MR.put(20011, 1);
        L1ItemPower.MR.put(120011, 1);
        L1ItemPower.MR.put(20110, 1);
        L1ItemPower.MR.put(120110, 1);
        L1ItemPower.MR.put(20056, 2);
        L1ItemPower.MR.put(120056, 2);
        L1ItemPower.MR.put(330005, 1);
        L1ItemPower.MR.put(20117, 1);
        L1ItemPower.MR.put(20049, 2);
        L1ItemPower.MR.put(20050, 2);
        L1ItemPower.MR.put(20078, 3);
        L1ItemPower.MR.put(400019, 1);
        L1ItemPower.MR.put(400023, 1);
        L1ItemPower.MR.put(300427, 1);
        L1ItemPower.MR.put(300433, 3);
        L1ItemPower.MR.put(1300433, 3);
        L1ItemPower.MR.put(310126, 2);
        L1ItemPower.MR.put(70092, 2);
        L1ItemPower.MR.put(70093, 2);
        L1ItemPower.MR.put(401028, 1);
        L1ItemPower.MR.put(401029, 1);
        L1ItemPower.MR.put(401030, 1);
        L1ItemPower.MR.put(401031, 1);
        L1ItemPower.MR.put(400052, 2);
        L1ItemPower.weightReductionByEnchant.put(310132, 60);
        L1ItemPower.MPR.put(410163, 1);
        L1ItemPower.MPR.put(1410163, 1);
        L1ItemPower.SP.put(20107, 1);
        L1ItemPower.SP.put(120107, 1);
        L1ItemPower.SP.put(300431, 1);
        L1ItemPower.SP.put(300433, 1);
        L1ItemPower.SP.put(1300433, 1);
        L1ItemPower.SP.put(300432, 1);
        L1ItemPower.SP.put(1300432, 1);
        //L1ItemPower.SP.put(1300432, new Integer(1));
        L1ItemPower.SP.put(124, 1);
        L1ItemPower.SP.put(410158, 1);
        L1ItemPower.X2.put(410188, 1);
        L1ItemPower.HIT.put(400051, 1);
        L1ItemPower.HP.put(300429, 25);
        L1ItemPower.Attack.put(21106, 1);
        L1ItemPower.BowAttack.put(21105, 1);
        L1ItemPower.DMG_REDUCE.put(300430, 1);
        L1ItemPower.DMG_REDUCE.put(120489, 1);
        L1ItemPower.DMG_REDUCE.put(120490, 1);
        L1ItemPower.DMG_REDUCE.put(120491, 1);
        L1ItemPower.DMG_REDUCE.put(120492, 1);
        L1ItemPower.DMG_REDUCE.put(310125, 1);
        L1ItemPower.MP.put(21103, 10);
        L1ItemPower.HP.put(21103, 20);
    }


    protected int getMr() {
        int mr = _itemInstance.getItem().get_mdef();
        Integer integer = L1ItemPower.MR.get(_itemInstance.getItemId());
        if (integer != null) {
            mr += _itemInstance.getEnchantLevel() * integer;
        }
        return mr;
    }

    protected int getMpr() {
        int mpr = _itemInstance.getItem().get_addmpr();
        Integer integer = L1ItemPower.MPR.get(_itemInstance.getItemId());
        if (integer != null) {
            mpr += _itemInstance.getEnchantLevel() * integer;
        }
        return mpr;
    }

    protected int getweightReductionByEnchant() {
        int Weight = _itemInstance.getItem().getWeight();
        Integer integer = L1ItemPower.weightReductionByEnchant
                .get(_itemInstance.getItemId());
        if (integer != null) {
            if (_itemInstance.getItemId() == 310132) {
                if (_itemInstance.getEnchantLevel() >= 5) {
                    Weight += (_itemInstance.getEnchantLevel() - 4) * integer;
                }
            } else {
                Weight += _itemInstance.getEnchantLevel() * integer;
            }
        }
        return Weight;
    }

    protected int getSp() {
        int sp = _itemInstance.getItem().get_addsp();
        Integer integer = L1ItemPower.SP.get(_itemInstance.getItemId());
        if (integer != null) {
            if (_itemInstance.getItemId() == 20107 || _itemInstance.getItemId() == 120107) {
                if (_itemInstance.getEnchantLevel() >= 3) {
                    sp += (_itemInstance.getEnchantLevel() - 2) * integer;
                }
            } else if (_itemInstance.getItemId() == 300433) {
                if (_itemInstance.getEnchantLevel() >= 7) {
                    sp += (_itemInstance.getEnchantLevel() - 6) * integer;
                }
            } else if (_itemInstance.getItemId() == 300432 || _itemInstance.getItemId() == 1300432) {
                if (_itemInstance.getEnchantLevel() >= 8) {
                    sp += (_itemInstance.getEnchantLevel() - 7) * integer;
                }
            } else if (_itemInstance.getItemId() == 124 || _itemInstance.getItemId() == 410158) {
                if (_itemInstance.getEnchantLevel() >= 7) {
                    sp += (_itemInstance.getEnchantLevel() - 6) * integer;
                }
            } else if (_itemInstance.getItemId() == 300431) {
                int enchantlvl = _itemInstance.getEnchantLevel();
                if (enchantlvl >= 5 && enchantlvl <= 6) {
                    sp += integer;
                } else if (enchantlvl >= 7 && enchantlvl <= 8) {
                    sp += integer * 2;
                } else if (enchantlvl >= 9) {
                    sp += integer * 3;
                }
            } else if (_itemInstance.getItemId() == 1300433) {
                if (_itemInstance.getEnchantLevel() >= 7) {
                    sp += (_itemInstance.getEnchantLevel() - 6) * integer.intValue();
                }
            } else {
                sp += _itemInstance.getEnchantLevel() * integer.intValue();
            }
        }
        return sp;
    }

    protected int getDoubleDmgChance() {
        int x2 = _itemInstance.getItem().getDoubleDmgChance();
        Integer integer = L1ItemPower.X2.get(_itemInstance.getItemId());
        if (integer != null) {
            x2 += _itemInstance.getEnchantLevel() * integer;
        }
        return x2;
    }

    protected int getHitModifierByArmor() {
        int hit = _itemInstance.getItem().getHitModifierByArmor();
        Integer integer = L1ItemPower.HIT.get(_itemInstance.getItemId());
        if (integer != null) {
            if (_itemInstance.getItemId() == 400051) {
                if (_itemInstance.getEnchantLevel() >= 5) {
                    hit += (_itemInstance.getEnchantLevel() - 4) * integer;
                }
            } else {
                hit += _itemInstance.getEnchantLevel() * integer;
            }
        }
        return hit;
    }

    /*  protected int get_addmp() {
          int mp = _itemInstance.getItem().get_addmp();
          Integer integer = L1ItemPower.MP.get(Integer.valueOf(_itemInstance.getItemId()));
          if (integer != null) {
              if (_itemInstance.getItemId() == 21103) {
                  int enchantlvl = _itemInstance.getEnchantLevel();
                  if (enchantlvl == 6) {
                      mp += integer.intValue();
                  }
              } else {
                  mp += _itemInstance.getEnchantLevel() * integer.intValue();
              }
          }
          return mp;
      }
  */
    protected int get_addhp() {
        int hp = _itemInstance.getItem().get_addhp();
        Integer integer = L1ItemPower.HP.get(Integer.valueOf(_itemInstance.getItemId()));
        if (integer != null) {
            if (_itemInstance.getItemId() == 300429) {
                int enchantlvl = _itemInstance.getEnchantLevel();
                if (enchantlvl >= 5 && enchantlvl <= 6) {
                    hp += integer.intValue();
                } else if (enchantlvl >= 7 && enchantlvl <= 8) {
                    hp += integer.intValue() * 2;
                } else if (enchantlvl >= 9) {
                    hp += integer.intValue() * 3;
                }
            }
            //Kevin 增加 龍麟臂甲
           /* else if (_itemInstance.getItemId() == 21103) {
                int enchantlvl = _itemInstance.getEnchantLevel();
                if (enchantlvl == 6) {
                    hp += integer.intValue();
                }
                if (enchantlvl == 7) {
                    hp += integer.intValue() * 2;
                }
                if (enchantlvl == 8) {
                    hp += integer.intValue() * 3;
                }
                if (enchantlvl >= 9) {
                    hp += integer.intValue() * 4;
                }
            } */
            else {
                hp += _itemInstance.getEnchantLevel() * integer.intValue();
            }
        }
        return hp;
    }

    protected int get_addattack() {
        int attack = _itemInstance.getItem().get_addattack();
        Integer integer = L1ItemPower.Attack.get(_itemInstance.getItemId());
        if (integer != null) {
            if (_itemInstance.getItemId() == 21106) {
                int enchantlvl = _itemInstance.getEnchantLevel();
                //Kevin 古代鬥士臂甲加成
                if (enchantlvl == 6) {
                    attack += integer;
                } else if (enchantlvl == 7) {
                    attack += integer * 2;
                } else if (enchantlvl == 8) {
                    attack += integer * 3;
                } else if (enchantlvl >= 9) {
                    attack += integer * 4;
                }
				/*if (enchantlvl >= 5 && enchantlvl <= 6) {
					attack += integer.intValue();
				} else if (enchantlvl >= 7 && enchantlvl <= 8) {
					attack += integer.intValue() * 2;
				} else if (enchantlvl >= 9) {
					attack += integer.intValue() * 3;
				}*/
            } else {
                attack += _itemInstance.getEnchantLevel() * integer;
            }
        }
        return attack;
    }

    protected int get_addbowattack() {
        int bowattack = _itemInstance.getItem().get_addbowattack();
        Integer integer = L1ItemPower.BowAttack.get(_itemInstance.getItemId());
        if (integer != null) {
            if (_itemInstance.getItemId() == 21105) {
                int enchantlvl = _itemInstance.getEnchantLevel();
                //Kevin 古代神射臂甲加成
                if (enchantlvl == 6) {
                    bowattack += integer;
                }
                if (enchantlvl == 7) {
                    bowattack += integer * 2;
                }
                if (enchantlvl == 8) {
                    bowattack += integer * 3;
                }
                if (enchantlvl >= 9) {
                    bowattack += integer * 4;
                }
                /*if (enchantlvl >= 5 && enchantlvl <= 6) {
                    bowattack += integer.intValue();
                } else if (enchantlvl >= 7 && enchantlvl <= 8) {
                    bowattack += integer.intValue() * 2;
                } else if (enchantlvl >= 9) {
                    bowattack += integer.intValue() * 3;
                }*/
            } else {
                bowattack += _itemInstance.getEnchantLevel() * integer;
            }
        }
        return bowattack;
    }

    protected int getDamageReduction() {
        int reduce = _itemInstance.getItem().getDamageReduction();
        Integer integer = L1ItemPower.DMG_REDUCE.get(_itemInstance.getItemId());
        if (integer != null && _itemInstance.getItemId() == 300430) {
            int enchantlvl = _itemInstance.getEnchantLevel();
            if (enchantlvl >= 5 && enchantlvl <= 6) {
                reduce += integer;
            } else if (enchantlvl >= 7 && enchantlvl <= 8) {
                reduce += integer * 2;
            } else if (enchantlvl >= 9) {
                reduce += integer * 3;
            } else if (_itemInstance.getItemId() == 310125 || _itemInstance.getItemId() == 1310125) {
                if (_itemInstance.getEnchantLevel() >= 7) {
                    reduce += (_itemInstance.getEnchantLevel() - 6) * integer;
                }
            } else {
                reduce += _itemInstance.getEnchantLevel() * integer;
            }
        }
        return reduce;
    }

    protected void GreaterAtEnchant(L1PcInstance owner, int randomELevel) {
        int level = _itemInstance.getEnchantLevel();
        if (randomELevel == -1) {
            ++level;
        }

        switch (_itemInstance.getItem().get_greater()) {
            case 0: {
                switch (level) {
                    case 1:
                    case 2: {
                        owner.addMaxHp(randomELevel * 5);
                        break;
                    }
                    case 3:
                    case 4: {
                        owner.addMaxHp(randomELevel * 10);
                        break;
                    }
                    case 5:
                    case 7: {
                        owner.addMaxHp(randomELevel * 10);
                        owner.add_up_hp_potion(randomELevel * 2);
                        break;
                    }
                    case 6:
                    case 8: {
                        owner.add_up_hp_potion(randomELevel * 2);
                        break;
                    }
                }
                break;
            }
            case 1: {
                switch (level) {
                    case 1:
                    case 2: {
                        owner.addMaxHp(randomELevel * 5);
                        break;
                    }
                    case 3:
                    case 4: {
                        owner.addMaxHp(randomELevel * 10);
                        break;
                    }
                    case 5:
                    case 7: {
                        owner.addMaxHp(randomELevel * 10);
                        owner.addDmgModifierByArmor(randomELevel);
                        owner.addBowDmgModifierByArmor(randomELevel);
                        break;
                    }
                    case 6:
                    case 8: {
                        owner.addDmgModifierByArmor(randomELevel);
                        owner.addBowDmgModifierByArmor(randomELevel);
                        break;
                    }
                }
                break;
            }
            case 2: {
                switch (level) {
                    case 0: {
                        break;
                    }
                    case 1:
                    case 2: {
                        owner.addMaxHp(randomELevel * 5);
                        break;
                    }
                    case 3:
                    case 4: {
                        owner.addMaxHp(randomELevel * 10);
                        break;
                    }
                    case 5:
                    case 7: {
                        owner.addMaxHp(randomELevel * 10);
                        owner.addDamageReductionByArmor(randomELevel);
                        break;
                    }
                    case 6:
                    case 8: {
                        owner.addDamageReductionByArmor(randomELevel);
                        break;
                    }
                }
            }
        }
    }

    protected void greater(L1PcInstance owner, boolean equipment) {
        int level = _itemInstance.getEnchantLevel();
        if (level <= 0) {
            return;
        }
        if (equipment) {
            switch (_itemInstance.getItem().get_greater()) {
                case 0: {
                    switch (level) {
                        case 1: {
                            owner.addMaxHp(5);
                            break;
                        }
                        case 2: {
                            owner.addMaxHp(10);
                            break;
                        }
                        case 3: {
                            owner.addMaxHp(20);
                            break;
                        }
                        case 4: {
                            owner.addMaxHp(30);
                            owner.addDmgModifierByArmor(1);
                            owner.addBowDmgModifierByArmor(1);
                            break;
                        }
                        case 5: {
                            owner.addMaxHp(40);
                            owner.add_up_hp_potion(4);
                            owner.addDmgModifierByArmor(2);
                            owner.addBowDmgModifierByArmor(2);
                            owner.addDamageReductionByArmor(1);
                            break;
                        }
                        case 6: {
                            owner.addMaxHp(40);
                            owner.add_up_hp_potion(6);
                            owner.add_uhp_number(2);
                            owner.addDmgModifierByArmor(4);
                            owner.addBowDmgModifierByArmor(4);
                            owner.addOriginalMagicHit(2);
                            owner.addDamageReductionByArmor(2);
                            break;
                        }
                        case 7: {
                            owner.addMaxHp(50);
                            owner.add_up_hp_potion(10);
                            owner.add_uhp_number(4);
                            owner.addDmgModifierByArmor(5);
                            owner.addBowDmgModifierByArmor(5);
                            owner.addOriginalMagicHit(4);
                            owner.addPVPdmg(1);
                            owner.addDamageReductionByArmor(3);
                            break;
                        }
                        case 8: {
                            owner.addMaxHp(50);
                            owner.add_up_hp_potion(14);
                            owner.add_uhp_number(6);
                            owner.addDmgModifierByArmor(6);
                            owner.addBowDmgModifierByArmor(6);
                            owner.addOriginalMagicHit(8);
                            owner.addPVPdmg(2);
                            owner.addDamageReductionByArmor(3);
                            break;
                        }
                    }
                    break;
                }
                case 1: {
                    switch (level) {
                        case 1: {
                            owner.addMaxHp(5);
                            break;
                        }
                        case 2: {
                            owner.addMaxHp(10);
                            break;
                        }
                        case 3: {
                            owner.addMaxHp(20);
                            break;
                        }
                        case 4: {
                            owner.addMaxHp(30);
                            owner.addDmgModifierByArmor(1);
                            owner.addBowDmgModifierByArmor(1);
                            owner.addDamageReductionByArmor(1);
                            break;
                        }
                        case 5: {
                            owner.addMaxHp(40);
                            owner.addDmgModifierByArmor(3);
                            owner.addBowDmgModifierByArmor(3);
                            owner.addAc(-1);
                            owner.addDamageReductionByArmor(3);
                            break;
                        }
                        case 6: {
                            owner.addMaxHp(40);
                            owner.addDmgModifierByArmor(4);
                            owner.addBowDmgModifierByArmor(4);
                            owner.addSp(1);
                            owner.addAc(-3);
                            owner.addDamageReductionByArmor(3);
                            break;
                        }
                        case 7: {
                            owner.addMaxHp(50);
                            owner.addDmgModifierByArmor(4);
                            owner.addBowDmgModifierByArmor(4);
                            owner.addSp(2);
                            owner.addAc(-4);
                            owner.addPVPdmg(1);
                            owner.addDamageReductionByArmor(4);
                            break;
                        }
                        case 8: {
                            owner.addMaxHp(50);
                            owner.addDmgModifierByArmor(5);
                            owner.addBowDmgModifierByArmor(5);
                            owner.addSp(4);
                            owner.addAc(-5);
                            owner.addPVPdmg(2);
                            owner.addDamageReductionByArmor(5);
                            break;
                        }
                    }
                    break;
                }
                case 2: {
                    switch (level) {
                        case 1: {
                            owner.addMaxMp(5);
                            break;
                        }
                        case 2: {
                            owner.addMaxMp(10);
                            break;
                        }
                        case 3: {
                            owner.addMaxMp(20);
                            break;
                        }
                        case 4: {
                            owner.addMaxMp(30);
                            owner.addDamageReductionByArmor(1);
                            break;
                        }
                        case 5: {
                            owner.addMaxMp(40);
                            owner.addDamageReductionByArmor(2);
                            owner.addMr(3);
                            break;
                        }
                        case 6: {
                            owner.addMaxMp(40);
                            owner.addDamageReductionByArmor(4);
                            owner.addMr(5);
                            owner.addAc(-1);
                            break;
                        }
                        case 7: {
                            owner.addMaxMp(50);
                            owner.addDamageReductionByArmor(4);
                            owner.addMr(7);
                            owner.addAc(-2);
                            owner.addPVPdmgReduction(1);
                            break;
                        }
                        case 8: {
                            owner.addMaxMp(50);
                            owner.addDamageReductionByArmor(5);
                            owner.addMr(10);
                            owner.addAc(-3);
                            owner.addPVPdmgReduction(2);
                            break;
                        }
                    }
                    break;
                }
            }
        } else {
            switch (_itemInstance.getItem().get_greater()) {
                case 0: {
                    switch (level) {
                        case 1: {
                            owner.addMaxHp(-5);
                            break;
                        }
                        case 2: {
                            owner.addMaxHp(-10);
                            break;
                        }
                        case 3: {
                            owner.addMaxHp(-20);
                            break;
                        }
                        case 4: {
                            owner.addMaxHp(-30);
                            owner.addDmgModifierByArmor(-1);
                            owner.addBowDmgModifierByArmor(-1);
                            break;
                        }
                        case 5: {
                            owner.addMaxHp(-40);
                            owner.add_up_hp_potion(-4);
                            owner.addDmgModifierByArmor(-2);
                            owner.addBowDmgModifierByArmor(-2);
                            owner.addDamageReductionByArmor(-1);
                            break;
                        }
                        case 6: {
                            owner.addMaxHp(-40);
                            owner.add_up_hp_potion(-6);
                            owner.add_uhp_number(-2);
                            owner.addDmgModifierByArmor(-4);
                            owner.addBowDmgModifierByArmor(-4);
                            owner.addOriginalMagicHit(-2);
                            owner.addDamageReductionByArmor(-2);
                            break;
                        }
                        case 7: {
                            owner.addMaxHp(-50);
                            owner.add_up_hp_potion(-10);
                            owner.add_uhp_number(-4);
                            owner.addDmgModifierByArmor(-5);
                            owner.addBowDmgModifierByArmor(-5);
                            owner.addOriginalMagicHit(-4);
                            owner.addPVPdmg(-1);
                            owner.addDamageReductionByArmor(-3);
                            break;
                        }
                        case 8: {
                            owner.addMaxHp(-50);
                            owner.add_up_hp_potion(-14);
                            owner.add_uhp_number(-6);
                            owner.addDmgModifierByArmor(-6);
                            owner.addBowDmgModifierByArmor(-6);
                            owner.addOriginalMagicHit(-8);
                            owner.addPVPdmg(-2);
                            owner.addDamageReductionByArmor(-3);
                            break;
                        }
                    }
                    break;
                }
                case 1: {
                    switch (level) {
                        case 1: {
                            owner.addMaxHp(-5);
                            break;
                        }
                        case 2: {
                            owner.addMaxHp(-10);
                            break;
                        }
                        case 3: {
                            owner.addMaxHp(-20);
                            break;
                        }
                        case 4: {
                            owner.addMaxHp(-30);
                            owner.addDmgModifierByArmor(-1);
                            owner.addBowDmgModifierByArmor(-1);
                            owner.addDamageReductionByArmor(-1);
                            break;
                        }
                        case 5: {
                            owner.addMaxHp(-40);
                            owner.addDmgModifierByArmor(-3);
                            owner.addBowDmgModifierByArmor(-3);
                            owner.addAc(1);
                            owner.addDamageReductionByArmor(-3);
                            break;
                        }
                        case 6: {
                            owner.addMaxHp(-40);
                            owner.addDmgModifierByArmor(-4);
                            owner.addBowDmgModifierByArmor(-4);
                            owner.addSp(-1);
                            owner.addAc(3);
                            owner.addDamageReductionByArmor(-3);
                            break;
                        }
                        case 7: {
                            owner.addMaxHp(-50);
                            owner.addDmgModifierByArmor(-4);
                            owner.addBowDmgModifierByArmor(-4);
                            owner.addSp(-2);
                            owner.addAc(4);
                            owner.addPVPdmg(-1);
                            owner.addDamageReductionByArmor(-4);
                            break;
                        }
                        case 8: {
                            owner.addMaxHp(-50);
                            owner.addDmgModifierByArmor(-5);
                            owner.addBowDmgModifierByArmor(-5);
                            owner.addSp(-4);
                            owner.addAc(5);
                            owner.addPVPdmg(-2);
                            owner.addDamageReductionByArmor(-5);
                            break;
                        }
                    }
                    break;
                }
                case 2: {
                    switch (level) {
                        case 1: {
                            owner.addMaxMp(-5);
                            break;
                        }
                        case 2: {
                            owner.addMaxMp(-10);
                            break;
                        }
                        case 3: {
                            owner.addMaxMp(-20);
                            break;
                        }
                        case 4: {
                            owner.addMaxMp(-30);
                            owner.addDamageReductionByArmor(-1);
                            break;
                        }
                        case 5: {
                            owner.addMaxMp(-40);
                            owner.addDamageReductionByArmor(-2);
                            owner.addMr(-3);
                            break;
                        }
                        case 6: {
                            owner.addMaxMp(-40);
                            owner.addDamageReductionByArmor(-4);
                            owner.addMr(-5);
                            owner.addAc(1);
                            break;
                        }
                        case 7: {
                            owner.addMaxMp(-50);
                            owner.addDamageReductionByArmor(-4);
                            owner.addMr(-7);
                            owner.addAc(2);
                            owner.addPVPdmgReduction(-1);
                            break;
                        }
                        case 8: {
                            owner.addMaxMp(-50);
                            owner.addDamageReductionByArmor(-5);
                            owner.addMr(-10);
                            owner.addAc(3);
                            owner.addPVPdmgReduction(-2);
                            break;
                        }
                    }
                    break;
                }
            }
        }
    }
}
