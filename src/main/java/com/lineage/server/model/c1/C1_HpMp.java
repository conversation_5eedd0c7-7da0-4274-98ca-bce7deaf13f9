package com.lineage.server.model.c1;

import com.lineage.server.serverpackets.S_MPUpdate;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_HPUpdate;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class C1_HpMp extends C1Executor {
	private static final Log _log;
	private int _int1;
	private int _int2;

	static {
		_log = LogFactory.getLog(C1_HpMp.class);
	}

	public static C1Executor get() {
		return new C1_HpMp();
	}

	@Override
	public void set_power(final int int1, final int int2, final int int3, final int int4) {
		try {
			this._int1 = int1;
			this._int2 = int2;
		} catch (Exception e) {
			C1_HpMp._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void set_c1(final L1PcInstance pc) {
		try {
			pc.addMaxHp(this._int1);
			pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
			pc.addMaxMp(this._int2);
			pc.sendPackets(new S_MPUpdate(pc));
		} catch (Exception e) {
			C1_HpMp._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void remove_c1(final L1PcInstance pc) {
		try {
			pc.addMaxHp(-this._int1);
			pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
			pc.addMaxMp(-this._int2);
			pc.sendPackets(new S_MPUpdate(pc));
		} catch (Exception e) {
			C1_HpMp._log.error(e.getLocalizedMessage(), e);
		}
	}
}
