package com.lineage.server.model;

import java.util.List;

import com.lineage.server.utils.RandomArrayList;

import java.util.ArrayList;
import java.util.Map;

import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.model.Instance.L1PetInstance;
import com.lineage.server.model.Instance.L1SummonInstance;
import com.lineage.server.types.Point;

import java.util.HashMap;

import com.lineage.server.templates.L1Skills;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.serverpackets.S_Paralysis;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.S_PacketBoxWindShackle;
import com.lineage.server.serverpackets.S_SkillSound;
import com.lineage.server.model.skill.L1SkillUse;

import java.util.Iterator;

import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_DoActionGFX;
import com.lineage.server.utils.L1SpawnUtil;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.world.World;
import com.lineage.server.model.skill.L1SkillDelayforMob;
import com.lineage.server.datatables.MobSkillTable;
import org.apache.commons.logging.LogFactory;

import java.util.Random;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.templates.L1MobSkill;
import org.apache.commons.logging.Log;

public class L1MobSkillUse {
    private static final Log _log;
    private L1MobSkill _mobSkillTemplate;
    private L1NpcInstance _attacker;
    private L1Character _target;
    private static final Random _rnd;
    private int _sleepTime;
    private int[] _skillUseCount;
    private int _skillSize;

    static {
        _log = LogFactory.getLog(L1MobSkillUse.class);
        _rnd = new Random();
    }

    public L1MobSkillUse(final L1NpcInstance npc) {
        this._mobSkillTemplate = null;
        this._attacker = null;
        this._target = null;
        this._sleepTime = 0;
        try {
            this._sleepTime = 0;
            this._mobSkillTemplate = MobSkillTable.getInstance().getTemplate(npc.getNpcTemplate().get_npcId());
            if (this._mobSkillTemplate == null) {
                return;
            }
            this._attacker = npc;
            this._skillSize = this.getMobSkillTemplate().getSkillSize();
            this._skillUseCount = new int[this._skillSize];
        } catch (Exception e) {
            L1MobSkillUse._log.error(e.getLocalizedMessage(), e);
        }
    }

    private int getSkillUseCount(final int idx) {
        return this._skillUseCount[idx];
    }

    private void skillUseCountUp(final int idx) {
        final int[] skillUseCount = this._skillUseCount;
        ++skillUseCount[idx];
    }

    public void resetAllSkillUseCount() {
        if (this._mobSkillTemplate == null) {
            return;
        }
        int i = 0;
        while (i < this._skillSize) {
            this._skillUseCount[i] = 0;
            ++i;
        }
    }

    public int getSleepTime() {
        return this._sleepTime;
    }

    public void setSleepTime(final int i) {
        this._sleepTime = i;
    }

    public L1MobSkill getMobSkillTemplate() {
        return this._mobSkillTemplate;
    }

    public boolean isSkillTrigger(final L1Character tg) {
        if (this._mobSkillTemplate == null) {
            return false;
        }
        this._target = tg;
        final int type = this.getMobSkillTemplate().getType(0);
        if (type == 0) {
            return false;
        }
        int i = 0;
        while (i < this.getMobSkillTemplate().getSkillSize() && this.getMobSkillTemplate().getType(i) != 0) {
            if (this.isSkillUseble(i, false)) {
                return true;
            }
            ++i;
        }
        return false;
    }

    public final boolean skillUse(final L1Character tg, final boolean isTriRnd) {
        if (this._mobSkillTemplate == null) {
            return false;
        }
        final int type = this._mobSkillTemplate.getType(0);
        if (type == 0) {
            return false;
        }
        int[] skills = null;
        int skillSizeCounter = 0;
        final int skillSize = this._mobSkillTemplate.getSkillSize();
        if (skillSize >= 0) {
            skills = new int[skillSize];
        }
        int i = 0;
        i = 0;
        while (i < this.getMobSkillTemplate().getSkillSize() && this.getMobSkillTemplate().getType(i) != 0) {
            final int changeType = this.getMobSkillTemplate().getChangeTarget(i);
            if (changeType > 0) {
                this._target = this.changeTarget(changeType, i);
            } else {
                this._target = tg;
            }
            if (this.isSkillUseble(i, isTriRnd)) {
                skills[skillSizeCounter] = i;
                ++skillSizeCounter;
            }
            ++i;
        }
        if (skillSizeCounter != 0) {
            final int actNo = skills[L1MobSkillUse._rnd.nextInt(skillSizeCounter)];
            final int changeType2 = this._mobSkillTemplate.getChangeTarget(actNo);
            if (changeType2 > 0) {
                this._target = this.changeTarget(changeType2, i);
            } else {
                this._target = tg;
            }
            if (this.useSkill(actNo)) {
                return true;
            }
        }
        return false;
    }

    private void setDelay(final int idx) {
        if (this._mobSkillTemplate.getReuseDelay(idx) > 0) {
            L1SkillDelayforMob.onSkillUse(this._attacker, this._mobSkillTemplate.getReuseDelay(idx), idx);
        }
    }

    private boolean useSkill(final int idx) {
        boolean isUseSkill = false;
        final int type = this.getMobSkillTemplate().getType(idx);
        switch (type) {
            case 1: {
                if (this.physicalAttack(idx)) {
                    this.skillUseCountUp(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 2: {
                if (this.magicAttack(idx)) {
                    this.skillUseCountUp(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 3: {
                if (this.summon(idx)) {
                    this.skillUseCountUp(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 4: {
                if (this.poly(idx)) {
                    this.skillUseCountUp(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 5: {
                if (this.areashock_stun(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 6: {
                if (this.areacancellation(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 7: {
                if (this.weapon_break(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 8: {
                if (this.potionturntodmg(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 9: {
                if (this.pollutewaterwave(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 10: {
                if (this.healturntodmg(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 11: {
                if (this.areasilence(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 12: {
                if (this.areadecaypotion(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 13: {
                if (this.areawindshackle(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 14: {
                if (this.areadebuff(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
            case 15: {
                if (this.area_poison(idx)) {
                    this.skillUseCountUp(idx);
                    this.setDelay(idx);
                    isUseSkill = true;
                    break;
                }
                break;
            }
        }
        return isUseSkill;
    }

    private boolean area_poison(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 1;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.isGmInvis()) {
                L1SpawnUtil.spawnEffect(86125, 3, pc.getX(), pc.getY(), this._attacker.getMapId(), this._attacker, 0);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areadebuff(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int skillid = this.getMobSkillTemplate().getSkillId(idx);
        final int actId = this.getMobSkillTemplate().getActid(idx);
        final int gfxId = this.getMobSkillTemplate().getGfxid(idx);
        final int time = this.getMobSkillTemplate().getLeverage(idx);
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (skillid == 71 && pc.hasSkillEffect(4011)) {
                continue;
            }
            final L1SkillUse skillUse = new L1SkillUse();
            boolean canUseSkill = false;
            if (skillid > 0) {
                canUseSkill = skillUse.checkUseSkill(null, skillid, pc.getId(), pc.getX(), pc.getY(), time, 4,
                        this._attacker, actId, gfxId);
            }
            if (!canUseSkill || pc.hasSkillEffect(skillid)) {
                continue;
            }
            skillUse.handleCommands(null, skillid, pc.getId(), pc.getX(), pc.getY(), time, 4, this._attacker);
        }
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        this._attacker.broadcastPacketAll(new S_DoActionGFX(this._attacker.getId(), actionid));
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areawindshackle(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final int gfxId = this.getMobSkillTemplate().getGfxid(idx);
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(167)) {
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), gfxId));
                pc.sendPackets(new S_PacketBoxWindShackle(pc.getId(), 16));
                pc.setSkillEffect(167, 16000);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areadecaypotion(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(71)) {
                new L1SkillUse().handleCommands(pc, 71, pc.getId(), pc.getX(), pc.getY(), 0, 4);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areasilence(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(64) && !pc.hasSkillEffect(161)) {
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 10708));
                pc.setSkillEffect(64, 16000);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean healturntodmg(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(4011) && !pc.hasSkillEffect(4012) && !pc.hasSkillEffect(4013)) {
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 7780));
                pc.setSkillEffect(4013, 12000);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean pollutewaterwave(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(4011) && !pc.hasSkillEffect(4012) && !pc.hasSkillEffect(4013)) {
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 7782));
                pc.setSkillEffect(4012, 12000);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean potionturntodmg(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            if (!pc.hasSkillEffect(4011) && !pc.hasSkillEffect(4012) && !pc.hasSkillEffect(4013)
                    && !pc.hasSkillEffect(71)) {
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 7781));
                pc.setSkillEffect(4011, 12000);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean weapon_break(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            final L1ItemInstance weapon = pc.getWeapon();
            if (weapon != null) {
                final int weaponDamage = L1MobSkillUse._rnd.nextInt(this._attacker.getInt() / 3) + 1;
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 172));
                pc.sendPackets(new S_ServerMessage(268, weapon.getLogName()));
                pc.getInventory().receiveDamage(weapon, weaponDamage);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areacancellation(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            new L1SkillUse().handleCommands(pc, 44, pc.getId(), pc.getX(), pc.getY(), 0, 4);
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean areashock_stun(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 1;
        if (actId > 0) {
            actionid = actId;
        }
        final Iterator<L1PcInstance> iterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1PcInstance pc = iterator.next();
            final Random random = new Random();
            int shock = random.nextInt(4) + 1;
            if (pc.hasSkillEffect(87)) {
                shock += pc.getSkillEffectTimeSec(87);
            }
            if (shock > 4) {
                shock = 4;
            }
            if (!pc.isGmInvis()) {
                pc.setSkillEffect(87, shock * 1000);
                pc.sendPackets(new S_Paralysis(5, true));
                L1SpawnUtil.spawnEffect(81162, shock, pc.getX(), pc.getY(), this._attacker.getMapId(), this._attacker,
                        0);
            }
        }
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean summon(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        final int summonId = this.getMobSkillTemplate().getSummon(idx);
        final int min = this.getMobSkillTemplate().getSummonMin(idx);
        final int max = this.getMobSkillTemplate().getSummonMax(idx);
        int count = 0;
        if (summonId == 0) {
            return false;
        }
        count = L1MobSkillUse._rnd.nextInt(max) + min;
        final L1MobSkillUseSpawn skillUseSpawn = new L1MobSkillUseSpawn();
        skillUseSpawn.mobspawn(this._attacker, this._target, summonId, count);
        this._attacker.broadcastPacketAll(new S_SkillSound(this._attacker.getId(), 761));
        final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
        this._attacker.broadcastPacketAll(gfx);
        this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        return true;
    }

    private boolean poly(final int idx) {
        if (this._attacker.getMapId() == 93) {
            return false;
        }
        final int polyId = this.getMobSkillTemplate().getPolyId(idx);
        final int actId = this.getMobSkillTemplate().getActid(idx);
        int actionid = 19;
        if (actId > 0) {
            actionid = actId;
        }
        boolean usePoly = false;
        if (polyId == 0) {
            return false;
        }
        Iterator<L1PcInstance> localIterator = World.get().getVisiblePlayer(this._attacker).iterator();
        while (localIterator.hasNext()) {
            final L1PcInstance pc = localIterator.next();
            if (!pc.isDead() && !pc.isGhost() && !pc.isGmInvis() && this._attacker.glanceCheck(pc.getX(), pc.getY())) {
                final int npcId = this._attacker.getNpcTemplate().get_npcId();
                switch (npcId) {
                    case 81082: {
                        pc.getInventory().takeoffEquip(945);
                        break;
                    }
                }
                L1PolyMorph.doPoly(pc, polyId, 1800, 4);
                usePoly = true;
            }
        }
        if (usePoly) {
            localIterator = World.get().getVisiblePlayer(this._attacker).iterator();
            if (localIterator.hasNext()) {
                final L1PcInstance pc = localIterator.next();
                pc.sendPacketsAll(new S_SkillSound(pc.getId(), 230));
            }
            final S_DoActionGFX gfx = new S_DoActionGFX(this._attacker.getId(), actionid);
            this._attacker.broadcastPacketAll(gfx);
            this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
        }
        return usePoly;
    }

    private boolean magicAttack(final int idx) {
        final L1SkillUse skillUse = new L1SkillUse();
        final int skillid = this.getMobSkillTemplate().getSkillId(idx);
        final int actId = this.getMobSkillTemplate().getActid(idx);
        final int gfxId = this.getMobSkillTemplate().getGfxid(idx);
        boolean canUseSkill = false;
        if (skillid > 0) {
            canUseSkill = skillUse.checkUseSkill(null, skillid, this._target.getId(), this._target.getX(),
                    this._target.getY(), 0, 0, this._attacker, actId, gfxId);
        }
        if (canUseSkill) {
            if (this.getMobSkillTemplate().getLeverage(idx) > 0) {
                skillUse.setLeverage(this.getMobSkillTemplate().getLeverage(idx));
            }
            skillUse.handleCommands(null, skillid, this._target.getId(), this._target.getX(), this._target.getX(), 0, 0,
                    this._attacker);
            final L1Skills skill = SkillsTable.get().getTemplate(skillid);
            if (skill.getTarget().equals("attack") && skillid != 18) {
                this._sleepTime = this._attacker.getNpcTemplate().getAtkMagicSpeed();
            } else {
                this._sleepTime = this._attacker.getNpcTemplate().getSubMagicSpeed();
            }
            return true;
        }
        return false;
    }

    private boolean physicalAttack(final int idx) {
        final Map<Integer, Integer> targetList = new HashMap();
        final int areaWidth = this.getMobSkillTemplate().getAreaWidth(idx);
        final int areaHeight = this.getMobSkillTemplate().getAreaHeight(idx);
        final int range = this.getMobSkillTemplate().getRange(idx);
        final int actId = this.getMobSkillTemplate().getActid(idx);
        final int gfxId = this.getMobSkillTemplate().getGfxid(idx);
        if (this._attacker.getLocation().getTileLineDistance(this._target.getLocation()) > range) {
            return false;
        }
        if (!this._attacker.glanceCheck(this._target.getX(), this._target.getY())) {
            return false;
        }
        this._attacker.setHeading(this._attacker.targetDirection(this._target.getX(), this._target.getY()));
        if (areaHeight > 0) {
            final ArrayList<L1Object> objs = World.get().getVisibleBoxObjects(this._attacker,
                    this._attacker.getHeading(), areaWidth, areaHeight);
            final Iterator<L1Object> iterator = objs.iterator();
            while (iterator.hasNext()) {
                final L1Object obj = iterator.next();

                if (!(obj instanceof L1Character)) {
                    continue;
                }
                final L1Character cha = (L1Character) obj;
                if (cha.isDead()) {
                    continue;
                }
                if (cha instanceof L1PcInstance && ((L1PcInstance) cha).isGhost()) {
                    continue;
                }

                if (!this._attacker.glanceCheck(cha.getX(), cha.getY())) {
                    continue;
                }

                if (this._target instanceof L1PcInstance || this._target instanceof L1SummonInstance
                        || this._target instanceof L1PetInstance) {
                    if (obj instanceof L1PcInstance && (((L1PcInstance) obj).isGhost() || ((L1PcInstance) obj).isGmInvis())) {
                        continue;
                    }
                    if (!(obj instanceof L1SummonInstance) || !(obj instanceof L1PetInstance)) {
                        continue;
                    }
                    targetList.put(Integer.valueOf(obj.getId()), Integer.valueOf(0));
                } else {
                    if (!(obj instanceof L1MonsterInstance)) {
                        continue;
                    }
                    targetList.put(Integer.valueOf(obj.getId()), Integer.valueOf(0));
                }
            }
        } else {
            targetList.put(Integer.valueOf(this._target.getId()), Integer.valueOf(0));
        }
        if (targetList.size() == 0) {
            return false;
        }
        final Iterator<Integer> ite = targetList.keySet().iterator();
        while (ite.hasNext()) {
            final int targetId = ite.next().intValue();
            final L1Object object = World.get().findObject(targetId);
            final L1AttackMode attack = new L1AttackNpc(this._attacker, (L1Character) object);
            if (attack.calcHit()) {
                if (this.getMobSkillTemplate().getLeverage(idx) > 0) {
                    attack.setLeverage(this.getMobSkillTemplate().getLeverage(idx));
                }
                attack.calcDamage();
            }
            if (actId > 0) {
                attack.setActId(actId);
            }
            if (targetId == this._target.getId()) {
                if (gfxId > 0) {
                    this._attacker.broadcastPacketAll(new S_SkillSound(this._attacker.getId(), gfxId));
                }
                attack.action();
            }
            attack.commit();
        }
        this._sleepTime = this._attacker.getAtkspeed();
        return true;
    }

    private boolean isSkillUseble(final int skillIdx, final boolean isTriRnd) {
        boolean useble = false;
        final int type = this.getMobSkillTemplate().getType(skillIdx);
        final int chance = RandomArrayList.getInc(100, 1);
        if (chance > this.getMobSkillTemplate().getTriggerRandom(skillIdx)) {
            return false;
        }
        if (isTriRnd || type == 3 || type == 4) {
            useble = true;
        }
        if (this.getMobSkillTemplate().getTriggerHp(skillIdx) > 0) {
            final int hpRatio = this._attacker.getCurrentHp() * 100 / this._attacker.getMaxHp();
            if (hpRatio > this.getMobSkillTemplate().getTriggerHp(skillIdx)) {
                return false;
            }
            useble = true;
        }
        if (this.getMobSkillTemplate().getTriggerCompanionHp(skillIdx) > 0) {
            final L1NpcInstance companionNpc = this.searchMinCompanionHp();
            if (companionNpc != null) {
                final int hpRatio2 = companionNpc.getCurrentHp() * 100 / companionNpc.getMaxHp();
                if (hpRatio2 > this.getMobSkillTemplate().getTriggerCompanionHp(skillIdx)) {
                    return false;
                }
                useble = true;
                this._target = companionNpc;
            }
        }
        if (this.getMobSkillTemplate().getTriggerRange(skillIdx) != 0) {
            if (_attacker == null) {
                _log.info("MobSkill _attacker is null !!");
                return false;
            }
            if (_target == null) {
                _log.info("MobSkill _target is null !!");
                return false;
            }

            final int distance = this._attacker.getLocation().getTileLineDistance(this._target.getLocation());
            if (!this.getMobSkillTemplate().isTriggerDistance(skillIdx, distance)) {
                return false;
            }
            useble = true;
        }
        if (this.getMobSkillTemplate().getTriggerCount(skillIdx) > 0) {
            if (this.getSkillUseCount(skillIdx) >= this.getMobSkillTemplate().getTriggerCount(skillIdx)) {
                return false;
            }
            useble = true;
        }
        return useble;
    }

    private L1NpcInstance searchMinCompanionHp() {
        L1NpcInstance minHpNpc = null;
        final int family = this._attacker.getNpcTemplate().get_family();
        final Iterator<L1Object> iterator = World.get().getVisibleObjects(this._attacker).iterator();
        while (iterator.hasNext()) {
            final L1Object object = iterator.next();
            if (object instanceof L1NpcInstance) {
                final L1NpcInstance npc = (L1NpcInstance) object;
                if (npc.getNpcTemplate().get_family() != family) {
                    continue;
                }
                minHpNpc = npc;
            }
        }
        return minHpNpc;
    }

    private L1Character changeTarget(final int type, final int idx) {
        L1Character target = null;
        switch (type) {
            case 2: {
                target = this._attacker;
                break;
            }
            case 3: {
                final List<L1Character> targetList = new ArrayList();
                final Iterator<L1Object> iterator = World.get().getVisibleObjects(this._attacker).iterator();
                while (iterator.hasNext()) {
                    final L1Object obj = iterator.next();
                    if (obj instanceof L1PcInstance || obj instanceof L1PetInstance || obj instanceof L1SummonInstance
                            || (this._attacker.getMapId() == 93 && obj instanceof L1MonsterInstance)) {
                        final L1Character cha = (L1Character) obj;
                        final int distance = this._attacker.getLocation().getTileLineDistance(cha.getLocation());
                        if (!this.getMobSkillTemplate().isTriggerDistance(idx, distance)) {
                            continue;
                        }
                        if (!this._attacker.glanceCheck(cha.getX(), cha.getY())) {
                            continue;
                        }
                        if (this._attacker.getMapId() != 93 && !this._attacker.getHateList().containsKey(cha)) {
                            continue;
                        }
                        if (cha.isDead()) {
                            continue;
                        }
                        if (cha instanceof L1PcInstance) {
                            if (((L1PcInstance) cha).isGhost()) {
                                continue;
                            }
                            if (((L1PcInstance) cha).isGmInvis()) {
                                continue;
                            }
                        }
                        targetList.add((L1Character) obj);
                    }
                }
                if (targetList.size() == 0) {
                    target = this._target;
                    break;
                }
                final int targetIndex = L1MobSkillUse._rnd.nextInt(targetList.size());
                target = targetList.get(targetIndex);
                break;
            }
            default: {
                target = this._target;
                break;
            }
        }
        return target;
    }
}
