package com.lineage.server.model.doll;

import com.lineage.server.templates.L1Skills;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.datatables.SkillsTable;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.LogFactory;
import org.apache.commons.logging.Log;

public class Doll_Add_Skill extends L1DollExecutor {
	private static final Log _log;
	private int _int1;
	private String _note;

	static {
		_log = LogFactory.getLog(Doll_Add_Skill.class);
	}

	public static L1DollExecutor get() {
		return new Doll_Add_Skill();
	}

	@Override
	public void set_power(final int int1, final int int2, final int int3) {
		try {
			this._int1 = int1;
		} catch (Exception e) {
			Doll_Add_Skill._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void set_note(final String note) {
		this._note = note;
	}

	@Override
	public String get_note() {
		return this._note;
	}

	@Override
	public void setDoll(final L1PcInstance pc) {
		try {
			L1ItemInstance tgItem = null;
			switch (this._int1) {
			case 8:
			case 12:
			case 48:
			case 107: {
				tgItem = pc.getWeapon();
				if (tgItem == null) {
					return;
				}
				break;
			}
			case 21: {
				tgItem = pc.getInventory().getItemEquipped(2, 2);
				if (tgItem == null) {
					return;
				}
				break;
			}
			}
			boolean is = false;
			if (tgItem != null) {
				if (!tgItem.isRunning()) {
					is = true;
				}
			} else if (!pc.hasSkillEffect(this._int1)) {
				is = true;
			}
			if (is) {
				final L1Skills skill = SkillsTable.get().getTemplate(this._int1);
				final L1SkillUse skillUse = new L1SkillUse();
				skillUse.handleCommands(pc, this._int1, pc.getId(), pc.getX(), pc.getY(), skill.getBuffDuration(), 4);
			}
		} catch (Exception e) {
			Doll_Add_Skill._log.error(e.getLocalizedMessage(), e);
		}
	}

	@Override
	public void removeDoll(final L1PcInstance pc) {
	}

	@Override
	public boolean is_reset() {
		return true;
	}
}
