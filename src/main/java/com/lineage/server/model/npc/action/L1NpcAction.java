package com.lineage.server.model.npc.action;

import com.lineage.server.model.npc.L1NpcHtml;
import com.lineage.server.model.L1Object;
import com.lineage.server.model.Instance.L1PcInstance;

public interface L1NpcAction {
	boolean acceptsRequest(final String p0, final L1PcInstance p1, final L1Object p2);

	L1NpcHtml execute(final String p0, final L1PcInstance p1, final L1Object p2, final byte[] p3);

	void execute(final String p0, final String p1);

	L1NpcHtml executeWithAmount(final String p0, final L1PcInstance p1, final L1Object p2, final long p3);
}
