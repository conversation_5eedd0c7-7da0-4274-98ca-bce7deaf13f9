package com.lineage.server.model.npc.action;

import com.lineage.server.model.npc.L1NpcHtml;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Object;
import java.util.StringTokenizer;
import com.lineage.server.datatables.ExpTable;
import java.util.Arrays;
import org.w3c.dom.Element;
import java.util.HashMap;
import java.util.Map;
import com.lineage.server.utils.RangeInt;

public abstract class L1NpcXmlAction implements L1NpcAction {
	public String _name;
	public String _npcids;
	private final int[] _npcIds;
	private final RangeInt _level;
	private final int _questId;
	private final int _questStep;
	private final int[] _classes;
	private static final Map<Character, Integer> _charTypes;

	static {
		(_charTypes = new HashMap()).put(Character.valueOf('P'), Integer.valueOf(0));
		_charTypes.put(Character.valueOf('K'), Integer.valueOf(1));
		_charTypes.put(Character.valueOf('E'), Integer.valueOf(2));
		_charTypes.put(Character.valueOf('W'), Integer.valueOf(3));
		_charTypes.put(Character.valueOf('D'), Integer.valueOf(4));
		_charTypes.put(Character.valueOf('R'), Integer.valueOf(5));
		_charTypes.put(Character.valueOf('I'), Integer.valueOf(6));
	}

	public L1NpcXmlAction(final Element element) {
		this._npcids = null;
		this._name = element.getAttribute("Name");
		this._name = (this._name.equals("") ? null : this._name);
		this._npcids = element.getAttribute("NpcId");
		this._npcIds = this.parseNpcIds(this._npcids);
		this._level = this.parseLevel(element);
		this._questId = L1NpcXmlParser.parseQuestId(element.getAttribute("QuestId"));
		this._questStep = L1NpcXmlParser.parseQuestStep(element.getAttribute("QuestStep"));
		this._classes = this.parseClasses(element);
	}

	private int[] parseClasses(final Element element) {
		final String classes = element.getAttribute("Class").toUpperCase();
		final int[] result = new int[classes.length()];
		int idx = 0;
		final char[] charArray;
		final int length = (charArray = classes.toCharArray()).length;
		int i = 0;
		while (i < length) {
			final Character cha = Character.valueOf(charArray[i]);
			result[idx++] = L1NpcXmlAction._charTypes.get(cha).intValue();
			++i;
		}
		Arrays.sort(result);
		return result;
	}

	private RangeInt parseLevel(final Element element) {
		final int level = L1NpcXmlParser.getIntAttribute(element, "Level", 0);
		final int min = L1NpcXmlParser.getIntAttribute(element, "LevelMin", 1);
		final int max = L1NpcXmlParser.getIntAttribute(element, "LevelMax", ExpTable.MAX_LEVEL);
		return (level == 0) ? new RangeInt(min, max) : new RangeInt(level, level);
	}

	private int[] parseNpcIds(final String npcIds) {
		final StringTokenizer tok = new StringTokenizer(npcIds.replace(" ", ""), ",");
		final int[] result = new int[tok.countTokens()];
		int i = 0;
		while (i < result.length) {
			result[i] = Integer.parseInt(tok.nextToken());
			++i;
		}
		Arrays.sort(result);
		return result;
	}

	private boolean acceptsNpcId(final L1Object obj) {
		if (this._npcIds.length > 0) {
			if (!(obj instanceof L1NpcInstance)) {
				return false;
			}
			final int npcId = ((L1NpcInstance) obj).getNpcTemplate().get_npcId();
			if (Arrays.binarySearch(this._npcIds, npcId) < 0) {
				return false;
			}
		}
		return true;
	}

	private boolean acceptsLevel(final int level) {
		return this._level.includes(level);
	}

	private boolean acceptsCharType(final int type) {
		return this._classes.length <= 0 || Arrays.binarySearch(this._classes, type) >= 0;
	}

	private boolean acceptsActionName(final String name) {
		return this._name == null || name.equals(this._name);
	}

	private boolean acceptsQuest(final L1PcInstance pc) {
		if (this._questId == -1) {
			return true;
		}
		if (this._questStep == -1) {
			return pc.getQuest().get_step(this._questId) > 0;
		}
		return pc.getQuest().get_step(this._questId) == this._questStep;
	}

	@Override
	public boolean acceptsRequest(final String actionName, final L1PcInstance pc, final L1Object obj) {
		return this.acceptsNpcId(obj) && this.acceptsLevel(pc.getLevel()) && this.acceptsQuest(pc)
				&& this.acceptsCharType(pc.getType()) && this.acceptsActionName(actionName);
	}

	@Override
	public abstract L1NpcHtml execute(final String p0, final L1PcInstance p1, final L1Object p2, final byte[] p3);

	@Override
	public abstract void execute(final String p0, final String p1);

	@Override
	public L1NpcHtml executeWithAmount(final String actionName, final L1PcInstance pc, final L1Object obj,
			final long amount) {
		return null;
	}
}
