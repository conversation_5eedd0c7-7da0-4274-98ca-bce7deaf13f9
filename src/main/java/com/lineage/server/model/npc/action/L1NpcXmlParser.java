package com.lineage.server.model.npc.action;

import java.util.Iterator;
import org.w3c.dom.NodeList;
import com.lineage.server.utils.IterableElementList;
import java.util.ArrayList;
import java.util.List;
import org.w3c.dom.Element;
import java.util.HashMap;
import java.util.Map;

public class L1NpcXmlParser {
	private static final Map<String, Integer> _questIds;

	static {
		_questIds = new HashMap();
	}

	public static List<L1NpcAction> listActions(final Element element) {
		final List<L1NpcAction> result = new ArrayList();
		final NodeList list = element.getChildNodes();
		final Iterator<Element> iterator = new IterableElementList(list).iterator();
		while (iterator.hasNext()) {
			final Element elem = iterator.next();
			final L1NpcAction action = L1NpcActionFactory.newAction(elem);
			if (action != null) {
				result.add(action);
			}
		}
		return result;
	}

	public static Element getFirstChildElementByTagName(final Element element, final String tagName) {
		final IterableElementList list = new IterableElementList(element.getElementsByTagName(tagName));
		final Iterator<Element> iterator = list.iterator();
		if (iterator.hasNext()) {
			final Element elem = iterator.next();
			return elem;
		}
		return null;
	}

	public static int getIntAttribute(final Element element, final String name, final int defaultValue) {
		int result = defaultValue;
		try {
			result = Integer.valueOf(element.getAttribute(name)).intValue();
		} catch (NumberFormatException ex) {
		}
		return result;
	}

	public static boolean getBoolAttribute(final Element element, final String name, final boolean defaultValue) {
		boolean result = defaultValue;
		final String value = element.getAttribute(name);
		if (!value.equals("")) {
			result = Boolean.valueOf(value).booleanValue();
		}
		return result;
	}

	public static int parseQuestId(final String questId) {
		if (questId.equals("")) {
			return -1;
		}
		final Integer result = L1NpcXmlParser._questIds.get(questId.toLowerCase());
		if (result == null) {
			throw new IllegalArgumentException();
		}
		return result.intValue();
	}

	public static int parseQuestStep(final String questStep) {
		if (questStep.equals("")) {
			return -1;
		}
		if (questStep.equalsIgnoreCase("End")) {
			return 255;
		}
		return Integer.parseInt(questStep);
	}
}
