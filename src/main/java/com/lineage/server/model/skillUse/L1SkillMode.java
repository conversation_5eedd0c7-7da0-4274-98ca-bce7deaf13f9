package com.lineage.server.model.skillUse;

import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.L1Character;
import com.lineage.server.model.Instance.L1PcInstance;

public abstract class L1SkillMode {
	public abstract int start(final L1PcInstance p0, final L1Character p1, final int p2, final int p3, final int p4,
			final String p5) throws Exception;

	public abstract int start(final L1NpcInstance p0, final L1Character p1, final int p2, final int p3, final int p4,
			final String p5) throws Exception;

	public void useMode(final L1Character user, final int skillid) {
	}

	public void useLoc(final L1Character user, final L1Character targ, final int skillid) {
	}

	public abstract void stop(final L1Character p0) throws Exception;
}
