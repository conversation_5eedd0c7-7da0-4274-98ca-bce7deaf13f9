package com.lineage.server.model;

import java.util.HashMap;
import com.lineage.server.datatables.lock.CharacterQuestReading;
import com.lineage.server.datatables.ServerQuestMobTable;
import com.lineage.data.event.QuestMobSet;
import org.apache.commons.logging.LogFactory;
import com.lineage.server.templates.CharQuest;
import java.util.Map;
import com.lineage.server.model.Instance.L1PcInstance;
import org.apache.commons.logging.Log;

public class L1PcQuest {
	private static final Log _log;
	public static final int QUEST_OILSKINMANT = 11;
	public static final int QUEST_DOROMOND = 20;
	public static final int QUEST_RUBA = 21;
	public static final int QUEST_AREX = 22;
	public static final int QUEST_LUKEIN1 = 23;
	public static final int QUEST_TBOX1 = 24;
	public static final int QUEST_TBOX2 = 25;
	public static final int QUEST_TBOX3 = 26;
	public static final int QUEST_SIMIZZ = 27;
	public static final int QUEST_DOIL = 28;
	public static final int QUEST_RUDIAN = 29;
	public static final int QUEST_RESTA = 30;
	public static final int QUEST_CADMUS = 31;
	public static final int QUEST_KAMYLA = 32;
	public static final int QUEST_CRYSTAL = 33;
	public static final int QUEST_LIZARD = 34;
	public static final int QUEST_KEPLISHA = 35;
	public static final int QUEST_DESIRE = 36;
	public static final int QUEST_SHADOWS = 37;
	public static final int QUEST_TOSCROLL = 39;
	public static final int QUEST_MOONOFLONGBOW = 40;
	public static final int QUEST_GENERALHAMELOFRESENTMENT = 41;
	public static final int QUEST_NOT = 0;
	public static final int QUEST_END = 255;
	public static final int QUEST_TUTOR = 300;
	public static final int QUEST_TUTOR2 = 304;
	public static final int QUEST_MARRY = 74;
	public static final int QUEST_SLOT59 = 81;
	public static final int QUEST_SLOT76 = 79;
	public static final int QUEST_SLOT81 = 80;
	public static final int QUEST_BOOKMARK = 82;
	public static final int QUEST_BaseCon25 = 85;
	public static final int QUEST_BaseCon35 = 86;
	public static final int QUEST_BaseCon45 = 87;
	public static final int QUEST_BaseWis25 = 88;
	public static final int QUEST_BaseWis35 = 89;
	public static final int QUEST_BaseWis45 = 90;
	public static final int QUEST_HAMO = 63;
	private L1PcInstance _owner;
	private Map<Integer, CharQuest> _quest;

	static {
		_log = LogFactory.getLog(L1PcQuest.class);
	}

	public L1PcQuest(final L1PcInstance owner) {
		this._owner = null;
		this._quest = null;
		this._owner = owner;
	}

	public L1PcInstance get_owner() {
		return this._owner;
	}

	public int get_step(final int quest_id) {
		try {
			final CharQuest step = this._quest.get(new Integer(quest_id));
			if (step == null) {
				return 0;
			}
			return step.get_quest_step();
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
			return 0;
		}
	}

	public void set_step(final int quest_id, final int step) {
		try {
			CharQuest quest = this._quest.get(new Integer(quest_id));
			if (quest == null) {
				quest = new CharQuest();
				quest.set_quest_step(step);
				if (QuestMobSet.START) {
					quest.set_mob_count(ServerQuestMobTable.get().getMobCount(quest_id, step));
				} else {
					quest.set_mob_count(null);
				}
				CharacterQuestReading.get().storeQuest(this._owner.getId(), quest_id, quest);
			} else {
				quest.set_quest_step(step);
				CharacterQuestReading.get().updateQuest(this._owner.getId(), quest_id, quest);
			}
			this._quest.put(new Integer(quest_id), quest);
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
	}

	public void set_end(final int quest_id) {
		try {
			final CharQuest quest = this._quest.get(new Integer(quest_id));
			if (quest != null) {
				quest.set_quest_step(255);
				CharacterQuestReading.get().updateQuest(this._owner.getId(), quest_id, quest);
			}
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
	}

	public boolean isStart(final int quest_id) {
		try {
			final int step = this.get_step(quest_id);
			if (step > 0 && step < 255) {
				return true;
			}
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
		return false;
	}

	public boolean isEnd(final int quest_id) {
		try {
			if (this.get_step(quest_id) == 255) {
				return true;
			}
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
		return false;
	}

	public void load() {
		try {
			this._quest = CharacterQuestReading.get().get(this._owner.getId());
			if (this._quest == null) {
				this._quest = new HashMap();
			}
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
	}

	public void set_step(final int quest_id, final int step, final int clear) {
		try {
			CharQuest quest = this._quest.get(new Integer(quest_id));
			if (quest == null) {
				quest = new CharQuest();
				quest.set_quest_step(step);
				if (QuestMobSet.START) {
					quest.set_mob_count(ServerQuestMobTable.get().getMobCount(quest_id, step));
				} else {
					quest.set_mob_count(null);
				}
				CharacterQuestReading.get().storeQuest(this._owner.getId(), quest_id, quest, clear);
			} else {
				quest.set_quest_step(step);
				CharacterQuestReading.get().updateQuest(this._owner.getId(), quest_id, quest);
			}
			this._quest.put(new Integer(quest_id), quest);
		} catch (Exception e) {
			L1PcQuest._log.error(e.getLocalizedMessage(), e);
		}
	}

	public int[] get_mob_count(final int quest_id) {
		return this._quest.get(new Integer(quest_id)).get_mob_count();
	}

	public void add_mob_count(final int quest_id, final int nob) {
		final CharQuest quest = this._quest.get(new Integer(quest_id));
		if (quest != null) {
			quest.add_mob_count(nob);
			CharacterQuestReading.get().updateQuest(this._owner.getId(), quest_id, quest);
		}
	}

	public void set_mob_count(final int quest_id, final int step) {
		final CharQuest quest = this._quest.get(new Integer(quest_id));
		if (quest != null) {
			quest.set_mob_count(ServerQuestMobTable.get().getMobCount(quest_id, step));
			CharacterQuestReading.get().updateQuest(this._owner.getId(), quest_id, quest);
		}
	}
}
