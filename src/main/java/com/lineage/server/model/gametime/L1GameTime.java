package com.lineage.server.model.gametime;

import java.text.SimpleDateFormat;
import com.lineage.server.utils.RangeInt;
import java.sql.Time;
import java.util.TimeZone;
import java.util.Calendar;

public class L1GameTime {
	private static final long BASE_TIME_IN_MILLIS_REAL = 1057233600000L;
	private final int _time;
	private final Calendar _calendar;

	private Calendar makeCalendar(final int time) {
		final Calendar cal = Calendar.getInstance(TimeZone.getTimeZone("UTC"));
		cal.setTimeInMillis(0L);
		cal.add(13, time);
		return cal;
	}

	private L1GameTime(final int time) {
		this._time = time;
		this._calendar = this.makeCalendar(time);
	}

	public static L1GameTime valueOf(final long timeMillis) {
		final long t1 = timeMillis - 1057233600000L;
		if (t1 < 0L) {
			throw new IllegalArgumentException();
		}
		final int t2 = (int) (t1 * 6L / 1000L);
		final int t3 = t2 % 3;
		return new L1GameTime(t2 - t3);
	}

	public static L1GameTime fromSystemCurrentTime() {
		return valueOf(System.currentTimeMillis());
	}

	public static L1GameTime valueOfGameTime(final Time time) {
		final long t = time.getTime() + TimeZone.getDefault().getRawOffset();
		return new L1GameTime((int) (t / 1000L));
	}

	public Time toTime() {
		final int t = this._time % 86400;
		return new Time(t * 1000L - TimeZone.getDefault().getRawOffset());
	}

	public int get(final int field) {
		return this._calendar.get(field);
	}

	public int getSeconds() {
		return this._time;
	}

	public Calendar getCalendar() {
		return (Calendar) this._calendar.clone();
	}

	public boolean isNight() {
		final int hour = this._calendar.get(11);
		return !RangeInt.includes(hour, 6, 17);
	}

	@Override
	public String toString() {
		final SimpleDateFormat f = new SimpleDateFormat("yyyy.MM.dd G 'at' HH:mm:ss z");
		f.setTimeZone(this._calendar.getTimeZone());
		return String.valueOf(f.format(this._calendar.getTime())) + "(" + this.getSeconds() + ")";
	}
}
