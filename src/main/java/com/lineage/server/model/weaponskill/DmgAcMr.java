package com.lineage.server.model.weaponskill;

import com.lineage.server.model.L1AttackList;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1MonsterInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.model.L1Character;
import org.apache.commons.logging.LogFactory;
import java.util.Random;
import org.apache.commons.logging.Log;

public class DmgAcMr {
	private static final Log _log;
	private static final Random _random;

	static {
		_log = LogFactory.getLog(DmgAcMr.class);
		_random = new Random();
	}

	public static int calcDefense(final L1Character target) {
		try {
			if (target instanceof L1PcInstance) {
				final L1PcInstance targetPc = (L1PcInstance) target;
				final int ac = Math.max(0, 10 - targetPc.getAc());
				final int acDefMax = targetPc.getClassFeature().getAcDefenseMax(ac);
				if (acDefMax != 0) {
					final int srcacd = Math.max(1, acDefMax >> 3);
					return DmgAcMr._random.nextInt(acDefMax) + srcacd;
				}
			} else if (target instanceof L1MonsterInstance) {
				final L1MonsterInstance targetNpc = (L1MonsterInstance) target;
				final int damagereduction = targetNpc.getNpcTemplate().get_damagereduction();
				final int srcac = targetNpc.getAc();
				final int ac2 = Math.max(0, 10 - srcac);
				final int acDefMax2 = ac2 / 7;
				if (acDefMax2 != 0) {
					final int srcacd2 = Math.max(1, acDefMax2);
					return DmgAcMr._random.nextInt(acDefMax2) + srcacd2 + damagereduction;
				}
			}
		} catch (Exception e) {
			DmgAcMr._log.error(e.getLocalizedMessage(), e);
		}
		return 0;
	}

	public static double calcMrDefense(final L1PcInstance pc, final L1Character target, double dmg,
			final L1ItemInstance weapon) {
		if (weapon.getItem().getType1() == 20) {
			return dmg;
		}
		int TotalMagicHit = 0;
		final Integer magichit = L1AttackList.INTH.get(Integer.valueOf(pc.getInt()));
		if (magichit != null) {
			TotalMagicHit += magichit.intValue();
		}
		if (pc.getBaseInt() >= 25 && pc.getBaseInt() <= 44) {
			TotalMagicHit += (pc.getBaseInt() - 15) / 10;
		} else if (pc.getBaseInt() >= 45) {
			TotalMagicHit += 5;
		}
		TotalMagicHit += pc.getOriginalMagicHit();
		final int mr = getTargetMr(target);
		double mrFloor = 0.0;
		if (mr < 100) {
			mrFloor = Math.floor((mr - TotalMagicHit) / 2);
		} else if (mr >= 100) {
			mrFloor = Math.floor((mr - TotalMagicHit) / 10);
		}
		double mrCoefficient = 0.0;
		if (mr < 100) {
			mrCoefficient = 1.0 - 0.01 * mrFloor;
		} else if (mr >= 100) {
			mrCoefficient = 0.6 - 0.01 * mrFloor;
		}
		dmg *= mrCoefficient;
		return dmg;
	}

	private static int getTargetMr(final L1Character target) {
		int mr = 0;
		if (target instanceof L1PcInstance) {
			final L1PcInstance targetPc = (L1PcInstance) target;
			mr = targetPc.getMr();
			switch (targetPc.guardianEncounter()) {
			case 0: {
				mr += 3;
				break;
			}
			case 1: {
				mr += 6;
				break;
			}
			case 2: {
				mr += 9;
				break;
			}
			}
		} else if (target instanceof L1MonsterInstance) {
			final L1MonsterInstance targetNpc = (L1MonsterInstance) target;
			mr = targetNpc.getMr();
		}
		return mr;
	}

	public static double getDamage(final L1PcInstance pc) {
		double dmg = 0.0;
		final int spByItem = pc.getSp() - pc.getTrueSp();
		final int intel = pc.getInt();
		final int charaIntelligence = pc.getInt() + spByItem - 12;
		double coefficientA = 1.0 + 0.09375 * charaIntelligence;
		if (coefficientA < 1.0) {
			coefficientA = 1.0;
		}
		double coefficientB = 0.0;
		if (intel > 25) {
			coefficientB = 1.625;
		} else if (intel <= 12) {
			coefficientB = 0.78;
		} else {
			coefficientB = intel * 0.065;
		}
		double coefficientC = 0.0;
		if (intel > 25) {
			coefficientC = 25.0;
		} else if (intel <= 12) {
			coefficientC = 12.0;
		} else {
			coefficientC = intel;
		}
		double bsk = 0.0;
		if (pc.hasSkillEffect(55)) {
			bsk = 0.1;
		}
		dmg = (DmgAcMr._random.nextInt(6) + 1 + 7) * (1.0 + bsk) * coefficientA * coefficientB / 10.5 * coefficientC;
		return dmg;
	}

	public static double getSpInt(final L1PcInstance pc) {
		final int spByItem = pc.getSp() - pc.getTrueSp();
		final int intel = pc.getInt();
		return (spByItem + intel) * 3;
	}

	public static double calcMrDefense2(final L1PcInstance pc, final L1Character target, double dmg,
			final L1ItemInstance weapon) {
		final int mr = getTargetMr(target);
		double mrFloor = 0.0;
		if (mr < 100) {
			mrFloor = Math.floor((mr - pc.getOriginalMagicHit()) / 2);
		} else if (mr >= 100) {
			mrFloor = Math.floor((mr - pc.getOriginalMagicHit()) / 10);
		}
		double mrCoefficient = 0.0;
		if (mr < 100) {
			mrCoefficient = 1.0 - 0.01 * mrFloor;
		} else if (mr >= 100) {
			mrCoefficient = 0.6 - 0.01 * mrFloor;
		}
		dmg *= mrCoefficient;
		return dmg;
	}

	public static double getDamageStr(final L1PcInstance pc) {
		double dmg = 0.0;
		final int intel = pc.getStr();
		final int charaIntelligence = pc.getStr();
		double coefficientA = 1.0 + 0.09375 * charaIntelligence;
		if (coefficientA < 1.0) {
			coefficientA = 1.0;
		}
		double coefficientB = 0.0;
		if (intel > 25) {
			coefficientB = 1.625;
		} else if (intel <= 12) {
			coefficientB = 0.78;
		} else {
			coefficientB = intel * 0.065;
		}
		double coefficientC = 0.0;
		if (intel > 25) {
			coefficientC = 25.0;
		} else if (intel <= 12) {
			coefficientC = 12.0;
		} else {
			coefficientC = intel;
		}
		double bsk = 0.0;
		if (pc.hasSkillEffect(55)) {
			bsk = 0.1;
		}
		dmg = (DmgAcMr._random.nextInt(6) + 1 + 7) * (1.0 + bsk) * coefficientA * coefficientB / 10.5 * coefficientC;
		return dmg;
	}

	public static double getDamageDex(final L1PcInstance pc) {
		double dmg = 0.0;
		final int intel = pc.getDex();
		final int charaIntelligence = pc.getDex();
		double coefficientA = 1.0 + 0.09375 * charaIntelligence;
		if (coefficientA < 1.0) {
			coefficientA = 1.0;
		}
		double coefficientB = 0.0;
		if (intel > 25) {
			coefficientB = 1.625;
		} else if (intel <= 12) {
			coefficientB = 0.78;
		} else {
			coefficientB = intel * 0.065;
		}
		double coefficientC = 0.0;
		if (intel > 25) {
			coefficientC = 25.0;
		} else if (intel <= 12) {
			coefficientC = 12.0;
		} else {
			coefficientC = intel;
		}
		double bsk = 0.0;
		if (pc.hasSkillEffect(55)) {
			bsk = 0.1;
		}
		dmg = (DmgAcMr._random.nextInt(6) + 1 + 7) * (1.0 + bsk) * coefficientA * coefficientB / 10.5 * coefficientC;
		return dmg;
	}

	public static double getDamageInt(final L1PcInstance pc) {
		double dmg = 0.0;
		final int intel = pc.getInt();
		final int charaIntelligence = pc.getInt();
		double coefficientA = 1.0 + 0.09375 * charaIntelligence;
		if (coefficientA < 1.0) {
			coefficientA = 1.0;
		}
		double coefficientB = 0.0;
		if (intel > 25) {
			coefficientB = 1.625;
		} else if (intel <= 12) {
			coefficientB = 0.78;
		} else {
			coefficientB = intel * 0.065;
		}
		double coefficientC = 0.0;
		if (intel > 25) {
			coefficientC = 25.0;
		} else if (intel <= 12) {
			coefficientC = 12.0;
		} else {
			coefficientC = intel;
		}
		double bsk = 0.0;
		if (pc.hasSkillEffect(55)) {
			bsk = 0.1;
		}
		dmg = (DmgAcMr._random.nextInt(6) + 1 + 7) * (1.0 + bsk) * coefficientA * coefficientB / 10.5 * coefficientC;
		return dmg;
	}
}
