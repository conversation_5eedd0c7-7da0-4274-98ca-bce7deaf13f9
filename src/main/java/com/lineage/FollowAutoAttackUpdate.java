package com.lineage;

import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.datatables.lock.CharSkillReading;
import com.lineage.server.model.L1Teleport;
import com.lineage.server.utils.FaceToFace;
import com.add.L1PcUnlock;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.logging.Logger;

public class FollowAutoAttackUpdate {
	private static Logger _log;
	private static FollowAutoAttackUpdate _instance;

	static {
		_log = Logger.getLogger(FollowAutoAttackUpdate.class.getName());
	}

	public static FollowAutoAttackUpdate getInstance() {
		if (FollowAutoAttackUpdate._instance == null) {
			FollowAutoAttackUpdate._instance = new FollowAutoAttackUpdate();
		}
		return FollowAutoAttackUpdate._instance;
	}

	public boolean PcCommand(final L1PcInstance _pc, final String cmd) {
		if (cmd.equalsIgnoreCase("followatk")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("您不是高寵狀態無法設定。"));
				return false;
			}
			if (_pc.getfollowatk()) {
				_pc.setfollowatk(false);
				if (_pc.isActived()) {
					_pc.setActived(false);
					L1PcUnlock.Pc_Unlock(_pc);
					if (_pc.get_fwgj() > 0) {
						_pc.setlslocx(0);
						_pc.setlslocy(0);
						_pc.set_fwgj(0);
					}
					_pc.killSkillEffectTimer(8853);
				}
				_pc.sendPackets(new S_SystemMessage("高寵自動打怪：關閉"));
			} else {
				_pc.setfollowatk(true);
				if (!_pc.isActived()) {
					_pc.startAI();
				}
				_pc.sendPackets(new S_SystemMessage("高寵自動打怪：開啟"));
			}
		} else if (cmd.equalsIgnoreCase("followatkmagic")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("您不是高寵狀態無法設定。"));
				return false;
			}
			if (_pc.getfollowatkmagic()) {
				_pc.setfollowatkmagic(false);
				_pc.sendPackets(new S_SystemMessage("高寵自動打怪施放攻擊魔法：關閉"));
			} else {
				_pc.setfollowatkmagic(true);
				_pc.sendPackets(new S_SystemMessage("高寵自動打怪施放攻擊魔法：開啟"));
			}
		} else if (cmd.equals("followmecheck")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.isfollowcheck()) {
				_pc.setfollowcheck(false);
				_pc.set_followmaster(null);
				_pc.setfollow(false);
				_pc.sendPackets(new S_SystemMessage("[關閉]禁止高寵跟隨狀態"));
				L1PcUnlock.Pc_Unlock(_pc);
				this.followpc(_pc);
			} else {
				_pc.setfollowcheck(true);
				_pc.sendPackets(new S_SystemMessage("[開啟]允許高寵跟隨狀態"));
				this.followpc(_pc);
			}
		} else if (cmd.equals("followme")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			final L1PcInstance target = FaceToFace.faceToFace(_pc);
			if (target != null) {
				if (!target.isfollowcheck()) {
					_pc.sendPackets(new S_ServerMessage("高寵對象目前關閉跟隨狀態中。"));
					return false;
				}
				if (!_pc.isfollowcheck()) {
					_pc.sendPackets(new S_ServerMessage("您目前關閉高寵狀態,請先開啟。"));
					return false;
				}
				if (target.get_followmaster() != null) {
					_pc.sendPackets(new S_ServerMessage("你指定的對象已有追隨者。"));
					return false;
				}
				if (_pc.get_followmaster() != null) {
					_pc.sendPackets(new S_ServerMessage("你已有追隨者。"));
					return false;
				}
				if (target.isfollow()) {
					_pc.sendPackets(new S_ServerMessage("你指定的對象已有追隨者。"));
					return false;
				}
				if (_pc.isfollow()) {
					_pc.sendPackets(new S_ServerMessage("你已有追隨者,請重新關閉再開啟。"));
					return false;
				}
				_pc.set_followmaster(target);
				target.setfollow(true);
				_pc.sendPackets(new S_ServerMessage("您成為:[" + target.getName() + "]高寵補助"));
				target.sendPackets(new S_ServerMessage("[" + _pc.getName() + " ]成為您的高寵補助"));
			}
		} else if (cmd.equals("followmeclose")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.hasSkillEffect(95414)) {
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_SystemMessage("您尚未有跟隨主人對象"));
				return false;
			}
			_pc.setSkillEffect(95414, 10000);
			_pc.set_followmaster(null);
			_pc.set_followstep(0);
			_pc.setfollow(false);
			L1PcUnlock.Pc_Unlock(_pc);
			L1Teleport.teleport(_pc, _pc.getLocation(), _pc.getHeading(), false);
			_pc.sendPackets(new S_SystemMessage("[關閉]高寵跟隨輔助"));
		} else if (cmd.equals("followmeskill")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.getfollowskilltype() == 0) {
				_pc.setfollowskilltype(1);
			} else if (_pc.getfollowskilltype() == 1) {
				_pc.setfollowskilltype(2);
			} else if (_pc.getfollowskilltype() == 2) {
				_pc.setfollowskilltype(3);
			} else if (_pc.getfollowskilltype() == 3) {
				_pc.setfollowskilltype(4);
			} else if (_pc.getfollowskilltype() == 4) {
				_pc.setfollowskilltype(0);
			}
			if (_pc.getfollowskilltype() == 1 && !CharSkillReading.get().spellCheck(_pc.getId(), 1)) {
				_pc.setfollowskilltype(0);
				_pc.sendPackets(new S_SystemMessage("您尚未學習治癒術"));
				this.followpc(_pc);
			}
			if (_pc.getfollowskilltype() == 2 && !CharSkillReading.get().spellCheck(_pc.getId(), 19)) {
				_pc.setfollowskilltype(0);
				_pc.sendPackets(new S_SystemMessage("您尚未學習治癒術"));
				this.followpc(_pc);
			}
			if (_pc.getfollowskilltype() == 3 && !CharSkillReading.get().spellCheck(_pc.getId(), 35)) {
				_pc.setfollowskilltype(0);
				_pc.sendPackets(new S_SystemMessage("您尚未學習治癒術"));
				this.followpc(_pc);
			}
			if (_pc.getfollowskilltype() == 4 && !CharSkillReading.get().spellCheck(_pc.getId(), 164)) {
				_pc.setfollowskilltype(0);
				_pc.sendPackets(new S_SystemMessage("您尚未學習治癒術"));
				this.followpc(_pc);
			}
			this.followpc(_pc);
		} else if (cmd.equals("followmeHp1")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.getfollowskillhp() >= 100) {
				return false;
			}
			_pc.setfollowskillhp(_pc.getfollowskillhp() + 5);
			this.followpc(_pc);
		} else if (cmd.equals("followmeHp2")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.getfollowskillhp() == 0) {
				return false;
			}
			_pc.setfollowskillhp(_pc.getfollowskillhp() - 5);
			this.followpc(_pc);
		} else if (cmd.equalsIgnoreCase("skill1")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 26)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習體魄術"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill26()) {
				_pc.setfollowskill26(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:體魄術"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill26(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:體魄術"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill2")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 42)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習通暢術"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill42()) {
				_pc.setfollowskill42(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:通暢術"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill42(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:通暢術"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill3")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 55)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習狂暴術"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (!_pc.isWizard()) {
				_pc.sendPackets(new S_SystemMessage("您不是法師,無法開啟狂暴術"));
				return false;
			}
			if (_pc.isfollowskill55()) {
				_pc.setfollowskill55(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:狂暴術"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill55(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:狂暴術"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill4")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 68)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習聖結界"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill68()) {
				_pc.setfollowskill68(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:聖結界"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill68(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:聖結界"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill6")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 79)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習靈魂昇華"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (!_pc.isWizard()) {
				_pc.sendPackets(new S_SystemMessage("您不是法師,無法開啟靈魂昇華"));
				return false;
			}
			if (_pc.isfollowskill79()) {
				_pc.setfollowskill79(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:靈魂昇華"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill79(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:靈魂昇華"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill7")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 148)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習火焰武器"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill148()) {
				_pc.setfollowskill148(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:火焰武器"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill148(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:火焰武器"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill8")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 151)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習大地防護"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill151()) {
				_pc.setfollowskill151(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:大地防護"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill151(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:大地防護"));
				this.followpc(_pc);
			}
		} else if (cmd.equalsIgnoreCase("skill9")) {
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 149)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習風之神射"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill149()) {
				_pc.setfollowskill149(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:風之神射"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill149(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:風之神射"));
				this.followpc(_pc);
			}
		} else {
			if (!cmd.equalsIgnoreCase("skill10")) {
				return false;
			}
			final L1ItemInstance AiITEM = _pc.getInventory().findItemId(95182);
			if (AiITEM == null) {
				_pc.sendPackets(new S_SystemMessage("休想竄改對話檔,我已經紀錄你了。"));
				return false;
			}
			if (!CharSkillReading.get().spellCheck(_pc.getId(), 158)) {
				_pc.sendPackets(new S_SystemMessage("您尚未學習生命之泉"));
				return false;
			}
			if (_pc.get_followmaster() == null) {
				_pc.sendPackets(new S_ServerMessage("你不是高寵無法使用此功能。"));
				return false;
			}
			if (_pc.isfollowskill158()) {
				_pc.setfollowskill158(false);
				_pc.sendPackets(new S_SystemMessage("關閉自動補助技能:生命之泉"));
				this.followpc(_pc);
			} else {
				_pc.setfollowskill158(true);
				_pc.sendPackets(new S_SystemMessage("開啟自動補助技能:生命之泉"));
				this.followpc(_pc);
			}
		}
		return true;
	}

	public void followpc(final L1PcInstance _pc) {
		try {
			String type1 = "尚未設置";
			String type2 = "0";
			String type3 = "禁止跟隨";
			String type4 = "關閉";
			String type5 = "關閉";
			String type6 = "關閉";
			String type7 = "關閉";
			String type8 = "關閉";
			String type9 = "關閉";
			String type10 = "關閉";
			String type11 = "關閉";
			String type12 = "關閉";
			final String type13 = "關閉";
			if (_pc.getfollowskilltype() == 0) {
				type1 = "尚未設置";
			} else if (_pc.getfollowskilltype() == 1) {
				type1 = "初級治癒術";
			} else if (_pc.getfollowskilltype() == 2) {
				type1 = "中級治癒術";
			} else if (_pc.getfollowskilltype() == 3) {
				type1 = "高級治癒術";
			} else if (_pc.getfollowskilltype() == 4) {
				type1 = "生命的祝福";
			}
			if (_pc.getfollowskillhp() > 0) {
				type2 = new StringBuilder().append(_pc.getfollowskillhp()).toString();
			}
			if (_pc.isfollowcheck()) {
				type3 = "允許跟隨";
			}
			if (_pc.isfollowskill26()) {
				type4 = "開啟";
			}
			if (_pc.isfollowskill42()) {
				type5 = "開啟";
			}
			if (_pc.isfollowskill55()) {
				type6 = "開啟";
			}
			if (_pc.isfollowskill68()) {
				type7 = "開啟";
			}
			if (_pc.isfollowskill79()) {
				type8 = "開啟";
			}
			if (_pc.isfollowskill148()) {
				type9 = "開啟";
			}
			if (_pc.isfollowskill151()) {
				type10 = "開啟";
			}
			if (_pc.isfollowskill149()) {
				type11 = "開啟";
			}
			if (_pc.isfollowskill158()) {
				type12 = "開啟";
			}
			_pc.sendPackets(new S_NPCTalkReturn(_pc.getId(), "Lh8", new String[] { type1, type2, type3, type4, type5,
					type6, type7, type8, type9, type10, type11, type12, type13 }));
		} catch (Exception ex) {
		}
	}
}
