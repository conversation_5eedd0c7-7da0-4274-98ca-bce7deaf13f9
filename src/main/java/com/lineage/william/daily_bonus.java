package com.lineage.william;

import java.util.StringTokenizer;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.server.utils.SQLUtil;
import com.lineage.DatabaseFactory;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.serverpackets.S_NPCTalkReturn;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.Server;
import java.util.ArrayList;

public class daily_bonus {
	private static ArrayList<ArrayList<Object>> aData17;
	private static boolean NO_MORE_GET_DATA17;
	public static final String TOKEN = ",";

	static {
		aData17 = new ArrayList();
		NO_MORE_GET_DATA17 = false;
	}

	public static void main(final String[] a) {
		try {
			while (true) {
				Server.main(null);
			}
		} catch (Exception ex) {
		}
	}

	public static void dailybonus(final L1PcInstance pc) {
		ArrayList<?> aTempData = null;
		if (!daily_bonus.NO_MORE_GET_DATA17) {
			daily_bonus.NO_MORE_GET_DATA17 = true;
			getData17();
		}
		int i = 0;
		while (i < daily_bonus.aData17.size()) {
			aTempData = daily_bonus.aData17.get(i);
			if (pc.getday() == ((Integer) aTempData.get(0)).intValue()) {
				if ((int[]) aTempData.get(1) != null && (int[]) aTempData.get(2) != null) {
					final int[] giveMaterials = (int[]) aTempData.get(1);
					final int[] giveCounts = (int[]) aTempData.get(2);
					int l = 0;
					while (l < giveMaterials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(giveMaterials[l]);
						if (item.isStackable()) {
							item.setCount(giveCounts[l]);
						} else {
							item.setCount(1L);
						}
						if (item != null) {
							if (pc.getInventory().checkAddItem(item, giveCounts[l]) != 0) {
								return;
							}
							pc.getInventory().storeItem(item);
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
						}
						++l;
					}
				}
				pc.getQuest().set_step(8991, 1);
				pc.sendPackets(new S_SystemMessage((String) aTempData.get(3)));
				if (((Integer) aTempData.get(5)).intValue() != 0) {
					pc.setpcdmg(pc.getpcdmg() + ((Integer) aTempData.get(5)).intValue());
				}
				if (((Integer) aTempData.get(6)).intValue() != 0) {
					pc.setReductionDmg(pc.getReductionDmg() + ((Integer) aTempData.get(6)).intValue());
				}
				if (((Integer) aTempData.get(7)).intValue() != 0) {
					pc.get_other().set_addhp(((Integer) aTempData.get(7)).intValue());
				}
				if (((Integer) aTempData.get(8)).intValue() != 0) {
					pc.get_other().set_addmp(((Integer) aTempData.get(8)).intValue());
				}
				String type1 = "";
				if (pc.getQuest().get_step(8991) != 1) {
					type1 = "今日尚未簽到";
				} else {
					type1 = String.valueOf(pc.getday());
				}
				type1 = String.valueOf(pc.getday());
				pc.sendPackets(new S_NPCTalkReturn(pc.getId(), "dailybonus", new String[] { type1 }));
			}
			++i;
		}
	}

	private static void getData17() {
		Connection con = null;
		try {
			con = DatabaseFactory.get().getConnection();
			final Statement stat = con.createStatement();
			final ResultSet rset = stat.executeQuery("SELECT * FROM w_每日簽到控制");
			ArrayList<Object> aReturn = null;
			if (rset != null) {
				while (rset.next()) {
					aReturn = new ArrayList();
					aReturn.add(0, new Integer(rset.getInt("簽到幾天")));
					aReturn.add(1, getArray(rset.getString("簽到物品獎勵"), ",", 1));
					aReturn.add(2, getArray(rset.getString("簽到物品數量"), ",", 1));
					aReturn.add(3, rset.getString("完成簽到顯示"));
					aReturn.add(4, Integer.valueOf(0));
					aReturn.add(5, new Integer(rset.getInt("永久物攻增加")));
					aReturn.add(6, new Integer(rset.getInt("永久減傷增加")));
					aReturn.add(7, new Integer(rset.getInt("永久血量增加")));
					aReturn.add(8, new Integer(rset.getInt("永久魔量增加")));
					daily_bonus.aData17.add(aReturn);
				}
			}
			SQLUtil.close(rset);
			SQLUtil.close(stat);
			SQLUtil.close(con);
		} catch (Exception ex) {
		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
