package com.lineage.william;

import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.serverpackets.S_SPMR;

public class L1WilliamEnchantOrginal {
    private final int _id;
    private final int _itemid;
    private final int _type;
    private final int _level;
    private final byte _addStr;
    private final byte _addDex;
    private final byte _addCon;
    private final byte _addInt;
    private final byte _addWis;
    private final byte _addCha;
    private final int _addAc;
    private final int _addMaxHp;
    private final int _addMaxMp;
    private final int _addHpr;
    private final int _addMpr;
    private final int _addDmg;
    private final int _addBowDmg;
    private final int _addHit;
    private final int _addBowHit;
    private final int _addDmgReduction;
    private final int _addMr;
    private final int _addSp;
    private final int _PVPdmg;
    private final int _PVPdmgReduction;
    private final int _potion_heal;
    private final int _potion_healling;
    private final int _magic_hit;
    private final double _weaponSkillDmg;
    private final int _weaponSkillChance;

    public L1WilliamEnchantOrginal(int id, int type, int itemid, int level, byte addStr,
                                   byte addDex, byte addCon, byte addInt, byte addWis, byte addCha,
                                   int addAc, int addMaxHp, int addMaxMp, int addHpr, int addMpr,
                                   int addDmg, int addBowDmg, int addHit, int addBowHit, int addDmgReduction,
                                   int addMr, int addSp, int PVPdmg, int PVPdmgReduction, int potion_heal,
                                   int potion_healling, int magic_hit, double weaponSkillDmg, int weaponSkillChance) {
        _id = id;
        _type = type;
        _itemid = itemid;
        _level = level;
        _addStr = addStr;
        _addDex = addDex;
        _addCon = addCon;
        _addInt = addInt;
        _addWis = addWis;
        _addCha = addCha;
        _addAc = addAc;
        _addMaxHp = addMaxHp;
        _addMaxMp = addMaxMp;
        _addHpr = addHpr;
        _addMpr = addMpr;
        _addDmg = addDmg;
        _addBowDmg = addBowDmg;
        _addHit = addHit;
        _addBowHit = addBowHit;
        _addDmgReduction = addDmgReduction;
        _addMr = addMr;
        _addSp = addSp;
        _PVPdmg = PVPdmg;
        _PVPdmgReduction = PVPdmgReduction;
        _potion_heal = potion_heal;
        _potion_healling = potion_healling;
        _magic_hit = magic_hit;
        _weaponSkillDmg = weaponSkillDmg;
        _weaponSkillChance = weaponSkillChance;
    }

    public static void getAddArmorOrginal(L1PcInstance pc, L1ItemInstance item) {
        L1WilliamEnchantOrginal armorOrginal = null;
        L1WilliamEnchantOrginal armorOrginalOk = null;
        L1WilliamEnchantOrginal[] armorOrginalSize = EnchantOrginal.getInstance().getArmorList();
        int i = 0;
        while (i < armorOrginalSize.length) {
            armorOrginalOk = EnchantOrginal.getInstance().getTemplate(i);
            if (armorOrginalOk.getType() == item.getItem().getType2()
                    && armorOrginalOk.getitemid() == item.getItem().getItemId()
                    && armorOrginalOk.getLevel() == item.getEnchantLevel()) {
                armorOrginal = armorOrginalOk;
                break;
            }
            ++i;
        }
        if (armorOrginal == null) {
            return;
        }
        if (armorOrginal.getAddStr() != 0) {
            pc.addStr(armorOrginal.getAddStr());
        }
        if (armorOrginal.getAddDex() != 0) {
            pc.addDex(armorOrginal.getAddDex());
        }
        if (armorOrginal.getAddCon() != 0) {
            pc.addCon(armorOrginal.getAddCon());
        }
        if (armorOrginal.getAddInt() != 0) {
            pc.addInt(armorOrginal.getAddInt());
        }
        if (armorOrginal.getAddWis() != 0) {
            pc.addWis(armorOrginal.getAddWis());
        }
        if (armorOrginal.getAddCha() != 0) {
            pc.addCha(armorOrginal.getAddCha());
        }
        if (armorOrginal.getAddAc() != 0) {
            pc.addAc(-armorOrginal.getAddAc());
        }
        if (armorOrginal.getAddMaxHp() != 0) {
            pc.addMaxHp(armorOrginal.getAddMaxHp());
        }
        if (armorOrginal.getAddMaxMp() != 0) {
            pc.addMaxMp(armorOrginal.getAddMaxMp());
        }
        if (armorOrginal.getAddHpr() != 0) {
            pc.addHpr(armorOrginal.getAddHpr());
        }
        if (armorOrginal.getAddMpr() != 0) {
            pc.addMpr(armorOrginal.getAddMpr());
        }
        if (armorOrginal.getAddDmg() != 0) {
            pc.addDmgup(armorOrginal.getAddDmg());
        }
        if (armorOrginal.getAddHit() != 0) {
            pc.addHitup(armorOrginal.getAddHit());
        }
        if (armorOrginal.getAddBowDmg() != 0) {
            pc.addBowDmgup(armorOrginal.getAddBowDmg());
        }
        if (armorOrginal.getAddBowHit() != 0) {
            pc.addBowHitup(armorOrginal.getAddBowHit());
        }
        if (armorOrginal.getAddDmgReduction() != 0) {
            pc.addDamageReductionByArmor(armorOrginal.getAddDmgReduction());
        }
        if (armorOrginal.getAddMr() != 0) {
            pc.addMr(armorOrginal.getAddMr());
        }
        if (armorOrginal.getAddSp() != 0) {
            pc.addSp(armorOrginal.getAddSp());
        }
        if (armorOrginal.getPVPdmg() != 0) {
            pc.add_PVPdmgg(armorOrginal.getPVPdmg());
        }
        if (armorOrginal.getPVPdmgReduction() != 0) {
            pc.addPVPdmgReduction(armorOrginal.getPVPdmgReduction());
        }
        if (armorOrginal.getPotion_Heal() != 0) {
            pc.add_potion_heal(armorOrginal.getPotion_Heal());
        }
        if (armorOrginal.getPotion_Healling() != 0) {
            pc.add_potion_healling(armorOrginal.getPotion_Healling());
        }
        if (armorOrginal.getAddMagicHit() != 0) {
            pc.addOriginalMagicHit(armorOrginal.getAddMagicHit());
        }
        if (armorOrginal.getWeaponSkillDmg() != 0.0) {
            pc.setWeaponSkillDmg(armorOrginal.getWeaponSkillDmg());
        }
        if (armorOrginal.getWeaponSkillChance() != 0) {
            pc.setWeaponSkillChance(armorOrginal.getWeaponSkillChance());
        }
        pc.sendPackets(new S_SPMR(pc));
        pc.sendPackets(new S_OwnCharStatus(pc));
    }

    public static void getReductionArmorOrginal(L1PcInstance pc, L1ItemInstance item) {
        L1WilliamEnchantOrginal armorOrginal = null;
        L1WilliamEnchantOrginal armorOrginalOk = null;
        L1WilliamEnchantOrginal[] armorOrginalSize = EnchantOrginal.getInstance().getArmorList();
        int i = 0;
        while (i < armorOrginalSize.length) {
            armorOrginalOk = EnchantOrginal.getInstance().getTemplate(i);
            if (armorOrginalOk.getType() == item.getItem().getType2()
                    && armorOrginalOk.getitemid() == item.getItem().getItemId()
                    && armorOrginalOk.getLevel() == item.getEnchantLevel()) {
                armorOrginal = armorOrginalOk;
                break;
            }
            ++i;
        }
        if (armorOrginal == null) {
            return;
        }
        if (armorOrginal.getAddStr() != 0) {
            pc.addStr(-armorOrginal.getAddStr());
        }
        if (armorOrginal.getAddDex() != 0) {
            pc.addDex(-armorOrginal.getAddDex());
        }
        if (armorOrginal.getAddCon() != 0) {
            pc.addCon(-armorOrginal.getAddCon());
        }
        if (armorOrginal.getAddInt() != 0) {
            pc.addInt(-armorOrginal.getAddInt());
        }
        if (armorOrginal.getAddWis() != 0) {
            pc.addWis(-armorOrginal.getAddWis());
        }
        if (armorOrginal.getAddCha() != 0) {
            pc.addCha(-armorOrginal.getAddCha());
        }
        if (armorOrginal.getAddAc() != 0) {
            pc.addAc(armorOrginal.getAddAc());
        }
        if (armorOrginal.getAddMaxHp() != 0) {
            pc.addMaxHp(-armorOrginal.getAddMaxHp());
        }
        if (armorOrginal.getAddMaxMp() != 0) {
            pc.addMaxMp(-armorOrginal.getAddMaxMp());
        }
        if (armorOrginal.getAddHpr() != 0) {
            pc.addHpr(-armorOrginal.getAddHpr());
        }
        if (armorOrginal.getAddMpr() != 0) {
            pc.addMpr(-armorOrginal.getAddMpr());
        }
        if (armorOrginal.getAddDmg() != 0) {
            pc.addDmgup(-armorOrginal.getAddDmg());
        }
        if (armorOrginal.getAddHit() != 0) {
            pc.addHitup(-armorOrginal.getAddHit());
        }
        if (armorOrginal.getAddBowDmg() != 0) {
            pc.addBowDmgup(-armorOrginal.getAddBowDmg());
        }
        if (armorOrginal.getAddBowHit() != 0) {
            pc.addBowHitup(-armorOrginal.getAddBowHit());
        }
        if (armorOrginal.getAddDmgReduction() != 0) {
            pc.addDamageReductionByArmor(-armorOrginal.getAddDmgReduction());
        }
        if (armorOrginal.getAddMr() != 0) {
            pc.addMr(-armorOrginal.getAddMr());
        }
        if (armorOrginal.getAddSp() != 0) {
            pc.addSp(-armorOrginal.getAddSp());
        }
        if (armorOrginal.getPVPdmg() != 0) {
            pc.add_PVPdmgg(-armorOrginal.getPVPdmg());
        }
        if (armorOrginal.getPVPdmgReduction() != 0) {
            pc.addPVPdmgReduction(-armorOrginal.getPVPdmgReduction());
        }
        if (armorOrginal.getPotion_Heal() != 0) {
            pc.add_potion_heal(-armorOrginal.getPotion_Heal());
        }
        if (armorOrginal.getPotion_Healling() != 0) {
            pc.add_potion_healling(-armorOrginal.getPotion_Healling());
        }
        if (armorOrginal.getAddMagicHit() != 0) {
            pc.addOriginalMagicHit(-armorOrginal.getAddMagicHit());
        }
        if (armorOrginal.getWeaponSkillDmg() != 0.0) {
            pc.setWeaponSkillDmg(0.0);
        }
        if (armorOrginal.getWeaponSkillChance() != 0) {
            pc.setWeaponSkillChance(0);
        }
        pc.sendPackets(new S_SPMR(pc));
        pc.sendPackets(new S_OwnCharStatus(pc));
    }

    public int getId() {
        return _id;
    }

    public int getType() {
        return _type;
    }

    public int getitemid() {
        return _itemid;
    }

    public int getLevel() {
        return _level;
    }

    public byte getAddStr() {
        return _addStr;
    }

    public byte getAddDex() {
        return _addDex;
    }

    public byte getAddCon() {
        return _addCon;
    }

    public byte getAddInt() {
        return _addInt;
    }

    public byte getAddWis() {
        return _addWis;
    }

    public byte getAddCha() {
        return _addCha;
    }

    public int getAddAc() {
        return _addAc;
    }

    public int getAddMaxHp() {
        return _addMaxHp;
    }

    public int getAddMaxMp() {
        return _addMaxMp;
    }

    public int getAddHpr() {
        return _addHpr;
    }

    public int getAddMpr() {
        return _addMpr;
    }

    public int getAddDmg() {
        return _addDmg;
    }

    public int getAddBowDmg() {
        return _addBowDmg;
    }

    public int getAddHit() {
        return _addHit;
    }

    public int getAddBowHit() {
        return _addBowHit;
    }

    public int getAddDmgReduction() {
        return _addDmgReduction;
    }

    public int getAddMr() {
        return _addMr;
    }

    public int getAddSp() {
        return _addSp;
    }

    public int getPVPdmg() {
        return _PVPdmg;
    }

    public int getPVPdmgReduction() {
        return _PVPdmgReduction;
    }

    public int getPotion_Heal() {
        return _potion_heal;
    }

    public int getPotion_Healling() {
        return _potion_healling;
    }

    public int getAddMagicHit() {
        return _magic_hit;
    }

    public double getWeaponSkillDmg() {
        return _weaponSkillDmg;
    }

    public int getWeaponSkillChance() {
        return _weaponSkillChance;
    }
}
