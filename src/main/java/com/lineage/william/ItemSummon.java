package com.lineage.william;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import java.util.logging.Level;
import com.lineage.DatabaseFactory;
import java.util.HashMap;
import java.util.logging.Logger;

public class ItemSummon {
	private static Logger _log;
	private static ItemSummon _instance;
	private final HashMap<Integer, L1WilliamItemSummon> _itemIdIndex;

	static {
		_log = Logger.getLogger(ItemSummon.class.getName());
	}

	public static ItemSummon getInstance() {
		if (ItemSummon._instance == null) {
			ItemSummon._instance = new ItemSummon();
		}
		return ItemSummon._instance;
	}

	private ItemSummon() {
		this._itemIdIndex = new HashMap();
		this.loadItemSummon();
	}

	private void loadItemSummon() {
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM w_道具召喚");
			rs = pstm.executeQuery();
			this.fillItemSummon(rs);
		} catch (SQLException e) {
			ItemSummon._log.log(Level.SEVERE, "error while creating w_道具召喚 table", e);
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstm);
			SQLUtil.close(con);
		}
	}

	private void fillItemSummon(final ResultSet rs) throws SQLException {
		while (rs.next()) {
			final int item_id = rs.getInt("item_id");
			final int checkLevel = rs.getInt("checkLevel");
			final int checkClass = rs.getInt("checkClass");
			final int checkItem = rs.getInt("checkItem");
			final int hpConsume = rs.getInt("hpConsume");
			final int mpConsume = rs.getInt("mpConsume");
			final int material = rs.getInt("material");
			final int material_count = rs.getInt("material_count");
			final int summon_id = rs.getInt("summon_id");
			final int summonCost = rs.getInt("summonCost");
			final int onlyOne = rs.getInt("onlyOne");
			final int removeItem = rs.getInt("removeItem");
			final L1WilliamItemSummon Item_Summon = new L1WilliamItemSummon(item_id, checkLevel, checkClass, checkItem,
					hpConsume, mpConsume, material, material_count, summon_id, summonCost, onlyOne, removeItem);
			this._itemIdIndex.put(Integer.valueOf(item_id), Item_Summon);
		}
	}

	public L1WilliamItemSummon getTemplate(final int itemId) {
		return this._itemIdIndex.get(Integer.valueOf(itemId));
	}
}
