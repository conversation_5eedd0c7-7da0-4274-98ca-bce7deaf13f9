package com.lineage.william;

import java.util.logging.Level;
import java.util.StringTokenizer;
import com.lineage.server.utils.RandomArrayList;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.world.World;
import com.lineage.data.cmd.CreateNewItem;
import com.lineage.server.model.Instance.L1PcInstance;
import java.util.logging.Logger;

public class L1WilliamLimitedReward {
	private static Logger _log;
	private int _id;
	private boolean _activity;
	private int _check_classId;
	private int _check_level;
	private int _check_item;
	private int _check_itemCount;
	private String _surplus_msg;
	private int _surplus_msg_color;
	private String _itemId;
	private String _count;
	private String _enchantlvl;
	private int _totalCount;
	private int _appearCount;
	private int _quest_id;
	private int _quest_step;
	private String _message;
	private String _message_end;
	private boolean _isCrown;
	private boolean _isKnight;
	private boolean _isElf;
	private boolean _isWizard;
	private boolean _isDarkelf;
	private boolean _isDragonKnight;
	private boolean _isIllusionist;
	private boolean _isWarrior;
	private static final int _int8 = 128;
	private static final int _int7 = 64;
	private static final int _int6 = 32;
	private static final int _int5 = 16;
	private static final int _int4 = 8;
	private static final int _int3 = 4;
	private static final int _int2 = 2;
	private static final int _int1 = 1;
	private int _use_type;

	static {
		_log = Logger.getLogger(L1WilliamLimitedReward.class.getName());
	}

	public L1WilliamLimitedReward(final int id, final boolean activity, final int check_classId, final int check_level,
			final int check_item, final int check_itemCount, final String surplus_msg, final int surplus_msg_color,
			final String itemId, final String count, final String enchantlvl, final int totalCount,
			final int appearCount, final int quest_id, final int quest_step, final String message,
			final String message_end) {
		this._use_type = 255;
		this._id = id;
		this._activity = activity;
		this._check_classId = check_classId;
		this._check_level = check_level;
		this._check_item = check_item;
		this._check_itemCount = check_itemCount;
		this._surplus_msg = surplus_msg;
		this._surplus_msg_color = surplus_msg_color;
		this._itemId = itemId;
		this._count = count;
		this._enchantlvl = enchantlvl;
		this._totalCount = totalCount;
		this._appearCount = appearCount;
		this._quest_id = quest_id;
		this._quest_step = quest_step;
		this._message = message;
		this._message_end = message_end;
	}

	public int get_id() {
		return this._id;
	}

	public boolean isActivity() {
		return this._activity;
	}

	public void set_Activity(final boolean i) {
		this._activity = i;
	}

	public int get_check_classId() {
		return this._check_classId;
	}

	public int get_check_level() {
		return this._check_level;
	}

	public int get_check_item() {
		return this._check_item;
	}

	public int get_check_itemCount() {
		return this._check_itemCount;
	}

	public String get_surplus_msg() {
		return this._surplus_msg;
	}

	public int get_surplus_msg_color() {
		return this._surplus_msg_color;
	}

	public String get_itemId() {
		return this._itemId;
	}

	public String get_count() {
		return this._count;
	}

	public String get_enchantlvl() {
		return this._enchantlvl;
	}

	public int getTotalCount() {
		return this._totalCount;
	}

	public int get_appearCount() {
		return this._appearCount;
	}

	public void set_appearCount(final int i) {
		this._appearCount = i;
	}

	public int get_quest_id() {
		return this._quest_id;
	}

	public int get_quest_step() {
		return this._quest_step;
	}

	public String get_message() {
		return this._message;
	}

	public String get_message_end() {
		return this._message_end;
	}

	public static void check_Task_For_Item(final L1PcInstance pc, final int itemId, final int count) {
		final L1WilliamLimitedReward limitedReward = LimitedReward.getInstance().getTemplate1(itemId);
		if (limitedReward == null) {
			return;
		}
		if (!limitedReward.isActivity()) {
			return;
		}
		if (limitedReward.getTotalCount() == limitedReward.get_appearCount()) {
			return;
		}
		if (limitedReward.get_quest_id() != 0
				&& pc.getQuest().get_step(limitedReward.get_quest_id()) == limitedReward.get_quest_step()) {
			return;
		}
		limitedReward.set_use_type(limitedReward.get_check_classId());
		if (!limitedReward.is_use(pc)) {
			return;
		}
		final int codes = 0;
		if (limitedReward.get_itemId() != null && !limitedReward.get_itemId().equals("")) {
			final int[] itemGive = (int[]) getArray(limitedReward.get_itemId(), ",", 1);
			final int[] itemCount = (int[]) getArray(limitedReward.get_count(), ",", 1);
			final int[] itemEnchantlvl = (int[]) getArray(limitedReward.get_enchantlvl(), ",", 1);
			int j = 0;
			while (j < itemGive.length) {
				CreateNewItem.createNewItem(pc, itemGive[j], itemCount[j], itemEnchantlvl[j]);
				++j;
			}
		}
		if (limitedReward.get_quest_id() != 0) {
			pc.getQuest().set_step(limitedReward.get_quest_id(), limitedReward.get_quest_step());
		}
		limitedReward.set_appearCount(limitedReward.get_appearCount() + 1);
		LimitedReward.limitedRewardToList(limitedReward.get_id(), limitedReward.get_appearCount());
		if (limitedReward.get_message() != null && !limitedReward.get_message().equals("")) {
			final int qq = limitedReward.getTotalCount() - limitedReward.get_appearCount();
			if (qq <= 0) {
				World.get().broadcastPacketToAll(
						new S_ServerMessage(String.format(limitedReward.get_message_end(), new Object[0])));
			} else {
				World.get().broadcastPacketToAll(
						new S_ServerMessage(String.format(limitedReward.get_message(), Integer.valueOf(qq))));
			}
		}
	}

	public static void check_Task_For_Level(final L1PcInstance pc) {
		L1WilliamLimitedReward limitedReward = null;
		final L1WilliamLimitedReward[] limitedRewardSize = LimitedReward.getInstance().getLimitedRewardList();
		int i = 0;
		while (i < limitedRewardSize.length) {
			limitedReward = LimitedReward.getInstance().getTemplate(i);
			if (limitedReward.isActivity() && limitedReward.getTotalCount() > limitedReward.get_appearCount()
					&& (limitedReward.get_quest_id() == 0
							|| pc.getQuest().get_step(limitedReward.get_quest_id()) != limitedReward.get_quest_step())
					&& limitedReward.get_check_level() <= pc.getLevel()) {
				limitedReward.set_use_type(limitedReward.get_check_classId());
				if (limitedReward.is_use(pc)) {
					int codes = 0;
					if (limitedReward.get_surplus_msg_color() != -1) {
						codes = limitedReward.get_surplus_msg_color();
					} else {
						final int[] _codes = { 14, 7, 24, 13, 47, 4, 3, 53, 10, 1, 2, 11 };
						codes = _codes[RandomArrayList.getInt(_codes.length)];
					}
					if (limitedReward.get_itemId() != null && !limitedReward.get_itemId().equals("")) {
						final int[] itemGive = (int[]) getArray(limitedReward.get_itemId(), ",", 1);
						final int[] itemCount = (int[]) getArray(limitedReward.get_count(), ",", 1);
						final int[] itemEnchantlvl = (int[]) getArray(limitedReward.get_enchantlvl(), ",", 1);
						int j = 0;
						while (j < itemGive.length) {
							CreateNewItem.createNewItem(pc, itemGive[j], itemCount[j], itemEnchantlvl[j]);
							++j;
						}
					}
					if (limitedReward.get_quest_id() != 0) {
						pc.getQuest().set_step(limitedReward.get_quest_id(), limitedReward.get_quest_step());
					}
					limitedReward.set_appearCount(limitedReward.get_appearCount() + 1);
					LimitedReward.limitedRewardToList(i, limitedReward.get_appearCount());
					if (limitedReward.get_message() != null && !limitedReward.get_message().equals("")) {
						final int qq = limitedReward.getTotalCount() - limitedReward.get_appearCount();
						if (qq <= 0) {
							limitedReward.set_Activity(false);
							LimitedReward.limitedRewardToList_isOver(i, 0);
							World.get().broadcastPacketToAll(new S_ServerMessage(
									String.format(limitedReward.get_message_end(), Integer.valueOf(codes))));
						} else {
							World.get().broadcastPacketToAll(new S_ServerMessage(
									String.format(limitedReward.get_message(), Integer.valueOf(qq))));
						}
					}
				}
			}
			++i;
		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		if (iType == 4) {
			final short[] iReturn2 = new short[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn2[i] = Short.parseShort(sTemp);
				++i;
			}
			return iReturn2;
		}
		return null;
	}

	public int get_use_type() {
		return this._use_type;
	}

	public void set_use_type(int use_type) {
		this._use_type = use_type;
		if (use_type >= 128) {
			use_type -= 128;
			this._isWarrior = true;
		}
		if (use_type >= 64) {
			use_type -= 64;
			this._isIllusionist = true;
		}
		if (use_type >= 32) {
			use_type -= 32;
			this._isDragonKnight = true;
		}
		if (use_type >= 16) {
			use_type -= 16;
			this._isDarkelf = true;
		}
		if (use_type >= 8) {
			use_type -= 8;
			this._isWizard = true;
		}
		if (use_type >= 4) {
			use_type -= 4;
			this._isElf = true;
		}
		if (use_type >= 2) {
			use_type -= 2;
			this._isKnight = true;
		}
		if (use_type >= 1) {
			--use_type;
			this._isCrown = true;
		}
	}

	public boolean is_use(final L1PcInstance pc) {
		try {
			if (pc.isCrown() && this._isCrown) {
				return true;
			}
			if (pc.isKnight() && this._isKnight) {
				return true;
			}
			if (pc.isElf() && this._isElf) {
				return true;
			}
			if (pc.isWizard() && this._isWizard) {
				return true;
			}
			if (pc.isDarkelf() && this._isDarkelf) {
				return true;
			}
			if (pc.isDragonKnight() && this._isDragonKnight) {
				return true;
			}
			if (pc.isIllusionist() && this._isIllusionist) {
				return true;
			}
		} catch (Exception e) {
			L1WilliamLimitedReward._log.log(Level.SEVERE, e.getLocalizedMessage(), e);
		}
		return false;
	}
}
