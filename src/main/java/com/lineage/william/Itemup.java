package com.lineage.william;

import java.util.StringTokenizer;
import java.io.IOException;
import java.io.Writer;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.server.utils.SQLUtil;
import com.lineage.DatabaseFactory;
import com.lineage.server.templates.L1Item;
import java.sql.Timestamp;
import com.lineage.server.serverpackets.S_AddItem;
import com.lineage.server.datatables.lock.CharItemsReading;
import com.lineage.server.serverpackets.S_DeleteInventoryItem;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.utils.log.PlayerLogUtil;

import java.util.Random;
import java.util.ArrayList;

public class Itemup {
	private static ArrayList aData17;
	private static Itemup _instance;
	private static boolean NO_MORE_GET_DATA17;
	private static Random _random;
	public static final String TOKEN = ",";

	static {
		aData17 = new ArrayList();
		NO_MORE_GET_DATA17 = false;
		_random = new Random();
	}

	public static Itemup get() {
		if (Itemup._instance == null) {
			Itemup._instance = new Itemup();
		}
		return Itemup._instance;
	}

	public static void forresolvent(final L1PcInstance pc, final L1ItemInstance item, final int l) {
		final int itemid = item.getItemId();
		final L1ItemInstance tgitem = pc.getInventory().getItem(l);
		ArrayList aTempData = null;
		final int[] iTemp = null;
		if (!Itemup.NO_MORE_GET_DATA17) {
			Itemup.NO_MORE_GET_DATA17 = true;
			getData17();
		}
		int i = 0;
		while (i < Itemup.aData17.size()) {
			aTempData = (ArrayList) Itemup.aData17.get(i);
			if (((Integer) aTempData.get(0)).intValue() == itemid) {
				if (((Integer) aTempData.get(2)).intValue() != 0
						&& pc.getLevel() < ((Integer) aTempData.get(2)).intValue()) {
					pc.sendPackets(new S_SystemMessage("等級" + ((Integer) aTempData.get(2)).intValue() + "以上才可使用此道具。"));
					return;
				}
				if (((Integer) aTempData.get(0)).intValue() == item.getItemId()
						&& ((Integer) aTempData.get(5)).intValue() == tgitem.getItemId()) {
					if (tgitem.getBless() >= 4) {
						pc.sendPackets(new S_SystemMessage("該裝備受到封印卷軸的影響。"));
						return;
					}
					if (pc.getDoll(tgitem.getId()) != null) {
						pc.sendPackets(new S_ServerMessage(1181));
						return;
					}
					if (tgitem.isEquipped()) {
						pc.sendPackets(new S_SystemMessage("請先把裝備脫掉在使用。"));
						return;
					}
					if (!pc.getDolls().isEmpty()) {
						pc.sendPackets(new S_ServerMessage("請先解除魔法娃娃。"));
						return;
					}
					if (pc.getInventory().getSize() >= 170) {
						pc.sendPackets(new S_SystemMessage("身上物品太多無法進行升級"));
						return;
					}
					if (pc.getInventory().getWeight() / pc.getMaxWeight() * 100.0 > 90.0) {
						pc.sendPackets(new S_SystemMessage("身上負重限制無法進行升級"));
						return;
					}
					final int[] materials = (int[]) aTempData.get(3);
					final int[] counts = (int[]) aTempData.get(4);
					boolean isCreate = true;
					int j = 0;
					while (j < materials.length) {
						if (!pc.getInventory().checkItem(materials[j], counts[j])) {
							final L1Item temp = ItemTable.get().getTemplate(materials[j]);
							pc.sendPackets(new S_ServerMessage(337, String.valueOf(temp.getName()) + "("
									+ (counts[j] - pc.getInventory().countItems(temp.getItemId())) + ")"));
							isCreate = false;
						}
						++j;
					}
					if (isCreate) {
						int k = 0;
						while (k < materials.length) {
							pc.getInventory().consumeItem(materials[k], counts[k]);
							++k;
						}
						int ran = 0;
						if (pc.isGm()) {
							ran = 99;
						} else {
							ran = ((Integer) aTempData.get(6)).intValue();
						}
						if (ran >= Itemup._random.nextInt(100) + 1) {
							final L1Item l1item = ItemTable.get().getTemplate(((Integer) aTempData.get(7)).intValue());
							pc.sendPackets(new S_DeleteInventoryItem(tgitem.getId()));
							tgitem.setItemId(((Integer) aTempData.get(7)).intValue());
							tgitem.setItem(l1item);
							tgitem.setBless(l1item.getBless());
							try {
								CharItemsReading.get().updateItemId_Name(tgitem);
							} catch (Exception e) {
								e.printStackTrace();
							}
							pc.sendPackets(new S_AddItem(tgitem));
							pc.sendPackets(new S_ServerMessage(403, tgitem.getLogName()));
							pc.sendPackets(new S_SystemMessage("\\fY" + (String) aTempData.get(9)));
							升級成功("玩家 :" + pc.getName() + "使用 " + item.getName() + "對" + tgitem.getName() + "升級成功 ,時間:"
									+ new Timestamp(System.currentTimeMillis()) + ")");
							isCreate = false;
							final L1ItemInstance item2 = pc.getInventory()
									.findItemId(((Integer) aTempData.get(0)).intValue());
							pc.getInventory().removeItem(item2.getId(), 1L);
							break;
						}
						isCreate = false;
						pc.sendPackets(new S_SystemMessage((String) aTempData.get(10)));
						if (((Integer) aTempData.get(11)).intValue() == 1) {
							pc.getInventory().removeItem(tgitem.getId(), 1L);
						}
						final L1ItemInstance item3 = pc.getInventory()
								.findItemId(((Integer) aTempData.get(0)).intValue());
						pc.getInventory().removeItem(item3.getId(), 1L);
						break;
					}
				}
			}
			++i;
		}
	}

	private static void getData17() {
		Connection con = null;
		try {
			con = DatabaseFactory.get().getConnection();
			final Statement stat = con.createStatement();
			final ResultSet rset = stat.executeQuery("SELECT * FROM w_道具升級系統");
			ArrayList aReturn = null;
			final String sTemp = null;
			if (rset != null) {
				while (rset.next()) {
					aReturn = new ArrayList();
					aReturn.add(0, new Integer(rset.getInt("item")));
					aReturn.add(1, rset.getString("note"));
					aReturn.add(2, new Integer(rset.getInt("CheckLevel")));
					if (rset.getString("CheckItem") != null && !rset.getString("CheckItem").equals("")
							&& !rset.getString("CheckItem").equals("0")) {
						aReturn.add(3, getArray(rset.getString("CheckItem"), ",", 1));
					} else {
						aReturn.add(3, null);
					}
					if (rset.getString("Checkcount") != null && !rset.getString("Checkcount").equals("")
							&& !rset.getString("Checkcount").equals("0")) {
						aReturn.add(4, getArray(rset.getString("Checkcount"), ",", 1));
					} else {
						aReturn.add(4, null);
					}
					aReturn.add(5, new Integer(rset.getInt("ne_item")));
					aReturn.add(6, new Integer(rset.getInt("random")));
					aReturn.add(7, new Integer(rset.getInt("giveitem")));
					aReturn.add(8, new Integer(rset.getInt("givecount")));
					aReturn.add(9, rset.getString("Success"));
					aReturn.add(10, rset.getString("Fail"));
					aReturn.add(11, new Integer(rset.getInt("save_type")));
					Itemup.aData17.add(aReturn);
				}
			}
			SQLUtil.close(rset);
			SQLUtil.close(stat);
			SQLUtil.close(con);
		} catch (Exception ex) {
		}
	}

	public static void 升級成功(final String info) {
		PlayerLogUtil.writeLog("[升級成功]", info);
//
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[升級成功].txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
