package com.lineage.william;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.Statement;
import com.lineage.server.utils.SQLUtil;
import java.sql.SQLException;
import com.lineage.DatabaseFactory;
import com.lineage.server.model.L1Clan;
import com.lineage.server.serverpackets.S_MPUpdate;
import com.lineage.server.serverpackets.S_HPUpdate;
import com.lineage.server.serverpackets.S_OwnCharStatus2;
import com.lineage.server.serverpackets.S_OwnCharStatus;
import com.lineage.server.serverpackets.S_SPMR;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.world.WorldClan;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.Server;
import java.util.ArrayList;

public class ClanOriginal {
	private static ArrayList<ArrayList<Object>> aData;
	private static boolean BUILD_DATA;
	private static ClanOriginal _instance;
	public static final String TOKEN = ",";

	static {
		aData = new ArrayList();
		BUILD_DATA = false;
	}

	public static ClanOriginal getInstance() {
		if (ClanOriginal._instance == null) {
			ClanOriginal._instance = new ClanOriginal();
		}
		return ClanOriginal._instance;
	}

	public static void main(final String[] a) {
		try {
			while (true) {
				Server.main(null);
			}
		} catch (Exception ex) {
		}
	}

	public static void forIntensifyArmor(final L1PcInstance pc) {
		ArrayList<Object> aTempData = null;
		if (!ClanOriginal.BUILD_DATA) {
			ClanOriginal.BUILD_DATA = true;
			getData();
		}
		int clan_level = 0;
		if (pc.getClanid() != 0) {
			final L1Clan clan = WorldClan.get().getClan(pc.getClanname());
			if (clan != null) {
				clan_level = clan.getClanLevel();
			}
		}
		int i = 0;
		while (i < ClanOriginal.aData.size()) {
			aTempData = ClanOriginal.aData.get(i);
			if (clan_level == ((Integer) aTempData.get(0)).intValue()) {
				String add1 = "";
				String add2 = "";
				String add3 = "";
				String add4 = "";
				String add5 = "";
				String add6 = "";
				String add7 = "";
				String add8 = "";
				String add9 = "";
				String add10 = "";
				String add11 = "";
				String add12 = "";
				String add13 = "";
				String add14 = "";
				String add15 = "";
				String add16 = "";
				String add17 = "";
				String add18 = "";
				String add19 = "";
				String add20 = "";
				String add21 = "";
				String add22 = "";
				String add23 = "";
				String add24 = "";
				String add25 = "";
				if (((Integer) aTempData.get(2)).intValue() != 0) {
					pc.addMaxHp(((Integer) aTempData.get(2)).intValue());
					pc.setCurrentHp(pc.getCurrentHp() + ((Integer) aTempData.get(2)).intValue());
					add1 = "(HP+ " + ((Integer) aTempData.get(2)).intValue() + ")";
				}
				if (((Integer) aTempData.get(3)).intValue() != 0) {
					pc.addMaxMp(((Integer) aTempData.get(3)).intValue());
					pc.setCurrentMp(pc.getCurrentMp() + ((Integer) aTempData.get(3)).intValue());
					add2 = "(MP+ " + ((Integer) aTempData.get(3)).intValue() + ")";
				}
				if (((Integer) aTempData.get(4)).intValue() != 0) {
					pc.addDmgup(((Integer) aTempData.get(4)).intValue());
					add3 = "(近距攻擊+ " + ((Integer) aTempData.get(4)).intValue() + ")";
				}
				if (((Integer) aTempData.get(5)).intValue() != 0) {
					pc.addBowDmgup(((Integer) aTempData.get(5)).intValue());
					add4 = "(遠距攻擊+ " + ((Integer) aTempData.get(5)).intValue() + ")";
				}
				if (((Integer) aTempData.get(6)).intValue() != 0) {
					pc.addHitup(((Integer) aTempData.get(6)).intValue());
					add5 = "(近距命中+ " + ((Integer) aTempData.get(6)).intValue() + ")";
				}
				if (((Integer) aTempData.get(7)).intValue() != 0) {
					pc.addBowHitup(((Integer) aTempData.get(7)).intValue());
					add6 = "(遠距命中+ " + ((Integer) aTempData.get(7)).intValue() + ")";
				}
				if (((Integer) aTempData.get(8)).intValue() != 0) {
					pc.addMr(((Integer) aTempData.get(8)).intValue());
					add7 = "(抗魔+ " + ((Integer) aTempData.get(8)).intValue() + ")";
				}
				if (((Integer) aTempData.get(9)).intValue() != 0) {
					pc.addSp(((Integer) aTempData.get(9)).intValue());
					add8 = "(魔攻+ " + ((Integer) aTempData.get(9)).intValue() + ")";
				}
				if (((Integer) aTempData.get(10)).intValue() != 0) {
					pc.addAc(-((Integer) aTempData.get(10)).intValue());
					add9 = "(防禦- " + ((Integer) aTempData.get(10)).intValue() + ")";
				}
				if (((Integer) aTempData.get(11)).intValue() != 0) {
					pc.addFire(((Integer) aTempData.get(11)).intValue());
					add10 = "(火屬性+ " + ((Integer) aTempData.get(11)).intValue() + ")";
				}
				if (((Integer) aTempData.get(12)).intValue() != 0) {
					pc.addWind(((Integer) aTempData.get(12)).intValue());
					add11 = "(風屬性+ " + ((Integer) aTempData.get(12)).intValue() + ")";
				}
				if (((Integer) aTempData.get(13)).intValue() != 0) {
					pc.addEarth(((Integer) aTempData.get(13)).intValue());
					add12 = "(地屬性+ " + ((Integer) aTempData.get(13)).intValue() + ")";
				}
				if (((Integer) aTempData.get(14)).intValue() != 0) {
					pc.addWater(((Integer) aTempData.get(14)).intValue());
					add13 = "(水屬性+ " + ((Integer) aTempData.get(14)).intValue() + ")";
				}
				if (((Integer) aTempData.get(15)).intValue() != 0) {
					pc.addStr(((Integer) aTempData.get(15)).intValue());
					add14 = "(力量+ " + ((Integer) aTempData.get(15)).intValue() + ")";
				}
				if (((Integer) aTempData.get(16)).intValue() != 0) {
					pc.addDex(((Integer) aTempData.get(16)).intValue());
					add15 = "(敏捷+ " + ((Integer) aTempData.get(16)).intValue() + ")";
				}
				if (((Integer) aTempData.get(17)).intValue() != 0) {
					pc.addCon(((Integer) aTempData.get(17)).intValue());
					add16 = "(體質+ " + ((Integer) aTempData.get(17)).intValue() + ")";
				}
				if (((Integer) aTempData.get(18)).intValue() != 0) {
					pc.addWis(((Integer) aTempData.get(18)).intValue());
					add17 = "(精神+ " + ((Integer) aTempData.get(18)).intValue() + ")";
				}
				if (((Integer) aTempData.get(19)).intValue() != 0) {
					pc.addInt(((Integer) aTempData.get(19)).intValue());
					add18 = "(智力+ " + ((Integer) aTempData.get(19)).intValue() + ")";
				}
				if (((Integer) aTempData.get(20)).intValue() != 0) {
					pc.addCha(((Integer) aTempData.get(20)).intValue());
					add19 = "(魅力+ " + ((Integer) aTempData.get(20)).intValue() + ")";
				}
				if (((Integer) aTempData.get(21)).intValue() != 0) {
					pc.addClan_ReductionDmg(((Integer) aTempData.get(21)).intValue());
					add20 = "(減免傷害+ " + ((Integer) aTempData.get(21)).intValue() + ")";
				}
				if (((Integer) aTempData.get(22)).intValue() != 0) {
					pc.add_Clanmagic_reduction_dmg(((Integer) aTempData.get(22)).intValue());
					add21 = "(減免魔法傷害+ " + ((Integer) aTempData.get(22)).intValue() + ")";
				}
				if (((Double) aTempData.get(23)).doubleValue() > 0.0) {
					pc.addExpByArmor(((Double) aTempData.get(23)).doubleValue());
					add22 = "(經驗增加" + ((Double) aTempData.get(23)).doubleValue() + "倍)";
				}
				if (((Integer) aTempData.get(24)).intValue() != 0) {
					pc.addHpr(((Integer) aTempData.get(24)).intValue());
					add23 = "(回血+ " + ((Integer) aTempData.get(24)).intValue() + ")";
				}
				if (((Integer) aTempData.get(25)).intValue() != 0) {
					pc.addMpr(((Integer) aTempData.get(25)).intValue());
					add24 = "(回魔+ " + ((Integer) aTempData.get(25)).intValue() + ")";
				}
				if (((Integer) aTempData.get(26)).intValue() != 0) {
					pc.addWeightReduction(((Integer) aTempData.get(26)).intValue());
					add25 = "(負重+ " + ((Integer) aTempData.get(26)).intValue() + ")";
				}
				pc.sendPackets(new S_SystemMessage("你所屬的" + pc.getClanname() + "血盟等級為： " + clan_level + " 級"));
				pc.sendPackets(new S_SystemMessage(String.valueOf(add1) + add2 + add3 + add4 + add5 + add6 + add7 + add8
						+ add9 + add10 + add11 + add12 + add13 + add14 + add15 + add16 + add17 + add18 + add19 + add20
						+ add21 + add22 + add23 + add24 + add25));
				pc.sendPackets(new S_SPMR(pc));
				pc.sendPackets(new S_OwnCharStatus(pc));
				pc.sendPackets(new S_OwnCharStatus2(pc));
				pc.sendPackets(new S_HPUpdate(pc.getCurrentHp(), pc.getMaxHp()));
				pc.sendPackets(new S_MPUpdate(pc.getCurrentMp(), pc.getMaxMp()));
				break;
			}
			++i;
		}
	}

	private static void getData() {
		Connection conn = null;
		PreparedStatement pstmt = null;
		ResultSet rs = null;
		try {
			conn = DatabaseFactory.get().getConnection();
			pstmt = conn.prepareStatement("SELECT * FROM w_血盟等級");
			rs = pstmt.executeQuery();
			ArrayList<Object> aReturn = null;
			if (rs != null) {
				while (rs.next()) {
					aReturn = new ArrayList();
					aReturn.add(0, new Integer(rs.getInt("ClanLevel")));
					aReturn.add(1, new Integer(rs.getInt("Contribution")));
					aReturn.add(2, new Integer(rs.getInt("AddMaxHp")));
					aReturn.add(3, new Integer(rs.getInt("AddMaxMp")));
					aReturn.add(4, new Integer(rs.getInt("AddDmg")));
					aReturn.add(5, new Integer(rs.getInt("AddBowDmg")));
					aReturn.add(6, new Integer(rs.getInt("AddHit")));
					aReturn.add(7, new Integer(rs.getInt("AddBowHit")));
					aReturn.add(8, new Integer(rs.getInt("AddMr")));
					aReturn.add(9, new Integer(rs.getInt("AddSp")));
					aReturn.add(10, new Integer(rs.getInt("AddAc")));
					aReturn.add(11, new Integer(rs.getInt("AddFire")));
					aReturn.add(12, new Integer(rs.getInt("AddWind")));
					aReturn.add(13, new Integer(rs.getInt("AddEarth")));
					aReturn.add(14, new Integer(rs.getInt("AddWater")));
					aReturn.add(15, new Integer(rs.getInt("AddStr")));
					aReturn.add(16, new Integer(rs.getInt("AddDex")));
					aReturn.add(17, new Integer(rs.getInt("AddCon")));
					aReturn.add(18, new Integer(rs.getInt("AddWis")));
					aReturn.add(19, new Integer(rs.getInt("AddInt")));
					aReturn.add(20, new Integer(rs.getInt("AddCha")));
					aReturn.add(21, new Integer(rs.getInt("reduction_dmg")));
					aReturn.add(22, new Integer(rs.getInt("reduction_magic_dmg")));
					aReturn.add(23, new Double(rs.getDouble("ExpRate")));
					aReturn.add(24, new Integer(rs.getInt("AddHpr")));
					aReturn.add(25, new Integer(rs.getInt("AddMpr")));
					aReturn.add(26, new Integer(rs.getInt("AddWeight")));
					ClanOriginal.aData.add(aReturn);
				}
			}
		} catch (SQLException ex) {
		} finally {
			SQLUtil.close(rs);
			SQLUtil.close(pstmt);
			SQLUtil.close(conn);
		}
	}
}
