package com.lineage.william;

import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
import com.lineage.DatabaseFactory;
import com.lineage.server.templates.L1Item;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.model.skill.L1SkillUse;
import com.lineage.server.model.Instance.L1NpcInstance;
import com.lineage.server.model.Instance.L1PcInstance;

public class WilliamBuff {
	private static final List<ArrayList<Object>> datas;

	static {
		datas = new CopyOnWriteArrayList<>();
	}

	private WilliamBuff() {
	}

	public static boolean giveBuff(final L1PcInstance pc, final L1NpcInstance npc, final String args) {
		boolean f = false;
		final Iterator<ArrayList<Object>> iterator = datas.iterator();
		while (iterator.hasNext()) {
			final ArrayList<Object> os = iterator.next();
			final int[] npcids = (int[]) os.get(0);
			final int[] array;
			int j = (array = npcids).length;
			int k = 0;
			while (k < j) {
				final int id = array[k];
				if (id == npc.getNpcId()) {
					f = true;
					break;
				}
				++k;
			}
			if (!f) {
				continue;
			}
			f = false;
			final String[] cmd = (String[]) os.get(1);
			final String[] array2;
			final int length = (array2 = cmd).length;
			j = 0;
			while (j < length) {
				final String c = array2[j];
				if (c.equals(args)) {
					f = true;
					break;
				}
				++j;
			}
			if (!f) {
				continue;
			}
			final int[] skills = (int[]) os.get(2);
			final int[] times = (int[]) os.get(3);
			final int[] m = (int[]) os.get(4);
			final int[] mc = (int[]) os.get(5);
			int i = 0;
			while (i < m.length) {
				if (!pc.getInventory().consumeItem(m[i], mc[i])) {
					final L1Item item = ItemTable.get().getTemplate(m[i]);
					pc.sendPackets(new S_SystemMessage(String.valueOf(item.getNameId()) + " 不足，無法為您施放輔助魔法。"));
					return true;
				}
				++i;
			}
			i = 0;
			while (i < skills.length) {
				new L1SkillUse().handleCommands(pc, skills[i], pc.getId(), pc.getX(), pc.getY(), times[i], 4);
				++i;
			}
			if (f) {
				return true;
			}
		}
		return f;
	}

	public static synchronized void load() {
		Connection con = null;
		PreparedStatement pstm = null;
		ResultSet rs = null;
		try {
			con = DatabaseFactory.get().getConnection();
			pstm = con.prepareStatement("SELECT * FROM `william_npc_give_skill`");
			rs = pstm.executeQuery();
			while (rs.next()) {
				final ArrayList<Object> data = new ArrayList();
				data.add(convert(rs.getString("npcid").split(",")));
				data.add(rs.getString("action").split(","));
				data.add(convert(rs.getString("skills").split(",")));
				data.add(convert(rs.getString("times").split(",")));
				data.add(convert(rs.getString("material").split(",")));
				data.add(convert(rs.getString("material_count").split(",")));
				datas.add(data);
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

	private static int[] convert(final String[] data) {
		final int[] i32 = new int[data.length];
		int j = 0;
		while (j < data.length) {
			i32[j] = Integer.parseInt(data[j]);
			++j;
		}
		return i32;
	}
}
