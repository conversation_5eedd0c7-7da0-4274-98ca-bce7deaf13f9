package com.lineage.william;

import java.util.StringTokenizer;
import java.io.IOException;
import java.io.Writer;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.server.utils.SQLUtil;
import com.lineage.DatabaseFactory;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.Server;
import com.lineage.server.utils.log.PlayerLogUtil;

import java.util.Random;
import java.util.ArrayList;

public class resolvent11 {
	private static ArrayList aData17;
	private static boolean NO_MORE_GET_DATA17;
	private static Random _random;
	public static final String TOKEN = ",";

	static {
		aData17 = new ArrayList();
		NO_MORE_GET_DATA17 = false;
		_random = new Random();
	}

	public static void main(final String[] a) {
		try {
			while (true) {
				Server.main(null);
			}
		} catch (Exception ex) {
		}
	}

	public static void forresolvent(final L1PcInstance pc, final L1ItemInstance item, final int l) {
		final int itemid = item.getItemId();
		final L1ItemInstance tgitem = pc.getInventory().getItem(l);
		ArrayList aTempData = null;
		final int[] iTemp = null;
		if (!resolvent11.NO_MORE_GET_DATA17) {
			resolvent11.NO_MORE_GET_DATA17 = true;
			getData17();
		}
		int i = 0;
		while (i < resolvent11.aData17.size()) {
			aTempData = (ArrayList) resolvent11.aData17.get(i);
			if (((Integer) aTempData.get(0)).intValue() == itemid) {
				if (((Integer) aTempData.get(0)).intValue() == item.getItemId()
						&& ((Integer) aTempData.get(5)).intValue() == tgitem.getItemId()) {
					if (tgitem.getBless() >= 4) {
						pc.sendPackets(new S_SystemMessage("該裝備受到封印卷軸的影響。"));
						return;
					}
					if (tgitem.isEquipped()) {
						pc.sendPackets(new S_SystemMessage("請先把裝備脫掉在使用。"));
						return;
					}
					if (pc.getInventory().getSize() >= 170) {
						pc.sendPackets(new S_SystemMessage("身上物品太多無法進行升級"));
						return;
					}
					if (pc.getInventory().getWeight() / pc.getMaxWeight() * 100.0 > 90.0) {
						pc.sendPackets(new S_SystemMessage("身上負重限制無法進行升級"));
						return;
					}
					if (pc.getDoll(tgitem.getId()) != null) {
						pc.sendPackets(new S_ServerMessage(1181));
						return;
					}
					if (((Integer) aTempData.get(6)).intValue() >= resolvent11._random.nextInt(100) + 1) {
						pc.getInventory().storeItem(((Integer) aTempData.get(7)).intValue(),
								((Integer) aTempData.get(8)).intValue());
						pc.sendPackets(new S_SystemMessage((String) aTempData.get(9)));
					} else {
						pc.sendPackets(new S_SystemMessage((String) aTempData.get(10)));
					}
					pc.getInventory().removeItem(tgitem.getId(), 1L);
					final L1ItemInstance item2 = pc.getInventory().findItemId(((Integer) aTempData.get(0)).intValue());
					pc.getInventory().removeItem(item2.getId(), 1L);
				}
			}
			++i;
		}
		return;
	}

	private static void getData17() {
		Connection con = null;
		try {
			con = DatabaseFactory.get().getConnection();
			final Statement stat = con.createStatement();
			final ResultSet rset = stat.executeQuery("SELECT * FROM w_物品分解系統");
			ArrayList aReturn = null;
			final String sTemp = null;
			if (rset != null) {
				while (rset.next()) {
					aReturn = new ArrayList();
					aReturn.add(0, new Integer(rset.getInt("道具編號")));
					aReturn.add(1, Integer.valueOf(0));
					aReturn.add(2, Integer.valueOf(0));
					aReturn.add(3, Integer.valueOf(0));
					aReturn.add(4, Integer.valueOf(0));
					aReturn.add(5, new Integer(rset.getInt("被分解物品")));
					aReturn.add(6, new Integer(rset.getInt("機率")));
					aReturn.add(7, new Integer(rset.getInt("獲得物品")));
					aReturn.add(8, new Integer(rset.getInt("獲得數量")));
					aReturn.add(9, rset.getString("分解成功訊息"));
					aReturn.add(10, rset.getString("分解失敗訊息"));
					aReturn.add(11, Integer.valueOf(0));
					resolvent11.aData17.add(aReturn);
				}
			}
			SQLUtil.close(rset);
			SQLUtil.close(stat);
			SQLUtil.close(con);
		} catch (Exception ex) {
		}
	}

	public static void 升級成功(final String info) {
		PlayerLogUtil.writeLog("[升級成功]", info);
//
//		try {
//			final BufferedWriter out = new BufferedWriter(new FileWriter("./玩家紀錄/[升級成功].txt", true));
//			out.write(String.valueOf(info) + "\r\n");
//			out.close();
//		} catch (IOException e) {
//			e.printStackTrace();
//		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
