package com.lineage.william;

import java.util.StringTokenizer;
import java.sql.ResultSet;
import java.sql.Statement;
import java.sql.Connection;
import com.lineage.DatabaseFactory;
import com.lineage.server.model.Instance.L1ItemInstance;
import com.lineage.server.serverpackets.S_ServerMessage;
import com.lineage.server.world.World;
import com.lineage.server.serverpackets.ServerBasePacket;
import com.lineage.server.serverpackets.S_SystemMessage;
import com.lineage.server.datatables.ItemTable;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.Server;
import java.util.ArrayList;

public class Reward {
	private static ArrayList<ArrayList<Object>> array;
	private static boolean GET_ITEM;
	public static final String TOKEN = ",";

	static {
		array = new ArrayList();
		GET_ITEM = false;
	}

	public static void main(final String[] a) {
		try {
			Server.main(null);
		} catch (Exception ex) {
		}
	}

	public static void getItem(final L1PcInstance pc) {
		ArrayList<?> data = null;
		if (!Reward.GET_ITEM) {
			Reward.GET_ITEM = true;
			getItemData();
		}
		int i = 0;
		while (i < Reward.array.size()) {
			data = Reward.array.get(i);
			if (pc.getLevel() >= ((Integer) data.get(0)).intValue() && (int[]) data.get(8) != null
					&& (int[]) data.get(9) != null && (int[]) data.get(10) != null && pc.getQuest()
							.get_step(((Integer) data.get(11)).intValue()) != ((Integer) data.get(12)).intValue()) {
				if (((Integer) data.get(1)).intValue() != 0 && pc.isCrown()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(2)).intValue() != 0 && pc.isKnight()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(3)).intValue() != 0 && pc.isWizard()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(4)).intValue() != 0 && pc.isElf()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(5)).intValue() != 0 && pc.isDarkelf()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(6)).intValue() != 0 && pc.isDragonKnight()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
				if (((Integer) data.get(7)).intValue() != 0 && pc.isIllusionist()) {
					boolean isGet = false;
					final int[] materials = (int[]) data.get(8);
					final int[] counts = (int[]) data.get(9);
					final int[] enchantLevel = (int[]) data.get(10);
					int j = 0;
					while (j < materials.length) {
						final L1ItemInstance item = ItemTable.get().createItem(materials[j]);
						if (item.isStackable()) {
							item.setCount(counts[j]);
						} else {
							item.setCount(1L);
						}
						if (item.getItem().getType2() == 1 || item.getItem().getType2() == 2) {
							item.setEnchantLevel(enchantLevel[j]);
						} else {
							item.setEnchantLevel(0);
						}
						if (item != null) {
							if ((String) data.get(13) != null && !isGet) {
								pc.sendPackets(new S_SystemMessage((String) data.get(13)));
								isGet = true;
							}
							if (pc.getInventory().checkAddItem(item, counts[j]) == 0) {
								pc.getInventory().storeItem(item);
							} else {
								World.get().getInventory(pc.getX(), pc.getY(), pc.getMapId()).storeItem(item);
							}
							pc.sendPackets(new S_ServerMessage(403, item.getLogName()));
							pc.getQuest().set_step(((Integer) data.get(11)).intValue(),
									((Integer) data.get(12)).intValue());
						}
						++j;
					}
				}
			}
			++i;
		}
	}

	private static void getItemData() {
		Connection con = null;
		try {
			con = DatabaseFactory.get().getConnection();
			final Statement stat = con.createStatement();
			final ResultSet rset = stat.executeQuery("SELECT * FROM w_升級獎勵");
			ArrayList<Object> arraylist = null;
			if (rset != null) {
				while (rset.next()) {
					arraylist = new ArrayList();
					arraylist.add(0, new Integer(rset.getInt("level")));
					arraylist.add(1, new Integer(rset.getInt("give_royal")));
					arraylist.add(2, new Integer(rset.getInt("give_knight")));
					arraylist.add(3, new Integer(rset.getInt("give_mage")));
					arraylist.add(4, new Integer(rset.getInt("give_elf")));
					arraylist.add(5, new Integer(rset.getInt("give_darkelf")));
					arraylist.add(6, new Integer(rset.getInt("give_dragonKnight")));
					arraylist.add(7, new Integer(rset.getInt("give_illusionist")));
					arraylist.add(8, getArray(rset.getString("getItem"), ",", 1));
					arraylist.add(9, getArray(rset.getString("count"), ",", 1));
					arraylist.add(10, getArray(rset.getString("enchantlvl"), ",", 1));
					arraylist.add(11, new Integer(rset.getInt("quest_id")));
					arraylist.add(12, new Integer(rset.getInt("quest_step")));
					arraylist.add(13, rset.getString("message"));
					Reward.array.add(arraylist);
				}
			}
			if (con != null && !con.isClosed()) {
				con.close();
			}
		} catch (Exception ex) {
		}
	}

	private static Object getArray(final String s, final String sToken, final int iType) {
		final StringTokenizer st = new StringTokenizer(s, sToken);
		final int iSize = st.countTokens();
		String sTemp = null;
		if (iType == 1) {
			final int[] iReturn = new int[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				iReturn[i] = Integer.parseInt(sTemp);
				++i;
			}
			return iReturn;
		}
		if (iType == 2) {
			final String[] sReturn = new String[iSize];
			int i = 0;
			while (i < iSize) {
				sTemp = st.nextToken();
				sReturn[i] = sTemp;
				++i;
			}
			return sReturn;
		}
		if (iType == 3) {
			String sReturn2 = null;
			int i = 0;
			while (i < iSize) {
				sTemp = (sReturn2 = st.nextToken());
				++i;
			}
			return sReturn2;
		}
		return null;
	}
}
