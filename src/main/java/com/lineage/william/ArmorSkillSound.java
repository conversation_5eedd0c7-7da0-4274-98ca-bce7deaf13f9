package com.lineage.william;

import com.lineage.DatabaseFactory;
import com.lineage.Server;
import com.lineage.server.model.Instance.L1PcInstance;
import com.lineage.server.serverpackets.S_DoActionGFX;
import com.lineage.server.serverpackets.S_SkillSound;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 裝備持續特效優化系統
 * 提供更好的效能、快取管理和特效控制
 */
public class ArmorSkillSound {
    public static final String TOKEN = ",";
    
    // 優化：使用ConcurrentHashMap提升效能
    private static final Map<Integer, EffectData> effectCache = new ConcurrentHashMap<>();
    private static final Map<Integer, Long> playerEffectTimestamps = new ConcurrentHashMap<>();
    
    // 效能監控
    private static long lastCleanupTime = System.currentTimeMillis();
    private static int totalEffectsProcessed = 0;
    private static long totalProcessingTime = 0;
    
    // 配置參數
    private static final long EFFECT_COOLDOWN = 5000L; // 特效冷卻時間 (毫秒)
    private static final long CACHE_CLEANUP_INTERVAL = 60000L; // 快取清理間隔 (毫秒)
    private static final int MAX_CACHE_SIZE = 1000; // 最大快取大小
    private static final boolean ENABLE_PERFORMANCE_MONITORING = true; // 啟用效能監控
    private static final boolean ENABLE_DETAILED_ERROR_LOGGING = true; // 啟用詳細錯誤日誌
    private static final boolean ENABLE_VERBOSE_LOGGING = false; // 啟用詳細日誌
    
    private static ArmorSkillSound _instance;
    private static boolean isInitialized = false;

    /**
     * 特效資料結構
     */
    private static class EffectData {
        final int[] armorIds;
        final int gfxId;
        final long loadTime;
        
        EffectData(int[] armorIds, int gfxId) {
            this.armorIds = armorIds;
            this.gfxId = gfxId;
            this.loadTime = System.currentTimeMillis();
        }
    }

    private ArmorSkillSound() {
        initializeSystem();
    }

    public static ArmorSkillSound getInstance() {
        if (_instance == null) {
            synchronized (ArmorSkillSound.class) {
                if (_instance == null) {
                    _instance = new ArmorSkillSound();
                }
            }
        }
        return _instance;
    }

    /**
     * 初始化系統
     */
    private void initializeSystem() {
        if (isInitialized) {
            return;
        }
        
        try {
            // 載入特效資料
            loadEffectData();
            
            // 啟動清理計時器
            startCleanupTimer();
            
            isInitialized = true;
            
            if (ENABLE_VERBOSE_LOGGING) {
                System.out.println("裝備持續特效系統初始化完成，載入 " + effectCache.size() + " 個特效");
            }
            
            // 輸出配置摘要
            if (ENABLE_VERBOSE_LOGGING) {
                System.out.println(getConfigSummary());
            }
        } catch (Exception e) {
            System.err.println("裝備持續特效系統初始化失敗: " + e.getMessage());
            if (ENABLE_DETAILED_ERROR_LOGGING) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 獲取配置摘要
     */
    private static String getConfigSummary() {
        return String.format("裝備特效配置 - 冷卻時間: %dms, 快取大小: %d, 效能監控: %s", 
                           EFFECT_COOLDOWN, MAX_CACHE_SIZE, ENABLE_PERFORMANCE_MONITORING);
    }

    /**
     * 重新載入特效資料
     */
    public static void reload() {
        synchronized (ArmorSkillSound.class) {
            effectCache.clear();
            playerEffectTimestamps.clear();
            isInitialized = false;
            _instance = null;
            getInstance();
        }
    }

    /**
     * 檢查玩家是否裝備了任何帶有持續特效的裝備
     * @param pc 玩家實例
     * @return 如果有則返回 true，否則返回 false
     */
    public static boolean hasContinuousEffectArmor(L1PcInstance pc) {
        if (pc == null || effectCache == null || effectCache.isEmpty()) {
            return false;
        }

        for (EffectData effectData : effectCache.values()) {
            if (hasRequiredArmor(pc, effectData.armorIds)) {
                return true; // 只要找到一組滿足條件的裝備，就返回 true
            }
        }

        return false;
    }

    /**
     * 優化的特效處理方法
     */
    public static void forArmorSkillSound(L1PcInstance pc) {
        if (pc == null || pc.isDead()) {
            return;
        }
        
        long startTime = System.currentTimeMillis();
        int effectsProcessed = 0;
        
        try {
            // 檢查玩家特效冷卻時間
            if (isEffectOnCooldown(pc)) {
                return;
            }
            
            // 處理特效
            if (effectCache != null) {
                for (EffectData effectData : effectCache.values()) {
                    if (effectData != null && effectData.armorIds != null && 
                        hasRequiredArmor(pc, effectData.armorIds)) {
                        playEffect(pc, effectData.gfxId);
                        effectsProcessed++;
                    }
                }
            }
            
            // 更新玩家特效時間戳
            updatePlayerEffectTimestamp(pc);
            
            // 效能監控
            if (ENABLE_PERFORMANCE_MONITORING) {
                updatePerformanceStats(effectsProcessed, System.currentTimeMillis() - startTime);
            }
            
        } catch (Exception e) {
            if (ENABLE_DETAILED_ERROR_LOGGING) {
                System.err.println("處理玩家 " + pc.getName() + " 的裝備特效時發生錯誤: " + e.getMessage());
                e.printStackTrace();
            } else {
                System.err.println("處理玩家 " + pc.getName() + " 的裝備特效時發生錯誤: " + e.getMessage());
            }
        }
    }

    /**
     * 檢查特效是否在冷卻中
     */
    private static boolean isEffectOnCooldown(L1PcInstance pc) {
        Long lastEffectTime = playerEffectTimestamps.get(pc.getId());
        if (lastEffectTime == null) {
            return false;
        }
        
        return (System.currentTimeMillis() - lastEffectTime) < EFFECT_COOLDOWN;
    }

    /**
     * 檢查玩家是否裝備了指定裝備
     */
    private static boolean hasRequiredArmor(L1PcInstance pc, int[] armorIds) {
        if (pc == null || armorIds == null || armorIds.length == 0) {
            return false;
        }
        
        try {
            for (int armorId : armorIds) {
                if (!pc.getInventory().checkEquipped(armorId)) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            // 記錄錯誤但不中斷流程
            System.err.println("檢查裝備時發生錯誤: " + e.getMessage());
            return false;
        }
    }

    /**
     * 播放特效
     */
    private static void playEffect(L1PcInstance pc, int gfxId) {
        try {
            if (pc != null && pc.getNetConnection() != null && 
                pc.getNetConnection().get_socket() != null && 
                !pc.getNetConnection().get_socket().isClosed()) {
                
                S_SkillSound skillSound = new S_SkillSound(pc.getId(), gfxId, 2); // 持續時間 2 秒
                pc.sendPackets(skillSound);
                pc.broadcastPacketAll(skillSound);
                
                if (ENABLE_VERBOSE_LOGGING) {
                    System.out.println("玩家 " + pc.getName() + " 播放特效: " + gfxId);
                }
            }
        } catch (Exception e) {
            System.err.println("播放特效失敗: " + e.getMessage());
        }
    }

    /**
     * 更新玩家特效時間戳
     */
    private static void updatePlayerEffectTimestamp(L1PcInstance pc) {
        if (pc != null) {
            playerEffectTimestamps.put(pc.getId(), System.currentTimeMillis());
        }
    }

    /**
     * 更新效能統計
     */
    private static void updatePerformanceStats(int effectsProcessed, long processingTime) {
        totalEffectsProcessed += effectsProcessed;
        totalProcessingTime += processingTime;
        
        if (ENABLE_VERBOSE_LOGGING) {
            System.out.println(String.format("效能統計 - 處理特效: %d, 處理時間: %dms", 
                                           effectsProcessed, processingTime));
        }
    }

    /**
     * 啟動清理計時器
     */
    private static void startCleanupTimer() {
        Timer timer = new Timer("ArmorSkillSoundCleanupTimer", true);
        timer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                cleanupCache();
            }
        }, CACHE_CLEANUP_INTERVAL, CACHE_CLEANUP_INTERVAL);
    }

    /**
     * 清理快取
     */
    private static void cleanupCache() {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 清理過期的玩家時間戳
            Iterator<Map.Entry<Integer, Long>> iterator = playerEffectTimestamps.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Integer, Long> entry = iterator.next();
                if (currentTime - entry.getValue() > CACHE_CLEANUP_INTERVAL) {
                    iterator.remove();
                }
            }
            
            // 清理過期的特效資料
            if (effectCache.size() > MAX_CACHE_SIZE) {
                Iterator<Map.Entry<Integer, EffectData>> effectIterator = effectCache.entrySet().iterator();
                while (effectIterator.hasNext() && effectCache.size() > MAX_CACHE_SIZE) {
                    Map.Entry<Integer, EffectData> entry = effectIterator.next();
                    if (currentTime - entry.getValue().loadTime > CACHE_CLEANUP_INTERVAL) {
                        effectIterator.remove();
                    }
                }
            }
            
            lastCleanupTime = currentTime;
            
            if (ENABLE_VERBOSE_LOGGING) {
                System.out.println("快取清理完成 - 特效快取: " + effectCache.size() + 
                                 ", 玩家時間戳: " + playerEffectTimestamps.size());
            }
            
        } catch (Exception e) {
            System.err.println("快取清理失敗: " + e.getMessage());
        }
    }

    /**
     * 載入特效資料
     */
    private static void loadEffectData() {
        Connection con = null;
        Statement stat = null;
        ResultSet rset = null;
        
        try {
            con = DatabaseFactory.get().getConnection();
            stat = con.createStatement();
            rset = stat.executeQuery("SELECT * FROM w_裝備持續特效");
            
            int loadedCount = 0;
            while (rset != null && rset.next()) {
                try {
                    int[] armorIds = null;
                    String armorIdStr = rset.getString("armor_id");
                    
                    if (armorIdStr != null && !armorIdStr.isEmpty() && !armorIdStr.equals("0")) {
                        armorIds = convert(armorIdStr.split(","));
                    }
                    
                    int gfxId = rset.getInt("gfxId");
                    
                    // 使用快取ID作為key
                    int cacheKey = generateCacheKey(armorIds, gfxId);
                    effectCache.put(cacheKey, new EffectData(armorIds, gfxId));
                    loadedCount++;
                    
                } catch (Exception e) {
                    System.err.println("載入特效資料時發生錯誤: " + e.getMessage());
                }
            }
            
            System.out.println("成功載入 " + loadedCount + " 個裝備特效");
            
        } catch (Exception e) {
            System.err.println("載入裝備特效資料失敗: " + e.getMessage());
        } finally {
            try {
                if (rset != null) rset.close();
                if (stat != null) stat.close();
                if (con != null && !con.isClosed()) con.close();
            } catch (Exception e) {
                System.err.println("關閉資料庫連接失敗: " + e.getMessage());
            }
        }
    }

    /**
     * 生成快取鍵值
     */
    private static int generateCacheKey(int[] armorIds, int gfxId) {
        if (armorIds == null || armorIds.length == 0) {
            return gfxId;
        }
        
        int key = gfxId;
        for (int armorId : armorIds) {
            key = key * 31 + armorId;
        }
        return key;
    }

    /**
     * 轉換字串陣列為整數陣列
     */
    private static int[] convert(String[] strings) {
        if (strings == null || strings.length == 0) {
            return new int[0];
        }
        
        int[] result = new int[strings.length];
        for (int i = 0; i < strings.length; i++) {
            try {
                result[i] = Integer.parseInt(strings[i].trim());
            } catch (NumberFormatException e) {
                result[i] = 0;
            }
        }
        return result;
    }
    
    /**
     * 獲取系統統計資訊
     */
    public static String getSystemStats() {
        return String.format("裝備特效系統統計 - 特效數量: %d, 玩家時間戳: %d, 總處理特效: %d, 總處理時間: %dms", 
                           effectCache.size(), playerEffectTimestamps.size(), 
                           totalEffectsProcessed, totalProcessingTime);
    }
}
