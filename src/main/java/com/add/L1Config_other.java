package com.add;

public class L1Config_other {
	public static int _2211;
	public static int _2212;
	public static int _2213;
	public static int _2216;
	public static int _2217;
	public static int _2218;
	public static int _4046;

	static {
		_2211 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2211).getMessage()).intValue();
		_2212 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2212).getMessage()).intValue();
		_2213 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2213).getMessage()).intValue();
		_2216 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2216).getMessage()).intValue();
		_2217 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2217).getMessage()).intValue();
		_2218 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(2218).getMessage()).intValue();
		_4046 = Integer.valueOf(L1SystemMessageTable.get().getTemplate(4046).getMessage()).intValue();
	}
}
