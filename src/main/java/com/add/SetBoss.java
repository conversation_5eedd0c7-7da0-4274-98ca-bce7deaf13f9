package com.add;

public class SetBoss {
	public static int _82;
	public static int _83;
	public static int _84;
	public static int _85;
	public static int _86;
	public static int _87;
	public static int _88;
	public static int _89;
	public static int _90;
	public static int _91;
	public static int _92;
	public static int _93;
	public static int _94;
	public static int _95;
	public static int _96;
	public static int _97;
	public static int _98;
	public static int _99;
	public static int _100;
	public static int _101;
	public static int _102;
	public static int _103;
	public static int _104;
	public static int _105;
	public static int _106;
	public static int _107;
	public static int _108;
	public static int _109;
	public static int _110;
	public static int _111;
	public static int _112;
	public static int _113;

	static {
		_82 = Integer.parseInt(SetBossTable.get().getTemplate(82).get_message());
		_83 = Integer.parseInt(SetBossTable.get().getTemplate(83).get_message());
		_84 = Integer.parseInt(SetBossTable.get().getTemplate(84).get_message());
		_85 = Integer.parseInt(SetBossTable.get().getTemplate(85).get_message());
		_86 = Integer.parseInt(SetBossTable.get().getTemplate(86).get_message());
		_87 = Integer.parseInt(SetBossTable.get().getTemplate(87).get_message());
		_88 = Integer.parseInt(SetBossTable.get().getTemplate(88).get_message());
		_89 = Integer.parseInt(SetBossTable.get().getTemplate(89).get_message());
		_90 = Integer.parseInt(SetBossTable.get().getTemplate(90).get_message());
		_91 = Integer.parseInt(SetBossTable.get().getTemplate(91).get_message());
		_92 = Integer.parseInt(SetBossTable.get().getTemplate(92).get_message());
		_93 = Integer.parseInt(SetBossTable.get().getTemplate(93).get_message());
		_94 = Integer.parseInt(SetBossTable.get().getTemplate(94).get_message());
		_95 = Integer.parseInt(SetBossTable.get().getTemplate(95).get_message());
		_96 = Integer.parseInt(SetBossTable.get().getTemplate(96).get_message());
		_97 = Integer.parseInt(SetBossTable.get().getTemplate(97).get_message());
		_98 = Integer.parseInt(SetBossTable.get().getTemplate(98).get_message());
		_99 = Integer.parseInt(SetBossTable.get().getTemplate(99).get_message());
		_100 = Integer.parseInt(SetBossTable.get().getTemplate(100).get_message());
		_101 = Integer.parseInt(SetBossTable.get().getTemplate(101).get_message());
		_102 = Integer.parseInt(SetBossTable.get().getTemplate(102).get_message());
		_103 = Integer.parseInt(SetBossTable.get().getTemplate(103).get_message());
		_104 = Integer.parseInt(SetBossTable.get().getTemplate(104).get_message());
		_105 = Integer.parseInt(SetBossTable.get().getTemplate(105).get_message());
		_106 = Integer.parseInt(SetBossTable.get().getTemplate(106).get_message());
		_107 = Integer.parseInt(SetBossTable.get().getTemplate(107).get_message());
		_108 = Integer.parseInt(SetBossTable.get().getTemplate(108).get_message());
		_109 = Integer.parseInt(SetBossTable.get().getTemplate(109).get_message());
		_110 = Integer.parseInt(SetBossTable.get().getTemplate(110).get_message());
		_111 = Integer.parseInt(SetBossTable.get().getTemplate(111).get_message());
		_112 = Integer.parseInt(SetBossTable.get().getTemplate(112).get_message());
		_113 = Integer.parseInt(SetBossTable.get().getTemplate(113).get_message());
	}
}
