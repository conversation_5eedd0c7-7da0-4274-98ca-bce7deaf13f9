package com.eric;

import java.util.logging.LogRecord;
import java.util.Calendar;
import com.eric.gui.J_Main;
import java.util.ResourceBundle;
import java.util.logging.Level;
import java.util.logging.Filter;
import java.util.logging.Handler;
import java.util.logging.Logger;

public class Eric<PERSON>ogger extends Logger {
	private static String className;
	private static String classResourceBundleName;

	static {
		className = null;
		classResourceBundleName = null;
	}

	public EricLogger(final String name, final String resourceBundleName) {
		super(name, resourceBundleName);
		EricLogger.className = name;
	}

	public static EricLogger getLogger2(final String name) {
		EricLogger.className = name;
		EricLogger.classResourceBundleName = Logger.getLogger(name).getResourceBundleName();
		return new EricLogger(EricLogger.className, EricLogger.classResourceBundleName);
	}

	@Override
	public synchronized void addHandler(final Handler handler) throws SecurityException {
		super.addHandler(handler);
	}

	@Override
	public void config(final String msg) {
		super.config(msg);
	}

	@Override
	public void entering(final String sourceClass, final String sourceMethod, final Object param1) {
		super.entering(sourceClass, sourceMethod, param1);
	}

	@Override
	public void entering(final String sourceClass, final String sourceMethod, final Object[] params) {
		super.entering(sourceClass, sourceMethod, params);
	}

	@Override
	public void entering(final String sourceClass, final String sourceMethod) {
		super.entering(sourceClass, sourceMethod);
	}

	@Override
	public void exiting(final String sourceClass, final String sourceMethod, final Object result) {
		super.exiting(sourceClass, sourceMethod, result);
	}

	@Override
	public void exiting(final String sourceClass, final String sourceMethod) {
		super.exiting(sourceClass, sourceMethod);
	}

	@Override
	public void fine(final String msg) {
		super.fine(msg);
	}

	@Override
	public void finer(final String msg) {
		super.finer(msg);
	}

	@Override
	public void finest(final String msg) {
		super.finest(msg);
	}

	@Override
	public Filter getFilter() {
		return super.getFilter();
	}

	@Override
	public synchronized Handler[] getHandlers() {
		return super.getHandlers();
	}

	@Override
	public Level getLevel() {
		return super.getLevel();
	}

	@Override
	public String getName() {
		return super.getName();
	}

	@Override
	public Logger getParent() {
		return super.getParent();
	}

	@Override
	public ResourceBundle getResourceBundle() {
		return super.getResourceBundle();
	}

	@Override
	public String getResourceBundleName() {
		return super.getResourceBundleName();
	}

	@Override
	public synchronized boolean getUseParentHandlers() {
		return super.getUseParentHandlers();
	}

	@Override
	public void info(final String msg) {
		J_Main.getInstance()
				.addConsol(String.valueOf(Calendar.getInstance().getTime().toString()) + " " + EricLogger.className);
		J_Main.getInstance().addConsol(msg);
		super.info(msg);
	}

	@Override
	public boolean isLoggable(final Level level) {
		return super.isLoggable(level);
	}

	@Override
	public void log(final Level level, final String msg, final Object param1) {
		super.log(level, msg, param1);
	}

	@Override
	public void log(final Level level, final String msg, final Object[] params) {
		super.log(level, msg, params);
	}

	@Override
	public void log(final Level level, final String msg, final Throwable thrown) {
		J_Main.getInstance()
				.addConsol(String.valueOf(Calendar.getInstance().getTime().toString()) + " " + EricLogger.className);
		J_Main.getInstance().addConsol(level + ": " + msg + "\n" + thrown);
		super.log(level, msg, thrown);
	}

	@Override
	public void log(final Level level, final String msg) {
		super.log(level, msg);
	}

	@Override
	public void log(final LogRecord record) {
		super.log(record);
	}

	@Override
	public void logp(final Level level, final String sourceClass, final String sourceMethod, final String msg,
			final Object param1) {
		super.logp(level, sourceClass, sourceMethod, msg, param1);
	}

	@Override
	public void logp(final Level level, final String sourceClass, final String sourceMethod, final String msg,
			final Object[] params) {
		super.logp(level, sourceClass, sourceMethod, msg, params);
	}

	@Override
	public void logp(final Level level, final String sourceClass, final String sourceMethod, final String msg,
			final Throwable thrown) {
		super.logp(level, sourceClass, sourceMethod, msg, thrown);
	}

	@Override
	public void logp(final Level level, final String sourceClass, final String sourceMethod, final String msg) {
		super.logp(level, sourceClass, sourceMethod, msg);
	}

	@Override
	public void logrb(final Level level, final String sourceClass, final String sourceMethod, final String bundleName,
			final String msg, final Object param1) {
		super.logrb(level, sourceClass, sourceMethod, bundleName, msg, param1);
	}

	@Override
	public void logrb(final Level level, final String sourceClass, final String sourceMethod, final String bundleName,
			final String msg, final Object[] params) {
		super.logrb(level, sourceClass, sourceMethod, bundleName, msg, params);
	}

	@Override
	public void logrb(final Level level, final String sourceClass, final String sourceMethod, final String bundleName,
			final String msg, final Throwable thrown) {
		super.logrb(level, sourceClass, sourceMethod, bundleName, msg, thrown);
	}

	@Override
	public void logrb(final Level level, final String sourceClass, final String sourceMethod, final String bundleName,
			final String msg) {
		super.logrb(level, sourceClass, sourceMethod, bundleName, msg);
	}

	@Override
	public synchronized void removeHandler(final Handler handler) throws SecurityException {
		super.removeHandler(handler);
	}

	@Override
	public void setFilter(final Filter newFilter) throws SecurityException {
		super.setFilter(newFilter);
	}

	@Override
	public void setLevel(final Level newLevel) throws SecurityException {
		super.setLevel(newLevel);
	}

	@Override
	public void setParent(final Logger parent) {
		super.setParent(parent);
	}

	@Override
	public synchronized void setUseParentHandlers(final boolean useParentHandlers) {
		super.setUseParentHandlers(useParentHandlers);
	}

	@Override
	public void severe(final String msg) {
		super.severe(msg);
	}

	@Override
	public void throwing(final String sourceClass, final String sourceMethod, final Throwable thrown) {
		super.throwing(sourceClass, sourceMethod, thrown);
	}

	@Override
	public void warning(final String msg) {
		J_Main.getInstance()
				.addConsol(String.valueOf(Calendar.getInstance().getTime().toString()) + " " + EricLogger.className);
		J_Main.getInstance().addConsol("Warning: " + msg);
		super.warning(msg);
	}

	@Override
	protected Object clone() throws CloneNotSupportedException {
		return super.clone();
	}

	@Override
	public boolean equals(final Object arg0) {
		return super.equals(arg0);
	}

	@Override
	protected void finalize() throws Throwable {
		super.finalize();
	}

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public String toString() {
		return super.toString();
	}
}
