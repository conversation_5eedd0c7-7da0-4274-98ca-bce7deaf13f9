# Debug 訊息關閉設定報告

## 問題描述
您遇到的 MariaDB JDBC debug 訊息：
```
01:52:12.030 [main] DEBUG org.mariadb.jdbc.client.impl.StandardClient - execute query: UPDATE `w_掉落廣播系統` SET `名稱`=? WHERE `itemid`=?
```

## 解決方案
已修改以下三個 logging 設定檔來關閉所有 debug 訊息：

### 1. config/log4j.properties (Log4j 1.x 設定)
**新增的設定：**
```properties
# MariaDB JDBC logging - disable debug messages
log4j.logger.org.mariadb.jdbc=WARN
log4j.logger.org.mariadb.jdbc.client=WARN
log4j.logger.org.mariadb.jdbc.client.impl=WARN
log4j.logger.org.mariadb.jdbc.client.impl.StandardClient=WARN
```

### 2. config/logging.properties (Java Util Logging 設定)
**新增的設定：**
```properties
# MariaDB JDBC logging - disable debug messages
org.mariadb.jdbc.level=WARNING
org.mariadb.jdbc.client.level=WARNING
org.mariadb.jdbc.client.impl.level=WARNING
org.mariadb.jdbc.client.impl.StandardClient.level=WARNING
```

### 3. config/log4j2.xml (Log4j 2.x 設定)
**新增的設定：**
```xml
<!-- MariaDB JDBC logging - disable debug messages -->
<Logger name="org.mariadb.jdbc" level="WARN" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFile"/>
</Logger>

<Logger name="org.mariadb.jdbc.client" level="WARN" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFile"/>
</Logger>

<Logger name="org.mariadb.jdbc.client.impl" level="WARN" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFile"/>
</Logger>

<Logger name="org.mariadb.jdbc.client.impl.StandardClient" level="WARN" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFile"/>
</Logger>
```

## 影響範圍
這些設定將關閉以下類別的 debug 訊息：
- `org.mariadb.jdbc.*` - 所有 MariaDB JDBC 相關訊息
- `org.mariadb.jdbc.client.*` - MariaDB 客戶端訊息
- `org.mariadb.jdbc.client.impl.*` - MariaDB 客戶端實作訊息
- `org.mariadb.jdbc.client.impl.StandardClient` - 標準客戶端訊息（您遇到的具體問題）

## 生效方式
1. **重新啟動伺服器** - 設定檔會在伺服器啟動時載入
2. **無需修改程式碼** - 純設定檔修改，不影響程式邏輯
3. **向後相容** - 保留原有的 logging 設定，只新增 MariaDB 相關設定

## 驗證方法
重新啟動伺服器後，您應該不再看到類似以下的 debug 訊息：
```
[main] DEBUG org.mariadb.jdbc.client.impl.StandardClient - execute query: ...
```

## 其他已關閉的 Debug 訊息
除了 MariaDB JDBC，設定檔中也已關閉：
- C3P0 連接池的 debug 訊息
- Apache 相關的 debug 訊息
- W3C 相關的 debug 訊息

## 注意事項
- 設定檔修改後需要重新啟動伺服器才能生效
- 如果仍有 debug 訊息出現，可能是其他 logging 框架或程式碼中的硬編碼設定
- 所有設定都設為 WARN 等級，只會顯示警告和錯誤訊息

## 檔案位置
- `config/log4j.properties` - Log4j 1.x 設定
- `config/logging.properties` - Java Util Logging 設定  
- `config/log4j2.xml` - Log4j 2.x 設定（新建立）

設定完成時間：2025-06-27 01:57 