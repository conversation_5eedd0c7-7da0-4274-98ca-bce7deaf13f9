# 攻擊動作問題分析報告

## 問題描述
使用者反映「目前更新過後，依然無法攻擊，無法施展攻擊動作」

## 分析時間
2024年12月19日

## 系統架構分析

### 1. 攻擊流程架構
```
玩家點擊攻擊 → C_Attack.start() → L1PcInstance.onAction() → L1AttackPc → action() → commit()
```

### 2. 攻擊動作關鍵點
- **C_Attack.start()**: 攻擊封包處理入口
- **L1PcInstance.onAction()**: 玩家攻擊處理
- **L1AttackPc.action()**: 攻擊動作執行
- **L1AttackPc.commit()**: 傷害提交

## 關鍵程式碼分析

### 1. 攻擊封包處理 (C_Attack.java)
**位置**: `com/lineage/server/clientpackets/C_Attack.java`

```java
@Override
public void start(final byte[] decrypt, final ClientExecutor client) {
    // 1. 基本狀態檢查
    if (pc.isDead() || pc.isTeleport() || pc.isPrivateShop()) {
        return;
    }
    
    // 2. 加速器檢查
    final int result = pc.speed_Attack().checkInterval(
        AcceleratorChecker.ACT_TYPE.ATTACK);
    if (result == 2) {
        C_Attack._log.error("要求角色攻擊:速度異常(" + pc.getName() + ")");
        return;
    }
    
    // 3. 隱身狀態檢查
    if (pc.isInvisble() || pc.isInvisDelay()) {
        return;
    }
    
    // 4. 特殊狀態檢查
    if (pc.isParalyzedX() || pc.hasSkillEffect(8007)) {
        return;
    }
    
    // 5. 目標檢查
    final L1Object target = World.get().findObject(targetId);
    if (target instanceof L1Character && target.getMapId() != pc.getMapId()) {
        return;
    }
}
```

### 2. 攻擊動作執行 (L1AttackPc.java)
**位置**: `com/lineage/server/model/L1AttackPc.java`

```java
@Override
public void action() {
    try {
        if (_pc == null || _target == null) {
            return;
        }
        
        _pc.setHeading(_pc.targetDirection(_targetX, _targetY));
        
        // 根據武器範圍決定攻擊類型
        if (_weaponRange == -1) {
            actionX1(); // 遠距離攻擊
        } else {
            actionX2(); // 近距離攻擊
        }
    } catch (Exception e) {
        L1AttackPc._log.error(e.getLocalizedMessage(), e);
    }
}
```

### 3. 攻擊封包發送
**位置**: `com/lineage/server/serverpackets/S_AttackPacketPc.java`

```java
public S_AttackPacketPc(L1PcInstance pc, L1Character target, int type, int dmg) {
    writeC(30); // 攻擊封包類型
    writeC(_actid); // 動作ID
    writeD(pc.getId()); // 攻擊者ID
    writeD(targetobj); // 目標ID
    writeH(dmg > 0 ? 10 : 0); // 傷害標記
    writeC(pc.getHeading()); // 方向
    writeD(sequentialNumber); // 序號
    writeH(spellgfx); // 特效ID
    writeC(0);
    writeH(pc.getX()); // 攻擊者X座標
    writeH(pc.getY()); // 攻擊者Y座標
    writeH(targetx); // 目標X座標
    writeH(targety); // 目標Y座標
}
```

## 可能問題點分析

### 1. 狀態檢查問題 (高風險)
**問題**: 玩家狀態檢查過於嚴格
**影響**: 可能阻止正常攻擊

```java
// 可能問題的檢查點
if (pc.isParalyzedX()) return; // 麻痺檢查
if (pc.hasSkillEffect(8007)) return; // 特殊技能效果
if (pc.isInvisble() || pc.isInvisDelay()) return; // 隱身檢查
```

### 2. 加速器檢查問題 (中風險)
**問題**: 攻擊速度檢查可能誤判
**影響**: 正常攻擊被誤判為加速

```java
final int result = pc.speed_Attack().checkInterval(
    AcceleratorChecker.ACT_TYPE.ATTACK);
if (result == 2) {
    C_Attack._log.error("要求角色攻擊:速度異常(" + pc.getName() + ")");
    return;
}
```

### 3. 武器檢查問題 (中風險)
**問題**: 武器狀態檢查可能影響攻擊
**影響**: 武器相關攻擊被阻止

```java
// 武器耐久度檢查
if (weapon.getEnchantLevel() < 0) {
    // 武器損壞，無法攻擊
    return;
}
```

### 4. 距離檢查問題 (低風險)
**問題**: 攻擊距離計算可能不準確
**影響**: 正常距離的攻擊被拒絕

```java
double distance = pc.getLocation().getLineDistance(target.getLocation());
if (distance > weaponRange) {
    // 距離過遠，無法攻擊
    return;
}
```

### 5. 封包發送問題 (低風險)
**問題**: 攻擊封包可能未正確發送
**影響**: 客戶端未收到攻擊動作

## 診斷工具功能

### 1. 攻擊動作診斷
```java
String result = AttackActionDiagnostic.diagnoseAttackAction(pc, target);
```
- 檢查玩家狀態
- 檢查武器狀態
- 檢查距離
- 檢查攻擊流程
- 檢查特效相關
- 檢查配置

### 2. 攻擊動作測試
```java
String result = AttackActionDiagnostic.testAttackAction(pc, target);
```
- 執行完整攻擊流程
- 記錄攻擊前後狀態
- 驗證傷害計算
- 檢查攻擊結果

### 3. 攻擊封包檢查
```java
String result = AttackActionDiagnostic.checkAttackPackets(pc, target);
```
- 檢查武器類型
- 檢查攻擊範圍
- 檢查封包參數
- 驗證封包格式

## 建議修復方案

### 1. 立即檢查項目
1. **玩家狀態檢查**: 確認是否有不必要的狀態檢查阻止攻擊
2. **加速器檢查**: 檢查攻擊速度檢查邏輯是否正確
3. **武器檢查**: 確認武器狀態檢查是否過於嚴格
4. **距離檢查**: 驗證攻擊距離計算是否準確

### 2. 調試步驟
1. 使用診斷工具檢查具體問題點
2. 檢查伺服器日誌中的錯誤訊息
3. 驗證攻擊封包是否正確發送
4. 測試不同武器類型的攻擊

### 3. 修復建議
1. **放寬狀態檢查**: 移除可能誤判的狀態檢查
2. **優化加速器檢查**: 調整攻擊速度檢查參數
3. **改進武器檢查**: 優化武器狀態判斷邏輯
4. **增強錯誤處理**: 添加更詳細的錯誤日誌

## 測試建議

### 1. 基本測試
- 測試空手攻擊
- 測試不同武器類型攻擊
- 測試不同距離攻擊
- 測試不同狀態下攻擊

### 2. 進階測試
- 測試變身狀態攻擊
- 測試技能效果下攻擊
- 測試特殊裝備攻擊
- 測試多人同時攻擊

### 3. 壓力測試
- 測試高頻率攻擊
- 測試大量玩家同時攻擊
- 測試網路延遲情況
- 測試伺服器負載情況

## 結論

攻擊動作無法施展的問題可能涉及多個層面：
1. **狀態檢查過於嚴格** (最可能)
2. **加速器檢查誤判** (較可能)
3. **武器檢查邏輯問題** (可能)
4. **封包發送問題** (較少可能)

建議優先使用診斷工具進行詳細檢查，找出具體的問題點，然後針對性地進行修復。 