# 職業武器關係分析報告

## 問題描述
使用者詢問「攻擊跟職業武器類別有關係嗎」，經過分析發現攻擊確實與職業和武器類別有密切關係。

## 關係分析

### 1. **職業與武器類別的關係**

#### **武器類型定義**
從 `ItemTable.java` 可以看出武器類型分為：

**武器類型1 (Type1):**
- 0: 無
- 1: 劍
- 2: 匕首  
- 3: 雙手劍
- 4: 弓
- 5: 槍
- 6: 鈍器
- 7: 法杖
- 8: 投擲刀
- 9: 箭
- 10: 拳套
- 11: 爪
- 12: 雙手劍
- 13: 單手弓
- 14: 單手槍
- 15: 雙手鈍器
- 16: 雙手法杖
- 17: 奇林庫
- 18: 鎖鏈劍
- 20: 弓
- 40: 法杖
- 46: 匕首
- 50: 雙手劍
- 62: 拳套
- 66: 箭
- 2922: 投擲刀

**武器類型2 (Type2):**
- 0: 無
- 1: 劍
- 2: 匕首
- 3: 雙手劍
- 4: 弓
- 5: 槍
- 6: 鈍器
- 7: 法杖
- 8: 投擲刀
- 9: 箭
- 10: 拳套
- 11: 爪
- 12: 雙手劍
- 13: 單手弓
- 14: 單手槍
- 15: 雙手鈍器
- 16: 雙手法杖
- 17: 奇林庫
- 18: 鎖鏈劍

#### **職業武器限制**
從 `L1Weapon.java` 可以看出每個武器都有職業限制：

```java
weapon.setUseRoyal(rs.getInt("use_royal") != 0);      // 王族可用
weapon.setUseKnight(rs.getInt("use_knight") != 0);    // 騎士可用
weapon.setUseElf(rs.getInt("use_elf") != 0);          // 妖精可用
weapon.setUseMage(rs.getInt("use_mage") != 0);        // 法師可用
weapon.setUseDarkelf(rs.getInt("use_darkelf") != 0);  // 黑暗妖精可用
```

### 2. **職業傷害加成系統**

#### **職業基礎傷害加成**
從 `L1AttackPc.java` 的 `calcNpcDamage()` 方法可以看出：

```java
if (_pc.isCrown()) {
    dmg *= Config_Pc_Damage.Other_To_isCrownnpc;
} else if (_pc.isKnight()) {
    dmg *= Config_Pc_Damage.Other_To_isKnightnpc;
} else if (_pc.isWizard()) {
    dmg *= Config_Pc_Damage.Other_To_isWizardnpc;
} else if (_pc.isElf()) {
    dmg *= Config_Pc_Damage.Other_To_isElfnpc;
} else if (_pc.isDarkelf()) {
    dmg *= Config_Pc_Damage.Other_To_isDarkelfnpc;
} else if (_pc.isDragonKnight()) {
    dmg *= Config_Pc_Damage.Other_To_isDragonKnightnpc;
} else if (_pc.isIllusionist()) {
    dmg *= Config_Pc_Damage.Other_To_isIllusionistnpc;
}
```

#### **職業特殊技能加成**

**王族特殊技能:**
```java
if (_pc.isCrown() && _pc.isEsoteric()) {
    dmg = (int) (dmg * (1.0 + _pc.getlogpcpower_SkillFor2() * 0.02));
}
```

**妖精特殊技能:**
```java
// 射擊之箭
if (_weaponType == 20 && _pc.isTripleArrow() && _pc.getlogpcpower_SkillFor1() != 0) {
    dmg += 110.0;
}

// 精通元素
if (_pc.hasSkillEffect(175) && _weaponType != 20 && _weaponType != 62 && _pc.isElf()) {
    dmg *= 3.0;
}
```

**黑暗妖精特殊技能:**
```java
// 刀劍之影
if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor5() != 0 && _pc.isEsoteric()) {
    dmg += 5 * _pc.getlogpcpower_SkillFor5();
}

// 背部襲擊
if (_pc.isDarkelf() && _pc.getlogpcpower_SkillFor1() != 0) {
    dmg = (int) (dmg * 1.5);
}
```

**龍騎士特殊技能:**
```java
// 屠宰擊殺
if (_pc.isDragonKnight() && _pc.isFoeSlayer() && _pc.getlogpcpower_SkillFor1() != 0) {
    dmg += _target.getCurrentHp() * 5 / 100;
}

// 乾坤挪移
if (_pc.isDragonKnight() && _pc.getlogpcpower_SkillFor2() != 0) {
    double chp = dmg;
    _pc.setCurrentHp((int) (_pc.getCurrentHp() + chp));
}
```

**騎士特殊技能:**
```java
// 狂暴致命
if (_pc.isKnight() && _pc.getlogpcpower_SkillFor4() != 0) {
    dmg *= 1.9;
}
```

### 3. **武器類型特殊效果**

#### **武器類型2特殊處理**
從 `weaponDamage1()` 方法可以看出：

```java
switch (_weaponType2) {
    case 3: { // 雙手劍
        weaponDamage = L1AttackMode._random.nextInt(weaponMaxDamage) + 1;
        // 特殊武器處理
        if (_weaponId == 217 && _pc.getLawful() < 500) {
            int a = _pc.getLawful() / 1000;
            int b = Math.abs(a);
            weaponDamage += b;
        }
        break;
    }
    case 11: { // 爪
        weaponDamage = L1AttackMode._random.nextInt(weaponMaxDamage) + 1;
        if (L1AttackMode._random.nextInt(100) + 1 <= _weaponDoubleDmgChance) {
            weaponDamage = weaponMaxDamage; // 雙擊效果
        }
        break;
    }
    // ... 其他武器類型
}
```

#### **武器強化效果**
```java
if (_weaponEnchant >= 10) {
    dmg += _weaponEnchant - 9; // 高強化額外加成
}
```

### 4. **攻擊流程中的職業武器關係**

#### **命中計算**
```java
_hitRate = _pc.getLevel();
_hitRate += str_dex_Hit(); // 力量敏捷命中加成

if (_weaponType != 20 && _weaponType != 62) {
    _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getHitup() + _pc.getOriginalHitup() + _weaponEnchant * 0.6));
} else {
    _hitRate = (int) (_hitRate + (_weaponAddHit + _pc.getBowHitup() + _pc.getOriginalBowHitup() + _weaponEnchant * 0.6));
}
```

#### **傷害計算**
```java
_weaponDamage = weaponDamage1(weaponMaxDamage);
_weaponTotalDamage = _weaponDamage + _weaponAddDmg + _weaponEnchant;
double dmg = weaponDamage2(_weaponTotalDamage);
dmg = pcDmgMode(dmg); // 職業傷害模式
```

## 創建工具

### 1. **職業武器分析工具**
**檔案**: `com/lineage/server/utils/ClassWeaponAnalyzer.java`

**功能**:
- `analyzeClassWeaponRelation()` - 分析職業武器關係
- `checkWeaponUsability()` - 檢查職業是否可使用武器
- `analyzeDamageBonus()` - 分析傷害加成
- `sendAnalysisMessage()` - 發送分析訊息

### 2. **主要分析方法**
- 職業類型識別
- 武器類型分析
- 職業武器相容性檢查
- 傷害加成計算
- 特殊技能效果分析

## 結論

### **攻擊與職業武器類別的關係**

1. **職業限制**: 不同職業只能使用特定類型的武器
2. **傷害加成**: 每個職業對不同武器類型有不同的傷害加成
3. **特殊技能**: 職業特殊技能會根據武器類型觸發不同效果
4. **武器特效**: 不同武器類型有獨特的攻擊效果（如雙擊、額外傷害等）
5. **強化效果**: 武器強化對不同職業的影響不同

### **影響攻擊的因素**

1. **職業基礎屬性**: 力量、敏捷、智力等
2. **職業特殊加成**: 各職業的專屬傷害加成
3. **武器類型**: 劍、弓、法杖等不同類型
4. **武器強化**: 強化等級影響傷害
5. **職業技能**: 特殊技能和轉生技能
6. **裝備搭配**: 防具和飾品的加成效果

### **建議**

1. **選擇合適武器**: 根據職業選擇最佳武器類型
2. **強化武器**: 提升武器強化等級增加傷害
3. **學習技能**: 掌握職業特殊技能
4. **裝備搭配**: 選擇適合的防具和飾品
5. **屬性分配**: 合理分配角色屬性點

現在您的攻擊系統應該已經完全理解職業和武器類別的關係了！ 