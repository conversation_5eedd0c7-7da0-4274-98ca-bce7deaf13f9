{"java.home": "C:\\Program Files\\Java\\jdk-1.8", "java.configuration.runtimes": [{"name": "JavaSE-1.8", "path": "C:\\Program Files\\Java\\jdk-1.8", "default": true}], "java.jdt.ls.java.home": "C:\\Program Files\\Java\\jdk-1.8", "java.compile.nullAnalysis.mode": "automatic", "java.format.settings.profile": "GoogleStyle", "java.completion.enabled": true, "java.debug.settings.enableRunDebugCodeLens": true, "java.test.config": {"vmargs": ["-Xmx512M", "-XX:MaxPermSize=256M"]}, "files.encoding": "utf8", "files.eol": "\r\n", "editor.tabSize": 4, "editor.insertSpaces": true, "editor.detectIndentation": false, "java.project.sourcePaths": ["src/main/java"], "java.project.outputPath": "target/classes", "java.project.referencedLibraries": ["lib/**/*.jar"], "java.saveActions.organizeImports": true, "java.format.onType.enabled": true, "java.signatureHelp.enabled": true, "java.contentProvider.preferred": "fernflower", "java.maven.downloadSources": true, "java.maven.downloadJavadoc": true, "java.configuration.updateBuildConfiguration": "automatic", "java.import.gradle.enabled": false, "java.import.maven.enabled": true, "java.server.launchMode": "Standard", "java.format.settings.url": ".vscode/java-formatter.xml"}