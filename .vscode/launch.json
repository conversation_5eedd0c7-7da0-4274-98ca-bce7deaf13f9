{"version": "0.2.0", "configurations": [{"type": "java", "name": "Launch Lineage Server", "request": "launch", "mainClass": "com.lineage.Server", "projectName": "Lineage381", "vmArgs": ["-Xmx2G", "-Xms1G", "-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200", "-XX:+UseStringDeduplication", "-XX:+OptimizeStringConcat", "-Dfile.encoding=UTF-8"], "env": {"JAVA_HOME": "C:\\Program Files\\Java\\jdk-1.8"}, "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}"}, {"type": "java", "name": "Debug Lineage Server", "request": "launch", "mainClass": "com.lineage.Server", "projectName": "Lineage381", "vmArgs": ["-Xmx2G", "-Xms1G", "-XX:+UseG1GC", "-XX:MaxGCPauseMillis=200", "-Dfile.encoding=UTF-8", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005"], "env": {"JAVA_HOME": "C:\\Program Files\\Java\\jdk-1.8"}, "console": "internalConsole", "stopOnEntry": false, "cwd": "${workspaceFolder}"}]}