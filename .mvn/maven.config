# Maven 構建優化配置
# 此檔案包含 Maven 執行時的預設參數

# 並行構建 - 使用 4 個執行緒
-T 4

# 編譯器優化
-Dmaven.compile.fork=true
-Dmaven.compiler.maxmem=1024m

# 檔案編碼設定
-Dfile.encoding=UTF-8
-Dproject.build.sourceEncoding=UTF-8

# JVM 記憶體設定
-Xmx2g
-Xms512m

# 垃圾回收器優化
-XX:+UseG1GC
-XX:+UseStringDeduplication

# 系統屬性
-Djava.awt.headless=true
-Duser.timezone=Asia/Taipei

# Maven 行為優化
-Dmaven.artifact.threads=10
-Dmaven.resolver.transport=wagon

# 測試優化 (如果需要跳過測試，可以取消註解)
# -DskipTests=true
# -Dmaven.test.skip=true

# 安靜模式 (減少輸出，可選)
# -q

# 批次模式 (非互動式)
-B

# 顯示版本資訊
-V
