# JVM 優化配置
# 此檔案包含 Maven 執行時的 JVM 參數

# 堆記憶體設定
-Xms1g
-Xmx2g

# 新生代記憶體設定
-Xmn512m

# 垃圾回收器設定
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+UseStringDeduplication
-XX:+OptimizeStringConcat

# GC 日誌設定 (Java 11+)
-Xlog:gc*:gc.log:time,tags

# 記憶體優化
-XX:+UseCompressedOops
-XX:+UseCompressedClassPointers

# 編譯優化
-XX:+TieredCompilation
-XX:TieredStopAtLevel=1

# 系統屬性
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Taipei

# Maven 特定優化
-Dmaven.multiModuleProjectDirectory=${maven.multiModuleProjectDirectory}

# 安全設定
-Djava.security.egd=file:/dev/./urandom

# 網路優化
-Djava.net.preferIPv4Stack=true
-Dsun.net.useExclusiveBind=false

# 調試選項 (開發時可啟用)
# -XX:+PrintGCDetails
# -XX:+PrintGCTimeStamps
# -XX:+HeapDumpOnOutOfMemoryError
# -XX:HeapDumpPath=./heapdumps/

# 遠程調試 (開發時可啟用)
# -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005
