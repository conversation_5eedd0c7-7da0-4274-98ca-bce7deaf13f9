# ç¶²è·¯å®å¨ç®¡çå¨åªåéç½®æªæ¡
# æªæ¡è·¯å¾: ./config/ç¶²è·¯å®å¨ç®¡çå¨åªåéç½®.properties

# ========================================
# åºæ¬å®å¨è¨­å®
# ========================================

# åç¨åªåçå®å¨ç®¡çå¨ (true/false)
ENABLE_OPTIMIZED_SECURITY=true

# åç¨è©³ç´°æ¥èªè¨é (true/false)
ENABLE_DETAILED_LOGGING=false

# åç¨çµ±è¨å ±å (true/false)
ENABLE_STATISTICS=true

# ========================================
# IP æª¢æ¥è¨­å®
# ========================================

# åç¨å°åæ»ææª¢æ¥ (true/false)
IPCHECKPACK=false

# åç¨ä¸è¬IPæª¢æ¥ (true/false)
IPCHECK=true

# é£æ¥æéééæª¢æ¥ (æ¯«ç§)
TIMEMILLIS=1000

# åè¨±çé£æ¥æ¬¡æ¸
COUNT=10

# å®IPéå¶éé (true/false)
ISONEIP=false

# å®IPéå¶æé (æ¯«ç§)
ONETIMEMILLIS=20000

# Socket è¶ææé (ç§)
timeOutSocket=60

# ========================================
# è¨æ¶é«ç®¡çè¨­å®
# ========================================

# æå¤§å¿«åå¤§å°
MAX_CACHE_SIZE=10000

# è¨æ¶é«æ¸çéé (ç§)
MEMORY_CLEANUP_INTERVAL=60

# LRU å¿«åå¤§å° (IPæ»ææª¢æ¥å¨)
LRU_CACHE_SIZE=5000

# ========================================
# æ¸çä»»åè¨­å®
# ========================================

# è¨æå°éæ¸çéé (ç§)
TEMP_BAN_CLEANUP_INTERVAL=1

# å®IPéå¶æ¸çéé (ç§)
SINGLE_IP_CLEANUP_INTERVAL=10

# çµ±è¨å ±åéé (ç§)
STATISTICS_INTERVAL=300

# ========================================
# è³æåº«è¨­å®
# ========================================

# åç¨è³æåº«è¨é (true/false)
SETDB=true

# åç¨ UFW é²ç«çæ´å (true/false)
UFW=true

# ========================================
# ç½åå®è¨­å®
# ========================================

# åç¨ç½åå®æ©å¶ (true/false)
ENABLE_WHITELIST=true

# ç½åå®æªæ¡è·¯å¾
WHITELIST_FILE=./config/whitelist.txt

# ========================================
# æ§è½åªåè¨­å®
# ========================================

# ç·ç¨æ± å¤§å°
THREAD_POOL_SIZE=2

# é£æ¥æª¢æ¥ç·©è¡å¤§å°
CONNECTION_CHECK_BUFFER=1000

# åç¨é£æ¥æ±  (true/false)
ENABLE_CONNECTION_POOL=true

# ========================================
# ç£æ§è¨­å®
# ========================================

# åç¨å¯¦æç£æ§ (true/false)
ENABLE_MONITORING=true

# ç£æ§æ´æ°éé (ç§)
MONITORING_INTERVAL=30

# åç¨æ§è½ææ¨æ¶é (true/false)
ENABLE_PERFORMANCE_METRICS=true

# ========================================
# é²éå®å¨è¨­å®
# ========================================

# åç¨å°çä½ç½®æª¢æ¥ (true/false)
ENABLE_GEO_LOCATION_CHECK=false

# åç¨ä»£çæª¢æ¸¬ (true/false)
ENABLE_PROXY_DETECTION=false

# åç¨è¡çºåæ (true/false)
ENABLE_BEHAVIOR_ANALYSIS=false

# åç¨æ©å¨å­¸ç¿æª¢æ¸¬ (true/false)
ENABLE_ML_DETECTION=false

# ========================================
# æ¥èªè¨­å®
# ========================================

# æ¥èªç´å¥ (DEBUG, INFO, WARN, ERROR)
LOG_LEVEL=INFO

# åç¨å®å¨äºä»¶æ¥èª (true/false)
ENABLE_SECURITY_EVENT_LOG=true

# å®å¨äºä»¶æ¥èªæªæ¡è·¯å¾
SECURITY_LOG_FILE=./loginfo/security.log

# åç¨å¯©è¨æ¥èª (true/false)
ENABLE_AUDIT_LOG=false

# å¯©è¨æ¥èªæªæ¡è·¯å¾
AUDIT_LOG_FILE=./loginfo/audit.log

# ========================================
# ç·æ¥èçè¨­å®
# ========================================

# åç¨ç·æ¥æ¨¡å¼ (true/false)
ENABLE_EMERGENCY_MODE=false

# ç·æ¥æ¨¡å¼é¾å¼ (é£æ¥æ¸)
EMERGENCY_THRESHOLD=1000

# ç·æ¥æ¨¡å¼æçºæé (ç§)
EMERGENCY_DURATION=300

# åç¨èªåæ¢å¾© (true/false)
ENABLE_AUTO_RECOVERY=true

# ========================================
# éç¥è¨­å®
# ========================================

# åç¨ç®¡çå¡éç¥ (true/false)
ENABLE_ADMIN_NOTIFICATION=false

# éç¥é¾å¼ (æ»ææ¬¡æ¸)
NOTIFICATION_THRESHOLD=50

# éç¥éé (ç§)
NOTIFICATION_INTERVAL=300

# ========================================
# æ¸¬è©¦è¨­å®
# ========================================

# åç¨æ¸¬è©¦æ¨¡å¼ (true/false)
ENABLE_TEST_MODE=false

# æ¸¬è©¦IPåè¡¨
TEST_IPS=127.0.0.1,***********

# åç¨å£åæ¸¬è©¦ (true/false)
ENABLE_STRESS_TEST=false

# å£åæ¸¬è©¦æçºæé (ç§)
STRESS_TEST_DURATION=60 