# è£åç¹æåªåè¨­å®æª
# ç¨æ¼ç®¡çè£åæçºç¹æç³»çµ±çåç¨®åªååè½
# æ³¨æï¼æææ¸å¼é½ç¶éå¹³è¡¡èéï¼è«è¬¹æèª¿æ´

# ========================================
# åºæ¬ç¹æç³»çµ±è¨­å®
# ========================================
# åç¨è£åç¹æåªå
ENABLE_EQUIPMENT_EFFECT_OPTIMIZATION=true

# ç¹ææª¢æ¥é »ç (æ¯«ç§)
EFFECT_CHECK_INTERVAL=15000          # ç¹ææª¢æ¥éé (15ç§)

# ç¹ææçºæéè¨­å®
EFFECT_DURATION_BASE=300000          # åºç¤ç¹ææçºæé (5åé)
EFFECT_DURATION_EXTENDED=600000      # å»¶é·ç¹ææçºæé (10åé)

# ========================================
# æè½åªåè¨­å®
# ========================================
# å¿«åè¨­å®
EFFECT_CACHE_SIZE=1000               # ç¹æå¿«åå¤§å°
EFFECT_CACHE_EXPIRE_TIME=300000      # å¿«åéææé (5åé)

# è¨æ¶é«ç®¡ç
MAX_EFFECTS_PER_PLAYER=50            # æ¯åç©å®¶æå¤§ç¹ææ¸é
EFFECT_CLEANUP_INTERVAL=60000        # ç¹ææ¸çéé (1åé)

# ========================================
# ç¹æé¡åè¨­å®
# ========================================
# æ­¦å¨ç¹æ
ENABLE_WEAPON_EFFECTS=true
WEAPON_EFFECT_BONUS=1.2              # æ­¦å¨ç¹æå æ

# é²å·ç¹æ
ENABLE_ARMOR_EFFECTS=true
ARMOR_EFFECT_BONUS=1.1               # é²å·ç¹æå æ

# é£¾åç¹æ
ENABLE_ACCESSORY_EFFECTS=true
ACCESSORY_EFFECT_BONUS=1.3           # é£¾åç¹æå æ

# ========================================
# ç¹æè§¸ç¼æ¢ä»¶
# ========================================
# ç­ç´ç¸éç¹æ
ENABLE_LEVEL_BASED_EFFECTS=true
MIN_LEVEL_FOR_EFFECTS=1              # ç¹ææä½ç­ç´è¦æ±
MAX_LEVEL_FOR_EFFECTS=99             # ç¹ææé«ç­ç´éå¶

# è·æ¥­ç¸éç¹æ
ENABLE_CLASS_BASED_EFFECTS=true
CLASS_EFFECT_BONUS=1.1               # è·æ¥­ç¹æå æ

# ========================================
# ç¹æåè³ªè¨­å®
# ========================================
# è£ååè³ªç¹æ
ENABLE_QUALITY_BASED_EFFECTS=true

# åè³ªç¹æå æ
RARE_ITEM_EFFECT_BONUS=1.2           # ç¨æè£åç¹æå æ
LEGENDARY_ITEM_EFFECT_BONUS=1.3      # å³èªªè£åç¹æå æ
MYTHIC_ITEM_EFFECT_BONUS=1.5         # ç¥è©±è£åç¹æå æ

# ========================================
# ç¹æçå è¨­å®
# ========================================
# ç¹æçå 
ENABLE_EFFECT_STACKING=true
MAX_EFFECT_STACKS=3                  # æå¤§ç¹æçå å±¤æ¸
STACK_BONUS_MULTIPLIER=0.8           # çå å æåæ¸

# ========================================
# ç¹æè¡çªèç
# ========================================
# ç¹æè¡çª
ENABLE_EFFECT_CONFLICT_RESOLUTION=true
CONFLICT_RESOLUTION_PRIORITY=1       # è¡çªè§£æ±ºåªåç´ (1=é«, 2=ä¸­, 3=ä½)

# ========================================
# é¯èª¤èçè¨­å®
# ========================================
# é¯èª¤èç
ENABLE_ERROR_HANDLING=true
MAX_ERROR_RETRY_COUNT=3              # æå¤§é¯èª¤éè©¦æ¬¡æ¸
ERROR_RETRY_DELAY=5000               # é¯èª¤éè©¦å»¶é² (5ç§)

# ========================================
# æ¥èªè¨­å®
# ========================================
# æ¥èªè¨é
ENABLE_EFFECT_LOGGING=true
LOG_LEVEL=INFO                       # æ¥èªç­ç´ (DEBUG, INFO, WARN, ERROR)
LOG_EFFECT_TRIGGERS=true             # è¨éç¹æè§¸ç¼
LOG_EFFECT_FAILURES=true             # è¨éç¹æå¤±æ

# ========================================
# é¤é¯è¨­å®
# ========================================
# é¤é¯æ¨¡å¼
ENABLE_DEBUG_MODE=false
DEBUG_EFFECT_DETAILS=false           # é¤é¯ç¹æè©³ç´°è³è¨
DEBUG_PERFORMANCE_METRICS=false      # é¤é¯æè½ææ¨ 