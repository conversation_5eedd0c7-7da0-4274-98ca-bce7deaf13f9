# 開發環境配置
spring:
  application:
    name: lineage381-server-dev

# 伺服器配置
server:
  port: 2000
  name: "Lineage381 Server (Dev)"
  version: "Ver_2100381916"
  
  # 資料庫配置
  database:
    host: localhost
    port: 3306
    name: lineage
    username: root
    password: ""
    connection-pool:
      initial-size: 3
      max-size: 10
      min-idle: 3
      max-idle: 5
      timeout: 30000

  # 遊戲配置
  game:
    max-players: 100
    auto-save-interval: 60
    exp-rate: 2.0
    drop-rate: 2.0
    adena-rate: 2.0
    
  # 安全配置
  security:
    ip-check: false
    anti-hack: false
    max-connections-per-ip: 10
    
  # 日誌配置
  logging:
    level:
      com.lineage: DEBUG
      org.springframework: DEBUG
    file:
      name: ./loginfo/lineage-server-dev.log
      max-size: 50MB
      max-history: 7 