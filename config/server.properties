#-------------------------------------------------------------
# Server config
#-------------------------------------------------------------
ç®¡çä»é¢éé True=æ¯, False=å¦
GUI = false

# ä½¿ç¨çä¼ºæå¨ç·¨è
ServerNo = 1

# ä½¿ç¨ipä½ç½®
GameserverHostname= *

# æåå¨ç£è½ç«¯å£ä»¥"-"æ¸èåé åè¨±è¨­ç½®å¤å(åè¨±è¨­ç½®ä¸å)
GameserverPort= 2000

# èªç³»è¨­ç½® 0:è±æç 3:ç¹é«ä¸­æ 5:ç°¡é«ä¸­æ
ClientLanguage = 3

# èªåéæ°åå (å®ä½:æé å°æ:åé ç¯ä¾> 04:30)
# null ä¸å·è¡èªåéæ°åå(ä½¿ç¨,åééæ°ååæéæ¬ä½)
AutoRestart = null

# æ¯å¦éåæ©æ¿å¾å°BAN-IPç³»çµ±(true=æ ¸å¿L1PowerKickä¸è½ä½¿ç¨ï¼false=æ ¸å¿L1PowerKickå¯ä»¥ä½¿ç¨)
BanIpFromIDC=false

# æ¯å¦éåæ¸¬è©¦ä¼ºæå¨æ¨¡å¼(true = éå ï¼ false = éé)
# éåå¾ï¼TestServerPassWordså¯è¨­ç½®ç»å¥å°ç¨å¯ç¢¼ï¼è¼¸å¥é¯èª¤æç»å¥å¤±æã
# GM_ModeéåçºTrueï¼å¨ä¼ºæå¨ææè§è²ç»å¥é²å»é½æ¯GMã
TestServer = false
TestServerPassWords=wwffttest2
GM_Mode = false

# é¡¯ç¤ºç»å¥å¬å
News = false

# ä¼ºæå¨æåçè¨­å® CST:ä¸­åæ¨æºæ TST:å°ç£æ¨æºæ HKT:é¦æ¸¯æ¨æºæ CCT:ä¸­åæ¨æºæ JST:æ¥æ¬æ¨æºæ 
TimeZone = TST

# éç½®äººç©èªåæ·ç·æé (å®ä½:å)
# 0-35791(0ä¸å·è¡èªåæ·ç·)
AutomaticKick = 0

# äººç©è³æèªåä¿å­æé (å®ä½:ç§)
AutosaveInterval = 30

# äººç©ç©åè³æèªåä¿å­æé (å®ä½:ç§)
AutosaveIntervalOfInventory = 30

# èªåå»ºç«å¸³è
AutoCreateAccounts = true

# åè¨±æå¤§é£ç·ç¨æ¶
MaximumOnlineUsers = 5000

# å®¢æ¶ç«¯æ¥æ¶ä¿¡æ¯ç¯å (-1çºç«é¢å§å¯è¦)
PcRecognizeRange = -1

# å¿«ééµè¨æ¶èçå·è¡
CharacterConfigInServerSide = true

# ç«¯å£éç½®æé(å®ä½:åé)
restartlogin = 360

#æ¯å¦èªåçæå°åå¿«åæªæ¡ true or false
#å¦æéåå°å¨ ./data/mapcache/ å§ç¢çå¿«åæªæ¡
#ä¼ºæå¨ååæå°åæªæ¡è®åéåº¦å°æå¢å¿«2~5åå·¦å³
CacheMapFiles = false
