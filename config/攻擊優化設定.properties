# ========================================
# æ»æåªåè¨­å®æªæ¡
# æ³¨æï¼æææ¸å¼é½ç¶éå¹³è¡¡æ§èéï¼è«è¬¹æèª¿æ´
# ========================================

# ===== æ»æéåº¦åªå =====
ENABLE_ATTACK_SPEED_OPTIMIZATION=true
BRAVE_STATUS_HIT_BONUS=5
HIGH_LEVEL_HIT_BONUS_DIVISOR=5
MIN_LEVEL_FOR_HIT_BONUS=60

# ===== å·å®³åªå =====
ENABLE_DAMAGE_OPTIMIZATION=true
HIGH_ENCHANT_DAMAGE_MULTIPLIER=1
MIN_ENCHANT_FOR_BONUS=8

# ===== æºè½æ»æ =====
ENABLE_SMART_ATTACK=true
BOSS_DAMAGE_BONUS_DIVISOR=20
LOW_HP_MONSTER_DAMAGE_BONUS=2
LOW_HP_MONSTER_THRESHOLD=0.2
LOW_HP_PLAYER_HIT_BONUS=5
LOW_HP_PLAYER_THRESHOLD=0.3

# ===== æ´æç³»çµ± =====
ENABLE_CRITICAL_SYSTEM=true
CRITICAL_CHANCE_PERCENT=3
CRITICAL_DAMAGE_MULTIPLIER=1.3

# ===== é£æç³»çµ± =====
ENABLE_COMBO_SYSTEM=true
COMBO_BONUS_DIVISOR=30

# ===== è·æ¥­ç¹å =====
ENABLE_CLASS_SPECIALIZATION=true
KNIGHT_DAMAGE_BONUS_DIVISOR=20
ELF_DAMAGE_BONUS_DIVISOR=10

# ========================================
# åè¨»
# æ¬æªæ¡å¯é¨æèª¿æ´ï¼ä¿®æ¹å¾å»ºè­°éåSERVERä»¥å¥ç¨æ°è¨­å®ã
# è¥éç±éè¼ï¼è«æ¼ç®¡çä»é¢æGMæä»¤è§¸ç¼ã
# ======================================== 