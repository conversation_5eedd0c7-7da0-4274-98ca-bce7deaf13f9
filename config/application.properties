# Lineage381 Server Spring Boot Configuration
spring.application.name=lineage381-server
spring.profiles.active=dev

# ä¼ºæå¨éç½®
server.port=2000
server.name=Lineage381 Server
server.version=Ver_2100381916

# è³æåº«éç½®
server.database.host=localhost
server.database.port=3306
server.database.name=lineage
server.database.username=root
server.database.password=
server.database.connection-pool.initial-size=5
server.database.connection-pool.max-size=20
server.database.connection-pool.min-idle=5
server.database.connection-pool.max-idle=10
server.database.connection-pool.timeout=30000

# éæ²éç½®
server.game.max-players=1000
server.game.auto-save-interval=300
server.game.exp-rate=1.0
server.game.drop-rate=1.0
server.game.adena-rate=1.0

# å®å¨éç½®
server.security.ip-check=true
server.security.anti-hack=true
server.security.max-connections-per-ip=5

# æ¥èªéç½®
server.logging.level.com.lineage=INFO
server.logging.level.org.springframework=WARN
server.logging.file.name=./loginfo/lineage-server.log
server.logging.file.max-size=100MB
server.logging.file.max-history=30 