#-------------------------------------------------------------
# log4j properties
#-------------------------------------------------------------

log4j.rootLogger=WARN, A1, A2

# A1 is set to be ConsoleAppender sending its output to System.out
log4j.appender.A1=org.apache.log4j.ConsoleAppender

# A1 uses PatternLayout.
log4j.appender.A1.layout=org.apache.log4j.PatternLayout

# The conversion pattern consists of date in ISO8601 format, level,
# thread name, logger name truncated to its rightmost two components
# and left justified to 17 characters, location information consisting
# of file name (padded to 13 characters) and line number, nested
# diagnostic context, the and the application supplied message

log4j.appender.A1.layout.ConversionPattern=%p - %m%n

# Appender A2 writes to the file "test".
log4j.appender.A2=org.apache.log4j.RollingFileAppender
log4j.appender.A2.File=./loginfo/log4j-Message.log
log4j.appender.A2.MaxFileSize = 200KB
log4j.appender.A2.MaxBackupIndex = 200

# Truncate 'test' if it aleady exists.
log4j.appender.A2.Append=false

# Appender A2 uses the PatternLayout.
log4j.appender.A2.layout=org.apache.log4j.PatternLayout
log4j.appender.A2.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss},%m%n

# ééææ c3p0 é£æ¥æ± çèª¿è©¦è¨æ¯
log4j.logger.com.mchange.v2.c3p0=ERROR
log4j.logger.com.mchange.v2.c3p0.impl=ERROR
log4j.logger.com.mchange.v2.c3p0.impl.NewPooledConnection=ERROR
log4j.logger.com.mchange.v2.c3p0.stmt=ERROR
log4j.logger.com.mchange.v2.c3p0.impl.C3P0PooledConnectionPool=ERROR
log4j.logger.com.mchange.v2.resourcepool=ERROR
log4j.logger.com.mchange.v2.async=ERROR

# ééå¶ä»å¸¸è¦çèª¿è©¦è¨æ¯
log4j.logger.org.apache.commons.dbcp=ERROR
log4j.logger.org.apache.commons.pool=ERROR
log4j.logger.org.hibernate=ERROR
log4j.logger.org.springframework=ERROR

# åªé¡¯ç¤ºéè¦çä¼ºæå¨è¨æ¯
log4j.logger.com.lineage.server=INFO
log4j.logger.com.lineage.Server=INFO

# In this example, we are not interested in INNER loop or SWAP
# messages.  You might try to set INNER and SWAP to DEBUG for more
# verbose output.

log4j.logger.org.apache.log4j.examples.SortAlgo.INNER=WARN
log4j.logger.org.apache.log4j.examples.SortAlgo.SWAP=WARN