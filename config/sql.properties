#-------------------------------------------------------------
# SQL config (MariaDB 10.11 ç¸å®¹ç)
#-------------------------------------------------------------

# è³æåº«é£æ¥é©åç¨å¼ (MariaDB 10.11)
Driver_LOGIN = org.mariadb.jdbc.Driver

# è³æåº«ä½ç½®(ç»å¥ä¼ºæå¨) (MariaDB 10.11)
URL1_LOGIN = *****************************/

# è³æåº«åç¨±(ç»å¥ä¼ºæå¨)
URL2_LOGIN = 381

# é£æ¥éå æ¢ä»¶(ç»å¥ä¼ºæå¨) (MariaDB 10.11 åªå)
URL3_LOGIN = ?useUnicode=true&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Taipei&rewriteBatchedStatements=true&cachePrepStmts=true&useServerPrepStmts=false&autoReconnect=true&maxReconnects=3&socketTimeout=30000&connectTimeout=30000

# è³æåº« å¸³æ¶åç¨±(ç»å¥ä¼ºæå¨)
Login_LOGIN = root

# è³æåº« å¸³æ¶å¯ç¢¼(ç»å¥ä¼ºæå¨)
Password_LOGIN = 0913z1007Y

# è³æåº«ä½ç½®(éæ²ä¼ºæå¨) (MariaDB 10.11)
URL1 = *****************************/

# è³æåº«åç¨±(éæ²ä¼ºæå¨)
URL2 = 381

# é£æ¥éå æ¢ä»¶(éæ²ä¼ºæå¨) (MariaDB 10.11 åªå)
URL3 = ?useUnicode=true&characterEncoding=utf8&useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Taipei&rewriteBatchedStatements=true&cachePrepStmts=true&useServerPrepStmts=false&autoReconnect=true&maxReconnects=3&socketTimeout=30000&connectTimeout=30000

# è³æåº« å¸³æ¶åç¨±(éæ²ä¼ºæå¨)
Login = root

# è³æåº« å¸³æ¶å¯ç¢¼(éæ²ä¼ºæå¨)
Password = 0913z1007Y
