# è§è²éç½®åªåç³»çµ±è¨­å®æªæ¡ (Java 1.8 ç¸å®¹ç)
# æªæ¡è·¯å¾: ./config/è§è²éç½®åªåè¨­å®.properties
# ç¸å®¹æ§: Java 1.8+

# ========================================
# åºæ¬è¨­å®
# ========================================

# åç¨åªåçè§è²éç½®ç³»çµ± (true/false)
ENABLE_OPTIMIZED_CHARACTER_CONFIG=true

# åç¨è©³ç´°æ¥èªè¨é (true/false)
ENABLE_DETAILED_LOGGING=false

# åç¨çµ±è¨å ±å (true/false)
ENABLE_STATISTICS=true

# Java çæ¬ç¸å®¹æ§æª¢æ¥ (true/false)
ENABLE_JAVA_VERSION_CHECK=true

# ========================================
# å¿«åè¨­å®
# ========================================

# LRU å¿«åæå¤§å¤§å°
MAX_CACHE_SIZE=10000

# ä¸»å¿«åæå¤§å¤§å°
MAIN_CACHE_SIZE=50000

# å¿«åæ¸çéé (ç§)
CACHE_CLEANUP_INTERVAL=300

# éæéç½®æ¸çå¤©æ¸
EXPIRED_CONFIG_DAYS=30

# ========================================
# è³æå£ç¸®è¨­å®
# ========================================

# åç¨è³æå£ç¸® (true/false)
ENABLE_DATA_COMPRESSION=true

# å£ç¸®é¾å¼ (bytes) - è¶éæ­¤å¤§å°æå£ç¸®
COMPRESSION_THRESHOLD=1024

# æå°å£ç¸®ç - åªæéå°æ­¤å£ç¸®çæå²å­å£ç¸®è³æ
MIN_COMPRESSION_RATIO=0.8

# ========================================
# è³æåº«è¨­å®
# ========================================

# éåæ­¥å·è¡å¨ç·ç¨æ¸
ASYNC_EXECUTOR_THREADS=4

# æ¹æ¬¡èçå¤§å°
BATCH_SIZE=100

# æ¹æ¬¡èçè¶ææé (æ¯«ç§)
BATCH_TIMEOUT=5000

# è³æåº«é£æ¥è¶ææé (ç§)
DB_CONNECTION_TIMEOUT=30

# ========================================
# è³æå®æ´æ§è¨­å®
# ========================================

# åç¨è³æå®æ´æ§é©è­ (true/false)
ENABLE_DATA_INTEGRITY_CHECK=true

# æ ¡é©åæ¼ç®æ³ (MD5/SHA1/SHA256)
CHECKSUM_ALGORITHM=MD5

# èªåä¿®å¾©æå£è³æ (true/false)
AUTO_REPAIR_CORRUPTED_DATA=false

# ========================================
# æ§è½ç£æ§è¨­å®
# ========================================

# åç¨æ§è½ç£æ§ (true/false)
ENABLE_PERFORMANCE_MONITORING=true

# ç£æ§éé (ç§)
MONITORING_INTERVAL=60

# æ§è½è­¦åé¾å¼
PERFORMANCE_WARNING_THRESHOLD=1000

# è¨æ¶é«ä½¿ç¨è­¦åé¾å¼ (MB)
MEMORY_WARNING_THRESHOLD=512

# ========================================
# åä»½è¨­å®
# ========================================

# åç¨èªååä»½ (true/false)
ENABLE_AUTO_BACKUP=true

# åä»½éé (å°æ)
BACKUP_INTERVAL=24

# åä»½ä¿çå¤©æ¸
BACKUP_RETENTION_DAYS=7

# åä»½æªæ¡è·¯å¾
BACKUP_PATH=./backup/character_config/

# ========================================
# é¤é¯è¨­å®
# ========================================

# åç¨é¤é¯æ¨¡å¼ (true/false)
ENABLE_DEBUG_MODE=false

# é¤é¯æ¥èªç­ç´ (DEBUG/INFO/WARN/ERROR)
DEBUG_LOG_LEVEL=INFO

# é¤é¯æªæ¡è·¯å¾
DEBUG_LOG_PATH=./logs/character_config_debug.log

# ========================================
# å®å¨è¨­å®
# ========================================

# åç¨è³æå å¯ (true/false)
ENABLE_DATA_ENCRYPTION=false

# å å¯æ¼ç®æ³ (AES/DES)
ENCRYPTION_ALGORITHM=AES

# å å¯éé° (è«ä¿®æ¹çºå®å¨çéé°)
ENCRYPTION_KEY=your_secure_encryption_key_here

# åç¨å­åæ§å¶ (true/false)
ENABLE_ACCESS_CONTROL=true

# åè¨±ç IP ç¯å (ç¨éèåé)
ALLOWED_IP_RANGES=127.0.0.1,***********/24

# ========================================
# ç¸å®¹æ§è¨­å®
# ========================================

# åç¨åå¾ç¸å®¹æ§ (true/false)
ENABLE_BACKWARD_COMPATIBILITY=true

# èªåé·ç§»èè³æ (true/false)
AUTO_MIGRATE_OLD_DATA=true

# ä¿çèè³ææ ¼å¼ (true/false)
KEEP_OLD_DATA_FORMAT=false

# Java 1.8 ç¸å®¹æ§æ¨¡å¼ (true/false)
JAVA_1_8_COMPATIBILITY_MODE=true

# ========================================
# é²éè¨­å®
# ========================================

# åç¨é è¼å¥ (true/false)
ENABLE_PRELOADING=true

# é è¼å¥ç·ç¨æ¸
PRELOAD_THREADS=2

# é è¼å¥æ¹æ¬¡å¤§å°
PRELOAD_BATCH_SIZE=50

# åç¨æºæ§å¿«å (true/false)
ENABLE_SMART_CACHING=true

# å¿«åé æ¸¬æ¨¡å¼ (LRU/LFU/ARC)
CACHE_PREDICTION_MODE=LRU

# åç¨åæ£å¼å¿«å (true/false)
ENABLE_DISTRIBUTED_CACHE=false

# åæ£å¼å¿«åç¯é»
DISTRIBUTED_CACHE_NODES=localhost:11211

# ========================================
# ç¶­è­·è¨­å®
# ========================================

# åç¨èªåç¶­è­· (true/false)
ENABLE_AUTO_MAINTENANCE=true

# ç¶­è­·æé (24å°æå¶ï¼ç¨éèåé)
MAINTENANCE_TIME=02:00,14:00

# ç¶­è­·æçºæé (åé)
MAINTENANCE_DURATION=30

# åç¨è³æåº«åªå (true/false)
ENABLE_DB_OPTIMIZATION=true

# è³æåº«åªåéé (å¤©)
DB_OPTIMIZATION_INTERVAL=7

# ========================================
# Java 1.8 ç¹å®è¨­å®
# ========================================

# ä½¿ç¨å³çµ±è¿´åèé Stream API (true/false)
USE_TRADITIONAL_LOOPS=true

# ä½¿ç¨å¿åé¡å¥èé Lambda è¡¨éå¼ (true/false)
USE_ANONYMOUS_CLASSES=true

# åç¨æ³åé¡åæ¨æ· (true/false)
ENABLE_GENERIC_TYPE_INFERENCE=false

# è¨æ¶é«ç®¡çæ¨¡å¼ (CONSERVATIVE/AGGRESSIVE)
MEMORY_MANAGEMENT_MODE=CONSERVATIVE 