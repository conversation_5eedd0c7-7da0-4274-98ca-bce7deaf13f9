#-------------------------------------------------------------
# other.properties
#-------------------------------------------------------------

# åè¨±ç©å®¶è§£æ£è¡çï¼trueåè¨±ï¼flaseä¸åè¨±ï¼
clanadel = true

# åè¨±çå¡èªè¡å»ºç«å°è
clanatitle = true

# èªè¡å»ºç«è¡çäººæ¸ä¸é
# è¨­ç½®çº0æ ä¾ç§å®æ¹è¨­ç½®
sclancount = 200

# åç¨äººç©å¨æåç§(trueåç¨ falseéé)
light = false

# é¡¯ç¤ºæªç©è¡æ¢
hpbar = true

# ä¸è¬ååºæ¯å¦é¡¯ç¤ºè©³ç´°è¨æ¯
shopinfo = true

# å»£æ­æ£é¤ æå®éå· ææ¯ é£½é£åº¦ (0:é£½é£åº¦    å¶ä»:æå®éå·ç·¨è)
set_global = 40308

# å»£æ­æ£é¤è³ª(set_globalè¨­ç½®0:æ£é¤é£½é£åº¦é    set_globalè¨­ç½®å¶ä»:æ£é¤æå®éå·æ¸é)
set_global_count = 2000

# å»£æ­/è²·è³£é »éééç§æ¸
set_global_time = 10
# å¤§åé »éééç§æ¸
set_shout_time = 5

# ç¶é©å¼é¡¯ç¤ºèæ¬åæ¸
# ç¶é©å¼è¨­å®500å éè£¡è¨­ç½®2ç©å®¶æçå°1000å
rate_xp_who = 1

# æªç©æ¯å¦ä¸»åæ»æç´äºº
kill_red = true

# æ°é¬¥ç¹åé­éçå®è­·ç­ç´(å®æ¹é è¨­20)
# è¨­ç½®ç­ç´ä»¥ä¸è§è² è¢«è¶é10ç´ä»¥ä¸çç©å®¶æ»æèæ­»äº¡æï¼ä¸æå¤±å»ç¶é©å¼ï¼ä¹ä¸ææè½ç©å
encounter_lv = 20

# åç¨å éæª¢æ¸¬ï¼trueç¨ï¼flaseä¸ç¨ï¼
speed = true

# åè¨±éçç¯åè³ª
speed_time = 1.02

# å éèç½°æ©å¶ ( 0:åé¤éæ² æ·ç·åæ¬¡å°éå¸³è30å¤©  1:è¡æäººç©10ç§   2:å³å°å°å   3:å³å°GMæ¿ï¼30ç§å¾å³å  4:ç¶è³10ç§)
Punishment = 4

# æ»åæå¹å§æ¯å¦åè¨±æå¸¶å¨å¨  true:åè¨± false:ç¦æ­¢
war_doll = true

#âç©å®¶åºç/ç»å¥æé å¬åâ
#ç©å®¶åºçå¬å (null = ä¸å¬å)
CreateCharInfo = null

#ç©å®¶ä¸ç·æ¯å¦æéGM
WHO_ONLINE_MSG_ON = True
#æ°æåºçåº§æ¨
NEW_CHAR_LOC = 32712,32874,69

#âçªé£å¸é­ä¸éâ
drainedMana = 150

#âæ°æä¿è­·æ©å¶(æ®ç©ºè¨­ç½®) /WHOæééâ
#æ¯å¦éå
newcharpra = false
#ç­ç´éå¶ ä½æ¼å¤å°å¯ä»¥ä¿è­·
newcharpralv = 30


#âç«è²æè½å¼·å¶éé(æªç©æè½)â
dropcolor = flase

#âæå®æªç©(åæªç·¨è) æªç©è¢«æç¡åä½â
AtkNo = 10913,2544

#âæ»åç¦ç¨çé­æ³â
WAR_DISABLE_SKILLS = 33,50,22,63,58,72,66

#âæ»åç¦ç¨çéå·â
WAR_DISABLE_ITEM = 40040,140006

#âç¦æ­¢ä½¿ç¨é­æ³å°åç·¨è(ä½¿ç¨ , åé)â
MAP_IDSKILL = 999,998

#âç¦æ­¢çé­æ³ç·¨è(ä½¿ç¨ , åé)â
MAP_SKILL = 3,4

#âç¦æ­¢ä½¿ç¨éå·å°åç·¨è(ä½¿ç¨ , åé)â
MAP_IDITEM = 0,800,725

#âç¦æ­¢çéå·ç·¨è(ä½¿ç¨ , åé)â
MAP_ITEM = 140006,40006,20077,120077,40205,49403,20077,40265

#âå¯µç©/å¬å æééå¶(é è¨­3600ç§)â
summtime = 3600

#âè½èº«å¾ç­ç´å¤§æ¼50æ¯å¦çµ¦äºåç´é»æ¸è¦çªâ
logpcpower = true

#âèçµçæ§å¶(ç©å®¶)â
IMMUNE_TO_HARM = 1.33

#âèçµçæ§å¶(NPCæªç©)â
IMMUNE_TO_HARM_NPC = 1.33

#âæ¸å·çç²å¤©è³¦å¼·åå· è§¸ç¼ç¼åç type1-type5 (lv1-5)â
#âå¢å·æå¥å¤©è³¦å¼·åå· è§¸ç¼ç¼åç type6-type10 (lv1-5)â
#âçæé ­çå¤©è³¦å¼·åå· è§¸ç¼ç¼åç type11-type15 (lv1-5)â
#âéé¿é·èå¤©è³¦å¼·åå· è§¸ç¼ç¼åç type16-type20 (lv1-5)â
#âé­æ¸æç¯·å¤©è³¦å¼·åå· è§¸ç¼ç¼åç type21-type25 (lv1-5)â
armor_type1 = 3
armor_type2 = 3
armor_type3 = 3
armor_type4 = 3
armor_type5 = 3

armor_type6 = 3
armor_type7 = 3
armor_type8 = 3
armor_type9 = 3
armor_type10 = 3

armor_type11 = 3
armor_type12 = 3
armor_type13 = 3
armor_type14 = 3
armor_type15 = 3

armor_type16 = 1
armor_type17 = 2
armor_type18 = 3
armor_type19 = 5
armor_type20 = 8

armor_type21 = 3
armor_type22 = 3
armor_type23 = 3
armor_type24 = 3
armor_type25 = 3

#âå¨ææ­»ææ¯å¦æ£é¤å¨æâ
dead_score = 5

#âå·å®³é¡¯ç¤ºç·¨èSPRâ
Attack_1 = 19600
Attack_2 = 19610
Attack_3 = 19620
Attack_4 = 19630
Attack_5 = 19640

#âåµå»ºè§è²ååå­ä¸²æ¸éå¶(æªéæåéè¨­ç½®éæå¾å»ºè­°å¿è®åâ
#é è¨­å¼ä¸­æ:5  è±æ:12
#éå¶åµè§åå­éå¶å¹¾åå­
CharNamebyte = 5
#éå¶åµè§è±æéå¶å¹¾åå­
CharNameEnbyte = 12

#âéæå°åéç½®æé(mapids_group)â
#æ¯æ¬¡éåæ¯å¦éç½®
Reset_Map = true
#æå®æééåéç½®(å¦æå®æéReset_Map è«è¼¸å¥false)(6 = æ©ä¸6   18 = æä¸6)
Reset_Map_Time = 18

#âåè±ªNPCæ½æ¾BUFFâ
#å°[è¡ç]æ½æ¾çæ(é«é­.éæ¢.ç¥ç¦.ä¿è­·)
NeedItem = 44070
NeedItemCount = 20
ItemMsg = åå¯¶ä¸è¶³20å
#çµ¦äºçæé­æ³(è¡ç)
Give_skill = 26,42,48
Msg = æè¬[åè±ªç©å®¶:%S]è®æåæéæ²å¯ä»¥ç©ã

#å°[ææç©å®¶]æ½æ¾çæ(é«é­.éæ¢.ç¥ç¦.ä¿è­·)
NeedItem1 = 44070
NeedItemCount1 = 50
ItemMsg1 = åå¯¶ä¸è¶³50å
#çµ¦äºçæé­æ³(å¨é«ç©å®¶)
Give_skill1 = 26,42,48
Msg1 = æè¬[çåè±ªç©å®¶:%S]è®ææç©å®¶åå°çç¥ç¦çåéã

#å°[èªå·±]æ½æ¾çæ(é«é­.éæ¢.ç¥ç¦.ä¿è­·)
NeedItem2 = 44070
NeedItemCount2 = 10
ItemMsg2 = éåå¯¶ä¸è¶³10å
#çµ¦äºçæé­æ³(èªå·±)
Give_skill2 = 26,42,48
Msg2 = å¨èº«åæ»¿åéã
#âéå·-è¯çæ¯æ´ä»¤â
#ä½¿ç¨è¯çæ¯æ´ä»¤æ£é¤éå·æ¸é(æ¥åè¯çæ¯æ´çæ£é¤ä»è³ªåè¡çæ£é¤ä»è³ªå±ç¨)
Call_alliance_itemid=240205
Call_alliance_count=20
#éåºè¯çæ¯æ´ä»¤è¨æ¯ç¸é
alliancemsg=[%Sè¡ç]è¯çæå¡[%S]å¨[%S]ä½¿ç¨[%S],åè»è¬é¦¬ä¾ç¸è¦
alliancemsg1=åå¯¶ä¸è¶³20éæ¢å¬ååè»è¬é¦¬æ¯æ´?
alliancemsg2=éå¹£ä¸è¶³1è¬éæ¢å»æ¯æ´?
#âéå·-è¡çæ¯æ´ä»¤â
#ç©¿é²ç®­è¶æéå¶(è¨­å®5çºç©¿é²ç®­è¨æ¯è¶é5ç§å¾ç¡æï¼é²æ­¢å¡è¨æ¯)
#call_all_reply_second=5 (ä¸æ¡ç¨ï¼Markæ)
#æ¯å¦ç¾å¶çææå¯ä½¿ç¨(false = å¨è·æ¥­åå¯)
AllCall_clan_Crown = false
#ä½¿ç¨æ¯æ´ä»¤æ£é¤éå·æ¸é
Call_clan_itemid = 240205
Call_clan_count = 10
#è¡çåæ¥åæ¯æ´å¬éä»¤æ£é¤éå·æ¸é
target_clan_itemid = 40308
target_clan_count = 0
#éåºè¨æ¯ç¸é
clanmsg = [%Sè¡ç]æå¡[%S]å¨[%S]ä½¿ç¨[%S],åè»è¬é¦¬ä¾ç¸è¦
clanmsg1 = åå¯¶ä¸è¶³10éæ¢å¬ååè»è¬é¦¬æ¯æ´?
clanmsg2 = éå¹£ä¸è¶³1è¬éæ¢å»æ¯æ´?

#âéå·-éä¼æ¯æ´ä»¤â
#ä½¿ç¨æ¯æ´ä»¤æ£é¤éå·æ¸é
Call_party_itemid = 44070
Call_party_count = 5
#éä¼æ¥åæ¯æ´å¬éä»¤æ£é¤éå·æ¸é
target_party_itemid = 40308
target_party_count = 0
#éåºè¨æ¯ç¸é
partymsg = ç©å®¶[%S]å¨[%S]ä½¿ç¨[%S],åè»è¬é¦¬ä¾ç¸è¦
clanmsg5 = åå¯¶ä¸è¶³5éæ¢å¬ååè»è¬é¦¬æ¯æ´
clanmsg6 = éå¹£ä¸è¶³1è¬éæ¢å»æ¯æ´?

#âæè¡é¢¨é²æ¦æ¯å¦é¡¯ç¤ºç­ç´â
RankLevel = false

#âæ¯æ¥ç°½å°ç­ç´éå¶â
day_level = 50
#æ¯æ¥ç°½å°éåæ¯å¦éç½®
#è¨­å® 0 = æ¯æ¬¡éåéç½®
#è¨­å® 18 = æ¯å¤©æä¸6é»éåéç½®(å¶ä»æééåä¸éç½®)
restday = 18

#âæ¯å¦éæ¾é¾é¨å£«è§è²â
#true = éé  false = éæ¾   (é¾é¨å£«)
Illusionistpc = true

#æ¯å¦éæ¾å¹»è¡å£«è§è²
#true = éé  false = éæ¾   (å¹»è¡å£«)
DragonKnightpc = true

#âè¡çç¶é©å æè¨­ç½®â
#è¡çä¸ç·äººæ¸å¤å°é²è¡å æçæ
clancount = 30
#è¡çç¶é©å æ(10=10% 20=20%)
clancountexp = 20

#âçæå®£æ°ç­ç´æ§å¶â
clanLeaderlv = 45

#âæ»åæ¯å¦å¯æå¸¶å®è­·èçéé­â
warProtector = true

#âåæ¶è ç­å®æå¾æ¯å¦èªåæ´è¡â
restsavepclog = true

#âå²å¥æ®-ç¸éè¨­å®â
#76ç­ææéééæ±ç©å
checkitem76 = 40308
#76ç­ææéééæ±ç©åæ¸é
checkitemcount76 = 10000000

#81ç­ææéééæ±ç©å
checkitem81 = 40308
#81ç­ææéééæ±ç©åæ¸é
checkitemcount81 = 30000000

#59ç­è³ç°éééæ±ç©å
checkitem59 = 40308
#59ç­è³ç°éééæ±ç©åæ¸é
checkitemcount59 = 20000000


#âæ¯æ¥åª½ç¥ç¸éè¨­ç½®â
#æ½æ¾éæ±è¨­ç©åç·¨è æ¸é
monbossitem = 40308
monbossitemcount = 10000
#åª½ç¥æææåºæé(å)
montime = 40
#éåæ¯å¦éç½®(è¨­å®0 = éåéç½®) (ä¾:è¨­å®18 = æä¸6é»éåææéç½®å¶ä»ææ®µéåä¸æéç½®)
monsec = 18

#âå»£æ­è¨­å®â
#æ®ºäººå»£æ­ 0 = å°è©±æ¡  1=è¢å¹ä¸­é
killmsg = 1
#æå¯¶å»£æ­ 0 = å°è©±æ¡  1=è¢å¹ä¸­é
dropmsg = 1
#éå¯¶ç¸å»£æ­ 0 = å°è©±æ¡  1=è¢å¹ä¸­é
boxsmsg = 1

#âç­ç´å¤§æ¼å¤å°æ®ºäººå»£æ­â
killlevel = 40


#âç¼ççæ­¡æâ
#å°ç©å®¶ç¼çå·å®³/ è¨­å®å¼
JOY_OF_PAIN_PC = 5
#å°æªç©ç¼çå·å®³/ è¨­å®å¼
JOY_OF_PAIN_NPC = 6
#ç¼çæå¤§å·å®³éå¶
JOY_OF_PAIN_DMG = 500

#âè½èº«ç¸éè¨­å®â
#æ¯å¦çµ¦äºè¿çè¥æ°´
logpcgiveitem = true
#ç­ç´éå¶ä½¿ç¨
logpclevel = 99
#è½çä¿çHP
logpcresthp = 15
#è½çä¿çMP
logpcrestmp = 15
#è½èº«æ¯å¦ä¸çå»£æ­
logpcallmsg = true
#æ¯è½èº«å¤©è³¦çµ¦äºå¹¾é»
logpctfcount = 1

#âè½ç-å¤©è³¦éè£½éæ±â
recaseff = 44070
recaseffcount = 499

#âæ¯å¦éåç¯æ¶èªåç¼ç¦® william_day_presentâ
onlydaypre = false
#æ¯ééæéç¼éä¸æ¬¡(ç§)
onlydaytime = 3600

#âæç©ºè£çæ¨ç®å¹¾å¤©å¾éåâ
dateStartTime = 2

#âå¨å¨æ§å¶â
#å¯æå¸¶é­æ³å¨å¨æ¸ééå¶
dollcount = 1
#å¯æå¸¶è£å©é­æ³å¨å¨æ¸ééå¶
#classnameè¼¸å¥:doll.Magic_Doll_Power
#etcitem_doll_type   è¼¸å¥ mode = 1
dollcount1 = 1

#âå¯µç©-ç­ç´ä¸éâ
petlevel = 50
#å¯µç©ç¶é©
petexp = 20

#âç­ç´å°æ¼å¤å°ç¡æ³é åç·ä¸çåµâ
#æ§å¶çåµé¨åserver_even   OnlineGiftSet
giftset = 1

#âå¤äººçµéç¶é©æ§å¶(åç«é¢)â
#æ¯å¦éååç«é¢çµéç¶é©å ä¹
partyexp = false
#çµéäººæ¸æ¯+1äººç¶é©å¤å¤å°-å¬å¼è¨ç®( çµéäººæ¸ *  1.0+è¨­å®å¼ )
partyexp1 = 0.1

#âçµéäººæ¸éå¶â
partycount = 8

#âé åç­ç´éå¶ [ååº«/è¡ç/å¦å/åäºº]â
warehouselevel = 30

#âshop  shopcnæ¯æ¥éå·è³¼è²·æ¸éä¸ééç½®â
#éåæ¯å¦éç½®(è¨­å®0 = æ¯æ¬¡éåéç½®) (ä¾:è¨­å®18 = æä¸6é»éåææéç½®å¶ä»ææ®µéåä¸æéç½®)
shopitemrest = 0

#âéæ¹äº¤æç­ç´éå¶(å¼·å¶)â
tradelevel = 30

#âæ¯å¦éåæªç©è­å¥é¡è²(ç­ç´é¡è²åå)â
npclevlecolor = false

#âæ¨é ­äººç­ç´ä¸éâ
Scarecrowlevel = 5

#âç©å®¶ç­ç´ä¸éâ
PcLevelUp = 99

#âå¯µç©é²åç­ç´éæ±â
PetLevelUp = 30

#âç©å®¶å´ç©åå°ä¸æ¯å¦ä¸çå»£æ­â
DropitemMsgall = true
#âç©å®¶æ¿åç©åå»£æ­â
Pickupitemtoall = false

#âæ¯å¦éåé¢ç·æºæ¤â
Quitgameshop = false

#âæå®è®èº«(åæªç·¨è) é²çè¡_ç©å®¶è¢«æç¡åä½(è·æ­¥è®èº«å°ç¨)â
AtkNo_pc = 13604,18605,21639,16422,21650,18615,18610,20019,20014,20029,20024,20049,20044,20873,20868,20034,20104,20124,20062

#âå¬å/è¿·é­ æ¸é = [é­å+(é±èé­å)/6] æ°çå¬æ falseâ
#âå¬å/è¿·é­ æ¸é = [é­å / 2] èæä»£å¬æ true â
summoncountcha = false
#ä½¿ç¨[è¿·é­]æå¤æéå¶æ¸é
tamingmonstercount = 6
#ä½¿ç¨[å¬å]æå¤å¬éå¶æ¸é
summmonstercount = 8

#âå¯µç© æ¸é = [é­å+(é±èé­å)/6] æ°çå¬æ falseâ
#âå¯µç© æ¸é = [é­å / 2] èæä»£å¬æ trueâ
petcountcha = false
#å¯µç©æå¸¶ä¸éæ¸é[é­å+(é±èé­å)/6] ä¸æ¹è¨­å®false
petcountchatype = 6
#å¯µç©æå¸¶ä¸éæ¸é[é­å / 2] ä¸æ¹è¨­å®true
petcountchatype1 = 6

#ââæ»åæ°æ¯å¦å¯æå¸¶(å¯µç©/å¬å)ââ
war_pet_summ = false
#âå¯µç©æ[æªç©]é¡å¤æ§å¶å·å®³(ä¹)(0 = ä¸è®, 1.1 = *1.1åå·å®³)â
petdmgother = 1.0
#âå¬åæ[æªç©]é¡å¤æ§å¶å·å®³(ä¹)(0 = ä¸è®, 1.1 = *1.1åå·å®³)â
summondmgother = 1.0

#â(æå­å¤)å¯µç©æ[ç©å®¶]é¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
petdmgotherpc = 0.1
#â(æå­å§)å¯µç©æ[ç©å®¶]é¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
petdmgotherpc_war = 0.1
#â(æå­å¤)å¬åæ[ç©å®¶]é¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
summondmgotherpc = 0.1
#â(æå­å§)å¬åæ[ç©å®¶]é¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
summondmgotherpc_war = 0.1

#â(æå­å§)ç©å®¶æå¯µç©Peté¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
pcdmgpet_war = 2
#â(æå­å¤)ç©å®¶æå¯µç©Peté¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
pcdmgpet = 1
#â(æå­å§)ç©å®¶æå¬åSummé¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
pcdmgsumm_war = 2
#â(æå­å¤)ç©å®¶æå¬åSummé¡å¤æ§å¶å·å®³(ä¹)(1 = ä¸è®, 1.1 = *1.1åå·å®³)â
pcdmgsumm = 1


# ----------------------------------------å´è£å
# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Item_high =32769
Lost_Item_Low  =30000

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count = 4

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd = 100


# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Item_high_2 =29999
Lost_Item_Low_2  =20000

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_2 = 4

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_2 = 90

# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Item_high_3 =19999
Lost_Item_Low_3  =10000

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_3 = 3

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_3 = 90


# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Item_high_4 =9999
Lost_Item_Low_4  =0

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_4 = 1

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_4 = 90


#å·²ä¸æ¯æ­£ç¾©(è)è¨­å®
# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Item_high_5 = 1
Lost_Item_Low_5  = 9999

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_5 = 1

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_5 = 60


# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Item_high_6 = 10000
Lost_Item_Low_6  = 19999

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_6 = 1

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_6 = 50


# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Item_high_7 = 20000
Lost_Item_Low_7  = 29999

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_7 = 1

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_7 = 40

# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Item_high_8 = 30000
Lost_Item_Low_8  = 32766

# æ­»äº¡æè½ç©åæ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Item_Count_8 = 1

# æ­»äº¡æè½ç©åæ©ç(ç¾åæ¯)
Lost_Item_Rnd_8 = 10

# ----------------------------------------å´æè½
# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Skill_high=32769
Lost_Skill_Low=30000

# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count=5
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd=100

# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Skill_high_2=29999
Lost_Skill_Low_2=20000
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_2=4
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_2=90

# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Skill_high_3=19999
Lost_Skill_Low_3=10000
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_3=3
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_3=90

# æ­»äº¡éªæ¡ç¯åå¼(åè² æ¸)
Lost_Skill_high_4=9999
Lost_Skill_Low_4=2
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_4=2
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_4=80

#å·²ä¸æ¯æ­£ç¾©(è)è¨­å®
# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Skill_high_5=1
Lost_Skill_Low_5=9999
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_5=1
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_5=70

# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Skill_high_6=10000
Lost_Skill_Low_6=19999
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_6=1
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_6=60

# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Skill_high_7=20000
Lost_Skill_Low_7=29999
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_7=1
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_7=40

# æ­»äº¡æ­£ç¾©(è)ç¯åå¼
Lost_Skill_high_8=30000
Lost_Skill_Low_8=32766
# æ­»äº¡æè½æè½æ¸é(è¨­å®20 = é¨æ©å´1~20æ¨£)
Lost_Skill_Count_8=1
# æ­»äº¡æè½æè½æ©ç(ç¾åæ¯)
Lost_Skill_Rnd_8=10
#---------------------------------------- é²å´ç¶é©ç¾½æ¯åè½(æ£èåæç)
# æ­»äº¡èç¶é©åå°ä¿è­·(å¼·å¥ªç³»çµ±)
# etcitem(44164)æææ­¤åè½ææ
# LostItem_ON ( 0=ä¸éæ­¤åè½  1=éåæ­¤åè½)
# Lost_Item  æå®æ¶å¥ªéå·
# Lost_Count_1 éå·æ¶èæ¸éæä½
# Lost_Count_2 éå·æ¶èæ¸éæé«
LostItem_ON=1
Lost_Item=240205
Lost_Count_1=1
Lost_Count_2=30
#---------------------------------------- é²å´ç¶é©ç¾½æ¯åè½éé(1 = ä¿è­·ç¶é©å¼   0 = ä¸ä¿è­·ç¶é©å¼)
ExpRateLost=1
#---------------------------------------- é²å´ç¶é©ç¾½æ¯åè½(ä¸æ£èåéå·ï¼ç´æ¥ç²å¾)
# æ­»äº¡èç¶é©åå°ä¿è­·(ç´æ¥ç²å¾éå·ç³»çµ±)
# etcitem(44164)æææ­¤åè½ææ
# LostItem2_ON ( 0=ä¸éæ­¤åè½  1=éåæ­¤åè½)
# Lost_Item2  æå®ç´æ¥ç²å¾éå·
# Lost_Count2_1 éå·æ¶èæ¸éæä½
# Lost_Count2_2 éå·æ¶èæ¸éæé«
LostItem2_ON=0
Lost_Item2=240205
Lost_Count2_1=1
Lost_Count2_2=30
#---------------è¡æä¹æ(çæééï¼0=ä¸è½çæã1=å¯ä»¥çæ)
#---------------ä¸è½çæä¹æéåç¹æ(å¯¦éä¸æ²æ)
SHOCK_STUN_OVERLAY = 0
