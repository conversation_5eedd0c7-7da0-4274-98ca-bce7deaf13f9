# Lineage381 Server Spring Boot Configuration
spring:
  profiles:
    active: dev
  application:
    name: lineage381-server

# 伺服器配置
server:
  port: 2000
  name: "Lineage381 Server"
  version: "Ver_2100381916"
  
  # 資料庫配置
  database:
    host: localhost
    port: 3306
    name: lineage
    username: root
    password: ""
    connection-pool:
      initial-size: 5
      max-size: 20
      min-idle: 5
      max-idle: 10
      timeout: 30000

  # 遊戲配置
  game:
    max-players: 1000
    auto-save-interval: 300
    exp-rate: 1.0
    drop-rate: 1.0
    adena-rate: 1.0
    
  # 安全配置
  security:
    ip-check: true
    anti-hack: true
    max-connections-per-ip: 5
    
  # 日誌配置
  logging:
    level:
      com.lineage: INFO
      org.springframework: WARN
    file:
      name: ./loginfo/lineage-server.log
      max-size: 100MB
      max-history: 30

# 開發環境配置
---
spring:
  config:
    activate:
      on-profile: dev
  application:
    name: lineage381-server-dev

server:
  port: 2000
  database:
    host: localhost
    port: 3306
    name: lineage_dev
    username: root
    password: ""

# 生產環境配置
---
spring:
  config:
    activate:
      on-profile: prod
  application:
    name: lineage381-server-prod

server:
  port: 2000
  database:
    host: localhost
    port: 3306
    name: lineage_prod
    username: lineage_user
    password: "secure_password"

# 測試環境配置
---
spring:
  config:
    activate:
      on-profile: test
  application:
    name: lineage381-server-test

server:
  port: 2001
  database:
    host: localhost
    port: 3306
    name: lineage_test
    username: test_user
    password: "test_password" 