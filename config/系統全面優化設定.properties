# ========================================
# å¤©å æ ¸å¿ç³»çµ±å¨é¢åªåè¨­å®æª
# çæ¬: 1.0.0
# å»ºè­°æ¾ç½®æ¼ config/ ç®éä¸ï¼ä¸¦æ¼ååæµç¨èªåè¼å¥
# ========================================

# åç¨ç³»çµ±å¨é¢åªå (true/false)
ENABLE_SYSTEM_OPTIMIZATION=true

# åç¨è¨æ¶é«ç£æ§ (true/false)
ENABLE_MEMORY_MONITORING=true

# åç¨æ§è½ç£æ§ (true/false)
ENABLE_PERFORMANCE_MONITORING=true

# åç¨ç¶²è·¯åªå (true/false)
ENABLE_NETWORK_OPTIMIZATION=true

# åç¨è³æåº«åªå (true/false)
ENABLE_DATABASE_OPTIMIZATION=true

# åç¨å¿«ååªå (true/false)
ENABLE_CACHE_OPTIMIZATION=true

# åç¨ç·ç¨æ± åªå (true/false)
ENABLE_THREAD_OPTIMIZATION=true

# ========================================
# è¨æ¶é«ç®¡ç
# ========================================
# è¨æ¶é«è­¦åé¾å¼ (0~1, 0.8=80%)
MEMORY_WARNING_THRESHOLD=0.8
# è¨æ¶é«å´éè­¦åé¾å¼ (0~1, 0.9=90%)
MEMORY_CRITICAL_THRESHOLD=0.9
# èªåGCè§¸ç¼é¾å¼ (0~1, 0.7=70%)
AUTO_GC_THRESHOLD=0.7

# ========================================
# å¿«åç³»çµ±
# ========================================
# L1å¿«åå¤§å° (è¨æ¶é«å¿«å)
L1_CACHE_SIZE=10000
# L2å¿«åå¤§å° (ç£ç¢å¿«å)
L2_CACHE_SIZE=50000
# å¿«åå­æ´»æé(ç§)
CACHE_TTL=3600
# å¿«åèªåæ¸çéé(ç§)
CACHE_CLEANUP_INTERVAL=300

# ========================================
# ç·ç¨æ± 
# ========================================
GAME_THREAD_POOL_SIZE=8
DATABASE_THREAD_POOL_SIZE=4
NETWORK_THREAD_POOL_SIZE=4

# ========================================
# æ¹æ¬¡èç
# ========================================
BATCH_SIZE=100
BATCH_TIMEOUT=5000
MAX_BATCH_QUEUE_SIZE=1000

# ========================================
# ç£æ§èå¥åº·æª¢æ¥
# ========================================
# æ§è½ç£æ§éé(ç§)
PERFORMANCE_CHECK_INTERVAL=60
# è¨æ¶é«ç£æ§éé(ç§)
MEMORY_CHECK_INTERVAL=30
# å¥åº·æª¢æ¥éé(ç§)
HEALTH_CHECK_INTERVAL=300

# ========================================
# æ¥èªèé¤é¯
# ========================================
ENABLE_DETAILED_LOGGING=false
LOG_LEVEL=INFO

# ========================================
# åè¨»
# æ¬æªæ¡å¯é¨æèª¿æ´ï¼ä¿®æ¹å¾å»ºè­°éåSERVERä»¥å¥ç¨æ°è¨­å®ã
# è¥éç±éè¼ï¼è«æ¼ç®¡çä»é¢æGMæä»¤è§¸ç¼ã 